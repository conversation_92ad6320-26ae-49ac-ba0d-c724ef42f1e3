# Adnomio API Dokümantasyonu

Bu do<PERSON>üman, Adnomio projesinde kullanılan backend API endpointlerini, frontend'deki kullanımlarını ve ilgili yardımcı fonksiyon/hook'ları açıklamaktadır.

Backend API, [Swagger dokümantasyonu](https://devapi.adnomio.com/api-docs/#/) ile desteklenmektedir.

## Endpoint Listesi

API dokümantasyonuna Swagger endpointleri sırasıyla eklenmiştir.

### 1. User Controller

*   **POST /user/login**
    *   **Açıklama:** Kullanıcı girişi yapar. Başarılı durumda kullanıcı bilgilerini ve kimlik doğrulama tokenını döndürür.
    *   **Parametreler:**
        *   `email` (string): Kullanıcı email adresi.
        *   `password` (string): Kullanıcı şifresi.
    *   **Frontend Kullanımı:** `authService.login` fonksiyonu ile çağrılır. Genellikle login sayfasında veya kimlik doğrulama akışında kullanılır. Token ve kullanıcı bilgisi `localStorage`'a kaydedilir.
    *   **İlgili Frontend Kodu:**
        ```68:84:src/shared/services/auth-service.ts
// ... existing code ...
```

*   **POST /user/register**
    *   **Açıklama:** Yeni bir kullanıcı hesabı oluşturur (Yayıncı veya Reklam Veren rolü için).
    *   **Parametreler:** Kullanıcı bilgileri (email, password, name, surname, created_at). Rol bilgisi backend tarafından belirleniyor olabilir.
    *   **Yetkilendirme:** Gerektirmez.
    *   **Frontend Kullanımı:** `publisherRegister` veya `adminRegister` gibi role özel fonksiyonlar bu endpointi veya benzerini çağırıyor olabilir. (Kod tabanında tam eşleşme bulunamadı, bu endpointin genel register veya role özel register endpointlerinden biri olduğu tahmin ediliyor.)
    *   **İlgili Frontend Kodu:**
        ```23:31:src/shared/services/publisher/publisher-register.ts
// ... existing code ...
```
        ```29:42:src/shared/services/admin/admin-register.ts
// ... existing code ...
```

*   **GET /user/logout**
    *   **Açıklama:** Kullanıcının oturumunu kapatır. Frontend'de `localStorage`'daki kullanıcı bilgilerini siler.
    *   **Parametreler:** Yok
    *   **Yetkilendirme:** Gerektirir (Authorization header ile token gönderilmelidir).
    *   **Frontend Kullanımı:** `authService.logout` fonksiyonu ile çağrılır. Genellikle çıkış butonuna tıklandığında veya oturum süresi dolduğunda kullanılır. `useAuth` hook'u bu fonksiyonu kullanır.
    *   **İlgili Frontend Kodu:**
        ```71:82:src/shared/hooks/useAuth.ts
// ... existing code ...
```
        ```68:82:src/shared/services/auth-service.ts
// ... existing code ...
```

*   **POST /user/list**
    *   **Açıklama:** Kullanıcıları listeler. Admin paneli için kullanılır.
    *   **Parametreler:** Sayfalama, filtreleme (limit, skip, status, role, search)
    *   **Yetkilendirme:** Gerektirir (Admin rolü ve Authorization header ile token gönderilmelidir).
    *   **Frontend Kullanımı:** `authService.listUsers` fonksiyonu ile çağrılır. Admin paneli kullanıcı listeleme sayfalarında kullanılır.
    *   **İlgili Frontend Kodu:**
        ```148:165:src/shared/services/auth-service.ts
// ... existing code ...
```

### 2. Admin Controller

*   **GET /admin/user/statistics**
    *   **Açıklama:** Admin paneli için kullanıcı istatistiklerini getirir.
    *   **Parametreler:** Yok
    *   **Yetkilendirme:** Gerektirir (Admin rolü ve Authorization header ile token gönderilmelidir).
    *   **Frontend Kullanımı:** `fetchAdminUserStatistics` fonksiyonu ile çağrılır. Admin paneli dashboard veya istatistik sayfalarında kullanılır.
    *   **İlgili Frontend Kodu:**
        ```74:82:src/shared/services/admin/admin-list.ts
// ... existing code ...
```

*   **POST /admin/user/list**
    *   **Açıklama:** Admin paneli için kullanıcıların listesini getirir.
    *   **Parametreler:** Sayfalama, filtreleme (limit, skip, status, role)
    *   **Yetkilendirme:** Gerektirir (Admin rolü ve Authorization header ile token gönderilmelidir).
    *   **Frontend Kullanımı:** `fetchAdminUserList` fonksiyonu ile çağrılır. Admin paneli kullanıcı listeleme sayfalarında kullanılır.
    *   **İlgili Frontend Kodu:**
        ```38:48:src/shared/services/admin/admin-list.ts
// ... existing code ...
```

*   **POST /admin/notification/list**
    *   **Açıklama:** Admin paneli için bildirimlerin listesini getirir.
    *   **Parametreler:** Sayfalama, filtreleme (limit, skip, user_type, process_type)
    *   **Yetkilendirme:** Gerektirir (Admin rolü ve Authorization header ile token gönderilmelidir).
    *   **Frontend Kullanımı:** `fetchAdminNotificationList` fonksiyonu ile çağrılır. Admin paneli bildirim sayfalarında kullanılır.
    *   **İlgili Frontend Kodu:**
        ```41:50:src/shared/services/admin/admin-notification-list.ts
// ... existing code ...
```

### 3. Advertiser Controller

*   **POST /advertiser/campaign/create**
    *   **Açıklama:** Yeni bir reklam veren kampanyası oluşturur.
    *   **Parametreler:** Kampanya detayları (title, campaign_aim, advertising_platform, country, category, campaign_title, campaign_desc, destination_url, campaign_image, cta_button, campaign_budget, bid_strategy, cpm_bid_price, cpc_bid_price, brand_safety_check, starts_at, ends_at, status, language)
    *   **Yetkilendirme:** Gerektirir (Reklam veren rolü ve Authorization header ile token gönderilmelidir).
    *   **Frontend Kullanımı:** `fetchCampaignCreate` fonksiyonu ile çağrılır. Reklam veren paneli kampanya oluşturma sayfalarında kullanılır. Yetkilendirme ve zaman aşımı hata yönetimi içerir.
    *   **İlgili Frontend Kodu:**
        ```40:53:src/shared/services/advertiser/advertiser-campaign-create.ts
// ... existing code ...
```
    *   **Not:** Endpoint adresi `API_CONFIG`'den alınmaktadır.

*   **POST /advertiser/campaign/list**
    *   **Açıklama:** Reklam verenin kampanyalarının listesini getirir.
    *   **Parametreler:** Sayfalama, filtreleme (limit, skip, status)
    *   **Yetkilendirme:** Gerektirir (Reklam veren rolü ve Authorization header ile token gönderilmelidir).
    *   **Frontend Kullanımı:** `fetchCampaignList` fonksiyonu ile çağrılır. Reklam veren paneli kampanya listeleme sayfalarında kullanılır.
    *   **İlgili Frontend Kodu:**
        ```84:98:src/shared/services/advertiser/advertiser-campaign-list.ts
// ... existing code ...
```

*   **POST /advertiser/campaign/delete**
    *   **Açıklama:** Belirli bir reklam veren kampanyasını siler.
    *   **Parametreler:** `campaign_id` (string): Silinecek kampanya ID'si.
    *   **Yetkilendirme:** Gerektirir (Reklam veren rolü ve Authorization header ile token gönderilmelidir).
    *   **Frontend Kullanımı:** `fetchCampaignDelete` fonksiyonu ile çağrılır. Kampanya listeleme ekranında silme butonu actions içinde kullanılır.
    *   **İlgili Frontend Kodu:**
        ```62:71:src/shared/services/advertiser/advertiser-campaign-list.ts
// ... existing code ...
```

*   **GET /advertiser/campaign/statistics**
    *   **Açıklama:** Reklam verenin kampanyalarına ait genel istatistikleri getirir.
    *   **Parametreler:** Yok
    *   **Yetkilendirme:** Gerektirir (Reklam veren rolü ve Authorization header ile token gönderilmelidir).
    *   **Frontend Kullanımı:** `fetchCampaignStatistics` fonksiyonu ile çağrılır. Reklam veren paneli dashboard veya istatistik sayfalarında kullanılır.
    *   **İlgili Frontend Kodu:**
        ```117:125:src/shared/services/advertiser/advertiser-campaign-list.ts
// ... existing code ...
```

*   **GET /advertiser/lastCampaign**
    *   **Açıklama:** Reklam verenin son kampanyalarının bir listesini getirir.
    *   **Parametreler:** Yok
    *   **Yetkilendirme:** Gerektirir (Reklam veren rolü ve Authorization header ile token gönderilmelidir).
    *   **Frontend Kullanımı:** `fetchLastCampaigns` fonksiyonu ile çağrılır. Reklam veren paneli dashboard gibi alanlarda son kampanyaları göstermek için kullanılır.
    *   **İlgili Frontend Kodu:**
        ```22:33:src/shared/services/advertiser/advertiser-last-campaigns.ts
// ... existing code ...
```

*   **POST /advertiser/balanceAdd**
    *   **Açıklama:** Reklam verenin hesabına bakiye ekler.
    *   **Parametreler:** `amount` (number), `payment_method` (string: 'CREDIT_CARD', 'BANK_TRANSFER', 'PAYPAL').
    *   **Yetkilendirme:** Gerektirir (Reklam veren rolü ve Authorization header ile token gönderilmelidir).
    *   **Frontend Kullanımı:** `fetchBalanceAdd` fonksiyonu ile çağrılır. Reklam veren paneli bakiye ekleme sayfalarında kullanılır.
    *   **İlgili Frontend Kodu:**
        ```24:37:src/shared/services/advertiser/advertiser-balance-add.ts
// ... existing code ...
```

*   **POST /advertiser/transactionList**
    *   **Açıklama:** Reklam verenin işlem geçmişini getirir.
    *   **Parametreler:** Sayfalama, filtreleme (limit, skip, search, status, type, date)
    *   **Yetkilendirme:** Gerektirir (Reklam veren rolü ve Authorization header ile token gönderilmelidir).
    *   **Frontend Kullanımı:** `fetchTransactionList` fonksiyonu ile çağrılır. Reklam veren paneli işlem geçmişi sayfalarında kullanılır.
    *   **İlgili Frontend Kodu:**
        ```43:57:src/shared/services/advertiser/advertiser-transaction-list.ts
// ... existing code ...
```

*   **GET /advertiser/dashboard/campaignStatistics**
    *   **Açıklama:** Reklam veren dashboard'u için genel kampanya istatistiklerini getirir.
    *   **Parametreler:** Yok
    *   **Yetkilendirme:** Gerektirir (Reklam veren rolü ve Authorization header ile token gönderilmelidir).
    *   **Frontend Kullanımı:** `fetchDashboardStats` fonksiyonu ile çağrılır. Reklam veren paneli dashboard sayfasında kullanılır. Yetkilendirme ve zaman aşımı hata yönetimi içerir.
    *   **İlgili Frontend Kodu:**
        ```19:35:src/shared/services/advertiser/advertiser-dashboard-stats.ts
// ... existing code ...
```

*   **POST /advertiser/analytics/campaignCommunityList**
    *   **Açıklama:** Reklam verenle ilişkili toplulukların listesini getirir.
    *   **Parametreler:** Sayfalama, filtreleme (skip, limit, campaign_id, search)
    *   **Yetkilendirme:** Gerektirir (Reklam veren rolü ve Authorization header ile token gönderilmelidir).
    *   **Frontend Kullanımı:** `fetchCommunityList` fonksiyonu ile çağrılır. Reklam veren paneli topluluk listeleme veya analiz sayfalarında kullanılır.
    *   **İlgili Frontend Kodu:**
        ```37:51:src/shared/services/advertiser/advertiser-community-list.ts
// ... existing code ...
```

*   **GET /advertiser/analytics/campaignDailyPerformanceAnalysis**
    *   **Açıklama:** Reklam veren kampanyalarının günlük performans verilerini getirir.
    *   **Parametreler:** İsteğe bağlı `campaignId` (string).
    *   **Yetkilendirme:** Gerektirir (Reklam veren rolü ve Authorization header ile token gönderilmelidir).
    *   **Frontend Kullanımı:** `fetchDailyPerformance` fonksiyonu ile çağrılır. Reklam veren paneli performans veya analiz grafiklerinde kullanılır.
    *   **İlgili Frontend Kodu:**
        ```22:38:src/shared/services/advertiser/advertiser-daily-performance.ts
// ... existing code ...
```

*   **POST /advertiser/analytics/campaignPerformanceAnalysis**
    *   **Açıklama:** Reklam veren kampanyalarının belirli bir zaman filtresine göre performans analizini getirir.
    *   **Parametreler:** `filter` (string: 'week', 'month', 'three_month', 'year'), isteğe bağlı `campaign_id` (string).
    *   **Yetkilendirme:** Gerektirir (Reklam veren rolü ve Authorization header ile token gönderilmelidir).
    *   **Frontend Kullanımı:** `fetchPerformanceAnalysis` fonksiyonu ile çağrılır. Reklam veren paneli performans analiz grafiklerinde veya raporlarında kullanılır.
    *   **İlgili Frontend Kodu:**
        ```31:45:src/shared/services/advertiser/advertiser-performance-analysis.ts
// ... existing code ...
```

### 4. Publisher Controller

*   **POST /publisher/community/create**
    *   **Açıklama:** Yeni bir yayıncı topluluğu oluşturur.
    *   **Parametreler:** Topluluk detayları (community_username, community_name, category, country, language)
    *   **Yetkilendirme:** Gerektirir (Yayıncı rolü ve Authorization header ile token gönderilmelidir).
    *   **Frontend Kullanımı:** `createPublisherCommunityFirst` fonksiyonu ile çağrılır. Yayıncı paneli ilk topluluk oluşturma veya topluluk ekleme sayfalarında kullanılır.
    *   **İlgili Frontend Kodu:**
        ```22:36:src/shared/services/publisher/community-create-first.ts
// ... existing code ...
```

*   **POST /publisher/community/list**
    *   **Açıklama:** Yayıncının topluluklarının listesini getirir.
    *   **Parametreler:** Sayfalama (limit, skip)
    *   **Yetkilendirme:** Gerektirir (Yayıncı rolü ve Authorization header ile token gönderilmelidir).
    *   **Frontend Kullanımı:** `getCommunityList` fonksiyonu ile çağrılır. Yayıncı paneli topluluk listeleme sayfalarında kullanılır.
    *   **İlgili Frontend Kodu:**
        ```39:52:src/shared/services/publisher/community-list.ts
// ... existing code ...
```

*   **POST /publisher/community/delete**
    *   **Açıklama:** Belirli bir yayıncı topluluğunu siler.
    *   **Parametreler:** `community_id` (string): Silinecek topluluk ID'si.
    *   **Yetkilendirme:** Gerektirir (Yayıncı rolü ve Authorization header ile token gönderilmelidir).
    *   **Frontend Kullanımı:** `deleteCommunity` fonksiyonu ile çağrılır. Yayıncı paneli topluluk listeleme ekranında silme butonu actions içinde kullanılır.
    *   **İlgili Frontend Kodu:**
        ```68:81:src/shared/services/publisher/community-list.ts
// ... existing code ...
```

*   **POST /publisher/transactionHistory**
    *   **Açıklama:** Yayıncının işlem geçmişini getirir.
    *   **Parametreler:** Sayfalama, filtreleme (limit, skip, search, status)
    *   **Yetkilendirme:** Gerektirir (Yayıncı rolü ve Authorization header ile token gönderilmelidir).
    *   **Frontend Kullanımı:** `getTransactionHistory` fonksiyonu ile çağrılır. Yayıncı paneli işlem geçmişi sayfalarında kullanılır.
    *   **İlgili Frontend Kodu:**
        ```43:56:src/shared/services/publisher/transaction-history.ts
// ... existing code ...
```

*   **POST /publisher/withdraw/request**
    *   **Açıklama:** Yayıncı için para çekme talebi oluşturur.
    *   **Parametreler:** Çekim detayları (amount, payment_method, iban, bank_name, account_holder_name, description)
    *   **Yetkilendirme:** Gerektirir (Yayıncı rolü ve Authorization header ile token gönderilmelidir).
    *   **Frontend Kullanımı:** `requestWithdrawal` fonksiyonu ile çağrılır. Yayıncı paneli para çekme sayfalarında kullanılır.
    *   **İlgili Frontend Kodu:**
        ```94:109:src/shared/services/publisher/transaction-history.ts
// ... existing code ...
```

*   **GET /publisher/balanceAndEarnings**
    *   **Açıklama:** Yayıncının genel bakiye ve kazanç özetini getirir.
    *   **Parametreler:** Yok
    *   **Yetkilendirme:** Gerektirir (Yayıncı rolü ve Authorization header ile token gönderilmelidir).
    *   **Frontend Kullanımı:** `getBalanceAndEarnings` fonksiyonu ile çağrılır. Yayıncı paneli dashboard veya finans sayfalarında kullanılır.
    *   **İlgili Frontend Kodu:**
        ```15:28:src/shared/services/publisher/balance-and-earnings.ts
// ... existing code ...
```

*   **GET /publisher/earnings/get**
    *   **Açıklama:** Yayıncının daha detaylı kazanç bilgilerini getirir.
    *   **Parametreler:** Yok
    *   **Yetkilendirme:** Gerektirir (Yayıncı rolü ve Authorization header ile token gönderilmelidir).
    *   **Frontend Kullanımı:** `getEarningsDetails` fonksiyonu ile çağrılır. Yayıncı paneli kazanç detay sayfalarında kullanılır.
    *   **İlgili Frontend Kodu:**
        ```60:73:src/shared/services/publisher/balance-and-earnings.ts
// ... existing code ...
```

*   **GET /publisher/paymentMethod/list**
    *   **Açıklama:** Yayıncının eklediği ödeme yöntemlerinin bir listesini getirir.
    *   **Parametreler:** Yok
    *   **Yetkilendirme:** Gerektirir (Yayıncı rolü ve Authorization header ile token gönderilmelidir).
    *   **Frontend Kullanımı:** `getPaymentMethodList` fonksiyonu ile çağrılır. Yayıncı paneli ödeme yöntemleri sayfalarında kullanılır.
    *   **İlgili Frontend Kodu:**
        ```26:39:src/shared/services/publisher/payment-methods.ts
// ... existing code ...
```

*   **GET /publisher/communities/top-performers**
    *   **Açıklama:** Yayıncının en iyi performans gösteren (en çok kazandıran) topluluklarının bir listesini getirir.
    *   **Parametreler:** Yok
    *   **Yetkilendirme:** Gerektirir (Yayıncı rolü ve Authorization header ile token gönderilmelidir).
    *   **Frontend Kullanımı:** `getTopPerformingCommunities` fonksiyonu ile çağrılır. Yayıncı paneli dashboard gibi alanlarda veya topluluk performans raporlarında kullanılır.
    *   **İlgili Frontend Kodu:**
        ```21:35:src/shared/services/publisher/community-service.ts
// ... existing code ...
```

*   **POST /publisher/notification/list**
    *   **Açıklama:** Yayıncının bildirimlerinin listesini getirir.
    *   **Parametreler:** Sayfalama, filtreleme (limit, skip, status)
    *   **Yetkilendirme:** Gerektirir (Yayıncı rolü ve Authorization header ile token gönderilmelidir).
    *   **Frontend Kullanımı:** `getNotificationList` fonksiyonu ile çağrılır. Yayıncı paneli bildirim sayfalarında kullanılır.
    *   **İlgili Frontend Kodu:**
        ```44:53:src/shared/services/publisher/publisher-notifications.ts
// ... existing code ...
```

*   **GET /publisher/notification/readAll**
    *   **Açıklama:** Yayıncının tüm bildirimlerini okundu olarak işaretler.
    *   **Parametreler:** Yok
    *   **Yetkilendirme:** Gerektirir (Yayıncı rolü ve Authorization header ile token gönderilmelidir).
    *   **Frontend Kullanımı:** `readAllNotifications` fonksiyonu ile çağrılır. Yayıncı paneli bildirim sayfalarında "Tümünü Okundu İşaretle" gibi bir butonda kullanılır.
    *   **İlgili Frontend Kodu:**
        ```75:83:src/shared/services/publisher/publisher-notifications.ts
// ... existing code ...
```

*   **GET /publisher/notification/deleteAll**
    *   **Açıklama:** Yayıncının tüm bildirimlerini siler.
    *   **Parametreler:** Yok
    *   **Yetkilendirme:** Gerektirir (Yayıncı rolü ve Authorization header ile token gönderilmelidir).
    *   **Frontend Kullanımı:** `deleteAllNotifications` fonksiyonu ile çağrılır. Yayıncı paneli bildirim sayfalarında "Tümünü Sil" gibi bir butonda kullanılır.
    *   **İlgili Frontend Kodu:**
        ```100:108:src/shared/services/publisher/publisher-notifications.ts
// ... existing code ...
```

*   **POST /publisher/notification/get**
    *   **Açıklama:** Belirli bir yayıncı bildirimini getirir.
    *   **Parametreler:** `notification_id` (string): Bildirim ID'si.
    *   **Yetkilendirme:** Gerektirir (Yayıncı rolü ve Authorization header ile token gönderilmelidir).
    *   **Frontend Kullanımı:** `getNotification` fonksiyonu ile çağrılır. Bildirim detaylarını göstermek için kullanılır (varsayımsal kullanım).
    *   **İlgili Frontend Kodu:**
        ```125:138:src/shared/services/publisher/publisher-notifications.ts
// ... existing code ...
```

*   **POST /publisher/notification/read**
    *   **Açıklama:** Belirli bir yayıncı bildirimini okundu olarak işaretler.
    *   **Parametreler:** `notification_id` (string): Bildirim ID'si.
    *   **Yetkilendirme:** Gerektirir (Yayıncı rolü ve Authorization header ile token gönderilmelidir).
    *   **Frontend Kullanımı:** `readNotification` fonksiyonu ile çağrılır. Bir bildirime tıklandığında veya detay sayfası açıldığında kullanılır.
    *   **İlgili Frontend Kodu:**
        ```156:169:src/shared/services/publisher/publisher-notifications.ts
// ... existing code ...
```

*   **GET /publisher/dashboard/campaignPostStatistics**
    *   **Açıklama:** Yayıncı dashboard'u için genel kampanya istatistiklerini getirir.
    *   **Parametreler:** Yok
    *   **Yetkilendirme:** Gerektirir (Yayıncı rolü ve Authorization header ile token gönderilmelidir).
    *   **Frontend Kullanımı:** `getDashboardStatistics` fonksiyonu ile çağrılır. Yayıncı paneli dashboard sayfasında kullanılır.
    *   **İlgili Frontend Kodu:**
        ```19:31:src/shared/services/publisher/dashboard-statistics.ts
// ... existing code ...
```

## Ortak Kullanılan Helper Fonksiyonlar ve Hook'lar

*   **`authService` (src/shared/services/auth-service.ts):** Kullanıcı kimlik doğrulama tokenını almak (`getAuthToken`), mevcut kullanıcıyı elde etmek (`getCurrentUser`), kullanıcının kimlik doğrulaması yapılıp yapılmadığını kontrol etmek (`isAuthenticated`) gibi işlemler için kullanılır. Birçok API servisi bu servisteki `getAuthToken` fonksiyonunu kullanarak isteklere Authorization header'ını ekler.
    *   **İlgili Kod:**
        ```116:132:src/shared/services/auth-service.ts
// ... existing code ...
```
        ```99:114:src/shared/services/auth-service.ts
// ... existing code ...
```
        ```134:138:src/shared/services/auth-service.ts
// ... existing code ...
```

*   **`useNotificationStore` (src/shared/services/notification-service.ts):** Zustand kullanılarak oluşturulmuş bir hook'tur. Frontend tarafında bildirimlerin durumunu (liste, okunmamış sayısı) yönetmek için kullanılır. API'den bildirimler çekildikten sonra bu store güncellenebilir veya frontend etkileşimleriyle (okundu işaretleme, silme) durumu yönetilebilir.
    *   **İlgili Kod:**
        ```38:65:src/shared/services/notification-service.ts
// ... existing code ...
```

*   **`useAuth` (src/shared/hooks/useAuth.ts):** Kullanıcı kimlik doğrulama durumunu (giriş yapıldı mı, hangi rolde) sağlayan ve login/logout akışlarını yöneten custom hook. Sayfa erişim kontrolü ve yönlendirme için kullanılır. `authService`'ı kullanır.
    *   **İlgili Kod:**
        ```13:89:src/shared/hooks/useAuth.ts
// ... existing code ...
```

## Dökümanı Paylaşıma Açma

Bu doküman oluşturulduktan sonra, ekip içinde kolayca erişilebilir bir yere (örneğin, projenin GitLab/GitHub deposuna, Confluence/Notion sayfasına veya paylaşılan bir ağ sürücüsüne) konulmalıdır. Dokümanın güncel tutulması önemlidir.

## Endpoint Tamamlama Durumu

Bu liste, [Swagger dokümantasyonu](https://devapi.adnomio.com/api-docs/#/) ve bu doküman (`API_Dokumantasyonu.md`) baz alınarak endpointlerin mevcut tamamlama durumunu göstermektedir.

*   POST /user/login (Endpoint tamamlandı ) +
*   POST /user/register (Endpoint tamamlandı ) +
*   GET /user/logout (Endpoint tamamlandı ) +
*   POST /user/list (Endpoint tamamlandı ) +
*   GET /admin/user/statistics (Endpoint tamamlandı ) +
*   POST /admin/user/list (Endpoint tamamlandı ) +
*   POST /admin/notification/list (Endpoint tamamlandı ) +
*   POST /advertiser/campaign/create (Endpoint tamamlandı ) +
*   POST /advertiser/campaign/list (Endpoint tamamlandı ) +
*   POST /advertiser/campaign/delete (Endpoint tamamlandı ) +
*   GET /advertiser/campaign/statistics (Endpoint tamamlandı ) +
*   GET /advertiser/lastCampaign (Endpoint tamamlandı ) +
*   POST /advertiser/balanceAdd (Endpoint tamamlandı ) +
*   POST /advertiser/transactionList (Endpoint tamamlandı ) +
*   GET /advertiser/dashboard/campaignStatistics (Endpoint tamamlandı ) +
*   POST /advertiser/analytics/campaignCommunityList (Endpoint tamamlandı ) +
*   GET /advertiser/analytics/campaignDailyPerformanceAnalysis (Endpoint tamamlandı ) +
*   POST /advertiser/analytics/campaignPerformanceAnalysis (Endpoint tamamlandı ) +
*   POST /publisher/community/create (Endpoint tamamlandı ) +
*   POST /publisher/community/list (Endpoint tamamlandı ) +
*   POST /publisher/community/delete (Endpoint tamamlandı ) +
*   POST /publisher/transactionHistory (Endpoint tamamlandı ) +
*   POST /publisher/withdraw/request (Endpoint tamamlandı ) +
*   GET /publisher/balanceAndEarnings (Endpoint tamamlandı ) +
*   GET /publisher/earnings/get (Endpoint tamamlandı ) +
*   GET /publisher/paymentMethod/list (Endpoint tamamlandı ) +
*   GET /publisher/communities/top-performers (Endpoint tamamlandı ) +
*   POST /publisher/notification/list (Endpoint tamamlandı ) +
*   GET /publisher/notification/readAll (Endpoint tamamlandı ) +
*   GET /publisher/notification/deleteAll (Endpoint tamamlandı ) +
*   POST /publisher/notification/get (Endpoint tamamlandı ) +
*   POST /publisher/notification/read (Endpoint tamamlandı ) +
*   GET /publisher/dashboard/campaignPostStatistics (Endpoint tamamlandı ) +
*  

---

[Swagger Dokümantasyonu](https://devapi.adnomio.com/api-docs/#/)

## Frontend Tarafında Bulunmayan veya Eksik Olabilecek Endpointler

Bu liste, [Swagger dokümantasyonu](https://devapi.adnomio.com/api-docs/#/) referans alınarak, frontend kod tabanında (`src/shared/services` ve `src/shared/hooks` dizinleri incelendiğinde) doğrudan kullanımına rastlanmayan veya eksik olabileceği düşünülen endpointleri içermektedir. Backend'de tanımlı olup frontend'de henüz entegre edilmemiş olabilirler.

*   GET /system/locales/language (Frontend kodunda bulunamadı)
*   GET /system/locales/country (Frontend kodunda bulunamadı)
*   GET /system/communityStatusCheck (Frontend kodunda bulunamadı)
*   GET /system/campaignStatusCheck (Frontend kodunda bulunamadı)
*   GET /system/campaignPostCheck (Frontend kodunda bulunamadı)
*   GET /system/campaignPostDelete (Frontend kodunda bulunamadı)
*   POST /upload/create (Frontend kodunda bulunamadı)
*   POST /upload/list (Frontend kodunda bulunamadı)
*   POST /advertiser/campaign/pause (Servis dosyası boş)
*   POST /advertiser/campaign/resume (Servis dosyası boş)
*   Kullanıcı bilgilerini güncelleme veya silme ile ilgili endpointler (Örn: /user/update, /user/delete)
*   Admin, Reklam Veren veya Yayıncı profillerini/ayarlarını güncelleme ile ilgili endpointler.
*   Ödeme yöntemlerini ekleme, güncelleme veya silme ile ilgili endpointler (Yayıncı için listeleme dışında).
*   Bildirimleri güncelleme veya silme ile ilgili daha spesifik endpointler (Tümünü silme/okuma dışında).
*   Swagger dokümantasyonunda listelenen ancak mevcut frontend servis dosyalarında karşılığı bulunmayan diğer endpointler.

  