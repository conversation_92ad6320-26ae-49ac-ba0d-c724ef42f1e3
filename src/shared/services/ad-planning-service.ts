import { API_CONFIG, ApiResponse } from "./api-config";
import { authService } from "./auth-service";

// Ad planning API yanıt tipleri
export interface AdStatistics {
  approved_ads: number;
  pending_ads: number;
  total_ads: number;
  total_channels: number;
}

export interface CommunityDetails {
  member_count: number;
  category: string;
  views: number;
  status: string;
  community_id: string;
}

export interface Campaign {
  campaign_id: string;
  community_username: string;
  campaign_budget: number;
  spending: number;
  views: number;
  pending_status_count: number;
  approved_status_count: number;
  total_posts_count: number;
  community_details: CommunityDetails;
}

export interface HourlyData {
  hour: string;
  hour_start: string;
  hour_end: string;
  campaigns: Campaign[];
}

// Ad planning servisi
class AdPlanningService {
  /**
   * Reklam istatistiklerini al
   * @param date YYYY-MM-DD formatında tarih
   * @returns ApiResponse<AdStatistics>
   */
  async getAdvertStatistics(date: string): Promise<ApiResponse<AdStatistics>> {
    const token = authService.getAuthToken();
    
    try {
      const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.ADMIN.ADVERT_STATISTICS}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          ...(token && { "Authorization": token })
        },
        body: JSON.stringify({ date })
      });
      
      const data = await response.json();
      return data;
    } catch (error) {
      console.error("İstatistik verisi alma hatası:", error);
      return {
        status: false,
        errorCode: "API_ERROR",
        operation: "GET_ADVERT_STATISTICS",
        httpStatus: 500,
        desc: error instanceof Error ? error.message : "İstatistik verisi alınırken bir hata oluştu",
        result: {
          approved_ads: 0,
          pending_ads: 0,
          total_ads: 0,
          total_channels: 0
        }
      };
    }
  }

  /**
   * Mevcut günlerin listesini al
   * @returns ApiResponse<string[]>
   */
  async getDayList(): Promise<ApiResponse<string[]>> {
    const token = authService.getAuthToken();
    
    try {
      const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.ADMIN.ADVERT_PLAN_DAY_LIST}`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          ...(token && { "Authorization": token })
        }
      });
      
      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Gün listesi alma hatası:", error);
      return {
        status: false,
        errorCode: "API_ERROR",
        operation: "GET_DAY_LIST",
        httpStatus: 500,
        desc: error instanceof Error ? error.message : "Gün listesi alınırken bir hata oluştu",
        result: []
      };
    }
  }

  /**
   * Belirli bir günün saatlik kampanya listesini al
   * @param date YYYY-MM-DD formatında tarih
   * @returns ApiResponse<HourlyData[]>
   */
  async getPlanDayListByHour(date: string): Promise<ApiResponse<HourlyData[]>> {
    const token = authService.getAuthToken();
    
    try {
      const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.ADMIN.PLAN_DAY_LIST_BY_HOUR}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          ...(token && { "Authorization": token })
        },
        body: JSON.stringify({ date })
      });
      
      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Saatlik kampanya listesi alma hatası:", error);
      return {
        status: false,
        errorCode: "API_ERROR",
        operation: "GET_PLAN_DAY_LIST_BY_HOUR",
        httpStatus: 500,
        desc: error instanceof Error ? error.message : "Saatlik kampanya listesi alınırken bir hata oluştu",
        result: []
      };
    }
  }
}

// Servis örneği oluştur
export const adPlanningService = new AdPlanningService(); 