import { create } from 'zustand';

// Bildirim türü
export interface Notification {
  id: string;
  title: string;
  message: string;
  time: string;
  read: boolean;
  type?: "campaign" | "payment" | "system" | "report"; 
  link?: string;
}

// Örnek bildirim verileri
const initialNotifications: Notification[] = [
  { id: "1", title: "Yeni Kampanya Onaylandı", message: "'<PERSON>z İndirimleri' kampanyanız başarıyla onaylandı.", time: "1 saat önce", read: false, type: "campaign", link: "/advertiser/campaigns" },
  { id: "2", title: "Bakiye Düşük Uyarısı", message: "Reklam bakiyeniz 10 TL'nin altına düştü.", time: "3 saat önce", read: false, type: "payment", link: "/advertiser/balance" },
  { id: "3", title: "Haftalık Rapor Hazır", message: "Geçen haftanın performans raporu hazırlandı.", time: "<PERSON>ün", read: true, type: "report", link: "/advertiser/statistics" },
  { id: "4", title: "Yeni Reklam Alanı Eklendi", message: "Popüler blog sitesi Example.com sisteme eklendi.", time: "2 gün önce", read: true, type: "system" },
  { id: "5", title: "Kampanya Bitiyor", message: "'Bahar Fırsatları' kampanyanız 3 gün içinde sona erecek.", time: "4 gün önce", read: false, type: "campaign", link: "/advertiser/campaigns" },
  { id: "6", title: "Yeni Ödeme Alındı", message: "50TL tutarındaki ödemeniz başarıyla gerçekleştirildi.", time: "1 hafta önce", read: true, type: "payment", link: "/advertiser/balance" },
  { id: "7", title: "Hesap Ayarlarınızı Güncelleyin", message: "Güvenlik nedeniyle şifrenizi değiştirmeniz önerilir.", time: "2 hafta önce", read: true, type: "system", link: "/advertiser/settings" },
];

// Bildirim Store'u
type NotificationStore = {
  notifications: Notification[];
  unreadCount: number;
  markAsRead: (id: string) => void;
  markAllAsRead: () => void;
  deleteNotification: (id: string) => void;
};

// Zustand store ile oluşturulmuş bildirim yönetimi
export const useNotificationStore = create<NotificationStore>((set, get) => ({
  notifications: initialNotifications,
  unreadCount: initialNotifications.filter(n => !n.read).length,
  
  markAsRead: (id: string) => set(state => {
    const updatedNotifications = state.notifications.map(notification => 
      notification.id === id ? { ...notification, read: true } : notification
    );
    
    return {
      notifications: updatedNotifications,
      unreadCount: updatedNotifications.filter(n => !n.read).length
    };
  }),
  
  markAllAsRead: () => set(state => {
    const updatedNotifications = state.notifications.map(notification => ({ 
      ...notification, 
      read: true 
    }));
    
    return {
      notifications: updatedNotifications,
      unreadCount: 0
    };
  }),
  
  deleteNotification: (id: string) => set(state => {
    const updatedNotifications = state.notifications.filter(n => n.id !== id);
    
    return {
      notifications: updatedNotifications,
      unreadCount: updatedNotifications.filter(n => !n.read).length
    };
  })
})); 