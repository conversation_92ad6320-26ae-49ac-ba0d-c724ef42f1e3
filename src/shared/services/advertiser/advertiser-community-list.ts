import { authService } from '../auth-service';
import { API_CONFIG } from '../api-config';

export interface Community {
  community_username: string;
  clicks: number;
  views: number;
  ctr: number;
  earnings: number;
  conversions: number;
  community_name: string;
  category: string;
}

export interface CommunityListResponse {
  status: boolean;
  errorCode: string | null;
  operation: string | null;
  httpStatus: number;
  desc: string;
  result: {
    data: Community[];
    stats: {
      total: number;
    };
  };
}

export interface CommunityListParams {
  skip: number;
  limit: number;
  campaign_id?: string;
  search?: string;
}

export const fetchCommunityList = async (params: CommunityListParams): Promise<CommunityListResponse> => {
  const headers: Record<string, string> = {
    "Content-Type": "application/json"
  };
  
  const token = authService.getAuthToken();
  if (token) {
    headers["Authorization"] = token;
  }

  try {
    const response = await fetch(`${API_CONFIG.BASE_URL}/advertiser/analytics/campaignCommunityList`, {
      method: "POST",
      headers,
      body: JSON.stringify(params)
    });

    const data = await response.json();
    console.log('Raw API Response:', data);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return data;
  } catch (error) {
    console.error("Topluluk listesi alınırken hata:", error);
    throw error;
  }
}; 