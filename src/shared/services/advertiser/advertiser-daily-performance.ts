import { authService } from '../auth-service';
import { API_CONFIG } from '../api-config';

export interface DailyPerformanceData {
  date: string;
  views: number;
  clicks: number;
  conversions: number;
  ctr: number;
}

export interface DailyPerformanceResponse {
  status: boolean;
  errorCode: string | null;
  operation: string | null;
  httpStatus: number;
  desc: string;
  result: DailyPerformanceData[];
}

export const fetchDailyPerformance = async (campaignId?: string): Promise<DailyPerformanceResponse> => {
  const headers: Record<string, string> = {
    "Content-Type": "application/json"
  };
  
  const token = authService.getAuthToken();
  if (token) {
    headers["Authorization"] = token;
  }

  try {
    const url = new URL(`${API_CONFIG.BASE_URL}/advertiser/analytics/campaignDailyPerformanceAnalysis`);
    if (campaignId) {
      url.searchParams.append('campaign_id', campaignId);
    }

    const response = await fetch(url.toString(), {
      method: "GET",
      headers
    });

    const data = await response.json();
    console.log('Raw API Response:', data);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return data;
  } catch (error) {
    console.error("Günlük performans verisi alınırken hata:", error);
    throw error;
  }
}; 