import { authService } from '../auth-service';
import { API_CONFIG } from '../api-config';

export type PaymentMethod = 'CREDIT_CARD' | 'BANK_TRANSFER' | 'PAYPAL';

export interface BalanceAddParams {
  amount: number;
  payment_method: PaymentMethod;
}

export interface BalanceAddResponse {
  status: boolean;
  errorCode: string | null;
  operation: string | null;
  httpStatus: number;
  desc: string;
  result: {
    balance: number;
    transaction_id: string;
  };
}

export const fetchBalanceAdd = async (params: BalanceAddParams): Promise<BalanceAddResponse> => {
  const headers: Record<string, string> = {
    "Content-Type": "application/json"
  };
  
  const token = authService.getAuthToken();
  if (token) {
    headers["Authorization"] = token;
  }

  try {
    const response = await fetch(`${API_CONFIG.BASE_URL}/advertiser/balanceAdd`, {
      method: "POST",
      headers,
      body: JSON.stringify(params)
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Bakiye ekleme işlemi sırasında hata:", error);
    return {
      status: false,
      errorCode: "API_ERROR",
      operation: "BALANCE_ADD",
      httpStatus: 500,
      desc: error instanceof Error ? error.message : "Bakiye ekleme işlemi sırasında bir hata oluştu",
      result: {
        balance: 0,
        transaction_id: ""
      }
    };
  }
}; 