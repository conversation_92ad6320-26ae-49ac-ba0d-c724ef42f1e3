// Reklamveren kampanya oluşturma servisi
import { authService } from '../auth-service';
import { API_CONFIG } from '../api-config';

export interface CampaignCreateParams {
  title: string;
  campaign_aim: string;
  advertising_platform: string;
  country: string;
  category: string[];
  campaign_title: string;
  campaign_desc: string;
  destination_url: string;
  campaign_image: string;
  cta_button: string;
  campaign_budget: string;
  bid_strategy: string;
  cpm_bid_price: string;
  cpc_bid_price: string;
  brand_safety_check: string;
  starts_at: string;
  ends_at: string;
  status: string;
  language: string;
}

export interface CampaignCreateResponse {
  status: boolean;
  errorCode: string | null;
  operation: string | null;
  httpStatus: number;
  desc: string;
  result: {
    campaign_id?: string;
  };
}

export async function fetchCampaignCreate(params: CampaignCreateParams): Promise<CampaignCreateResponse> {
  const headers: Record<string, string> = {
    "Content-Type": "application/json"
  };
  
  // AuthService kullanarak token al - Bu, TokenService'in doğru token formatını kullandığından emin olur
  const token = authService.getAuthToken();
  console.log("Token (authService ile):", token);
  
  // Token validasyonu
  if (!token) {
    console.error("Yetkilendirme token'ı bulunamadı! Oturum kontrolü yapılmalı.");
    
    // Kullanıcıyı tekrar giriş yapmaya yönlendirme uyarısı oluştur
    return {
      status: false,
      errorCode: "AUTH_FAILED",
      operation: "CAMPAIGN_CREATE",
      httpStatus: 401,
      desc: "Yetkilendirme hatası. Lütfen tekrar giriş yapın.",
      result: {}
    };
  }
  
  // Token varsa ekle
  headers["Authorization"] = token;
  
  // Veri ön işlem - bütçe ve fiyat değerlerinin doğru formatta olduğundan emin olun
  const processedParams = { ...params };
  
  // Bütçe değerini düzelt
  if (processedParams.campaign_budget) {
    // Nokta içeriyorsa (TR formatı), temizle
    processedParams.campaign_budget = processedParams.campaign_budget.replace(/\./g, '');
  }
  
  // Teklif fiyatlarını düzelt
  if (processedParams.cpm_bid_price) {
    processedParams.cpm_bid_price = processedParams.cpm_bid_price.replace(/\./g, '');
  }
  
  if (processedParams.cpc_bid_price) {
    processedParams.cpc_bid_price = processedParams.cpc_bid_price.replace(/\./g, '');
  }
  
  console.log("Kampanya Oluşturma - İşlenmiş İstek:", processedParams);
  console.log("Kampanya Oluşturma - Headers:", headers);
  
  try {
    // Timeout ekleyin (5 saniye)
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000);
    
    const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.CAMPAIGN.CREATE}`, {
      method: "POST",
      headers,
      body: JSON.stringify(processedParams), // İşlenmiş parametreleri gönder
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    
    console.log("Kampanya Oluşturma - API yanıt durumu:", response.status, response.statusText);
    
    if (!response.ok) {
      console.error("Kampanya Oluşturma - API hatası:", response.status, response.statusText);
      
      // 401 hatası için özel işleme
      if (response.status === 401) {
        console.error("Kampanya Oluşturma - Yetkilendirme hatası. Lütfen yeniden giriş yapın veya tokenı kontrol edin!");
        
        // Süresi dolmuş token için oturumu temizleme önerisi
        try {
          localStorage.removeItem("user");
          console.log("Kampanya Oluşturma - Oturum temizlendi, kullanıcının tekrar giriş yapması gerekiyor.");
        } catch (e) {
          console.error("Kampanya Oluşturma - Oturum temizleme hatası:", e);
        }
        
        return {
          status: false,
          errorCode: "AUTH_FAILED",
          operation: "CAMPAIGN_CREATE",
          httpStatus: 401,
          desc: "Oturumunuzun süresi dolmuş. Lütfen tekrar giriş yapın.",
          result: {}
        };
      }
    }
    
    try {
      const data = await response.json();
      console.log("Kampanya Oluşturma - API yanıtı:", data);
      return data;
    } catch (parseError) {
      console.error("Kampanya Oluşturma - API yanıtı JSON olarak ayrıştırılamadı:", parseError);
      return {
        status: false,
        errorCode: "API_PARSE_ERROR",
        operation: "CAMPAIGN_CREATE",
        httpStatus: 500,
        desc: "API yanıtı işlenirken bir hata oluştu",
        result: {}
      };
    }
  } catch (error) {
    console.error("Kampanya Oluşturma - API isteği sırasında hata:", error);
    
    // Ağ hatası mı yoksa timeout mu kontrol et
    let errorMessage = "API isteği sırasında bir hata oluştu";
    let errorCode = "API_ERROR";
    
    if (error instanceof DOMException && error.name === "AbortError") {
      errorMessage = "İstek zaman aşımına uğradı";
      errorCode = "API_TIMEOUT";
    }
    
    return {
      status: false,
      errorCode: errorCode,
      operation: "CAMPAIGN_CREATE",
      httpStatus: 500,
      desc: errorMessage,
      result: {}
    };
  }
} 