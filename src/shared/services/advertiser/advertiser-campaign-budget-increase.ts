import { authService } from "../auth-service"
import { API_CONFIG } from "../api-config"

export interface CampaignBudgetIncreaseParams {
  campaign_id: string
  additional_budget: string // Additional budget amount as string
}

export interface CampaignBudgetIncreaseResponse {
  status: boolean
  desc: string
  result?: unknown
}

export async function fetchCampaignBudgetIncrease(params: CampaignBudgetIncreaseParams): Promise<CampaignBudgetIncreaseResponse> {
  const authToken = authService.getAuthToken()
  
  if (!authToken) {
    throw new Error('Authentication token not found')
  }

  try {
    const response = await fetch(`${API_CONFIG.BASE_URL}/advertiser/campaigns/budget/increase`, {
      method: 'POST',
      headers: {
        'Authorization': authToken,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(params)
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data = await response.json()
    return data
  } catch (error) {
    console.error('Campaign budget increase error:', error)
    throw error
  }
} 