import { authService } from '../auth-service';
import { API_CONFIG } from '../api-config';

export interface LastCampaign {
  _id: string;
  campaign_id: string;
  title: string;
  campaign_title: string;
  campaign_desc: string;
  campaign_budget: number;
  status: 'ACTIVE' | 'STOP' | 'PLANNED' | 'FINISHED';
  clicks: number;
  views: number;
}

export interface LastCampaignsResponse {
  status: boolean;
  result: LastCampaign[];
}

export const fetchLastCampaigns = async (): Promise<LastCampaignsResponse> => {
  const headers: Record<string, string> = {
    "Content-Type": "application/json"
  };
  
  const token = authService.getAuthToken();
  if (token) {
    headers["Authorization"] = token;
  }

  try {
    const response = await fetch(`${API_CONFIG.BASE_URL}/advertiser/lastCampaign`, {
      method: "GET",
      headers
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error("Son kampanyalar yüklenirken hata:", error);
    return {
      status: false,
      result: []
    };
  }
}; 