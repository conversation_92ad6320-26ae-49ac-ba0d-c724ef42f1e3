// Reklamveren kampanya listeleme servisi
import { authService } from '../auth-service';
import { API_CONFIG } from '../api-config';

export interface CampaignListParams {
  limit?: number;
  skip?: number;
  campaign_status?: string;
  status?: string;
  community_name?: string;
  search?: string;
}

export interface Campaign {
  campaign_id: string;
  title: string;
  campaign_aim: string;
  advertising_platform: string;
  country: string;
  language: string;
  category: string[];
  campaign_title: string;
  campaign_desc: string;
  destination_url: string;
  campaign_budget: number;
  bid_strategy: string;
  cmp_bid_price: number;
  cpc_bid_price: number;
  starts_at: number | string;
  ends_at: number | string;
  status: string;
  created_at: number;
  brand_safety_check: string;
  created_from: string;
  cta_button: string;
  ctr: number;
  clicks: number;
  views: number;
  conversions: number;
  earnings: number;
  spending: number;
  updated_at?: number;
  updated_from?: string;
}

export interface CampaignListResponse {
  status: boolean;
  errorCode: string | null;
  operation: string | null;
  httpStatus: number;
  desc: string;
  result: {
    data: Campaign[];
    stats: {
      total: number;
    };
  };
}

export interface CampaignStatisticsResponse {
  status: boolean;
  errorCode: string | null;
  operation: string | null;
  httpStatus: number;
  desc: string;
  result: {
    active_campaign_count: number;
    total_budget: number;
    total_campaign: number;
    total_view: number;
    total_click: number;
    total_earnings: number;
  };
}

export interface CampaignGetParams {
  campaign_id: string;
}

export interface CampaignGetResponse {
  status: boolean;
  errorCode: string | null;
  operation: string | null;
  httpStatus: number;
  desc: string;
  result: {
    campaign_id: string;
    title: string;
    campaign_aim: string;
    advertising_platform: string;
    country: string;
    language: string;
    category: string[];
    campaign_title: string;
    campaign_desc: string;
    destination_url: string;
    campaign_image: string;
    campaign_budget: number;
    bid_strategy: string;
    cpm_bid_price: number;
    cpc_bid_price: number;
    starts_at: number;
    ends_at: number;
    status: string;
    created_at: number;
    brand_safety_check: string;
    created_from: string;
    cta_button: string;
    ctr: number;
    clicks: number;
    views: number;
    conversions: number;
    earnings: number;
  };
}

export async function fetchCampaignDelete(params: { campaign_id: string }) {
  const headers: Record<string, string> = {
    "Content-Type": "application/json"
  };
  const token = authService.getAuthToken();
  if (token) {
    headers["Authorization"] = token;
  }

  try {
    const response = await fetch(`${API_CONFIG.BASE_URL}/advertiser/campaign/delete`, {
      method: "POST",
      headers,
      body: JSON.stringify(params)
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return data;
  } catch (error) {
    console.error("Kampanya silme hatası:", error);
    throw error;
  }
}

export const fetchCampaignList = async (params: CampaignListParams): Promise<CampaignListResponse> => {
  const headers: Record<string, string> = {
    "Content-Type": "application/json"
  };
  
  const token = authService.getAuthToken();
  if (token) {
    headers["Authorization"] = token;
  }

  try {
    const response = await fetch(`${API_CONFIG.BASE_URL}/advertiser/campaign/list`, {
      method: "POST",
      headers,
      body: JSON.stringify(params)
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return data;
  } catch (error) {
    console.error("Kampanya listesi alınırken hata:", error);
    throw error;
  }
};

export async function fetchCampaignStatistics(): Promise<CampaignStatisticsResponse> {
  const headers: Record<string, string> = {
    "Content-Type": "application/json"
  };
  const token = authService.getAuthToken();
  if (token) {
    headers["Authorization"] = token;
  }
  const response = await fetch("https://devapi.adnomio.com/advertiser/campaign/statistics", {
    method: "GET",
    headers
  });
  return await response.json();
}

export const fetchCampaignGet = async (params: CampaignGetParams): Promise<CampaignGetResponse> => {
  try {
    const response = await fetch(`${API_CONFIG.BASE_URL}/advertiser/campaign/get`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `${authService.getAuthToken()}`
      },
      body: JSON.stringify(params)
    });

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Kampanya detayları alınamadı:', error);
    throw error;
  }
};