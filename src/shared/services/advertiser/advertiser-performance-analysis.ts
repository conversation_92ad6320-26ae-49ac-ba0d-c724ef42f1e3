import { authService } from '../auth-service';
import { API_CONFIG } from '../api-config';

export type PerformanceFilter = 'week' | 'month' | 'three_month' | 'year';

export interface PerformanceAnalysisParams {
  filter: PerformanceFilter;
  campaign_id?: string;
}

export interface PerformanceMetric {
  value: number;
  percentage_change: number;
}

export interface PerformanceAnalysisResponse {
  status: boolean;
  errorCode: string | null;
  operation: string | null;
  httpStatus: number;
  desc: string;
  result: {
    total_views: PerformanceMetric;
    total_clicks: PerformanceMetric;
    total_conversions: PerformanceMetric;
    avg_ctr: PerformanceMetric;
  };
}

export const fetchPerformanceAnalysis = async (params: PerformanceAnalysisParams): Promise<PerformanceAnalysisResponse> => {
  const headers: Record<string, string> = {
    "Content-Type": "application/json"
  };
  
  const token = authService.getAuthToken();
  if (token) {
    headers["Authorization"] = token;
  }

  try {
    const response = await fetch(`${API_CONFIG.BASE_URL}/advertiser/analytics/campaignPerformanceAnalysis`, {
      method: "POST",
      headers,
      body: JSON.stringify(params)
    });

    const data = await response.json();
    console.log('Raw API Response:', data);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return data;
  } catch (error) {
    console.error("Performans analizi alınırken hata:", error);
    throw error;
  }
}; 