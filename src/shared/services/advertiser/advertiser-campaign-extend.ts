import { authService } from "../auth-service"
import { API_CONFIG } from "../api-config"

export interface CampaignExtendParams {
  campaign_id: string
  new_end_date: string // Unix timestamp as string
}

export interface CampaignExtendResponse {
  status: boolean
  desc: string
  result?: unknown
}

export async function fetchCampaignExtend(params: CampaignExtendParams): Promise<CampaignExtendResponse> {
  const authToken = authService.getAuthToken()
  
  if (!authToken) {
    throw new Error('Authentication token not found')
  }

  try {
    const response = await fetch(`${API_CONFIG.BASE_URL}/advertiser/campaigns/extend`, {
      method: 'POST',
      headers: {
        'Authorization': authToken,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(params)
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data = await response.json()
    return data
  } catch (error) {
    console.error('Campaign extend error:', error)
    throw error
  }
} 