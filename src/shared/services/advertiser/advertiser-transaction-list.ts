import { authService } from '../auth-service';
import { API_CONFIG } from '../api-config';

export type TransactionStatus = 'PENDING' | 'COMPLETED' | 'FAILED' | 'REJECTED';
export type TransactionType = 'BALANCE_ADD' | 'PAYMENT' | 'RETURN';

export interface TransactionListParams {
  limit: number;
  skip: number;
  search?: string;
  status?: TransactionStatus;
  type?: TransactionType;
  date?: number;
}

export interface Transaction {
  _id: string;
  transaction_id: string;
  created_at: number;
  amount: number;
  payment_method: string;
  user_id: string;
  status: TransactionStatus;
  type: TransactionType;
  description: string;
  email: string;
  name: string;
  surname: string;
}

export interface TransactionListResponse {
  status: boolean;
  errorCode: string | null;
  operation: string | null;
  httpStatus: number;
  desc: string;
  result: {
    data: Transaction[];
    stats: {
      total: number;
    };
  };
}

export const fetchTransactionList = async (params: TransactionListParams): Promise<TransactionListResponse> => {
  const headers: Record<string, string> = {
    "Content-Type": "application/json"
  };
  
  const token = authService.getAuthToken();
  if (token) {
    headers["Authorization"] = token;
  }

  try {
    const response = await fetch(`${API_CONFIG.BASE_URL}/advertiser/transactionList`, {
      method: "POST",
      headers,
      body: JSON.stringify(params)
    });

    const data = await response.json();
    console.log('Raw API Response:', data);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return data;
  } catch (error) {
    console.error("İşlem geçmişi alınırken hata:", error);
    throw error;
  }
}; 