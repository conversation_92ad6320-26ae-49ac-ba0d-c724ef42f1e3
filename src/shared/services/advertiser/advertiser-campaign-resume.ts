import { authService } from '../auth-service';
import { API_CONFIG } from '../api-config';

export interface CampaignResumeRequest {
  campaign_id: string;
}

export interface CampaignResumeResponse {
  status: boolean;
  errorCode: string | null;
  operation: string | null;
  httpStatus: number;
  desc: string;
  result: {
    campaign_id?: string;
  };
}

export async function fetchCampaignResume(params: CampaignResumeRequest): Promise<CampaignResumeResponse> {
  const headers: Record<string, string> = {
    "Content-Type": "application/json"
  };
  const token = authService.getAuthToken();
  if (token) {
    headers["Authorization"] = token;
  }

  const url = `${API_CONFIG.BASE_URL}/advertiser/campaign/resume`;
  console.log('Making resume request to:', url);
  console.log('With headers:', headers);
  console.log('With body:', params);

  try {
    const response = await fetch(url, {
      method: "POST",
      headers,
      body: JSON.stringify(params)
    });

    console.log('Response status:', response.status);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));
    
    const data = await response.json();
    console.log('Response data:', data);

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    return data;
  } catch (error) {
    console.error("Kampanya aktifleştirme hatası:", error);
    throw error;
  }
} 