import { authService } from '../auth-service';
import { API_CONFIG } from '../api-config';

export interface DashboardStatsResponse {
  status: boolean;
  errorCode: string | null;
  operation: string | null;
  httpStatus: number;
  desc: string;
  result: {
    active_campaign_count: number;
    total_campaign: number;
    total_view: number;
    total_click: number;
    total_budget: number;
    total_earnings: number;
  };
}

export async function fetchDashboardStats(): Promise<DashboardStatsResponse> {
  const headers: Record<string, string> = {
    "Content-Type": "application/json"
  };
  
  const token = authService.getAuthToken();
  if (!token) {
    return {
      status: false,
      errorCode: "AUTH_FAILED",
      operation: "DASHBOARD_STATS",
      httpStatus: 401,
      desc: "Yetkilendirme hatası. Lütfen tekrar giriş yapın.",
      result: {
        active_campaign_count: 0,
        total_campaign: 0,
        total_view: 0,
        total_click: 0,
        total_budget: 0,
        total_earnings: 0
      }
    };
  }
  
  headers["Authorization"] = token;
  
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), 5000);
    
    const response = await fetch(`${API_CONFIG.BASE_URL}/advertiser/dashboard/campaignStatistics`, {
      method: "GET",
      headers,
      signal: controller.signal
    });
    
    clearTimeout(timeoutId);
    
    if (!response.ok) {
      if (response.status === 401) {
        try {
          localStorage.removeItem("user");
        } catch (e) {
          console.error("Dashboard Stats - Oturum temizleme hatası:", e);
        }
        
        return {
          status: false,
          errorCode: "AUTH_FAILED",
          operation: "DASHBOARD_STATS",
          httpStatus: 401,
          desc: "Oturumunuzun süresi dolmuş. Lütfen tekrar giriş yapın.",
          result: {
            active_campaign_count: 0,
            total_campaign: 0,
            total_view: 0,
            total_click: 0,
            total_budget: 0,
            total_earnings: 0
          }
        };
      }
    }
    
    try {
      const data = await response.json();
      return data;
    } catch (parseError) {
      return {
        status: false,
        errorCode: "API_PARSE_ERROR",
        operation: "DASHBOARD_STATS",
        httpStatus: 500,
        desc: "API yanıtı işlenirken bir hata oluştu",
        result: {
          active_campaign_count: 0,
          total_campaign: 0,
          total_view: 0,
          total_click: 0,
          total_budget: 0,
          total_earnings: 0
        }
      };
    }
  } catch (error) {
    let errorMessage = "API isteği sırasında bir hata oluştu";
    let errorCode = "API_ERROR";
    
    if (error instanceof DOMException && error.name === "AbortError") {
      errorMessage = "İstek zaman aşımına uğradı";
      errorCode = "API_TIMEOUT";
    }
    
    return {
      status: false,
      errorCode: errorCode,
      operation: "DASHBOARD_STATS",
      httpStatus: 500,
      desc: errorMessage,
      result: {
        active_campaign_count: 0,
        total_campaign: 0,
        total_view: 0,
        total_click: 0,
        total_budget: 0,
        total_earnings: 0
      }
    };
  }
} 