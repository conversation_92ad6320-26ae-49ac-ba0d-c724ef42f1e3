import { API_CONFIG, ApiResponse } from "../api-config";

export interface Notification {
  notification_id: string;
  title: string;
  message: string;
  type: string;
  isRead: boolean;
  created_at: string;
  updated_at?: string;
}

export interface NotificationListResult {
  data: Notification[];
  stats: {
    total: number;
  };
}

export interface NotificationListRequest {
  limit: number;
  skip: number;
  isRead?: boolean;
}

export interface NotificationActionRequest {
  notification_id: string;
}

// 1. Bildirim listesini getir
export const fetchNotificationList = async (params: NotificationListRequest): Promise<ApiResponse<NotificationListResult>> => {
  try {
    const response = await fetch(`${API_CONFIG.BASE_URL}/advertiser/notification/list`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": localStorage.getItem("user") ? JSON.parse(localStorage.getItem("user")!).token : ""
      },
      body: JSON.stringify(params)
    });

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Notification list fetch error:", error);
    return {
      status: false,
      errorCode: "API_ERROR",
      operation: "GET_NOTIFICATION_LIST",
      httpStatus: 500,
      desc: error instanceof Error ? error.message : "Notification list fetch failed",
      result: { data: [], stats: { total: 0 } }
    };
  }
};

// 2. Tüm bildirimleri okundu olarak işaretle
export const markAllNotificationsRead = async (): Promise<ApiResponse<{ success: boolean }>> => {
  try {
    const response = await fetch(`${API_CONFIG.BASE_URL}/advertiser/notification/readAll`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": localStorage.getItem("user") ? JSON.parse(localStorage.getItem("user")!).token : ""
      },
      body: JSON.stringify({})
    });

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Mark all notifications read error:", error);
    return {
      status: false,
      errorCode: "API_ERROR",
      operation: "MARK_ALL_READ",
      httpStatus: 500,
      desc: error instanceof Error ? error.message : "Mark all notifications read failed",
      result: { success: false }
    };
  }
};

// 3. Belirli bir bildirimi getir
export const fetchNotificationDetails = async (params: NotificationActionRequest): Promise<ApiResponse<Notification>> => {
  try {
    const response = await fetch(`${API_CONFIG.BASE_URL}/advertiser/notification/get`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": localStorage.getItem("user") ? JSON.parse(localStorage.getItem("user")!).token : ""
      },
      body: JSON.stringify(params)
    });

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Notification details fetch error:", error);
    return {
      status: false,
      errorCode: "API_ERROR",
      operation: "GET_NOTIFICATION_DETAILS",
      httpStatus: 500,
      desc: error instanceof Error ? error.message : "Notification details fetch failed",
      result: null as unknown as Notification
    };
  }
};

// 4. Bildirimi okundu olarak işaretle
export const markNotificationRead = async (params: NotificationActionRequest): Promise<ApiResponse<{ success: boolean }>> => {
  try {
    const response = await fetch(`${API_CONFIG.BASE_URL}/advertiser/notification/read`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": localStorage.getItem("user") ? JSON.parse(localStorage.getItem("user")!).token : ""
      },
      body: JSON.stringify(params)
    });

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Mark notification read error:", error);
    return {
      status: false,
      errorCode: "API_ERROR",
      operation: "MARK_NOTIFICATION_READ",
      httpStatus: 500,
      desc: error instanceof Error ? error.message : "Mark notification read failed",
      result: { success: false }
    };
  }
};

// 5. Bildirimi sil
export const deleteNotification = async (params: NotificationActionRequest): Promise<ApiResponse<{ success: boolean }>> => {
  try {
    const response = await fetch(`${API_CONFIG.BASE_URL}/advertiser/notification/delete`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": localStorage.getItem("user") ? JSON.parse(localStorage.getItem("user")!).token : ""
      },
      body: JSON.stringify(params)
    });

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Delete notification error:", error);
    return {
      status: false,
      errorCode: "API_ERROR",
      operation: "DELETE_NOTIFICATION",
      httpStatus: 500,
      desc: error instanceof Error ? error.message : "Delete notification failed",
      result: { success: false }
    };
  }
}; 