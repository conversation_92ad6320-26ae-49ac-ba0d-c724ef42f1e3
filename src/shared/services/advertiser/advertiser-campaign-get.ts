import { API_CONFIG, ApiResponse } from "../api-config";

export interface CampaignDetails {
  campaign_id: string;
  title: string;
  campaign_aim: string;
  advertising_platform: string;
  country: string;
  category: string[];
  campaign_title: string;
  campaign_desc: string;
  destination_url: string;
  campaign_image: string;
  cta_button: string;
  campaign_budget: string;
  bid_strategy: string;
  cpm_bid_price: string;
  cpc_bid_price: string;
  brand_safety_check: string;
  starts_at: string;
  ends_at: string;
  status: string;
  language: string;
}

export const fetchCampaignDetails = async (campaignId: string): Promise<ApiResponse<CampaignDetails>> => {
  try {
    const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.CAMPAIGN.LIST}`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": localStorage.getItem("user") ? JSON.parse(localStorage.getItem("user")!).token : ""
      },
      body: JSON.stringify({ campaign_id: campaignId })
    });

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Campaign fetch error:", error);
    return {
      status: false,
      errorCode: "API_ERROR",
      operation: "GET_CAMPAIGN",
      httpStatus: 500,
      desc: error instanceof Error ? error.message : "Campaign fetch failed",
      result: null as unknown as CampaignDetails
    };
  }
}; 