import { authService } from '../auth-service';
import { API_CONFIG } from '../api-config';

export type TransactionStatus = 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'REJECTED' | 'CANCELED';

export interface TransactionHistoryRequest {
  limit: number;
  skip: number;
  search?: string;
  status?: TransactionStatus;
}

export interface TransactionHistoryItem {
  _id: string;
  withdraw_request_id: string;
  user_id: string;
  amount: number;
  payment_method: string;
  iban?: string;
  bank_name?: string;
  account_holder_name?: string;
  status: TransactionStatus;
  description?: string;
  created_at: number;
  processing_at?: number | null;
  completed_at?: number | null;
  rejected_at?: number | null;
  canceled_at?: number | null;
}

export interface TransactionHistoryResponse {
  status: boolean;
  errorCode: string | null;
  operation: string | null;
  httpStatus: number;
  desc: string;
  result: {
    data: TransactionHistoryItem[];
    stats: {
      total: number;
    };
  };
}

export async function getTransactionHistory(params: TransactionHistoryRequest): Promise<TransactionHistoryResponse['result']> {
  try {
    const authToken = authService.getAuthToken();
    if (!authToken) {
      throw new Error('Kimlik doğrulama tokeni bulunamadı.');
    }

    const response = await fetch(`${API_CONFIG.BASE_URL}/publisher/transactionHistory`, {
      method: 'POST',
      headers: {
        'Authorization': authToken,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(params),
    });

    if (!response.ok) {
      throw new Error('İşlem geçmişi alınamadı');
    }

    const data: TransactionHistoryResponse = await response.json();
    return data.result;
  } catch (error) {
    console.error('İşlem geçmişi alınırken hata oluştu:', error);
    throw error;
  }
}

export interface WithdrawalRequest {
  amount: number;
  payment_method_id: string;
  iban?: string;
  bank_name?: string;
  account_holder_name?: string;
  description?: string;
}

export interface WithdrawalResponse {
  status: boolean;
  errorCode: string | null;
  operation: string | null;
  httpStatus: number;
  desc: string;
  result: {
    transaction_id: string;
    status: TransactionStatus;
    amount: number;
    payment_method: string;
  };
}

export async function requestWithdrawal(params: WithdrawalRequest): Promise<WithdrawalResponse['result']> {
  try {
    const authToken = authService.getAuthToken();
    if (!authToken) {
      throw new Error('Kimlik doğrulama tokeni bulunamadı.');
    }

    const response = await fetch(`${API_CONFIG.BASE_URL}/publisher/withdraw/request`, {
      method: 'POST',
      headers: {
        'Authorization': authToken,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(params),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.desc || 'Para çekme talebi oluşturulamadı');
    }

    const data: WithdrawalResponse = await response.json();
    return data.result;
  } catch (error) {
    console.error('Para çekme talebi oluşturulurken hata oluştu:', error);
    throw error;
  }
} 