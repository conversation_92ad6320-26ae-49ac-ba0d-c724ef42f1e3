import { authService } from '../auth-service';
import { API_CONFIG } from '../api-config';

// Bildirim listesi için istek ve yanıt tipleri (varsayımsal)
export interface NotificationListRequest {
  limit: number;
  skip: number;
  status?: 'read' | 'unread'; // Örnek filtreleme alanı
}

export interface Notification {
  _id: string;
  title: string;
  message: string;
  isRead: boolean;
  createdAt: number;
  type: string; // API'den geldiğini varsayarak eklendi
  // Diğer bildirim alanları...
}

export interface NotificationListResponse {
  status: boolean;
  errorCode: string | null;
  operation: string | null;
  httpStatus: number;
  desc: string;
  result: {
    data: Notification[];
    stats: {
      total: number;
    };
  };
}

export interface NotificationResponse {
  status: boolean;
  errorCode: string | null;
  operation: string | null;
  httpStatus: number;
  desc: string;
  result: Notification;
}

// Bildirim listesini getir
export async function getNotificationList(params: NotificationListRequest): Promise<NotificationListResponse['result']> {
  try {
    const authToken = authService.getAuthToken();
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    if (authToken) {
      headers['Authorization'] = authToken;
    }

    const response = await fetch(`${API_CONFIG.BASE_URL}/publisher/notification/list`, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(params),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.desc || 'Bildirim listesi alınamadı');
    }

    const data: NotificationListResponse = await response.json();
    return data.result;
  } catch (error) {
    console.error('Bildirim listesi alınırken hata oluştu:', error);
    throw error;
  }
}

// Tüm bildirimleri okundu olarak işaretle
export async function readAllNotifications(): Promise<void> {
  try {
    const authToken = authService.getAuthToken();
    if (!authToken) {
      throw new Error('Kimlik doğrulama tokeni bulunamadı.');
    }

    const response = await fetch(`${API_CONFIG.BASE_URL}/publisher/notification/readAll`, {
      method: 'GET',
      headers: {
        'Authorization': authToken,
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.desc || 'Tüm bildirimler okundu olarak işaretlenemedi');
    }
  } catch (error) {
    console.error('Tüm bildirimleri okundu olarak işaretlerken hata oluştu:', error);
    throw error;
  }
}

// Tüm bildirimleri sil
export async function deleteAllNotifications(): Promise<void> {
  try {
    const authToken = authService.getAuthToken();
    if (!authToken) {
      throw new Error('Kimlik doğrulama tokeni bulunamadı.');
    }

    const response = await fetch(`${API_CONFIG.BASE_URL}/publisher/notification/deleteAll`, {
      method: 'GET',
      headers: {
        'Authorization': authToken,
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.desc || 'Tüm bildirimler silinemedi');
    }
  } catch (error) {
    console.error('Tüm bildirimleri silerken hata oluştu:', error);
    throw error;
  }
}

// Belirli bir bildirimi getir (varsayımsal)
export async function getNotification(notificationId: string): Promise<Notification> {
  try {
    const authToken = authService.getAuthToken();
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    if (authToken) {
      headers['Authorization'] = authToken;
    }

    const response = await fetch(`${API_CONFIG.BASE_URL}/publisher/notification/get`, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify({ notification_id: notificationId }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.desc || 'Bildirim alınamadı');
    }

    const data: NotificationResponse = await response.json();
    return data.result;
  } catch (error) {
    console.error('Bildirim alınırken hata oluştu:', error);
    throw error;
  }
}

// Belirli bir bildirimi okundu olarak işaretle
export async function readNotification(notificationId: string): Promise<void> {
  try {
    const authToken = authService.getAuthToken();
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    if (authToken) {
      headers['Authorization'] = authToken;
    }

    const response = await fetch(`${API_CONFIG.BASE_URL}/publisher/notification/read`, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify({ notification_id: notificationId }),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.desc || 'Bildirim okundu olarak işaretlenemedi');
    }
  } catch (error) {
    console.error('Bildirim okundu olarak işaretlenirken hata oluştu:', error);
    throw error;
  }
} 