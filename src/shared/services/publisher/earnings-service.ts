import { API_CONFIG, ApiResponse } from '../api-config';
import { authService } from '../auth-service';

// Earnings graph types
export type EarningsType = 'daily' | 'weekly' | 'monthly';

export interface DailyEarnings {
  day: string;
  date: string;
  earnings: number;
}

export interface WeeklyEarnings {
  week: string;
  period: string;
  earnings: number;
}

export interface MonthlyEarnings {
  month: string;
  period: string;
  earnings: number;
}

export type EarningsData = DailyEarnings[] | WeeklyEarnings[] | MonthlyEarnings[];

export interface EarningsGraphResponse {
  status: boolean;
  errorCode: string | null;
  operation: string | null;
  httpStatus: number;
  desc: string;
  result: EarningsData;
}

/**
 * Get earnings graph data
 * @param type The type of earnings data (daily, weekly, monthly)
 * @returns Promise<EarningsData>
 */
export async function getEarningsGraph(type: EarningsType): Promise<EarningsData> {
  const authToken = authService.getAuthToken();
  if (!authToken) {
    throw new Error('Kimlik doğrulama tokeni bulunamadı.');
  }

  const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.PUBLISHER.EARNINGS_GRAPH}`, {
    method: 'POST',
    headers: {
      'Authorization': authToken,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ type })
  });

  if (!response.ok) {
    throw new Error('Kazanç grafiği verileri alınamadı');
  }

  const data: EarningsGraphResponse = await response.json();
  if (!data.status) {
    throw new Error(data.desc || 'Kazanç grafiği verileri alınamadı');
  }

  return data.result;
} 