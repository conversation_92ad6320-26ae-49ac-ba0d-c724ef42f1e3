import { authService } from '../auth-service';
import { API_CONFIG } from '../api-config';

export interface BalanceAndEarningsResponse {
  status: boolean;
  errorCode: string | null;
  operation: string | null;
  httpStatus: number;
  desc: string;
  result: {
    balance: number;
    total_earnings: number;
    monthly_earnings: number;
  };
}

export async function getBalanceAndEarnings(): Promise<BalanceAndEarningsResponse['result']> {
  try {
    const authToken = authService.getAuthToken();
    if (!authToken) {
      console.error('Kimlik doğrulama tokeni bulunamadı.');
      return {
        balance: 0,
        total_earnings: 0,
        monthly_earnings: 0
      };
    }

    const response = await fetch(`${API_CONFIG.BASE_URL}/publisher/balanceAndEarnings`, {
      method: 'GET',
      headers: {
        'Authorization': authToken,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      console.error('Bakiye ve kazançlar alınamadı');
      return {
        balance: 0,
        total_earnings: 0,
        monthly_earnings: 0
      };
    }

    const data: BalanceAndEarningsResponse = await response.json();
    return data.result;
  } catch (error) {
    console.error('Bakiye ve kazançlar alınırken hata oluştu:', error);
    return {
      balance: 0,
      total_earnings: 0,
      monthly_earnings: 0
    };
  }
}

export interface EarningsDetailsResponse {
  status: boolean;
  errorCode: string | null;
  operation: string | null;
  httpStatus: number;
  desc: string;
  result: {
    earnings: number;
  };
}

export async function getEarningsDetails(): Promise<EarningsDetailsResponse['result']> {
  try {
    const authToken = authService.getAuthToken();
    if (!authToken) {
      console.error('Kimlik doğrulama tokeni bulunamadı.');
      return { earnings: 0 };
    }

    const response = await fetch(`${API_CONFIG.BASE_URL}/publisher/earnings/get`, {
      method: 'GET',
      headers: {
        'Authorization': authToken,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      console.error('Kazanç detayları alınamadı');
      return { earnings: 0 };
    }

    const data: EarningsDetailsResponse = await response.json();
    return data.result;
  } catch (error) {
    console.error('Kazanç detayları alınırken hata oluştu:', error);
    return { earnings: 0 };
  }
} 