import { authService } from '../auth-service';
import { API_CONFIG } from '../api-config';

export interface DashboardStatisticsResponse {
  status: boolean;
  errorCode: string | null;
  operation: string | null;
  httpStatus: number;
  desc: string;
  result: {
    totalViews: number;
    viewsChangePercent: number;
    totalClicks: number;
    clicksChangePercent: number;
    avgCTR: number;
    ctrChangePercent: number;
    monthlyEarnings: number;
    earningsChangePercent: number;
  };
}

const defaultStatistics = {
  totalViews: 0,
  viewsChangePercent: 0,
  totalClicks: 0,
  clicksChangePercent: 0,
  avgCTR: 0,
  ctrChangePercent: 0,
  monthlyEarnings: 0,
  earningsChangePercent: 0
};

export async function getDashboardStatistics(): Promise<DashboardStatisticsResponse['result']> {
  try {
    const response = await fetch(`${API_CONFIG.BASE_URL}/publisher/dashboard/campaignPostStatistics`, {
      method: 'GET',
      headers: {
        'Authorization': authService.getAuthToken() || '',
        'Content-Type': 'application/json',
      },
    });

    if (response.status === 404) {
      return defaultStatistics;
    }

    const data: DashboardStatisticsResponse = await response.json();
    return data.result;
  } catch (error) {
    console.error('İstatistik verileri alınırken hata oluştu:', error);
    return defaultStatistics;
  }
} 