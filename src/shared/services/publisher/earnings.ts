import { authService } from '../auth-service';
import { API_CONFIG } from '../api-config';

export interface EarningsResponse {
  status: boolean;
  errorCode: string | null;
  operation: string | null;
  httpStatus: number;
  desc: string;
  result: {
    earnings: number;
  };
}

export async function getPublisherEarnings(): Promise<number> {
  try {
    const response = await fetch(`${API_CONFIG.BASE_URL}/publisher/earnings/get`, {
      method: 'GET',
      headers: {
        'Authorization': authService.getAuthToken(),
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      console.error('Kazanç verileri alınamadı');
      return 0;
    }

    const data: EarningsResponse = await response.json();
    return data.result.earnings;
  } catch (error) {
    console.error('Kazanç verileri alınırken hata oluştu:', error);
    return 0;
  }
} 