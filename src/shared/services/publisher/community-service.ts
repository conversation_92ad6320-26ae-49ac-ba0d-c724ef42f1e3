import { authService } from '../auth-service';
import { API_CONFIG } from '../api-config';

export interface TopPerformingCommunity {
  id: string;
  name: string;
  iconUrl?: string; // Veya emoji için string
  totalEarnings: number;
  // Gerekirse trend bilgisi de eklenebilir, örneğin:
  // trend?: 'up' | 'down' | 'neutral';
  // changePercentage?: number;
}

interface TopPerformersApiResponse {
  status: boolean;
  errorCode: string | null;
  operation: string | null;
  httpStatus: number;
  desc: string;
  result: TopPerformingCommunity[];
}

export async function getTopPerformingCommunities(): Promise<TopPerformingCommunity[]> {
  // Statik örnek veri
  console.log("getTopPerformingCommunities: Statik veri kullanılıyor.")
  return Promise.resolve([
    { id: "1", name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", totalEarnings: 1250.75, iconUrl: "💻" },
    { id: "2", name: "<PERSON><PERSON><PERSON>", totalEarnings: 980.50, iconUrl: "🍲" },
    { id: "3", name: "Seyahat Tutkunları", totalEarnings: 850.00, iconUrl: "✈️" },
    { id: "4", name: "Spor Günlüğü", totalEarnings: 760.25, iconUrl: "⚽" },
    { id: "5", name: "Kitap Kulübü", totalEarnings: 620.90, iconUrl: "📚" },
  ]);
}

// Yeni Arayüzler ve Fonksiyon (publisher/topCommunities için)
export interface TopCommunity {
  community_username: string;
  community_name: string;
  earnings: number;
  // Görselde bir ikon olduğu için, bunu opsiyonel olarak ekleyebiliriz veya client tarafında oluşturabiliriz.
  // iconPlaceholder?: string; // Örneğin baş harf veya genel bir ikon adı
}

export interface TopCommunitiesApiResponse {
  status: boolean;
  errorCode: string | null;
  operation: string | null;
  httpStatus: number;
  desc: string;
  result: TopCommunity[];
}

export async function getTopCommunities(): Promise<TopCommunity[] | null> {
  try {
    const authToken = authService.getAuthToken();
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };
    if (authToken) {
      headers['Authorization'] = authToken;
    }

    const response = await fetch(`${API_CONFIG.BASE_URL}/publisher/topCommunities`, {
      method: 'GET',
      headers: headers,
    });

    if (response.status === 404) {
      console.log('getTopCommunities: Endpoint 404 döndü (Veri yok).');
      return null; 
    }

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ desc: 'Bilinmeyen sunucu hatası' }));
      console.error('getTopCommunities API Error:', response.status, errorData.desc);
      throw new Error(errorData.desc || 'En iyi performans gösteren topluluklar alınamadı');
    }

    const data: TopCommunitiesApiResponse = await response.json();
    return data.result || [];
  } catch (error) {
    console.error('En iyi performans gösteren topluluklar alınırken hata oluştu:', error);
    // Hatanın sayfa seviyesinde yakalanması için yeniden fırlat
    // Ancak 404 dışındaki hatalar için null yerine hata fırlatmak daha doğru olabilir,
    // UI'da spesifik hata mesajı göstermek için.
    if (error instanceof Error && error.message.includes('404')) {
        return null; // Fetch sırasında network hatası vs. 404 gibi ise.
    }
    throw error; 
  }
} 