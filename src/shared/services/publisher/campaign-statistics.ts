import { authService } from '../auth-service';
import { API_CONFIG } from '../api-config';

export interface CampaignStatisticsResponse {
  status: boolean;
  errorCode: string | null;
  operation: string | null;
  httpStatus: number;
  desc: string;
  result: {
    total_views: number;
    total_clicks: number;
    avg_ctr: number;
    total_earnings: number;
  };
}

export async function getCampaignStatistics(): Promise<CampaignStatisticsResponse['result']> {
  try {
    const response = await fetch(`${API_CONFIG.BASE_URL}/publisher/campaignPostStatistics`, {
      method: 'GET',
      headers: {
        'Authorization': authService.getAuthToken(),
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Reklam istatistikleri alınamadı');
    }

    const data: CampaignStatisticsResponse = await response.json();
    return data.result;
  } catch (error) {
    console.error('Reklam istatistikleri alı<PERSON>ı<PERSON> hata oluş<PERSON>:', error);
    throw error;
  }
} 