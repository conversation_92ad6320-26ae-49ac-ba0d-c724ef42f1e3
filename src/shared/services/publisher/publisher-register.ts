import { API_CONFIG } from '../api-config';

export interface PublisherRegisterParams {
  email: string;
  password: string;
  name: string;
  surname: string;
  created_at: number;
}

export interface PublisherRegisterResponse {
  status: boolean;
  message: string;
  result?: {
    userId?: string;
    email?: string;
    name?: string;
    role?: string;
    created_at?: number;
  };
}

export async function publisherRegister(params: PublisherRegisterParams): Promise<PublisherRegisterResponse> {
  const res = await fetch(`${API_CONFIG.BASE_URL}/user/publisher/register`, {
    method: "POST",
    headers: {
      "Content-Type": "application/json"
    },
    body: JSON.stringify(params)
  });
  return res.json();
} 