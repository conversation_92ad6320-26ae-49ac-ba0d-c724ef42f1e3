import { authService } from '../auth-service';
import { API_CONFIG } from '../api-config';

export type CampaignStatus = 'ACTIVE' | 'STOP' | 'PLANNED' | 'FINISHED';

export interface CampaignListRequest {
  limit: number;
  skip: number;
  campaign_status?: CampaignStatus;
  community_username?: string;
  search?: string;
  campaign_id?: string;
}

export interface Campaign {
  _id: string;
  campaign_post_id: string;
  campaign_id: string;
  community_id: string;
  community_username: string;
  message_id: number;
  clicks: number;
  views: number;
  ctr: number;
  earnings: number;
  community_created_from: string;
  created_at: number;
  campaign_desc: string;
  campaign_title: string;
  ends_at: number;
  starts_at: number;
  community_name: string;
  campaign_status: CampaignStatus;
}

export interface CampaignListResponse {
  status: boolean;
  errorCode: string | null;
  operation: string | null;
  httpStatus: number;
  desc: string;
  result: {
    data: Campaign[];
    stats: {
      total: number;
    };
  };
}

export async function getCampaignList(params: CampaignListRequest): Promise<CampaignListResponse['result']> {
  try {
    const response = await fetch(`${API_CONFIG.BASE_URL}/publisher/campaign/list`, {
      method: 'POST',
      headers: {
        'Authorization': authService.getAuthToken(),
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(params),
    });

    if (!response.ok) {
      throw new Error('Reklam listesi alınamadı');
    }

    const data: CampaignListResponse = await response.json();
    return data.result;
  } catch (error) {
    console.error('Reklam listesi alınırken hata oluştu:', error);
    throw error;
  }
} 