import { authService } from '../auth-service';
import { API_CONFIG } from '../api-config';

export interface CommunityListRequest {
  limit: number;
  skip: number;
}

export interface Community {
  _id: string;
  community_id: string;
  community_username: string;
  community_name: string;
  status: string;
  member_count: number | null;
  view_count: number | null;
  ctr: number | null;
  earning_price: number | null;
  created_at: number;
  created_from: string;
  category: string;
  country?: string;
  language?: string;
  updated_at?: number;
}

export interface CommunityListResponse {
  status: boolean;
  errorCode: string | null;
  operation: string | null;
  httpStatus: number;
  desc: string;
  result: {
    data: Community[];
    stats: {
      total: number;
    };
  };
}

export async function getCommunityList(params: CommunityListRequest): Promise<CommunityListResponse['result']> {
  try {
    const authToken = authService.getAuthToken();
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    if (authToken) {
      headers['Authorization'] = authToken;
    }

    const response = await fetch(`${API_CONFIG.BASE_URL}/publisher/community/list`, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(params),
    });

    if (!response.ok) {
      throw new Error('Topluluk listesi alınamadı');
    }

    const data: CommunityListResponse = await response.json();
    return data.result;
  } catch (error) {
    console.error('Topluluk listesi alınırken hata oluştu:', error);
    throw error;
  }
}

export async function deleteCommunity(communityId: string): Promise<void> {
  try {
    const authToken = authService.getAuthToken();
    if (!authToken) {
      throw new Error('Kimlik doğrulama tokeni bulunamadı.');
    }

    const response = await fetch(`${API_CONFIG.BASE_URL}/publisher/community/delete`, {
      method: 'POST',
      headers: {
        'Authorization': authToken,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ community_id: communityId }),
    });

    if (!response.ok) {
      console.error('Delete API Request Failed:', response.status, response.statusText);
      try {
        const errorData = await response.json();
        console.error('Delete API Error Response Body:', errorData);
        const errorMessage = errorData.desc || 'Bilinmeyen bir hata oluştu.';
        throw new Error(`Topluluk silinirken bir hata oluştu: ${errorMessage}`);
      } catch (jsonError) {
        const errorText = await response.text();
        console.error('Delete API Error Response Text:', errorText);
        throw new Error(`Topluluk silinirken bir hata oluştu: ${errorText}`);
      }
    }
  } catch (error) {
    console.error('Topluluk silinirken hata oluştu:', error);
    throw error;
  }
} 