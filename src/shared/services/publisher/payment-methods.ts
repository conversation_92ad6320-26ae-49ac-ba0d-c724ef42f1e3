import { authService } from '../auth-service';
import { API_CONFIG } from '../api-config';

export interface PaymentMethodItem {
    payment_method_id: string;
    payment_method: 'BANK_TRANSFER' | 'DIGITAL_WALLET' | string;
    iban?: string;
    bank_name?: string;
    account_holder_name?: string;
    account_type?: 'INDIVIDUAL' | 'CORPORATE' | string;
    status?: 'ACTIVE' | 'INACTIVE' | string;
    created_at: number;
}

export interface PaymentMethodListResponse {
    status: boolean;
    errorCode: string | null;
    operation: string | null;
    httpStatus: number;
    desc: string;
    result: PaymentMethodItem[];
}

export async function getPaymentMethodList(): Promise<PaymentMethodItem[]> {
  try {
    const authToken = authService.getAuthToken();
    if (!authToken) {
      throw new Error('Kimlik doğrulama tokeni bulunamadı.');
    }

    const response = await fetch(`${API_CONFIG.BASE_URL}/publisher/paymentMethod/list`, {
      method: 'GET',
      headers: {
        'Authorization': authToken,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.desc || 'Ödeme yöntemleri alınamadı');
    }

    const data: PaymentMethodListResponse = await response.json();
    return data.result || [];
  } catch (error) {
    console.error('Ödeme yöntemleri alınırken hata oluştu:', error);
    throw error;
  }
}

// Ödeme yöntemi silme fonksiyonu
export interface DeletePaymentMethodRequest {
  payment_method_id: string;
}

export interface DeletePaymentMethodResponse {
  status: boolean;
  errorCode: string | null;
  operation: string | null;
  httpStatus: number;
  desc: string;
  result: {
    success: boolean;
  };
}

export async function deletePaymentMethod(params: DeletePaymentMethodRequest): Promise<boolean> {
  try {
    const authToken = authService.getAuthToken();
    if (!authToken) {
      throw new Error('Kimlik doğrulama tokeni bulunamadı.');
    }

    const response = await fetch(`${API_CONFIG.BASE_URL}/publisher/paymentMethod/delete`, {
      method: 'POST',
      headers: {
        'Authorization': authToken,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(params),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.desc || 'Ödeme yöntemi silinemedi');
    }

    const data: DeletePaymentMethodResponse = await response.json();
    return data.result?.success || false;
  } catch (error) {
    console.error('Ödeme yöntemi silinirken hata oluştu:', error);
    throw error;
  }
}

// Ödeme yöntemi ekleme için API isteği
export type PaymentMethodType = 'CREDIT_CARD' | 'BANK_TRANSFER' | 'PAYPAL';
export type AccountType = 'INDIVIDUAL' | 'CORPORATE';

export interface AddPaymentMethodRequest {
  payment_method: PaymentMethodType;
  iban?: string;
  bank_name?: string;
  account_holder_name?: string;
  account_type?: AccountType;
}

export interface AddPaymentMethodResponse {
  status: boolean;
  errorCode: string | null;
  operation: string | null;
  httpStatus: number;
  desc: string;
  result: {
    payment_method_id: string;
    payment_method: PaymentMethodType;
    iban?: string;
    bank_name?: string;
    account_holder_name?: string;
    account_type?: AccountType;
    status: string;
    created_at: number;
  };
}

export async function addPaymentMethod(params: AddPaymentMethodRequest): Promise<AddPaymentMethodResponse['result']> {
  try {
    const authToken = authService.getAuthToken();
    if (!authToken) {
      throw new Error('Kimlik doğrulama tokeni bulunamadı.');
    }

    const response = await fetch(`${API_CONFIG.BASE_URL}/publisher/paymentMethod/add`, {
      method: 'POST',
      headers: {
        'Authorization': authToken,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(params),
    });

    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.desc || 'Ödeme yöntemi eklenemedi');
    }

    const data: AddPaymentMethodResponse = await response.json();
    return data.result;
  } catch (error) {
    console.error('Ödeme yöntemi eklenirken hata oluştu:', error);
    throw error;
  }
} 