import { authService } from '../auth-service';
import { API_CONFIG } from '../api-config';

export interface CommunityCreateFirstParams {
  community_username: string;
  community_name: string;
  category: string;
  country: string;
  language: string;
}

export interface CommunityCreateFirstResponse {
  status: boolean;
  message: string;
  result?: {
    communityId?: string;
    name?: string;
    platform?: string;
    status?: string;
    created_at?: string;
  };
}

export async function createPublisherCommunityFirst(params: CommunityCreateFirstParams): Promise<CommunityCreateFirstResponse> {
  const headers: Record<string, string> = {
    "Content-Type": "application/json"
  };
  const token = authService.getAuthToken();
  console.log("TOKEN:", token);
  if (token) {
    headers["Authorization"] = `${token}`;
  }
  const res = await fetch(`${API_CONFIG.BASE_URL}/publisher/community/create`, {
    method: "POST",
    headers,
    body: JSON.stringify(params)
  });
  return res.json();
} 