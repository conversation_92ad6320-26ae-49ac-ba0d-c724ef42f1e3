import { API_CONFIG, ApiResponse } from '../api-config';
import { authService } from '../auth-service';

// Payment method types
export type PaymentMethodType = 'BANK_TRANSFER' | 'CREDIT_CARD' | 'PAYPAL' | 'CRYPTO';
export type AccountType = 'PERSONAL' | 'BUSINESS';

// Payment method request interface
export interface AddPaymentMethodRequest {
  payment_method: PaymentMethodType;
  iban: string;
  bank_name: string;
  account_holder_name: string;
  account_type: AccountType;
}

// Payment method response interface
export interface PaymentMethodResponse {
  status: boolean;
  errorCode: string | null;
  operation: string | null;
  httpStatus: number;
  desc: string;
  result: {
    id: string;
    payment_method: PaymentMethodType;
    iban: string;
    bank_name: string;
    account_holder_name: string;
    account_type: AccountType;
    created_at: number;
    updated_at: number;
  };
}

// Delete payment method request interface
export interface DeletePaymentMethodRequest {
  payment_method_id: string;
}

// Delete payment method response interface
export interface DeletePaymentMethodResponse {
  status: boolean;
  errorCode: string | null;
  operation: string | null;
  httpStatus: number;
  desc: string;
  result: {
    success: boolean;
  };
}

/**
 * Add a new payment method
 * @param data Payment method data
 * @returns Promise<PaymentMethodResponse['result']>
 */
export async function addPaymentMethod(data: AddPaymentMethodRequest): Promise<PaymentMethodResponse['result']> {
  const authToken = authService.getAuthToken();
  if (!authToken) {
    throw new Error('Kimlik doğrulama tokeni bulunamadı.');
  }

  const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.PUBLISHER.PAYMENT_METHOD_ADD}`, {
    method: 'POST',
    headers: {
      'Authorization': authToken,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(data)
  });

  if (!response.ok) {
    throw new Error('Ödeme yöntemi eklenemedi');
  }

  const responseData: PaymentMethodResponse = await response.json();
  if (!responseData.status) {
    throw new Error(responseData.desc || 'Ödeme yöntemi eklenemedi');
  }

  return responseData.result;
}

/**
 * Delete a payment method
 * @param paymentMethodId ID of the payment method to delete
 * @returns Promise<boolean> True if deletion was successful
 */
export async function deletePaymentMethod(paymentMethodId: string): Promise<boolean> {
  const authToken = authService.getAuthToken();
  if (!authToken) {
    throw new Error('Kimlik doğrulama tokeni bulunamadı.');
  }

  const requestData: DeletePaymentMethodRequest = {
    payment_method_id: paymentMethodId
  };

  const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.PUBLISHER.PAYMENT_METHOD_DELETE}`, {
    method: 'POST',
    headers: {
      'Authorization': authToken,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(requestData)
  });

  if (!response.ok) {
    throw new Error('Ödeme yöntemi silinemedi');
  }

  const responseData: DeletePaymentMethodResponse = await response.json();
  if (!responseData.status) {
    throw new Error(responseData.desc || 'Ödeme yöntemi silinemedi');
  }

  return responseData.result.success;
} 