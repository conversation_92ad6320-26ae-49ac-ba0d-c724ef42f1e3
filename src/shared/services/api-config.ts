// API servis yapılandırmaları
export const API_CONFIG = {
  BASE_URL: "https://devapi.adnomio.com",
  ENDPOINTS: {
    AUTH: {
      LOGIN: "/user/login",
      LOGOUT: "/user/logout"
    },
    USERS: "/user/list",
    ADMIN: {
      USER_LIST: "/admin/user/list",
      USER_STATISTICS: "/admin/user/statistics",
      PAYMENT_LIST: "/admin/advertiser/paymentList",
      USER_DETAIL: "/admin/user/get",
      PUBLISHER_PAYMENT_LIST: "/admin/publisher/paymentList",
      TOP_PUBLISHER_PAYMENTS: "/admin/publisher/top5PublisherPayment",
      CAMPAIGN_STATISTICS: "/admin/dashboard/campaignStatistics",
      MONTHLY_PERFORMANCE: "/admin/advertiser/monthlyPerformance",
      ADVERT_STATISTICS: "/admin/advertStatistics",
      ADVERT_PLAN_DAY_LIST: "/admin/advertPlan/dayList",
      PLAN_DAY_LIST_BY_HOUR: "/admin/campaigns/planDayListByHour"
    },
    PUBLISHER: {
      EARNINGS_GRAPH: "/publisher/earningsGraph",
      PAYMENT_METHOD_ADD: "/publisher/paymentMethod/add",
      PAYMENT_METHOD_DELETE: "/publisher/paymentMethod/delete"
    },
    CAMPAIGN: {
      LIST: "/advertiser/campaign/list",
      CREATE: "/advertiser/campaign/create",
      DELETE: "/advertiser/campaign/delete",
      PAUSE: "/advertiser/campaign/pause",
      RESUME: "/advertiser/campaign/resume",
      STATISTICS: "/advertiser/campaign/statistics",
      UPDATE: "/advertiser/campaign/update"
    }
  }
}


// Response tipi
export interface ApiResponse<T> {
  status: boolean;
  errorCode: string | null;
  operation: string | null;
  httpStatus: number;
  desc: string;
  result: T;
} 