// Admin paneli bildirim listeleme servisi
import { authService } from '../auth-service';
import { API_CONFIG } from '../api-config';

export interface AdminNotificationListParams {
  limit?: number;
  skip?: number;
  user_type?: string; // "publisher" | "advertiser" | "user" | "system"
  process_type?: string;
}

export interface AdminNotification {
  notification_id: string;
  created_at: number;
  type: string;
  user_type: string;
  process_type: string;
  isRead: boolean;
  user_id: string;
  email: string;
  name: string;
  surname: string;
}

export interface AdminNotificationListResponse {
  status: boolean;
  errorCode: string | null;
  operation: string | null;
  httpStatus: number;
  desc: string;
  result: {
    data: AdminNotification[];
    stats: {
      total: number;
    }
  }
}

export async function fetchAdminNotificationList(params: AdminNotificationListParams): Promise<AdminNotificationListResponse> {
  const headers: Record<string, string> = {
    "Content-Type": "application/json"
  };
  const token = authService.getAuthToken();
  if (token) {
    headers["Authorization"] = token;
  }
  const response = await fetch(`${API_CONFIG.BASE_URL}/admin/notification/list`, {
    method: "POST",
    headers,
    body: JSON.stringify(params)
  });
  return await response.json();
} 