import { authService } from '../auth-service';
import { API_CONFIG } from '../api-config';

export interface PaymentStatisticsResponse {
  status: boolean;
  errorCode: string | null;
  operation: string | null;
  httpStatus: number;
  desc: string;
  result: {
    total_payments: {
      amount: number;
      count: number;
      last_30_days_count: number;
      monthly_change_percentage: number;
    };
    pending_payments: {
      amount: number;
      count: number;
      oldest_payment_days: number;
    };
    failed_payments: {
      amount: number;
      count: number;
    };
    success_rate: {
      current_rate: number;
      monthly_change_percentage: number;
    };
    average_payment: {
      amount: number;
      monthly_change_percentage: number;
    };
  };
}

export async function getPaymentStatistics(): Promise<PaymentStatisticsResponse['result']> {
  try {
    const authToken = authService.getAuthToken();
    if (!authToken) {
      throw new Error('Kimlik doğrulama tokeni bulunamadı.');
    }

    const response = await fetch(`${API_CONFIG.BASE_URL}/admin/advertiser/paymentStatistics`, {
      method: 'GET',
      headers: {
        'Authorization': authToken,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('Ödeme istatistikleri alınamadı');
    }

    const data: PaymentStatisticsResponse = await response.json();
    return data.result;
  } catch (error) {
    console.error('Ödeme istatistikleri alınırken hata oluştu:', error);
    throw error;
  }
} 