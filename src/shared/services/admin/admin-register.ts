    // Admin advertiser register servisi

export interface AdminRegisterParams {
  email: string;
  password: string;
  name: string;
  surname: string;
  company_name: string;
  sector: string;
}

export interface AdminRegisterResponse {
  status: boolean;
  errorCode: string | null;
  operation: string | null;
  httpStatus: number;
  desc: string;
  result: {
    userId?: string;
    email?: string;
    name?: string;
    role?: string;
    created_at?: number;
  };
}

export async function adminRegister(params: AdminRegisterParams): Promise<AdminRegisterResponse> {
  const response = await fetch("https://devapi.adnomio.com/user/advertiser/register", {
    method: "POST",
    headers: {
      "Content-Type": "application/json"
    },
    body: JSON.stringify(params)
  });

  const data = await response.json();
  
  // HTTP durumunu kontrol et
  if (!response.ok) {
    if (response.status === 409) {
      return {
        status: false,
        errorCode: "CONFLICT",
        operation: "REGISTER",
        httpStatus: 409,
        desc: "Bu e-posta adresi ile daha önce kayıt yapılmı<PERSON>",
        result: {}
      };
    }
    
    return {
      status: false,
      errorCode: data.errorCode || String(response.status),
      operation: "REGISTER",
      httpStatus: response.status,
      desc: data.desc || "Kayıt işlemi sırasında bir hata oluştu",
      result: {}
    };
  }

  return data;
} 