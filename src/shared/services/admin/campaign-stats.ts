import { API_CONFIG, ApiResponse } from "../api-config";
import { authService } from '../auth-service';

export interface CampaignStatistics {
  total_view: number;
  total_click: number;
  total_conversion: number;
  total_earnings: number;
}


export async function fetchCampaignStatistics(): Promise<ApiResponse<CampaignStatistics>> {
  try {
    const token = authService.getAuthToken();

    const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.ADMIN.CAMPAIGN_STATISTICS}`, {
      method: "GET",
      headers: {
        "Authorization": `${token}`,
        "Content-Type": "application/json"
      }
    });
    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Campaign statistics fetch error:", error);
    return {
      status: false,
      errorCode: "API_ERROR",
      operation: "FETCH_CAMPAIGN_STATISTICS",
      httpStatus: 500,
      desc: error instanceof Error ? error.message : "Failed to fetch campaign statistics",
      result: {
        total_view: 0,
        total_click: 0,
        total_conversion: 0,
        total_earnings: 0
      }
    };
  }
} 