import { API_CONFIG } from '../api-config';
import { authService } from '../auth-service';

export interface Last12MonthsPaymentStat {
  month: string;
  month_full: string;
  year: number;
  month_start: string;
  month_end: string;
  amount: number;
  count: number;
}

export interface Last12MonthsPaymentStatisticsResponse {
  status: boolean;
  errorCode: string | null;
  operation: string | null;
  httpStatus: number;
  desc: string;
  result: Last12MonthsPaymentStat[];
}

export async function getLast12MonthsPaymentStatistics(): Promise<Last12MonthsPaymentStat[]> {
  const authToken = authService.getAuthToken();
  if (!authToken) {
    throw new Error('Kimlik doğrulama tokeni bulunamadı.');
  }
  const response = await fetch(`${API_CONFIG.BASE_URL}/admin/publisher/last12monthsPaymentStatistics`, {
    method: 'GET',
    headers: {
      'Authorization': authToken,
      'Content-Type': 'application/json',
    },
  });
  if (!response.ok) {
    // Hata durumunda 7 ay için sıfır veri döndür
    return Array.from({ length: 7 }).map((_, i) => ({
      month: '',
      month_full: '',
      year: 0,
      month_start: '',
      month_end: '',
      amount: 0,
      count: 0,
    }))
  }
  const data = await response.json();
  return data.result;
} 