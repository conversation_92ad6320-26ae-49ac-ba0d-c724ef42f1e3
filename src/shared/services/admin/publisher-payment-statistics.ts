import { API_CONFIG } from '../api-config';
import { authService } from '../auth-service';

export interface PublisherPaymentStatisticsResponse {
  status: boolean;
  errorCode: string | null;
  operation: string | null;
  httpStatus: number;
  desc: string;
  result: {
    total_payments: {
      count: number;
      amount: number;
    };
    pending_payments: {
      count: number;
      amount: number;
    };
    processing_payments: {
      count: number;
      amount: number;
    };
    completed_payments: {
      count: number;
      amount: number;
    };
  };
}

export interface PublisherPayment {
  withdraw_request_id: string;
  user_id: string;
  amount: number;
  payment_method?: string;
  iban?: string;
  bank_name?: string;
  account_holder_name?: string;
  status: string;
  description?: string;
  created_at: number;
  processing_at: number | null;
  completed_at: number | null;
  rejected_at: number | null;
  canceled_at: number | null;
  email?: string;
  name?: string;
  surname?: string;
}

export interface PublisherPaymentListResponse {
  status: boolean;
  errorCode: string | null;
  operation: string | null;
  httpStatus: number;
  desc: string;
  result: {
    data: PublisherPayment[];
    stats: {
      total: number;
    };
  };
}

export async function getPublisherPaymentList(params: { limit?: number; skip?: number; search?: string }): Promise<PublisherPaymentListResponse['result']> {
  const authToken = authService.getAuthToken();
  if (!authToken) {
    throw new Error('Kimlik doğrulama tokeni bulunamadı.');
  }

  const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.ADMIN.PUBLISHER_PAYMENT_LIST}`, {
    method: 'POST',
    headers: {
      'Authorization': authToken,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(params)
  });

  if (!response.ok) {
    throw new Error('Yayıncı ödeme listesi alınamadı');
  }

  const data = await response.json();
  return data.result;
}

export interface PublisherPaymentMethodStatisticsResponse {
  status: boolean;
  errorCode: string | null;
  operation: string | null;
  httpStatus: number;
  desc: string;
  result: {
    BANK_TRANSFER: number;
    CREDIT_CARD: number;
    PAYPAL: number;
    CRYPTO: number;
  };
}

export async function getPublisherPaymentStatistics(): Promise<PublisherPaymentStatisticsResponse['result']> {
  const authToken = authService.getAuthToken();
  if (!authToken) {
    throw new Error('Kimlik doğrulama tokeni bulunamadı.');
  }

  const response = await fetch(`${API_CONFIG.BASE_URL}/admin/publisher/paymentStatistics`, {
    method: 'GET',
    headers: {
      'Authorization': authToken,
      'Content-Type': 'application/json',
    },
  });
  if (!response.ok) {
    throw new Error('Yayıncı ödeme istatistikleri alınamadı');
  }
  const data = await response.json();
  return data.result;
}

export async function getPublisherPaymentMethodStatistics(): Promise<PublisherPaymentMethodStatisticsResponse['result']> {
  const authToken = authService.getAuthToken();
  if (!authToken) {
    throw new Error('Kimlik doğrulama tokeni bulunamadı.');
  }

  const response = await fetch(`${API_CONFIG.BASE_URL}/admin/publisher/paymentStatisticsByMethod`, {
    method: 'GET',
    headers: {
      'Authorization': authToken,
      'Content-Type': 'application/json',
    },
  });
  if (!response.ok) {
    throw new Error('Yayıncı ödeme yöntemi istatistikleri alınamadı');
  }
  const data = await response.json();
  return data.result;
}

export interface TopPublisherPayment {
  publisher_name: string;
  publisher_email: string;
  total_payment: number;
  transaction_count: number;
  last_payment_date: string;
}

export interface TopPublisherPaymentsResponse {
  status: boolean;
  errorCode: string | null;
  operation: string | null;
  httpStatus: number;
  desc: string;
  result: TopPublisherPayment[];
}

export async function getTopPublisherPayments(): Promise<TopPublisherPayment[]> {
  const authToken = authService.getAuthToken();
  if (!authToken) {
    throw new Error('Kimlik doğrulama tokeni bulunamadı.');
  }

  const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.ADMIN.TOP_PUBLISHER_PAYMENTS}`, {
    method: 'GET',
    headers: {
      'Authorization': authToken,
      'Content-Type': 'application/json',
    },
  });

  if (!response.ok) {
    throw new Error('En çok ödeme yapılan yayıncılar alınamadı');
  }

  const data = await response.json();
  return data.result;
} 