// Admin paneli kullanıcı listeleme servisi
import { authService } from '../auth-service';
import { API_CONFIG } from '../api-config';

export interface AdminUserListParams {
  limit?: number;
  skip?: number;
  status?: boolean;
  role?: 'advertiser' | 'admin' | 'publisher';
}

export interface AdminUser {
  user_id: string;
  email: string;
  created_at: number;
  status: boolean;
  name: string;
  surname: string;
  company_name: string;
  sector: string;
  role: 'advertiser' | 'admin' | 'publisher';
}

export interface AdminUserListResponse {
  status: boolean;
  errorCode: string | null;
  operation: string | null;
  httpStatus: number;
  desc: string;
  result: {
    data: AdminUser[];
    stats: {
      total: number;
    }
  }
}

export async function fetchAdminUserList(params: AdminUserListParams): Promise<AdminUserListResponse> {
  const headers: Record<string, string> = {
    "Content-Type": "application/json"
  };
  const token = authService.getAuthToken();
  if (token) {
    headers["Authorization"] = token;
  }
  const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.ADMIN.USER_LIST}`, {
    method: "POST",
    headers,
    body: JSON.stringify(params)
  });
  return await response.json();
}

// Kullanıcı istatistikleri tipi
export interface AdminUserStatisticsResponse {
  status: boolean;
  errorCode: string | null;
  operation: string | null;
  httpStatus: number;
  desc: string;
  result: {
    total_user: number;
    active_user: number;
    pending_user: number;
    banned_user: number;
    advertiser_count: number;
    publisher_count: number;
    admin_count: number;
    new_user_30d?: number;
    passive_user: number;
  }
}

export async function fetchAdminUserStatistics(): Promise<AdminUserStatisticsResponse> {
  const headers: Record<string, string> = {
    "Content-Type": "application/json"
  };
  const token = authService.getAuthToken();
  if (token) {
    headers["Authorization"] = token;
  }
  const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.ADMIN.USER_STATISTICS}`, {
    method: "GET",
    headers
  });
  return await response.json();
}

// Reklamveren listesini getir
export async function fetchAdvertiserList(): Promise<AdminUserListResponse> {
  const headers: Record<string, string> = {
    "Content-Type": "application/json"
  };
  const token = authService.getAuthToken();
  if (token) {
    headers["Authorization"] = token;
  }
  const params: AdminUserListParams = {
    role: 'advertiser',
    limit: 0,
    skip: 0
  };
  const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.ADMIN.USER_LIST}`, {
    method: "POST",
    headers,
    body: JSON.stringify(params)
  });
  return await response.json();
}

// Admin paneli kampanya listeleme servisi
export interface AdminCampaignListParams {
  limit?: number;
  skip?: number;
  status?: string;
  search?: string;
  sort?: 'asc' | 'desc';
  created_from?: string;
  created_at?: 'last7days' | 'last30days' | 'last90days';
}

export interface AdminCampaign {
  _id: string;
  campaign_id: string;
  title: string;
  campaign_aim: string;
  advertising_platform: string;
  country: string;
  language: string;
  category: string[];
  campaign_title: string;
  campaign_desc: string;
  destination_url: string;
  campaign_image?: string;
  campaign_budget: number;
  bid_strategy: string;
  cpm_bid_price: number;
  cpc_bid_price: number;
  brand_safety_check: string;
  starts_at: number | string;
  ends_at: number | string;
  status: string;
  created_at: number;
  created_from?: string;
  cta_button?: string;
  communities?: { active: number; pending: number; [key: string]: number };
  // Performance metrics
  ctr?: number | null;
  clicks?: number | null;
  views?: number | null;
  conversions?: number | null;
  earnings?: number | null;
  spending?: number | null;
  updated_at?: number;
  updated_from?: string;
}

export interface AdminCampaignListResponse {
  status: boolean;
  errorCode: string | null;
  operation: string | null;
  httpStatus: number;
  desc: string;
  result: {
    data: AdminCampaign[];
    stats?: {
      total?: number;
      active?: number;
      paused?: number;
      draft?: number;
      completed?: number;
    }
  }
}

export async function fetchAdminCampaignList(params: AdminCampaignListParams): Promise<AdminCampaignListResponse> {
  const headers: Record<string, string> = {
    "Content-Type": "application/json"
  };
  const token = authService.getAuthToken();
  if (token) {
    headers["Authorization"] = token;
  }
  // NOT: Endpoint'i backend'e göre güncelleyin
  const response = await fetch(`${API_CONFIG.BASE_URL}/admin/campaign/list`, {
    method: "POST",
    headers,
    body: JSON.stringify(params)
  });
  return await response.json();
}

export {}