import { authService } from '../auth-service';
import { API_CONFIG } from '../api-config';

export interface TopAdvertiser {
  total_earnings: number;
  total_views: number;
  total_clicks: number;
  user_id: string;
  name: string;
  surname: string;
  avg_ctr: number;
  campaign_count: number;
}

export interface TopAdvertisersResponse {
  status: boolean;
  errorCode: string | null;
  operation: string | null;
  httpStatus: number;
  desc: string;
  result: TopAdvertiser[];
}

export async function getTopAdvertisers(): Promise<TopAdvertisersResponse['result']> {
  try {
    const authToken = authService.getAuthToken();
    if (!authToken) {
      throw new Error('Kimlik doğrulama tokeni bulunamadı.');
    }

    const response = await fetch(`${API_CONFIG.BASE_URL}/admin/advertiser/topAdvertisers`, {
      method: 'GET',
      headers: {
        'Authorization': authToken,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error('En yüksek harcama yapan reklamverenler alınamadı');
    }

    const data: TopAdvertisersResponse = await response.json();
    return data.result;
  } catch (error) {
    console.error('En yüksek harcama yapan reklamverenler alınırken hata oluştu:', error);
    throw error;
  }
} 