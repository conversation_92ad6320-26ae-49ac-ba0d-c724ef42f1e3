import { API_CONFIG, ApiResponse } from "../api-config";
import { authService } from '../auth-service';

export interface AdvertiserPerformance {
  _id: string;
  views: number;
  clicks: number;
  conversions: number;
  earnings: number;
  user_id: string;
  name: string;
  surname: string;
  avg_ctr: number;
}

export interface MonthlyPerformance {
  month: string;
  month_start: string;
  month_end: string;
  advertisers: AdvertiserPerformance[];
}

export async function fetchMonthlyPerformance(): Promise<ApiResponse<MonthlyPerformance[]>> {
  try {
    const token = authService.getAuthToken();
    const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.ADMIN.MONTHLY_PERFORMANCE}`, {
      method: "GET",
      headers: {
        "Authorization": `${token}`,
        "Content-Type": "application/json"
      }
    });
    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Monthly performance fetch error:", error);
    return {
      status: false,
      errorCode: "API_ERROR",
      operation: "FETCH_MONTHLY_PERFORMANCE",
      httpStatus: 500,
      desc: error instanceof Error ? error.message : "Failed to fetch monthly performance",
      result: []
    };
  }
}

export async function getMonthlyPerformance(): Promise<MonthlyPerformanceResponse> {
    const authToken = authService.getAuthToken();
    if (!authToken) {
      throw new Error('Kimlik doğrulama tokeni bulunamadı.');
    }
  const response = await fetch(`${API_CONFIG.BASE_URL}/admin/advertiser/monthlyPerformance`, {
    method: "GET",
    headers: {
        'Authorization': authToken,
        'Content-Type': 'application/json',
      },
  })

  if (!response.ok) {
    throw new Error("Aylık performans verileri alınamadı")
  }

  const data = await response.json()
  return data
} 