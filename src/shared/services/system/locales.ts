import { authService } from '../auth-service';
import { API_CONFIG } from '../api-config';

export const getCountries = async () => {
  const headers: Record<string, string> = {
    "Content-Type": "application/json"
  };
  
  const token = authService.getAuthToken();
  if (token) {
    headers["Authorization"] = token;
  }

  const response = await fetch(`${API_CONFIG.BASE_URL}/system/locales/country`, {
    method: "GET",
    headers
  });

  return await response.json();
};

export const getLanguages = async () => {
  const headers: Record<string, string> = {
    "Content-Type": "application/json"
  };
  
  const token = authService.getAuthToken();
  if (token) {
    headers["Authorization"] = token;
  }

  const response = await fetch(`${API_CONFIG.BASE_URL}/system/locales/language`, {
    method: "GET",
    headers
  });

  const data = await response.json();
  if (data.status && data.result) {
    // Sadece key'leri alı<PERSON><PERSON>z
    return Object.keys(data.result);
  }
  return [];
}; 