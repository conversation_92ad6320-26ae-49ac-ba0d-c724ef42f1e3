import { API_CONFIG, ApiResponse } from "./api-config";

// Kullanıcı tipleri
export type UserRole = "admin" | "advertiser" | "publisher";

export interface User {
  user_id: string;
  email: string;
  created_at: number;
  status: boolean;
  name: string;
  surname: string;
  role: UserRole;
  updated_at: number;
}

// Login yanıtı tipi
export interface LoginResponse {
  user: User;
  token: string;
}

// Kimlik doğrulama servisi
class AuthService {
  /**
   * Login işlemi
   * @param email Kullanıcı email adresi
   * @param password Kullanıcı şifresi
   * @returns ApiResponse<LoginResponse>
   */
  async login(email: string, password: string): Promise<ApiResponse<LoginResponse>> {
    try {
      const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.AUTH.LOGIN}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({ email, password })
      });
      const data = await response.json();
      // Başarılı login ise localStorage'a hem user hem token birlikte kaydedilsin
      if (data.status && data.result && data.result.user && data.result.token) {
        localStorage.setItem("user", JSON.stringify({ ...data.result.user, token: data.result.token }));
      }
      return data;
    } catch (error) {
      console.error("Login hatası:", error);
      return {
        status: false,
        errorCode: "API_ERROR",
        operation: "LOGIN",
        httpStatus: 500,
        desc: error instanceof Error ? error.message : "Login sırasında bir hata oluştu",
        result: null as unknown as LoginResponse,
      };
    }
  }
  
  /**
   * Çıkış işlemi
   * @returns Promise<ApiResponse<Record<string, never>>> 
   */
  async logout(): Promise<ApiResponse<Record<string, never>>> {
    const token = this.getAuthToken();
    
    // Token yoksa sessiz bir şekilde başarılı dön
    if (!token) {
      localStorage.removeItem("user");
      return {
        status: true,
        errorCode: null,
        operation: "LOGOUT",
        httpStatus: 200,
        desc: "Zaten oturum açık değil",
        result: {}
      };
    }
    
    try {
      const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.AUTH.LOGOUT}`, {
        method: "GET",
        headers: {
          "Authorization": token
        }
      });
      
      const data = await response.json();
      
      // API'ye bakmaksızın kullanıcıyı local olarak çıkış yaptır
      localStorage.removeItem("user");
      
      return data;
    } catch (error) {
      console.error("Logout hatası:", error);
      
      // Hata olsa bile kullanıcıyı local olarak çıkış yaptır
    localStorage.removeItem("user");
      
      return {
        status: true, // Kullanıcı açısından başarılı kabul et
        errorCode: "API_ERROR",
        operation: "LOGOUT",
        httpStatus: 500,
        desc: error instanceof Error ? error.message : "Çıkış sırasında bir hata oluştu, ancak yerel oturum kapatıldı",
        result: {}
      };
    }
  }
  
  /**
   * Mevcut kullanıcıyı al
   * @returns User | null
   */
  getCurrentUser(): User | null {
    const userStr = localStorage.getItem("user");
    if (!userStr) return null;
    
    try {
      const userData = JSON.parse(userStr);
      return userData;
    } catch (error) {
      console.error("Kullanıcı bilgisi alınamadı:", error);
      return null;
    }
  }
  
  /**
   * Token'ı localStorage'dan alma
   * @returns string | null
   */
  getAuthToken(): string | null {
    const userStr = localStorage.getItem("user");
    if (!userStr) return null;
    
    try {
      const userData = JSON.parse(userStr);
      return userData.token || null;
    } catch (error) {
      console.error("Token alınamadı:", error);
      return null;
    }
  }
  
  /**
   * Kullanıcı giriş yapmış mı kontrolü
   * @returns boolean
   */
  isAuthenticated(): boolean {
    return !!this.getCurrentUser();
  }

  /**
   * Tüm kullanıcıları listele
   * @param params Filtre parametreleri
   * @returns ApiResponse<User[]>
   */
  async listUsers(params: { limit?: number; skip?: number; status?: boolean; role?: string; search?: string }): Promise<ApiResponse<User[]>> {
    try {
      const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.USERS}`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(params)
      });
      const data = await response.json();
      return data;
    } catch (error) {
      console.error("Kullanıcı listesi hatası:", error);
      return {
        status: false,
        errorCode: "API_ERROR",
        operation: "LIST_USERS",
        httpStatus: 500,
        desc: error instanceof Error ? error.message : "Kullanıcı listesi alınırken bir hata oluştu",
        result: [] as User[],
      };
    }
  }
}

// Servis örneği oluştur
export const authService = new AuthService(); 