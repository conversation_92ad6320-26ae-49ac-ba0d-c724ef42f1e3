import { useEffect, useState } from "react"
import { useRouter, usePathname } from "next/navigation"
import { authService, User } from "@/shared/services"
import { toast } from "@/components/ui/use-toast"
import { useLanguage } from "@/shared/hooks/useLanguage"

type RoleBasedAccess = {
  "/admin": string[]
  "/advertiser": string[]
  "/publisher": string[]
}

export function useAuth() {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)
  const router = useRouter()
  const pathname = usePathname()
  const { t } = useLanguage()

  useEffect(() => {
    // Auth service'den kullanıcı bilgisini al
    const currentUser = authService.getCurrentUser()
    if (currentUser) {
      setUser(currentUser)
    }
    setLoading(false)
  }, [])

  useEffect(() => {
    if (!loading) {
      // Kullanıcı giriş yapmamış<PERSON> ve login sayfasında değilse
      if (!user && pathname !== "/auth/login") {
        router.push("/auth/login")
      }

      // Role bazlı erişim kontrolü
      if (user) {
        const roleBasedAccess: RoleBasedAccess = {
          "/admin": ["admin"],
          "/advertiser": ["advertiser"],
          "/publisher": ["publisher"]
        }

        // Mevcut path'in başlangıcını kontrol et
        const currentPath = Object.keys(roleBasedAccess).find(path => 
          pathname.startsWith(path)
        ) as keyof RoleBasedAccess

        if (currentPath && !roleBasedAccess[currentPath].includes(user.role)) {
          // Kullanıcının rolüne göre doğru sayfaya yönlendir
          const redirectMap = {
            admin: "/admin",
            advertiser: "/advertiser",
            publisher: "/publisher"
          }
          router.push(redirectMap[user.role])
        }
      }
    }
  }, [user, loading, pathname, router])

  const logout = async () => {
    try {
      // Loading göster
      setLoading(true)
      
      // API'yi kullanarak logout işlemi yap
      const response = await authService.logout()
      
      // İşlem başarılı olsa da olmasa da kullanıcıyı çıkış yaptır
      setUser(null)
      router.push("/auth/login")
      
      // Bilgilendirme mesajı göster
      if (response && response.status) {
        toast({
          title: t('auth.logout.success.title'),
          description: t('auth.logout.success.description'),
          className: "bg-green-50 border-green-200 text-green-800"
        })
      } else {
        toast({
          title: t('auth.logout.completed.title'),
          description: response.desc || t('auth.logout.completed.description'),
          variant: "default"
        })
      }
    } catch (error) {
      console.error("Logout hatası:", error)
      
      // Hata durumunda bile kullanıcıyı çıkış yaptır
      setUser(null)
      router.push("/auth/login")
      
      toast({
        title: t('auth.logout.error.title'),
        description: t('auth.logout.error.description'),
        variant: "default"
      })
    } finally {
      setLoading(false)
    }
  }

  return {
    user,
    loading,
    logout,
    isAuthenticated: !!user,
    isAdmin: user?.role === "admin",
    isAdvertiser: user?.role === "advertiser",
    isPublisher: user?.role === "publisher"
  }
}
