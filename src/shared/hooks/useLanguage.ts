'use client';

import { useTranslation } from 'react-i18next';
import { useEffect, useState } from 'react';

export type Language = 'tr' | 'en';

export const SUPPORTED_LANGUAGES = {
  tr: {
    name: 'Türk<PERSON>e',
    flag: '🇹🇷',
    code: 'tr' as Language
  },
  en: {
    name: 'English',
    flag: '🇬🇧',
    code: 'en' as Language
  }
} as const;

export function useLanguage(namespace?: string) {
  const { i18n, t } = useTranslation(namespace);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    console.log('useLanguage hook mount edildi');
    setMounted(true);
    const savedLang = localStorage.getItem('preferredLanguage') as Language;
    console.log('Kaydedilmiş dil tercihi:', savedLang);
    
    if (savedLang && savedLang in SUPPORTED_LANGUAGES) {
      console.log('Kaydedilmiş dil yükleniyor:', savedLang);
      i18n.changeLanguage(savedLang);
    } else {
      console.log('Kaydedilmiş dil bulunamadı, varsayılan dil kullanılıyor');
    }
  }, [i18n]);

  const changeLanguage = async (language: Language) => {
    try {
      console.log('Dil değiştiriliyor:', language);
      await i18n.changeLanguage(language);
      localStorage.setItem('preferredLanguage', language);
      document.documentElement.lang = language;
      console.log('Dil başarıyla değiştirildi');
    } catch (error) {
      console.error('Dil değiştirme hatası:', error);
    }
  };

  const currentLanguage = i18n.language as Language;
  const isCurrentLanguageSupported = currentLanguage in SUPPORTED_LANGUAGES;

  const getCurrentLanguageInfo = () => {
    if (!mounted) {
      console.log('Component henüz mount edilmedi, varsayılan dil kullanılıyor');
      return SUPPORTED_LANGUAGES.tr;
    }
    console.log('Mevcut dil bilgisi:', {
      language: currentLanguage,
      isSupported: isCurrentLanguageSupported
    });
    return SUPPORTED_LANGUAGES[isCurrentLanguageSupported ? currentLanguage : 'tr'];
  };

  return {
    t,
    currentLanguage,
    changeLanguage,
    isReady: mounted && i18n.isInitialized,
    supportedLanguages: SUPPORTED_LANGUAGES,
    getCurrentLanguageInfo,
  };
} 