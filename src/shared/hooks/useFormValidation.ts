import { useState, useCallback } from 'react';

export type ValidationRule = {
  test: (value: any) => boolean;
  message: string;
};

export type ValidationRules = {
  [key: string]: ValidationRule[];
};

export type ValidationErrors = {
  [key: string]: string;
};

export type FormData = {
  [key: string]: string | boolean | number;
};

// Ortak validasyon kuralları
export const commonValidationRules = {
  required: {
    test: (value: any) => {
      if (typeof value === 'boolean') return value;
      if (typeof value === 'number') return true;
      return value.trim().length > 0;
    },
    message: 'Bu alan <PERSON>',
  },
  email: {
    test: (value: string) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value),
    message: 'Geçerli bir e-posta adresi giriniz',
  },
  password: {
    test: (value: string) => value.length >= 6,
    message: '<PERSON><PERSON>re en az 6 karakter olmalıdır',
  },
  name: {
    test: (value: string) => /^[a-zA-ZğüşıöçĞÜŞİÖÇ\s]{2,50}$/.test(value),
    message: 'İsim 2-50 karakter arası olmalı ve özel karakter içermemelidir',
  },
  phone: {
    test: (value: string) => /^\+?[1-9]\d{1,14}$/.test(value),
    message: 'Geçerli bir telefon numarası giriniz',
  },
  agreeTerms: {
    test: (value: boolean) => value === true,
    message: 'Kullanım Koşulları ve Gizlilik Politikası\'nı kabul etmelisiniz',
  },
};

// Rate limiting için
const rateLimitStore = new Map<string, { count: number; timestamp: number }>();

export const useFormValidation = (rules: ValidationRules) => {
  const [errors, setErrors] = useState<ValidationErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Rate limiting kontrolü
  const checkRateLimit = useCallback((key: string, limit: number = 5, windowMs: number = 60000) => {
    const now = Date.now();
    const rateLimit = rateLimitStore.get(key) || { count: 0, timestamp: now };

    if (now - rateLimit.timestamp > windowMs) {
      rateLimitStore.set(key, { count: 1, timestamp: now });
      return true;
    }

    if (rateLimit.count >= limit) {
      return false;
    }

    rateLimitStore.set(key, { count: rateLimit.count + 1, timestamp: rateLimit.timestamp });
    return true;
  }, []);

  // Form validasyonu
  const validateField = useCallback((name: string, value: any): string => {
    if (!rules[name]) return '';

    for (const rule of rules[name]) {
      if (!rule.test(value)) {
        return rule.message;
      }
    }

    return '';
  }, [rules]);

  // Tüm formun validasyonu
  const validateForm = useCallback((formData: FormData): boolean => {
    const newErrors: ValidationErrors = {};
    let isValid = true;

    Object.keys(rules).forEach((fieldName) => {
      const value = formData[fieldName];
      const error = validateField(fieldName, value);
      
      if (error) {
        newErrors[fieldName] = error;
        isValid = false;
      }
    });

    setErrors(newErrors);
    return isValid;
  }, [rules, validateField]);

  // Input değişikliği için handler
  const handleChange = useCallback((name: string, value: any) => {
    const error = validateField(name, value);
    setErrors(prev => ({
      ...prev,
      [name]: error
    }));
  }, [validateField]);

  // Form gönderimi için wrapper
  const handleSubmit = useCallback(async (
    formData: FormData,
    onSubmit: () => Promise<void>,
    rateLimitKey?: string
  ) => {
    if (isSubmitting) return;

    // Rate limit kontrolü
    if (rateLimitKey && !checkRateLimit(rateLimitKey)) {
      setErrors(prev => ({
        ...prev,
        submit: 'Çok fazla deneme yaptınız. Lütfen bir süre bekleyin.'
      }));
      return;
    }

    // Form validasyonu
    if (!validateForm(formData)) return;

    try {
      setIsSubmitting(true);
      await onSubmit();
    } catch (error) {
      setErrors(prev => ({
        ...prev,
        submit: error instanceof Error ? error.message : 'E-posta adresiniz veya şifreniz hatalı. Lütfen bilgilerinizi kontrol edip tekrar deneyin.'
      }));
    } finally {
      setIsSubmitting(false);
    }
  }, [validateForm, isSubmitting, checkRateLimit]);

  return {
    errors,
    isSubmitting,
    validateField,
    validateForm,
    handleChange,
    handleSubmit,
  };
}; 