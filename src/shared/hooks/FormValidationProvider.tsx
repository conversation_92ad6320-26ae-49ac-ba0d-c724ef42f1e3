import React, { createContext, useContext, ReactNode } from 'react';
import { ValidationRules, commonValidationRules } from './useFormValidation';

interface FormValidationContextType {
  commonRules: typeof commonValidationRules;
  getFieldRules: (formType: string) => ValidationRules;
}

const FormValidationContext = createContext<FormValidationContextType | undefined>(undefined);

// Farklı form tipleri için validasyon kuralları
const formValidationRules: Record<string, ValidationRules> = {
  publisherSignup: {
    firstName: [commonValidationRules.required, commonValidationRules.name],
    lastName: [commonValidationRules.required, commonValidationRules.name],
    email: [commonValidationRules.required, commonValidationRules.email],
    password: [commonValidationRules.required, commonValidationRules.password],
  },
  advertiserSignup: {
    firstName: [commonValidationRules.required, commonValidationRules.name],
    lastName: [commonValidationRules.required, commonValidationRules.name],
    email: [commonValidationRules.required, commonValidationRules.email],
    password: [commonValidationRules.required, commonValidationRules.password],
    companyName: [commonValidationRules.required],
    industry: [commonValidationRules.required],
  },
  login: {
    email: [commonValidationRules.required, commonValidationRules.email],
    password: [commonValidationRules.required],
  },
  forgotPassword: {
    email: [commonValidationRules.required, commonValidationRules.email],
  },
};

export function FormValidationProvider({ children }: { children: ReactNode }) {
  const getFieldRules = (formType: string): ValidationRules => {
    return formValidationRules[formType] || {};
  };

  const value = {
    commonRules: commonValidationRules,
    getFieldRules,
  };

  return (
    <FormValidationContext.Provider value={value}>
      {children}
    </FormValidationContext.Provider>
  );
}

export function useFormValidationContext() {
  const context = useContext(FormValidationContext);
  if (context === undefined) {
    throw new Error('useFormValidationContext must be used within a FormValidationProvider');
  }
  return context;
} 