'use client';

import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// Dil dosyaları
import commonTR from '../locales/tr/common.json';
import adminTR from '../locales/tr/admin.json';
import publisherTR from '../locales/tr/publisher.json';
import advertiserTR from '../locales/tr/advertiser.json';
import authTR from '../locales/tr/auth.json';

import commonEN from '../locales/en/common.json';
import adminEN from '../locales/en/admin.json';
import publisherEN from '../locales/en/publisher.json';
import advertiserEN from '../locales/en/advertiser.json';
import authEN from '../locales/en/auth.json';

console.log('i18n yapılandırması başlatılıyor...');
console.log('Mevcut dil dosyaları:', {
  tr: { common: commonTR, admin: adminTR, publisher: publisherTR, advertiser: advertiserTR, auth: authTR },
  en: { common: commonEN, admin: adminEN, publisher: publisherEN, advertiser: advertiserEN, auth: authEN }
});

const resources = {
  tr: {
    common: commonTR,
    admin: adminTR,
    publisher: publisherTR,
    advertiser: advertiserTR,
    auth: authTR
  },
  en: {
    common: commonEN,
    admin: adminEN,
    publisher: publisherEN,
    advertiser: advertiserEN,
    auth: authEN
  }
};

const i18nInstance = i18n.createInstance();

if (!i18nInstance.isInitialized) {
  console.log('i18n başlatılıyor...');
  i18nInstance
    .use(initReactI18next)
    .use(LanguageDetector)
    .init({
      resources,
      lng: 'tr',
      fallbackLng: 'tr',
      defaultNS: 'publisher',
      interpolation: {
        escapeValue: false,
      },
      react: {
        useSuspense: false,
      },
    }).then(() => {
      console.log('i18n başlatıldı');
      console.log('Mevcut dil:', i18nInstance.language);
      console.log('Kullanılabilir diller:', i18nInstance.languages);
    });
} else {
  console.log('i18n zaten başlatılmış');
}

export default i18nInstance; 