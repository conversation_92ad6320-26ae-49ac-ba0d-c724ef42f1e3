'use client';

import { I18nextProvider } from 'react-i18next';
import { useEffect, useState } from 'react';
import i18n from './i18n';

interface I18nProviderProps {
  children: React.ReactNode;
}

export default function I18nProvider({ children }: I18nProviderProps) {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    const savedLang = localStorage.getItem('preferredLanguage');
    if (savedLang) {
      i18n.changeLanguage(savedLang);
    } else {
      // Tarayıcı dilini al veya varsayılan olarak 'tr' kullan
      const browserLang = navigator.language.split('-')[0];
      const defaultLang = ['tr', 'en'].includes(browserLang) ? browserLang : 'tr';
      i18n.changeLanguage(defaultLang);
      localStorage.setItem('preferredLanguage', defaultLang);
    }
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
      </div>
    );
  }

  return (
    <I18nextProvider i18n={i18n}>
      {children}
    </I18nextProvider>
  );
} 