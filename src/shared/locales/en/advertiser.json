{"dashboard": {"title": "Advertiser Panel", "welcome": "Welcome,", "subtitle": "Manage your campaigns and track performance", "stats": {"totalSpent": "Total Spent", "activeCampaigns": "Active Campaigns", "totalReach": "Total Reach", "monthlySpent": "Monthly Spent", "totalCampaigns": "Total Campaigns", "totalEngagement": "Total Engagement", "totalClicks": "Total Clicks"}, "sections": {"campaigns": {"title": "My Campaigns", "description": "View, edit and manage all your campaigns", "create": "Create New Campaign", "table": {"title": "Recent Campaigns", "description": "Recently created and updated campaigns", "empty": "You haven't created any campaigns yet", "createFirst": "Create your first campaign", "viewAll": "View All Campaigns", "totalShowing": "Showing {count} campaigns in total", "actions": {"view": "View", "edit": "Edit"}}}, "statistics": {"title": "Statistics", "description": "Analyze your campaign performance", "charts": {"performance": {"title": "Campaign Performance", "description": "Campaign performance metrics for the last 30 days", "noData": "No data available yet"}}}, "payments": "My Payments", "settings": "Settings"}, "charts": {"performance": "Performance Chart", "campaignStats": "Campaign Statistics", "reachAnalytics": "Reach Analytics", "performanceMetrics": {"title": "Campaign Performance", "description": "Last 30 days performance metrics", "currentPeriod": "Current Period", "previousPeriod": "Previous Period", "tabs": {"clicks": "<PERSON>licks", "ctr": "CTR", "impressions": "Impressions"}}}, "header": {"welcome": "Welcome,", "defaultSubtitle": "Manage your campaigns and track performance", "newCampaign": "New Campaign"}}, "balance": {"title": "Balance Operations", "subtitle": "Add funds to your advertising account and view your transaction history", "addFunds": "Add Funds", "addFundsDescription": "Securely add funds to your advertising account", "amount": "Amount", "customAmount": "Custom Amount", "customAmountPlaceholder": "Enter amount (₺10 - ₺10,000)", "customAmountButton": "Custom Amount", "methods": {"title": "Payment Methods", "description": "Manage your payment methods", "savedCards": "Saved Cards", "savedCardsDescription": "View and manage your saved payment methods", "addNew": "Add New", "addNewCard": "Add New Card", "addNewBank": "Add New Bank Account", "cardNumber": "Card Number", "cardName": "Name on Card", "expiryDate": "Expiry Date", "cvv": "Security Code (CVV)", "bankName": "Bank Name", "iban": "IBAN Number", "default": "<PERSON><PERSON><PERSON>", "setAsDefault": "Set as <PERSON><PERSON><PERSON>", "delete": "Delete", "deleteConfirm": "Are you sure you want to delete this payment method?", "cardInfo": "Card Information", "bankInfo": "Bank Information", "fastest": "Fastest", "addPaymentMethod": "Add New Payment Method", "addPaymentMethodDescription": "Securely enter your card or bank details to use for adding funds.", "paymentType": {"title": "Payment Type", "creditCard": "Credit Card", "bankAccount": "Bank Account"}, "form": {"cardNumber": "Card Number", "cardNumberPlaceholder": "4111 1111 1111 1111", "cardName": "Name on Card", "cardNamePlaceholder": "Full Name", "expiryDate": "Expiry Date", "expiryDatePlaceholder": "MM/YY", "cvv": "CVV", "cvvPlaceholder": "123", "bankName": "Bank Name", "bankNamePlaceholder": "Bank Name", "iban": "IBAN Number", "ibanPlaceholder": "TR00 0000 0000 0000 0000 0000 00"}, "buttons": {"cancel": "Cancel", "add": "Add"}}, "transactions": {"title": "Transaction History", "description": "View and track your account movements", "filters": {"title": "Filters", "status": "Transaction Status", "type": "Transaction Type", "date": "Date", "allStatuses": "All Statuses", "allTypes": "All Types", "selectDate": "Select date"}, "types": {"deposit": "<PERSON><PERSON><PERSON><PERSON>", "payment": "Payment", "refund": "Refund"}, "table": {"date": "Date", "type": "Type", "amount": "Amount", "status": "Status", "description": "Description", "reference": "Reference No"}, "export": "Export", "exportSuccess": "Transaction history successfully exported", "noTransactions": "No transactions found", "showing": "Showing", "to": "to", "of": "of", "paginationSummary": "Showing {start}-{end} of {totalCount} transactions"}, "errors": {"amountRequired": "Please select an amount", "methodRequired": "Please select a payment method", "invalidAmount": "Invalid amount", "fetchError": "An error occurred while fetching transaction history", "addError": "An error occurred while adding funds"}, "success": {"addFunds": "Funds successfully added"}, "infoTitle": "Secure Payment", "infoDescription": "All your payments are processed through SSL certified secure connection.", "transactionDescription": "View and track your account movements", "transactionId": "Transaction ID", "date": "Date", "type": "Transaction Type", "method": "Payment Method", "paymentMethod": "Payment Method", "processing": "Processing...", "continuePayment": "Continue Payment", "totalAmount": "Total Amount", "loading": "Loading...", "noTransactionsFound": "No transactions found", "pageSize": {"label": "<PERSON>", "5records": "5 Records", "10records": "10 Records", "20records": "20 Records", "50records": "50 Records"}, "search": {"placeholder": "Search transactions..."}, "filters": {"button": "Filter", "title": "Filters", "clear": "Clear", "apply": "Apply"}, "status": {"completed": "Completed", "pending": "Pending", "failed": "Failed", "rejected": "Rejected"}, "metrics": {"spent": "Spent", "totalDeposited": "Total Deposited:"}}, "notifications": {"title": "Notifications", "subtitle": "Important updates and alerts about your account.", "markAllRead": "<PERSON> as <PERSON>", "noNotifications": "No notifications", "viewAll": "View All Notifications", "unreadCount": "You have {count} unread notifications.", "tabs": {"all": "All ({count})", "unread": "Unread ({count})"}, "toast": {"allRead": "All notifications marked as read", "allReadDesc": "{count} notifications marked as read", "oneRead": "Notification marked as read", "deleted": "Notification deleted"}, "filter": "Filter", "notificationType": "Notification Type", "all": "All", "campaigns": "Campaigns", "payments": "Payments", "reports": "Reports", "system": "System", "noFilteredNotifications": "No notifications found for the selected filter.", "showAllNotifications": "Show all notifications", "markAsRead": "<PERSON> <PERSON>", "deleteNotification": "Delete Notification"}, "settings": {"title": "Settings", "subtitle": "Manage your account settings and preferences", "tabs": {"profile": "Profile", "company": "Company", "billing": "Billing", "notifications": "Notifications", "security": "Security"}, "buttons": {"save": "Save Changes", "saving": "Saving...", "cancel": "Cancel"}, "profile": {"title": "Profile Information", "subtitle": "Update your personal information and account details", "photo": {"title": "Profile Photo", "upload": "Upload New Photo", "hint": "JPG, GIF or PNG. Max size of 1MB"}, "fields": {"fullName": "Full Name", "jobTitle": "Job Title", "email": "Email", "emailVerified": "Your email is verified. Contact support to change it.", "phone": "Phone", "about": "About", "aboutHint": "Write a short introduction about yourself. This will be displayed on your profile."}, "preferences": {"title": "Communication Preferences", "subtitle": "Choose how you want us to communicate with you", "marketing": {"title": "Marketing Emails", "description": "Receive updates about new features and promotions"}, "product": {"title": "Product Updates", "description": "Platform changes and new features"}, "sms": {"title": "SMS Notifications", "description": "Important notifications and security alerts"}}, "language": {"title": "Language & Region", "subtitle": "Set your language and local preferences", "language": "Language", "timezone": "Timezone", "dateFormat": "Date Format", "currency": "<PERSON><PERSON><PERSON><PERSON>"}}, "company": {"title": "Company Information", "subtitle": "Manage your company details and business information", "logo": {"title": "Company Logo", "upload": "Upload Logo", "hint": "PNG, SVG or JPEG. 512x512px recommended."}, "fields": {"name": "Company Name", "vatId": "VAT ID", "industry": "Industry", "size": "Company Size", "about": "About Company"}, "address": {"title": "Address Information", "subtitle": "Update your company's official address and location details", "line1": "Address Line 1", "line2": "Address Line 2", "city": "City", "state": "State/Province", "zip": "ZIP/Postal Code", "country": "Country"}, "verification": {"title": "Verification Required", "description": "Please upload your tax registration and business registration documents for account verification. This is required for security and verification purposes.", "upload": "Upload Documents"}}, "notifications": {"title": "Notification Preferences", "subtitle": "Choose which notifications you want to receive", "email": {"title": "Email Notifications", "campaign": {"title": "Campaign Updates", "description": "Get notified about campaign status and performance updates"}, "performance": {"title": "Performance Reports", "description": "Receive weekly and monthly performance reports"}, "billing": {"title": "Billing Updates", "description": "Get notified about invoices and payment updates"}}, "push": {"title": "Push Notifications", "campaign": {"title": "Campaign Alerts", "description": "Real-time updates about your campaigns"}, "performance": {"title": "Performance Alerts", "description": "Important metrics and threshold alerts"}, "billing": {"title": "Payment Alerts", "description": "Balance updates and payment confirmations"}}, "sms": {"title": "SMS Notifications", "campaign": {"title": "Campaign Status", "description": "Critical campaign status changes"}, "performance": {"title": "Performance Alerts", "description": "Important performance thresholds"}, "billing": {"title": "Payment Alerts", "description": "Critical payment and billing updates"}}}, "security": {"title": "Security Settings", "subtitle": "Manage your account security and authentication", "password": {"title": "Change Password", "current": "Current Password", "new": "New Password", "confirm": "Confirm New Password", "mismatch": "Passwords do not match"}, "twoFactor": {"title": "Two-Factor Authentication", "sms": {"title": "SMS Authentication", "description": "Use your phone number for two-factor authentication"}, "authenticator": {"title": "Authenticator App", "description": "Use an authenticator app for enhanced security"}}, "sessions": {"title": "Active Sessions", "current": {"title": "Current Session", "description": "This is your current active session", "terminate": "End Session"}, "all": {"title": "All Sessions", "description": "End all active sessions on other devices", "terminate": "End All Sessions"}}, "danger": {"title": "Danger Zone", "description": "Once you delete your account, there is no going back. Please be certain.", "delete": "Delete Account"}}}, "campaigns": {"title": "Campaigns", "subtitle": "View, edit and manage all your campaigns", "search": "Search campaigns...", "filter": "Filter:", "statusList": "Status", "status": {"all": "All Statuses", "active": "Active", "completed": "Completed", "planned": "Planned", "paused": "Paused"}, "metrics": {"budget": "Budget", "spent": "Spent", "remaining": "Remaining", "reach": "Reach", "engagement": "Engagement", "impressions": "Impressions", "clicks": "<PERSON>licks", "ctr": "CTR (Click Through Rate)"}, "name": "Campaign Name", "startDate": "Start Date", "endDate": "End Date", "actions": "Actions", "showing": "Showing", "to": "to", "of": "of", "campaigns": "campaigns", "previous": "Previous", "next": "Next", "campaignOperations": "Campaign Operations", "analytics": "Analytics", "edit": "Edit", "pause": "Pause", "activate": "Activate", "delete": "Delete", "deleting": "Deleting...", "myCampaigns": "My Campaigns", "campaignListDescription": "View and manage your advertising campaigns", "clearSearch": "Clear Search", "noCampaignsFound": "No Campaigns Found", "noCampaignsFoundDescription": "No campaigns match your search criteria.", "createNewCampaign": "Create New Campaign", "confirmDelete": "Are you sure you want to delete this campaign? This action cannot be undone.", "deleteSuccess": "Campaign successfully deleted.", "deleteError": "An error occurred while deleting the campaign.", "sessionError": "Your session has expired. Please log in again.", "notificationSettings": {"quietHours": "Notification Time Intervals", "quietHoursDescription": "Set the time intervals you want to receive notifications", "startTime": "Start Time", "endTime": "End Time", "workdayNotifications": "Workday Notifications", "workdayNotificationsDescription": "Receive notifications only on workdays"}}, "billing": {"paymentMethods": "Payment Methods", "paymentMethodsDescription": "Manage credit cards and bank accounts used for your advertising payments", "savedCards": "Saved Cards", "addNewCard": "Add New Card", "cardHolder": "Card Holder", "cardHolderPlaceholder": "Name on card", "cardNumber": "Card Number", "expiryDate": "Expiry Date", "securityCode": "Security Code (CVC)", "saveAsDefault": "Save this card as default payment method", "saveCard": "Save Card", "expiry": "Expiry", "default": "<PERSON><PERSON><PERSON>", "bankAccounts": "Bank Accounts", "addAccount": "Add Account", "billingInfo": "Billing Information", "billingInfoDescription": "Manage your billing and payment preferences", "billingEmail": "Billing Email Address", "billingEmailPlaceholder": "Your email address", "billingEmailDescription": "Invoices and payment notifications will be sent to this address.", "billingCycle": "Billing Cycle", "billingCyclePlaceholder": "Select billing cycle", "monthly": "Monthly", "quarterly": "Quarterly", "yearly": "Yearly", "taxId": "Tax ID / National ID", "taxIdPlaceholder": "Tax ID or National ID", "autoPaymentThreshold": "Automatic Payment Threshold", "autoPaymentThresholdPlaceholder": "Automatic payment threshold", "autoPaymentThresholdDescription": "Payment will be automatically charged when the invoice amount exceeds this threshold.", "recentInvoices": "Recent Invoices", "invoiceNo": "Invoice No", "date": "Date", "amount": "Amount", "status": "Status", "action": "Action", "paid": "Paid", "viewAllInvoices": "View All Invoices"}, "statistics": {"title": "Performance Analysis", "subtitle": "Analyze your campaign performance and user behavior", "filters": {"allCampaigns": "All Campaigns", "summerCampaign": "Summer Campaign", "winterDiscounts": "Winter Discounts", "specialOffers": "Special Offers", "holidayCampaign": "Holiday Campaign"}, "dateRanges": {"last7Days": "Last 7 Days", "last30Days": "Last 30 Days", "last90Days": "Last 90 Days", "thisYear": "This Year", "customDate": "Custom Date"}, "metrics": {"views": "Views", "clicks": "<PERSON>licks", "ctr": "CTR", "conversions": "Conversions"}, "charts": {"performance": "Performance Chart", "device": "Device Distribution", "demographic": "Demographic Distribution", "location": "Location Distribution"}, "communityStats": "Communities Where Ad is Shared", "communityStatsDescription": "Performance metrics of communities where your ad is shared", "communityName": "Community Name", "category": "Category", "views": "Views", "clicks": "<PERSON>licks", "conversions": "Conversions", "earnings": "Earnings", "showing": "Showing", "of": "of", "searchCommunity": "Search by community name...", "spend": "Spend", "bgm": "CPM", "tbm": "CPC", "dbm": "CPA", "to": "CR", "viewCommunity": "View Community", "details": "Details", "demographicDistribution": "Demographic Distribution", "userDistributionByAgeAndGender": "User distribution by age and gender", "ageGroup": "age group", "male": "Male", "female": "Female", "totalMale": "Total Male", "totalFemale": "Total Female", "deviceDistribution": "Device Distribution", "userDevicePreferences": "User device preferences", "desktop": "Desktop", "mobile": "Mobile", "tablet": "Tablet", "locationDistribution": "Location Distribution", "userDistributionByCity": "User distribution by city", "location": "Location", "users": "Users", "percentage": "Percentage", "distribution": "Distribution", "total": "Total", "istanbul": "Istanbul", "ankara": "Ankara", "izmir": "Izmir", "bursa": "Bursa", "other": "Other", "antalya": "<PERSON><PERSON><PERSON>", "performanceChart": "Performance Chart", "campaignMetricsComparison": "Campaign metrics comparison"}, "navigation": {"menu": {"dashboard": "Dashboard", "campaigns": "Campaigns", "analytics": "Analytics", "settings": "Settings"}, "notifications": {"title": "Notifications", "markAllRead": "<PERSON> as <PERSON>", "empty": "No notifications", "viewAll": "View All Notifications"}, "balance": {"title": "Balance", "current": "Current Balance", "reserved": "Reserved Amount", "addFunds": "Add Funds", "lastTransaction": {"title": "Last Transaction", "daysAgo": "{days} days ago"}}, "profile": {"role": "Advertiser", "settings": "Profile Settings", "helpCenter": "Help Center", "logout": "Logout", "professional": "Professional", "id": "ID: {id}"}}}