{"dashboard": {"title": "Home", "welcome": "Hello", "subtitle": "Your current statistics and performance", "stats": {"totalEarnings": "Total Earnings", "monthlyEarnings": "Monthly Earnings", "activeAds": "Active Ads", "totalCommunities": "Total Communities", "totalViews": "Total Views", "totalClicks": "Total Clicks", "averageCTR": "Average CTR", "last30Days": "Last 30 days", "thisMonth": "This month", "clickRate": "Click rate", "comparedToPrevMonth": "Previous month", "trend": {"up": "↑ %{{value}}", "down": "↓ %{{value}}", "noChange": "No change"}}, "sections": {"ads": {"title": "Ads", "description": "Ads will appear here when they are available for your communities", "recentAds": "Recent Ads", "noAds": "No Active Ads Yet", "viewAll": "View All Ads"}, "earnings": {"title": "Earnings", "chart": {"title": "Earnings Chart", "daily": "Daily", "weekly": "Weekly", "monthly": "Monthly"}}, "communities": {"title": "My Communities", "connect": "Connect Community", "management": "Community Management", "overview": {"title": "Community Overview", "active": "Active Communities", "pending": "Pending Approval", "total": "Total Communities"}}, "settings": "Settings"}, "charts": {"earnings": "Earnings Chart", "performance": "Performance Chart", "topPerformers": "Top Performers"}, "emptyStates": {"topPerformers": {"title": "No Top Performers Yet", "subtitle": "Connect your communities and start earning to see your top performers here."}}, "cta": {"connectChannel": {"title": "Connect Your Channel", "description": "It only takes 5 minutes to connect your accounts and increase your earnings.", "button": "Connect Account"}}}, "ads": {"title": "My Ads", "subtitle": "Manage ads displayed in your communities", "status": {"active": "Active", "completed": "Completed", "pending": "Pending", "stopped": "Stopped"}, "metrics": {"views": "Views", "clicks": "<PERSON>licks", "ctr": "CTR", "earnings": "Earnings", "allAds": "All ads", "clickRate": "Click rate"}, "filters": {"all": "All", "status": "Status", "date": "Date", "community": "Community", "group": "Group", "searchPlaceholder": "Search ads...", "allGroups": "All Groups", "sortOptions": {"date": "By Date", "views": "By Views", "clicks": "By Clicks", "ctr": "By CTR", "earnings": "By Earnings"}}, "table": {"name": "Ad Name", "status": "Status", "views": "Views", "clicks": "<PERSON>licks", "ctr": "CTR", "earnings": "Earnings", "startDate": "Start Date", "endDate": "End Date"}, "empty": {"title": "No Ads Yet", "description": "There are no active ads in your communities yet."}, "details": {"startDate": "Start Date", "endDate": "End Date", "group": "Group", "description": "Description"}, "image": "Ad Image", "viewDetails": "View Details"}, "communities": {"title": "My Communities", "subtitle": "Manage all communities and track their performance", "connect": {"title": "Add Community", "titleShort": "Add Community", "noCommunities": "There is no community yet", "addNew": "Add New Community", "addNewDescription": "Add a new community to show more ads and increase your earnings", "success": "Your community has been added successfully!", "error": "An error occurred. Please try again.", "adding": "Adding...", "add": "Add", "stepCount": "Step {{current}} / {{total}}", "validation": {"required": "Please fill in all required fields"}, "steps": {"platform": {"title": "Platform Selection", "stepTitle": "Community Type", "stepDescription": "Select your community's platform. We offer special features and integrations for each platform.", "searchPlaceholder": "Search platform..."}, "region": {"stepTitle": "Region", "stepDescription": "Select the region where your community's target audience is located. This helps us match the right ads.", "searchPlaceholder": "Search region..."}, "language": {"stepTitle": "Language", "stepDescription": "Select the main language used in your community. You can select multiple languages.", "searchPlaceholder": "Search language..."}, "category": {"stepTitle": "Category", "stepDescription": "Select your community's content category. This helps find relevant ads.", "searchPlaceholder": "Search category..."}, "info": {"stepTitle": "Community Information", "stepDescription": "Enter your community's username and other required information.", "placeholder": "Enter your community's username", "copied": "Copied!", "hint": "Enter only the username. Example:", "error": "Username should not contain spaces or '/' character."}, "bot": {"stepTitle": "Telegram Bot", "stepDescription": "Add the Adnomio bot to your group and make it an admin to verify your community.", "description": "To verify your community", "botName": "Adnomio Telegram bot", "telegramButton": "Go to Telegram", "addedButton": "I've added the bot", "successMessage": "<PERSON><PERSON> added successfully!", "infoBox": "Add the Adnomio Telegram bot to your group as an admin. The bot does not access your messages, it only manages ads.", "step1": "Open Telegram", "step2": "Add the bot", "step3": "Make admin"}}}, "filters": {"search": "Search communities...", "status": {"all": "All", "active": "Active", "pending": "Pending", "suspended": "Suspended"}, "viewType": {"grid": "Grid View", "list": "List View"}}, "list": {"columns": {"community": "Community", "platform": "Platform", "members": "members", "views": "views", "ctr": "CTR", "earnings": "Earnings", "status": "Status", "actions": "Actions"}, "deleteConfirmation": {"title": "Delete Community", "description": "Are you sure you want to delete this community? This action cannot be undone.", "confirm": "Delete", "cancel": "Cancel"}}, "card": {"addedOn": "Added on", "openPage": "Open Page", "details": "Details", "metrics": {"members": "Members", "views": "Views", "ctr": "CTR", "earnings": "Earnings"}}, "details": {"modal": {"title": "Community Details", "close": "Close", "metrics": {"title": "Detailed Metrics", "engagementRate": "Average Engagement Rate", "activeUsers": "Active Users Rate", "dailyViews": "Daily Average Views", "monthlyGrowth": "Monthly Growth Rate", "adScore": "Ad Performance Score"}, "info": {"title": "Community Details", "country": "Country", "language": "Language", "category": "Category", "targetAudience": "Target Audience", "url": "URL", "visitCommunity": "Visit Community"}, "delete": {"title": "Danger Zone", "description": "This action cannot be undone and all your community data will be deleted.", "button": "Remove Community", "confirm": {"title": "Remove Community", "description": "Are you sure you want to remove this community?", "warning": "This action cannot be undone and all your community data will be deleted.", "cancel": "Cancel", "confirm": "Yes, Remove Community"}}}}, "info": {"title": "Earn more from your communities", "description": "Increase your earnings by adding more communities and ensuring ads are relevant to your target audience.", "button": "Learn More"}, "platform": {"telegram": "Telegram", "whatsapp": "WhatsApp", "discord": "Discord", "instagram": "Instagram", "x": "X (Twitter)"}, "status": {"active": "Active", "pending": "Pending Approval", "suspended": "Suspended"}, "onboarding": {"steps": {"communityType": "Community Type", "region": "Region", "language": "Language", "category": "Category", "link": "Community Link", "bot": "Telegram Bot"}, "whichCommunity": "Which community do you have?", "whichRegion": "Which region is your community in?", "whichLanguages": "What language(s) does your community cater to?", "whichCategory": "What is your community's category?", "addCommunityLink": "Add your community link", "addBot": "Add Telegram Bot to Community", "searchRegion": "Search region", "searchLanguage": "Search language", "enterUsername": "Enter your community username", "usernameExample": "Enter only the username. Example:", "usernameError": "Username cannot contain spaces or '/' character.", "botDescription": "Add the Adnomio Telegram bot to your group and make it an admin to verify your community.", "goToTelegram": "Go to Telegram", "botAdded": "I've added the bot", "botAddedSuccess": "<PERSON><PERSON> successfully added!", "communityCreated": "Community successfully created!", "exitModal": {"title": "Go to home page", "description": "You can add your community later."}}, "common": {"loading": "Loading...", "success": "Success", "error": "Error", "back": "Back", "continue": "Continue", "finish": "Finish", "edit": "Edit", "delete": "Delete", "save": "Save", "saving": "Saving...", "cancel": "Cancel", "yes": "Yes, Go", "noResults": "No results found", "copied": "Copied!", "search": "Search"}, "empty": {"title": "No Communities Yet", "description": "Add your communities to start earning by displaying ads."}}, "notifications": {"title": "Notifications", "subtitle": "Manage and view your notifications", "empty": {"title": "No Notifications Yet", "description": "You don't have any new notifications."}, "markAllAsRead": "Mark all as read", "deleteAll": "Delete all", "notFiltre": "Filter", "notificationItem": {"read": "Read"}, "noNotifications": "No notifications yet.", "noNotificationsDesc": "Check back later for updates on your account activities.", "markAllRead": "<PERSON> as <PERSON>", "viewAll": "View All Notifications", "messages": {"newAdMatch": "New ad match in your {{community}} channel", "paymentReceived": "{{amount}} has been transferred to your account", "weeklyReport": "Your weekly performance report is ready"}, "time": {"today": "Today, {{time}}", "yesterday": "Yesterday", "date": "{{date}}", "days": {"monday": "Monday", "tuesday": "Tuesday", "wednesday": "Wednesday", "thursday": "Thursday", "friday": "Friday", "saturday": "Saturday", "sunday": "Sunday"}}, "filters": {"searchPlaceholder": "Search notifications...", "typePlaceholder": "All Types", "statusPlaceholder": "All Statuses", "sortByPlaceholder": "Sort By", "all": "All", "unread": "Unread", "type": {"all": "All Types", "system": "System", "payment": "Payment", "community": "Community", "ads": "Ads"}, "status": {"all": "All Statuses", "read": "Read", "unread": "Unread"}, "sortBy": {"newest": "Newest", "oldest": "Oldest"}, "buttonText": "Filter"}, "actions": {"markAsRead": "<PERSON> <PERSON>", "details": "Details", "delete": "Delete", "clearAll": "Clear All"}, "confirmDelete": {"title": "Delete All Notifications", "description": "Are you sure you want to delete all notifications? This action cannot be undone.", "confirm": "Yes, Delete All", "cancel": "Cancel"}, "types": {"system": "System", "payment": "Payment", "community": "Community", "ads": "Ads", "general": "General"}, "sort": {"newest": "Newest", "oldest": "Oldest"}, "unreadCount": "Unread"}, "payments": {"title": "Payments", "subtitle": "Track your earnings and make withdrawals", "balance": {"current": "Current Balance", "available": "Available", "monthly": "Monthly Earnings", "total": "Total Earnings", "pending": "Pending Payments"}, "status": {"completed": "Completed", "pending": "Processing", "failed": "Failed"}, "types": {"earning": "Earning", "withdrawal": "<PERSON><PERSON><PERSON>"}, "withdraw": {"availableBalance": "Available Balance", "minimumAmount": "Minimum Amount", "monthlyLimit": "Monthly Limit", "remainingMonthlyLimit": "Remaining Monthly Limit", "paymentInfo": "Payment Info", "fee": "Transaction Fee", "processingTime": "Processing Time", "processingDays": "1-3 business days", "processingInfo": "Withdrawals are usually completed within 1-3 business days.", "form": {"title": "Withdrawal Form", "description": "Fill in the fields below to withdraw from your balance.", "amount": "Amount", "withdrawAll": "Withdraw All", "minimumAmount": "Minimum withdrawal amount", "minimumRequired": "Minimum withdrawal amount ₺{minimum}. To withdraw, you need to earn ₺{remaining} more.", "paymentMethod": "Payment Method", "startWithdrawal": "Start Withdrawal"}, "errors": {"enterAmount": "Please enter an amount.", "minimumAmount": "The entered amount cannot be less than the minimum withdrawal.", "insufficientBalance": "Insufficient balance.", "monthlyLimitExceeded": "You have exceeded your monthly withdrawal limit."}, "verification": {"title": "Verification", "description": "Enter the verification code sent to your phone.", "transaction": "Transaction", "amount": "Amount", "method": "Method", "smsInfo": "A verification code was sent to {{phone}} via SMS.", "code": "Verification Code", "codePlaceholder": "6-digit code", "noCode": "Didn't receive the code?", "resend": "Resend", "timeRemaining": "Time remaining", "confirm": "Confirm"}, "warning": {"title": "Warning", "description": "Withdrawals are only made to registered and verified accounts."}, "success": {"title": "Request Received", "description": "Your withdrawal request for {{amount}} has been received successfully.", "transactionId": "Transaction ID", "amount": "Amount", "status": "Status", "processing": "Processing", "estimatedCompletion": "Estimated Completion", "businessDays": "1-3 business days", "method": "Method", "goToPayments": "Go to Payments", "goToHome": "Go to Home"}, "viewHistory": "View withdrawal history", "status": {"title": "Withdrawal Status", "available": "Available for With<PERSON>wal", "availableDesc": "Your balance is sufficient for withdrawal. You can initiate a withdrawal request.", "notAvailable": "Not Yet Available for With<PERSON><PERSON>", "notAvailableDesc": "Your balance is below the minimum withdrawal amount. Earn more to enable withdrawal.", "progressText": "{{currentAmount}} / {{minimumAmount}}", "withdrawNow": "Withdraw Now"}}, "history": {"title": "Transaction History", "subtitle": "All your earnings and withdrawal transactions", "filters": {"search": "Search...", "type": {"all": "All Transactions", "earnings": "Earnings", "withdrawals": "<PERSON><PERSON><PERSON><PERSON>"}, "status": {"all": "All Statuses", "completed": "Completed", "pending": "Processing", "failed": "Failed"}, "date": {"all": "All Time", "week": "Last 7 Days", "month": "Last 30 Days", "3months": "Last 3 Months"}}, "table": {"transaction": "Transaction", "description": "Description", "date": "Date", "amount": "Amount", "status": "Status", "noResults": "No transactions found. Try changing the filters.", "totalCount": "Total: {{count}} transactions"}, "actions": {"filter": "Filter", "export": "Export"}}, "methods": {"title": "Payment Methods", "addNew": "Add New Payment Method", "subtitle": "Add a payment method to receive your earnings", "primary": "Primary", "edit": "Edit", "delete": "Delete", "bankNames": {"title": "Select Bank", "select": "Select bank", "ziraat": "Ziraat Bank", "garanti": "Garanti BBVA", "isbank": "Isbank", "akbank": "Akbank", "yapikredi": "Yapı Kredi", "vakifbank": "VakıfBank", "halkbank": "HalkBank", "qnb": "QNB Finansbank", "denizbank": "DenizBank", "other": "Other"}, "walletTypes": {"title": "Digital Wallet Type", "select": "Select wallet type", "papara": "<PERSON><PERSON>", "ininal": "Ininal", "paycell": "Paycell"}, "form": {"accountHolder": {"label": "Account Holder", "placeholder": "Full name of the account holder", "hint": "The account holder name must match your bank records"}, "iban": {"label": "IBAN", "placeholder": "TR__ ____ ____ ____ ____ ____ __", "hint": "Enter your IBAN without spaces"}, "cardHolder": {"label": "Card Holder", "placeholder": "Name on the card"}, "cardNumber": {"label": "Card Number", "placeholder": "Card number"}, "accountType": {"label": "Account Type", "individual": "Individual", "business": "Business"}, "defaultPayment": "Set this as default payment method", "buttons": {"cancel": "Cancel", "add": "Add", "adding": "Adding..."}}, "alerts": {"bank": {"description": "Your bank account information is stored securely and only used for payment transactions."}, "card": {"description": "Your digital wallet information is stored securely and only used for payment transactions."}}, "comingSoon": {"paypal": {"title": "PayPal Coming Soon", "description": "Our PayPal integration will be available soon. We will notify you when it's ready."}, "crypto": {"title": "Crypto Payments Coming Soon", "description": "Crypto payments will be available soon. We will notify you when it's ready."}, "notify": "Notify Me When Ready"}, "success": {"title": "Payment Method Added", "description": {"bank": "Your {{bankName}} bank account has been added successfully", "card": "Your {{cardName}} digital wallet has been added successfully"}, "buttons": {"methods": "Go to Payment Methods", "payments": "Go to Payments"}}, "bankAccount": "Bank Account", "digitalWallet": "Digital Wallet", "info": {"title": "About Payment Methods", "description": "You can add multiple payment methods and manage them easily. Your information is stored securely.", "features": {"multiple": "Add and use multiple payment methods.", "edit": "Edit or delete your payment methods anytime.", "secure": "Your information is protected with advanced security measures."}}, "registeredMethods": "Registered Payment Methods", "description": "You can add one or more payment methods to receive your earnings. All your information is stored securely.", "faq": {"title": "Frequently Asked Questions", "description": "Find answers to the most common questions about payment methods here."}, "tips": {"title": "Tips", "description": "Check out the tips below for faster and more secure payments.", "bankAccountTip": "Make sure your bank account information is up-to-date and accurate.", "matchingDetailsTip": "Ensure your account holder name matches your bank records."}, "help": {"title": "Help & Support", "description": "If you need help with payment methods, you can visit our support center.", "supportCenter": "Support Center"}, "security": {"title": "Security", "description": "Your payment information and transactions are protected with the highest level of security.", "securePayment": "Secure Payment", "securePaymentDesc": "All your payments are processed securely and encrypted.", "verifiedTransactions": "Verified Transactions", "verifiedTransactionsDesc": "All payment transactions are verified and monitored by our system."}, "addedOn": "Added On", "setAsPrimary": "Set as Primary"}, "info": {"title": "Payment Information", "subtitle": "Your registered payment details", "idNumber": "ID Number", "address": "Address"}}, "theme": {"light": "Light Theme", "dark": "Dark Theme", "system": "System Theme"}, "language": {"changeSuccess": {"title": "Language Changed", "description": "Language set to English"}, "changeError": {"title": "Error", "description": "An error occurred while changing the language"}}, "profile": {"settings": "Profile Settings", "logout": "Logout"}}