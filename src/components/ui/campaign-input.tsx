import React from "react";

interface CampaignInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  error?: string;
  icon?: React.ReactNode;
}

export const CampaignInput = React.forwardRef<HTMLInputElement, CampaignInputProps>(
  ({ label, error, icon, className = "", ...props }, ref) => {
    return (
      <div className="space-y-1.5 w-full">
        {label && (
          <label
            htmlFor={props.id}
            className={`block text-lg font-medium mb-1  transition-colors duration-200 ${
              error ? "text-red-600" : "text-slate-700"
            }`}
          >
            {label}
          </label>
        )}
        <div className="relative">
          {icon && (
            <span className="absolute left-3 top-1/2 -translate-y-1/2 text-blue-400 pointer-events-none">
              {icon}
            </span>
          )}
          <input
            ref={ref}
            className={`
                w-full
                pl-${icon ? "10" : "4"} pr-4 py-4
                bg-slate-50
                border-2 border-slate-200
                rounded-xl
                text-base
                focus:border-blue-500
                focus:ring-2 focus:ring-blue-100
                focus:bg-white
                transition-all duration-200
                placeholder:text-slate-400
                shadow-sm
                hover:border-blue-300
                disabled:bg-slate-100 disabled:cursor-not-allowed
              ${error ? "border-red-400 focus:border-red-500 focus:ring-red-100" : ""}
              ${className}
            `}
            {...props}
          />
        </div>
        {error && <p className="text-xs text-red-500 mt-1">{error}</p>}
      </div>
    );
  }
);

CampaignInput.displayName = "CampaignInput"; 