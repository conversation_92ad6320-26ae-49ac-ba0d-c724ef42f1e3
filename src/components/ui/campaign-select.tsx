import * as React from "react";
import * as SelectPrimitive from "@radix-ui/react-select";
import { ChevronDown, Check } from "lucide-react";
import { cn } from "@/lib/utils";

interface CampaignSelectProps {
  label?: string;
  error?: string;
  icon?: React.ReactNode;
  children: React.ReactNode;
  value: string;
  onValueChange: (value: string) => void;
  placeholder?: string;
  id?: string;
  className?: string;
  disabled?: boolean;
}

export const CampaignSelect = React.forwardRef<HTMLButtonElement, CampaignSelectProps>(
  (
    {
      label,
      error,
      icon,
      children,
      value,
      onValueChange,
      placeholder,
      id,
      className = "",
      disabled = false,
      ...props
    },
    ref
  ) => {
    const triggerRef = React.useRef<HTMLButtonElement>(null);
    const [triggerWidth, setTriggerWidth] = React.useState<number>();

    React.useEffect(() => {
      if (triggerRef.current) {
        setTriggerWidth(triggerRef.current.offsetWidth);
      }
    }, []);

    return (
      <div className="space-y-1.5 w-full">
        {label && (
          <label
            htmlFor={id}
            className={`block text-sm font-medium mb-1 transition-colors duration-200 ${
              error ? "text-red-600" : "text-slate-700"
            }`}
          >
            {label}
          </label>
        )}
        <SelectPrimitive.Root value={value} onValueChange={onValueChange} disabled={disabled}>
          <SelectPrimitive.Trigger
            ref={triggerRef}
            id={id}
            type="button"
            className={cn(
              `relative w-full text-left bg-slate-50 border-2 border-slate-200 rounded-xl text-base pr-10 py-4
                focus:border-blue-500 focus:ring-2 focus:ring-blue-100 focus:bg-white transition-all duration-200
                placeholder:text-slate-400 shadow-sm hover:border-blue-300 disabled:bg-slate-100 disabled:cursor-not-allowed
                ${icon ? "pl-10" : "pl-4"} ${error ? "border-red-400 focus:border-red-500 focus:ring-red-100" : ""} ${className}`
            )}
            {...props}
          >
            {icon && (
              <span className="absolute left-3 top-1/2 -translate-y-1/2 text-blue-400 pointer-events-none">
                {icon}
              </span>
            )}
            <SelectPrimitive.Value placeholder={placeholder} />
            <SelectPrimitive.Icon asChild>
              <ChevronDown className="h-5 w-5 text-slate-400 absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none" />
            </SelectPrimitive.Icon>
          </SelectPrimitive.Trigger>
          <SelectPrimitive.Portal>
            <SelectPrimitive.Content
              className="z-50 max-h-96 overflow-auto rounded-xl border bg-white text-gray-950 shadow-md mt-2"
              position="popper"
              sideOffset={4}
              align="start"
              style={triggerWidth ? { width: triggerWidth } : undefined}
            >
              <SelectPrimitive.Viewport className="p-1">
                {children}
              </SelectPrimitive.Viewport>
            </SelectPrimitive.Content>
          </SelectPrimitive.Portal>
        </SelectPrimitive.Root>
        {error && <p className="text-xs text-red-500 mt-1">{error}</p>}
      </div>
    );
  }
);

CampaignSelect.displayName = "CampaignSelect";

export const CampaignSelectItem = React.forwardRef<
  React.ElementRef<typeof SelectPrimitive.Item>,
  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>
>(({ className, children, ...props }, ref) => (
  <SelectPrimitive.Item
    ref={ref}
    className={cn(
      "relative flex w-full cursor-default select-none items-center rounded-md py-2 pl-10 pr-4 text-base outline-none focus:bg-gray-100 focus:text-gray-900 data-[disabled]:pointer-events-none data-[disabled]:opacity-50",
      className
    )}
    {...props}
  >
    <span className="absolute left-3 flex h-4 w-4 items-center justify-center">
      <SelectPrimitive.ItemIndicator>
        <Check className="h-4 w-4 text-blue-500" />
      </SelectPrimitive.ItemIndicator>
    </span>
    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>
  </SelectPrimitive.Item>
));
CampaignSelectItem.displayName = "CampaignSelectItem"; 