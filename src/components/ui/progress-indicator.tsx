"use client"

import { motion } from "framer-motion"

interface ProgressIndicatorProps {
  steps: string[]
  currentStep: number
  className?: string
}

export function ProgressIndicator({ steps, currentStep, className = "" }: ProgressIndicatorProps) {
  return (
    <div className={`flex items-center justify-between ${className}`}>
      {steps.map((step, index) => (
        <div key={index} className="flex items-center">
          {/* Step Circle */}
          <motion.div
            initial={{ scale: 0.8, opacity: 0.5 }}
            animate={{ 
              scale: index <= currentStep ? 1 : 0.8,
              opacity: index <= currentStep ? 1 : 0.5
            }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
            className={`relative flex items-center justify-center w-8 h-8 rounded-full border-2 transition-all duration-300 ${
              index < currentStep
                ? 'bg-green-500 border-green-500 text-white'
                : index === currentStep
                ? 'bg-blue-500 border-blue-500 text-white'
                : 'bg-gray-100 border-gray-300 text-gray-500'
            }`}
          >
            {index < currentStep ? (
              <motion.svg
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ duration: 0.2, delay: 0.1 }}
                className="w-4 h-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </motion.svg>
            ) : (
              <span className="text-xs font-medium">{index + 1}</span>
            )}
            
            {/* Pulse effect for current step */}
            {index === currentStep && (
              <motion.div
                animate={{
                  scale: [1, 1.5, 1],
                  opacity: [0.5, 0, 0.5]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
                className="absolute inset-0 rounded-full bg-blue-500"
              />
            )}
          </motion.div>
          
          {/* Step Label */}
          <motion.span
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 + 0.2 }}
            className={`ml-2 text-xs font-medium transition-colors duration-300 ${
              index <= currentStep ? 'text-gray-900' : 'text-gray-500'
            }`}
          >
            {step}
          </motion.span>
          
          {/* Connector Line */}
          {index < steps.length - 1 && (
            <motion.div
              initial={{ scaleX: 0 }}
              animate={{ scaleX: index < currentStep ? 1 : 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 + 0.3 }}
              className="flex-1 h-0.5 mx-4 bg-green-500 origin-left"
            />
          )}
        </div>
      ))}
    </div>
  )
}
