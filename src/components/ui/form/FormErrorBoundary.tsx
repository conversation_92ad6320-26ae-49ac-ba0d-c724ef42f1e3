import React, { Component, ErrorInfo, ReactNode } from 'react';
import { toast } from "@/components/ui/use-toast";

interface Props {
  children: ReactNode;
  formName: string;
}

interface State {
  hasError: boolean;
}

export class FormErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false
  };

  public static getDerivedStateFromError(_: Error): State {
    return { hasError: true };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Form Error:', error, errorInfo);
    
    // Hata analitiklerini gönder
    // TODO: Implement error analytics service
    
    toast({
      title: "Bir hata oluştu",
      description: "Form işlemi sırasında beklenmeyen bir hata oluştu. Lütfen daha sonra tekrar deneyin.",
      variant: "destructive"
    });
  }

  public render() {
    if (this.state.hasError) {
      return (
        <div className="p-4 rounded-lg bg-red-50 border border-red-100">
          <h3 className="text-lg font-medium text-red-800 mb-2">
            Üzgünüz, bir hata oluştu
          </h3>
          <p className="text-red-700">
            {this.props.formName} formunda bir sorun oluştu. Lütfen sayfayı yenileyip tekrar deneyin.
          </p>
          <button
            onClick={() => window.location.reload()}
            className="mt-4 px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-lg hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
          >
            Sayfayı Yenile
          </button>
        </div>
      );
    }

    return this.props.children;
  }
} 