import React from 'react';
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

interface FormFieldProps {
  id: string;
  name: string;
  type: string;
  label: string;
  value: string;
  error?: string;
  icon?: React.ReactNode;
  placeholder?: string;
  autoComplete?: string;
  required?: boolean;
  focused?: boolean;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onFocus?: () => void;
  onBlur?: () => void;
}

export function FormField({
  id,
  name,
  type,
  label,
  value,
  error,
  icon,
  placeholder,
  autoComplete,
  required = false,
  focused = false,
  onChange,
  onFocus,
  onBlur,
}: FormFieldProps) {
  return (
    <div className="space-y-1.5">
      <Label 
        htmlFor={id} 
        className={`text-sm font-medium transition-colors duration-200 ${
          focused ? 'text-blue-600' : 'text-slate-700'
        }`}
      >
        {label}
      </Label>
      <div className="relative">
        {icon && (
          <div className={`absolute inset-y-0 left-0 flex items-center pl-3.5 pointer-events-none transition-colors duration-200 z-10 ${
            focused ? 'text-blue-600' : 'text-slate-600'
          }`}>
            {icon}
          </div>
        )}
        <Input
          id={id}
          name={name}
          type={type}
          value={value}
          onChange={onChange}
          onFocus={onFocus}
          onBlur={onBlur}
          placeholder={placeholder}
          className={`${icon ? 'pl-10' : ''} py-5 text-base bg-slate-50 border transition-all duration-200 ${
            focused 
              ? 'border-blue-400 ring-2 ring-blue-100 bg-white shadow-sm' 
              : error 
                ? 'border-red-300' 
                : 'border-slate-200'
          } rounded-xl focus-visible:ring-offset-0 focus-visible:ring-transparent`}
          required={required}
          autoComplete={autoComplete}
        />
      </div>
      {error && (
        <p className="text-xs text-red-500 mt-1">{error}</p>
      )}
    </div>
  );
} 