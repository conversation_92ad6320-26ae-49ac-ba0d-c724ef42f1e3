"use client"

import * as React from "react"
import { <PERSON><PERSON><PERSON>, Too<PERSON>ip<PERSON>ontent, TooltipProvider, TooltipTrigger } from "./tooltip"

export interface ChartConfig {
  [key: string]: {
    label: string
    color?: string
  }
}

interface ChartContainerProps extends React.HTMLAttributes<HTMLDivElement> {
  config: ChartConfig
}

export function ChartContainer({
  config,
  children,
  className,
  ...props
}: ChartContainerProps) {
  return (
    <div
      style={
        {
          "--color-desktop": "hsl(var(--chart-1))",
          "--color-mobile": "hsl(var(--chart-2))",
        } as React.CSSProperties
      }
      className={className}
      {...props}
    >
      {children}
    </div>
  )
}

interface ChartTooltipProps {
  content: React.ReactNode
  trigger?: React.ReactNode
}

export function ChartTooltip({ content, trigger }: ChartTooltipProps) {
  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>{trigger}</TooltipTrigger>
        <TooltipContent>{content}</TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}

export interface TooltipData {
  active: boolean
  payload?: Array<{
    value: number
    name: string
    dataKey: string
  }>
  label?: string
}

interface ChartTooltipContentProps {
  nameKey?: string
  valueFormatter?: (value: number) => string
  labelFormatter?: (value: string) => string
  className?: string
  children?: (data: TooltipData) => React.ReactNode
}

export function ChartTooltipContent({
  nameKey = "name",
  valueFormatter = (value: number) => value.toString(),
  labelFormatter = (value: string) => value,
  className,
  children,
  ...props
}: ChartTooltipContentProps) {
  const emptyData: TooltipData = { active: false }
  return (
    <div className={className}>
      {children?.(emptyData)}
    </div>
  )
} 