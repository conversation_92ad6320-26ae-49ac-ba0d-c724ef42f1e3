"use client";
import { useEffect, useState } from "react";

export default function ClientDate({ dateString }: { dateString?: string }) {
  const [formatted, setFormatted] = useState("Bilgi yok");
  useEffect(() => {
    if (dateString) {
      setFormatted(
        new Date(dateString).toLocaleDateString("tr-TR", {
          day: "numeric",
          month: "long",
          year: "numeric",
        })
      );
    }
  }, [dateString]);
  return <span>{formatted}</span>;
} 