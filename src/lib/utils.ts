import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatCurrency(value: number): string {
  return new Intl.NumberFormat('tr-TR', {
    style: 'currency',
    currency: 'TRY',
    minimumFractionDigits: 0
  }).format(value)
}

export function formatDate(timestamp: number) {
  const date = new Date(timestamp)
  return `${date.toLocaleDateString('tr-TR')} ${date.toLocaleTimeString('tr-TR', {
    hour: '2-digit',
    minute: '2-digit'
  })}`
}
