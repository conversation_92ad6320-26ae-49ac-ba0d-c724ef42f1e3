"use client"

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, <PERSON><PERSON><PERSON>, Legend } from "recharts"
import { Laptop, Smartphone, Tablet } from "lucide-react"
import { useLanguage } from "@/shared/hooks/useLanguage"

interface DeviceDistributionProps {
  data?: Record<string, unknown>[];
  className?: string
}

export function DeviceDistribution({ className }: DeviceDistributionProps) {
  const { t } = useLanguage()
  // Cihaz dağılımı verileri
  const deviceData = [
    { name: t('advertiser:statistics.desktop'), value: 45, icon: <Laptop className="h-4 w-4" />, color: "#1e293b" },
    { name: t('advertiser:statistics.mobile'), value: 40, icon: <Smartphone className="h-4 w-4" />, color: "#f43f5e" },
    { name: t('advertiser:statistics.tablet'), value: 15, icon: <Tablet className="h-4 w-4" />, color: "#10b981" },
  ]

  // Tooltip içeriği özelleştirme
  const CustomTooltip = ({ active, payload }: {
    active?: boolean; 
    payload?: Array<{
      name: string;
      value: number;
    }>;
  }) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-2 border rounded-md shadow-sm">
          <p className="text-sm font-medium">{payload[0].name}: %{payload[0].value}</p>
        </div>
      )
    }
    return null
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>{t('advertiser:statistics.deviceDistribution')}</CardTitle>
        <CardDescription>{t('advertiser:statistics.userDevicePreferences')}</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="h-[250px] flex items-center justify-center">
          <ResponsiveContainer width="100%" height="100%">
            <PieChart>
              <Pie
                data={deviceData}
                cx="50%"
                cy="50%"
                innerRadius={60}
                outerRadius={80}
                paddingAngle={2}
                dataKey="value"
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                labelLine={false}
              >
                {deviceData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <text x="50%" y="50%" textAnchor="middle" dominantBaseline="middle" className="text-xl font-bold">
                100%
              </text>
              <Tooltip content={<CustomTooltip />} />
            </PieChart>
          </ResponsiveContainer>
        </div>

        <div className="space-y-4 mt-4">
          {deviceData.map((device, index) => (
            <div key={index} className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div 
                  className="h-3 w-3 rounded-full" 
                  style={{ backgroundColor: device.color }}
                />
                <div className="flex items-center gap-1.5">
                  {device.icon}
                  <span className="text-sm font-medium">{device.name}</span>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-24 h-2 bg-gray-100 rounded-full overflow-hidden">
                  <div 
                    className="h-full rounded-full" 
                    style={{ 
                      width: `${device.value}%`, 
                      backgroundColor: device.color 
                    }}
                  />
                </div>
                <span className="text-sm font-semibold">{device.value}%</span>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
} 