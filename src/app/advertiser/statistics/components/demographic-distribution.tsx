"use client"

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, LabelList } from "recharts"
import { User, Users } from "lucide-react"
import type { TooltipProps } from 'recharts';
import type { NameType, ValueType } from 'recharts/types/component/DefaultTooltipContent';
import { useLanguage } from "@/shared/hooks/useLanguage"

interface DemographicDistributionProps {
  className?: string
}

export function DemographicDistribution({ className }: DemographicDistributionProps) {
  const { t } = useLanguage()
  // Demografik veriler
  const demographicData = [
    { age: "18-24", male: 15, female: 20 },
    { age: "25-34", male: 25, female: 30 },
    { age: "35-44", male: 20, female: 15 },
    { age: "45-54", male: 10, female: 8 },
    { age: "55+", male: 5, female: 2 },
  ]

  // Toolt<PERSON> içeriği özelleştirme
  const CustomTooltip = ({ active, payload, label }: TooltipProps<ValueType, NameType>) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 border rounded-md shadow-md">
          <p className="text-sm font-semibold mb-1">{label} {t('advertiser:statistics.ageGroup')}</p>
          {payload.map((entry, index: number) => (
            <div key={index} className="flex items-center gap-2 text-sm">
              <div 
                className="h-3 w-3 rounded-full" 
                style={{ backgroundColor: entry.color }}
              />
              <span>{entry.name === "male" ? t('advertiser:statistics.male') : t('advertiser:statistics.female')}: %{entry.value}</span>
            </div>
          ))}
        </div>
      )
    }
    return null
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>{t('advertiser:statistics.demographicDistribution')}</CardTitle>
            <CardDescription>{t('advertiser:statistics.userDistributionByAgeAndGender')}</CardDescription>
          </div>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <div className="h-3 w-3 bg-blue-500 rounded-full" />
              <span className="text-xs font-medium">{t('advertiser:statistics.male')}</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-3 w-3 bg-pink-500 rounded-full" />
              <span className="text-xs font-medium">{t('advertiser:statistics.female')}</span>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-8">
          {demographicData.map((data, index) => (
            <div key={index} className="space-y-2">
              <div className="flex items-center justify-between">
                <p className="text-sm font-medium">{data.age}</p>
                <p className="text-xs text-muted-foreground">{data.male}% / {data.female}%</p>
              </div>
              <div className="flex h-5 w-full rounded-md overflow-hidden">
                {/* Erkek Çubuğu */}
                <div 
                  className="h-full bg-blue-500 flex items-center justify-center text-white text-xs"
                  style={{ width: `${data.male * 3}px` }}
                />
                
                {/* Boşluk */}
                <div className="h-full bg-gray-200 w-full" />
                
                {/* Kadın Çubuğu */}
                <div 
                  className="h-full bg-pink-500 flex items-center justify-center text-white text-xs"
                  style={{ width: `${data.female * 3}px` }}
                />
              </div>
            </div>
          ))}
        </div>

        <div className="flex items-center justify-between mt-8 pt-4 border-t">
          <div className="text-center">
            <p className="text-sm text-muted-foreground">{t('advertiser:statistics.totalMale')}</p>
            <p className="text-lg font-semibold text-blue-600">75%</p>
          </div>
          <div className="text-center">
            <p className="text-sm text-muted-foreground">{t('advertiser:statistics.totalFemale')}</p>
            <p className="text-lg font-semibold text-pink-600">75%</p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
} 