"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { useState, useEffect } from "react"
import { BarChart, Bar, Line, LineChart, Area, AreaChart, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from "recharts"
import { useLanguage } from "@/shared/hooks/useLanguage"
import { fetchDailyPerformance, DailyPerformanceData } from "@/shared/services/advertiser/advertiser-daily-performance"
import { toast } from "sonner"

interface PerformanceChartProps {
  className?: string;
  campaignFilter: string;
}

export function PerformanceChart({ className, campaignFilter }: PerformanceChartProps) {
  const [selectedTab, setSelectedTab] = useState("impressions")
  const [data, setData] = useState<DailyPerformanceData[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const { t } = useLanguage()

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true)
      try {
        const response = await fetchDailyPerformance(campaignFilter !== "all" ? campaignFilter : undefined)
        setData(response.result)
      } catch (error) {
        toast.error(t('advertiser:statistics.errors.fetchError'))
        console.error("Veri alınırken hata oluştu:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [campaignFilter])

  const getGraphColor = () => {
    switch (selectedTab) {
      case "impressions": return "hsl(143, 85%, 47%)"
      case "clicks": return "hsl(185, 85%, 47%)"
      case "conversions": return "hsl(143, 85%, 47%)"
      case "ctr": return "hsl(280, 85%, 47%)"
      default: return "hsl(143, 85%, 47%)"
    }
  }

  const getTotalValue = (key: keyof DailyPerformanceData) => {
    return data.reduce((sum, item) => sum + item[key], 0)
  }

  const formatDate = (dateStr: string) => {
    const date = new Date(dateStr)
    return `${date.getDate()}/${date.getMonth() + 1}`
  }

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
            <div>
              <CardTitle>{t('advertiser:statistics.performanceChart')}</CardTitle>
              <CardDescription>{t('advertiser:statistics.campaignMetricsComparison')}</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="h-[300px] flex items-center justify-center">
            <p className="text-muted-foreground">Yükleniyor...</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  if (data.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
            <div>
              <CardTitle>{t('advertiser:statistics.performanceChart')}</CardTitle>
              <CardDescription>{t('advertiser:statistics.campaignMetricsComparison')}</CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="h-[300px] flex items-center justify-center">
            <p className="text-muted-foreground">Sonuç bulunamadı</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
          <div>
            <CardTitle>{t('advertiser:statistics.performanceChart')}</CardTitle>
            <CardDescription>{t('advertiser:statistics.campaignMetricsComparison')}</CardDescription>
          </div>
          <Tabs value={selectedTab} onValueChange={setSelectedTab}>
            <TabsList className="grid w-full md:w-auto grid-cols-4">
              <TabsTrigger value="impressions">{t('advertiser:statistics.metrics.views')}</TabsTrigger>
              <TabsTrigger value="clicks">{t('advertiser:statistics.clicks')}</TabsTrigger>
              <TabsTrigger value="conversions">{t('advertiser:statistics.conversions')}</TabsTrigger>
              <TabsTrigger value="ctr">CTR</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </CardHeader>
      <CardContent>
        <div className="h-[300px]">
          <ResponsiveContainer width="100%" height="100%">
            <AreaChart data={data.map(item => ({
              ...item,
              date: formatDate(item.date)
            }))}>
              <defs>
                <linearGradient id="colorMetric" x1="0" y1="0" x2="0" y2="1">
                  <stop offset="5%" stopColor={getGraphColor()} stopOpacity={0.1} />
                  <stop offset="95%" stopColor={getGraphColor()} stopOpacity={0} />
                </linearGradient>
              </defs>
              <CartesianGrid strokeDasharray="3 3" vertical={false} stroke="#f5f5f5" />
              <XAxis dataKey="date" />
              <YAxis />
              <Tooltip
                contentStyle={{
                  backgroundColor: "#fff",
                  border: "1px solid #f0f0f0",
                  borderRadius: "6px",
                  boxShadow: "0 4px 12px rgba(0, 0, 0, 0.05)"
                }}
                formatter={(value) => {
                  if (selectedTab === "ctr") {
                    return [`${value.toFixed(2)}%`, "CTR"]
                  }
                  return [`${value.toLocaleString()}`, selectedTab === "impressions" ? t('advertiser:statistics.metrics.views') : selectedTab === "clicks" ? t('advertiser:statistics.clicks') : t('advertiser:statistics.conversions')]
                }}
              />
              <Area
                type="monotone"
                dataKey={selectedTab === "impressions" ? "views" : selectedTab}
                stroke={getGraphColor()}
                fillOpacity={1}
                fill="url(#colorMetric)"
                strokeWidth={2}
              />
            </AreaChart>
          </ResponsiveContainer>
        </div>
        
        <div className="grid grid-cols-4 gap-4 mt-6">
          <div className="text-center">
            <p className="text-sm text-muted-foreground">{t('advertiser:statistics.metrics.views')}</p>
            <p className="text-lg font-semibold">{getTotalValue('views').toLocaleString()}</p>
          </div>
          <div className="text-center">
            <p className="text-sm text-muted-foreground">{t('advertiser:statistics.clicks')}</p>
            <p className="text-lg font-semibold">{getTotalValue('clicks').toLocaleString()}</p>
          </div>
          <div className="text-center">
            <p className="text-sm text-muted-foreground">{t('advertiser:statistics.conversions')}</p>
            <p className="text-lg font-semibold">{getTotalValue('conversions').toLocaleString()}</p>
          </div>
          <div className="text-center">
            <p className="text-sm text-muted-foreground">CTR</p>
            <p className="text-lg font-semibold">{(getTotalValue('ctr') / data.length).toFixed(2)}%</p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
} 