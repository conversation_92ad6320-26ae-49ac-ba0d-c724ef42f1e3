"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Eye, MousePointerClick, PercentIcon, ArrowUp, ArrowDown, CheckCircle, TrendingUp, TrendingDown, ArrowRight } from "lucide-react"
import { useLanguage } from "@/shared/hooks/useLanguage"
import { PerformanceAnalysisResponse } from "@/shared/services/advertiser/advertiser-performance-analysis"

interface AnalyticsStatsProps {
  data: PerformanceAnalysisResponse['result'] | null;
  isLoading: boolean;
}

export function AnalyticsStats({ data, isLoading }: AnalyticsStatsProps) {
  const { t } = useLanguage()

  if (isLoading) {
    return <div>Yükleniyor...</div>
  }

  if (!data) {
    return null
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">{t('advertiser:statistics.metrics.views')}</p>
              <h3 className="text-2xl font-bold">{data?.total_views?.value?.toLocaleString() ?? 0}</h3>
              <p className="text-sm text-green-500">+{data?.total_views?.percentage_change ?? 0}%</p>
            </div>
            <Eye className="h-8 w-8 text-gray-400" />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">{t('advertiser:statistics.metrics.clicks')}</p>
              <h3 className="text-2xl font-bold">{data?.total_clicks?.value?.toLocaleString() ?? 0}</h3>
              <p className="text-sm text-green-500">+{data?.total_clicks?.percentage_change ?? 0}%</p>
            </div>
            <MousePointerClick className="h-8 w-8 text-gray-400" />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">{t('advertiser:statistics.metrics.conversions')}</p>
              <h3 className="text-2xl font-bold">{data?.total_conversions?.value?.toLocaleString() ?? 0}</h3>
              <p className="text-sm text-green-500">+{data?.total_conversions?.percentage_change ?? 0}%</p>
            </div>
            <ArrowRight className="h-8 w-8 text-gray-400" />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-500">{t('advertiser:statistics.metrics.ctr')}</p>
              <h3 className="text-2xl font-bold">{data?.avg_ctr?.value?.toFixed(2) ?? 0}%</h3>
              <p className="text-sm text-green-500">+{data?.avg_ctr?.percentage_change ?? 0}%</p>
            </div>
            <PercentIcon className="h-8 w-8 text-gray-400" />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  change: {
    value: number;
    isPositive: boolean;
  };
  iconColor: string;
  chartColor: string;
}

function StatCard({ title, value, icon, change, iconColor, chartColor }: StatCardProps) {
  // Mini grafik için örnek veri noktaları
  const dataPoints = Array.from({ length: 10 }, () => 
    Math.floor(Math.random() * 30) + 10
  );
  
  return (
    <Card className="overflow-hidden">
      <CardContent className="p-6">
        <div className="flex justify-between items-start">
          <div>
            <p className="text-sm font-medium text-muted-foreground mb-1">{title}</p>
            <h3 className="text-2xl font-bold">{value}</h3>
            <div className={`flex items-center mt-1 ${change.isPositive ? 'text-emerald-600' : 'text-rose-600'}`}>
              {change.isPositive ? (
                <ArrowUp className="h-4 w-4 mr-1" />
              ) : (
                <ArrowDown className="h-4 w-4 mr-1" />
              )}
              <span className="text-sm font-medium">{Math.abs(change.value)}%</span>
            </div>
          </div>
          <div className={`h-10 w-10 rounded-full flex items-center justify-center ${iconColor.replace('text-', 'bg-').replace('500', '100')}`}>
            <div className={iconColor}>{icon}</div>
          </div>
        </div>
        
        {/* Mini Chart */}
        <div className="h-12 mt-4 flex items-end gap-[2px]">
          {dataPoints.map((value, index) => (
            <div 
              key={index}
              className={`w-full ${chartColor} ${change.isPositive ? '' : 'opacity-70'} rounded-sm`}
              style={{ 
                height: `${value}%`,
                opacity: index / 10 + 0.3
              }}
            ></div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
} 