"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { useLanguage } from "@/shared/hooks/useLanguage"
import { fetchCommunityList, Community } from "@/shared/services/advertiser/advertiser-community-list"
import { toast } from "sonner"
import Link from "next/link"
import classNames from "classnames"

interface CommunityStatsTableProps {
  className?: string;
  campaignFilter: string;
}

export function CommunityStatsTable({ className, campaignFilter }: CommunityStatsTableProps) {
  const { t } = useLanguage()
  const [communities, setCommunities] = useState<Community[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalItems, setTotalItems] = useState(0)
  const [searchQuery, setSearchQuery] = useState("")
  const itemsPerPage = 10

  const fetchData = async () => {
    try {
      setIsLoading(true)
      const skip = (currentPage - 1) * itemsPerPage
      const params = {
        skip,
        limit: itemsPerPage,
        search: searchQuery || undefined,
        ...(campaignFilter !== "all" && { campaign_id: campaignFilter })
      }
      const response = await fetchCommunityList(params)
      setCommunities(response.result.data)
      setTotalItems(response.result.stats.total)
    } catch (error) {
      console.error("Error fetching community list:", error)
      toast.error(t('common:errors.fetchError'))
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchData()
  }, [currentPage, campaignFilter, searchQuery])

  const totalPages = Math.ceil(totalItems / itemsPerPage)

  const handleSearch = (value: string) => {
    setSearchQuery(value)
    setCurrentPage(1) // Arama yapıldığında ilk sayfaya dön
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <CardTitle>{t('advertiser:statistics.communityStats')}</CardTitle>
            <CardDescription>{t('advertiser:statistics.communityStatsDescription')}</CardDescription>
          </div>
          <div className="w-full md:w-auto">
            <Input
              placeholder={t('advertiser:statistics.searchCommunity')}
              value={searchQuery}
              onChange={(e) => handleSearch(e.target.value)}
              className="w-full md:w-[300px]"
            />
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex items-center justify-center h-[200px]">
            <p>{t('common:loading')}</p>
          </div>
        ) : communities.length === 0 ? (
          <div className="flex items-center justify-center h-[200px]">
            <p>{t('common:noResults')}</p>
          </div>
        ) : (
          <>
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t('advertiser:statistics.communityName')}</TableHead>
                    <TableHead>{t('advertiser:statistics.category')}</TableHead>
                    <TableHead>{t('advertiser:statistics.views')}</TableHead>
                    <TableHead>{t('advertiser:statistics.clicks')}</TableHead>
                    <TableHead>{t('advertiser:statistics.conversions')}</TableHead>
                    <TableHead>{t('advertiser:statistics.earnings')}</TableHead>
                    <TableHead>CTR</TableHead>
                    <TableHead></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {communities.map((community) => {
                    const views = community.views || 0
                    const clicks = community.clicks || 0
                    const conversions = community.conversions || 0
                    const earnings = community.earnings || 0
                    const ctr = community.ctr !== undefined && community.ctr !== null ? community.ctr : "-"
                    const categoryColor =
                      community.category === "Teknoloji"
                        ? "bg-green-100 text-green-800"
                        : community.category === "Spor"
                        ? "bg-orange-100 text-orange-800"
                        : "bg-blue-100 text-blue-800"
                    return (
                      <TableRow key={community.community_username}>
                        <TableCell>
                          <div className="flex flex-col gap-1">
                            <span>{community.community_name}</span>
                            <Link
                              href={`https://t.me/${community.community_username}`}
                              target="_blank"
                              className="text-xs text-blue-600 hover:underline"
                            >
                              View Community
                            </Link>
                          </div>
                        </TableCell>
                        <TableCell>
                          <span className={classNames("px-2 py-1 rounded text-xs font-medium", categoryColor)}>
                            {community.category}
                          </span>
                        </TableCell>
                        <TableCell>{views.toLocaleString()}</TableCell>
                        <TableCell>{clicks.toLocaleString()}</TableCell>
                        <TableCell>{conversions.toLocaleString()}</TableCell>
                        <TableCell>{earnings.toLocaleString()} ₺</TableCell>
                        <TableCell>{ctr}</TableCell>
                        <TableCell>
                          <a
                            href={`https://t.me/${community.community_username}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="inline-flex items-center text-muted-foreground hover:text-blue-600"
                          >
                            <svg width="16" height="16" fill="none" viewBox="0 0 24 24"><path stroke="currentColor" strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 17 17 7m0 0H8m9 0v9"/></svg>
                          </a>
                        </TableCell>
                      </TableRow>
                    )
                  })}
                </TableBody>
              </Table>
            </div>
            <div className="flex items-center justify-between mt-4">
              <div className="text-sm text-muted-foreground">
                {t('advertiser:statistics.showing')} {communities.length} {t('advertiser:statistics.of')} {totalItems}
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                >
                  {t('advertiser:campaigns:previous')}
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                >
                  {t('advertiser:campaigns:next')}
                </Button>
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  )
}