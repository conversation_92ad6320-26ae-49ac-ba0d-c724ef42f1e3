"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { MapPin } from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { ChevronDown, ChevronUp } from "lucide-react"
import { useState } from "react"
import { useLanguage } from "@/shared/hooks/useLanguage"

interface LocationDistributionProps {
  className?: string
}

interface LocationData {
  city: string
  users: number
  percentage: number
}

export function LocationDistribution({ className }: LocationDistributionProps) {
  const [sortColumn, setSortColumn] = useState<keyof LocationData>("users")
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc")
  const { t } = useLanguage()

  // Konum verileri
  const locationData: LocationData[] = [
    { city: t('advertiser:statistics.istanbul'), users: 45000, percentage: 35 },
    { city: t('advertiser:statistics.ankara'), users: 25000, percentage: 20 },
    { city: t('advertiser:statistics.izmir'), users: 20000, percentage: 15 },
    { city: t('advertiser:statistics.bursa'), users: 15000, percentage: 12 },
    { city: t('advertiser:statistics.other'), users: 13000, percentage: 10 },
    { city: t('advertiser:statistics.antalya'), users: 10000, percentage: 8 },
  ]

  // Verileri sırala
  const sortedData = [...locationData].sort((a, b) => {
    const aValue = a[sortColumn]
    const bValue = b[sortColumn]
    
    if (sortDirection === "asc") {
      return aValue > bValue ? 1 : -1
    } else {
      return aValue < bValue ? 1 : -1
    }
  })

  // Sıralama değiştir
  const handleSort = (column: keyof LocationData) => {
    if (sortColumn === column) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc")
    } else {
      setSortColumn(column)
      setSortDirection("desc")
    }
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>{t('advertiser:statistics.locationDistribution')}</CardTitle>
        <CardDescription>{t('advertiser:statistics.userDistributionByCity')}</CardDescription>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead 
                className="w-[200px] cursor-pointer"
                onClick={() => handleSort("city")}
              >
                <div className="flex items-center gap-1">
                  {t('advertiser:statistics.location')}
                  {sortColumn === "city" && (
                    sortDirection === "asc" ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />
                  )}
                </div>
              </TableHead>
              <TableHead 
                className="text-right cursor-pointer"
                onClick={() => handleSort("users")}
              >
                <div className="flex items-center justify-end gap-1">
                  {t('advertiser:statistics.users')}
                  {sortColumn === "users" && (
                    sortDirection === "asc" ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />
                  )}
                </div>
              </TableHead>
              <TableHead 
                className="text-right cursor-pointer"
                onClick={() => handleSort("percentage")}
              >
                <div className="flex items-center justify-end gap-1">
                  {t('advertiser:statistics.percentage')}
                  {sortColumn === "percentage" && (
                    sortDirection === "asc" ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />
                  )}
                </div>
              </TableHead>
              <TableHead className="text-right">{t('advertiser:statistics.distribution')}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {sortedData.map((location, index) => (
              <TableRow key={index}>
                <TableCell className="font-medium">
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-gray-500" />
                    {location.city}
                  </div>
                </TableCell>
                <TableCell className="text-right">{location.users.toLocaleString()}</TableCell>
                <TableCell className="text-right">{location.percentage}%</TableCell>
                <TableCell className="text-right">
                  <div className="w-full h-2 bg-gray-100 rounded-full overflow-hidden">
                    <div 
                      className="h-full bg-blue-500"
                      style={{ width: `${location.percentage}%` }}
                    />
                  </div>
                </TableCell>
              </TableRow>
            ))}
            <TableRow>
              <TableCell colSpan={2} className="font-medium">
                {t('advertiser:statistics.total')}
              </TableCell>
              <TableCell className="text-right font-bold">100%</TableCell>
              <TableCell className="text-right font-bold">128.000</TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
} 