"use client"

import { useState, useEffect } from "react"
import { DashboardLayout } from "@/app/advertiser/components/layout/dashboard-layout"
import { DashboardHeader } from "@/app/advertiser/components/dashboard/header"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { CalendarIcon, ChevronDown, BarChart3, Eye, MousePointerClick, PercentIcon, ArrowRight } from "lucide-react"
import { AnalyticsStats } from "./components/analytics-stats"
import { PerformanceChart } from "./components/performance-chart"
import { DeviceDistribution } from "./components/device-distribution"
import { DemographicDistribution } from "./components/demographic-distribution"
import { LocationDistribution } from "./components/location-distribution"
import { CommunityStatsTable } from "./components/community-stats-table"
import { useLanguage } from "@/shared/hooks/useLanguage"
import { fetchPerformanceAnalysis, PerformanceFilter, PerformanceAnalysisResponse } from "@/shared/services/advertiser/advertiser-performance-analysis"
import { fetchCampaignList, Campaign } from "@/shared/services/advertiser/advertiser-campaign-list"
import { toast } from "sonner"

export default function StatisticsPage() {
  const [dateRange, setDateRange] = useState("7d")
  const [campaignFilter, setCampaignFilter] = useState("all")
  const [performanceData, setPerformanceData] = useState<PerformanceAnalysisResponse['result'] | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [campaigns, setCampaigns] = useState<Campaign[]>([])
  const [isLoadingCampaigns, setIsLoadingCampaigns] = useState(false)
  const { t } = useLanguage()

  const getFilterFromDateRange = (range: string): PerformanceFilter => {
    switch (range) {
      case "7d":
        return "week"
      case "30d":
        return "month"
      case "90d":
        return "three_month"
      case "year":
        return "year"
      default:
        return "week"
    }
  }

  const fetchData = async () => {
    setIsLoading(true)
    try {
      const params = {
        filter: getFilterFromDateRange(dateRange),
        ...(campaignFilter !== "all" && { campaign_id: campaignFilter })
      }

      const response = await fetchPerformanceAnalysis(params)
      setPerformanceData(response.result)
    } catch (error) {
      toast.error(t('advertiser:statistics.errors.fetchError'))
      console.error("Veri alınırken hata oluştu:", error)
    } finally {
      setIsLoading(false)
    }
  }

  const fetchCampaigns = async () => {
    setIsLoadingCampaigns(true)
    try {
      const response = await fetchCampaignList()
      setCampaigns(response.result.data)
    } catch (error) {
      toast.error(t('advertiser:statistics.errors.campaignFetchError'))
      console.error("Kampanya listesi alınırken hata oluştu:", error)
    } finally {
      setIsLoadingCampaigns(false)
    }
  }

  useEffect(() => {
    fetchData()
  }, [dateRange, campaignFilter])

  useEffect(() => {
    fetchCampaigns()
  }, [])

  return (
    <DashboardLayout>
      <DashboardHeader 
        title={t('advertiser:statistics.title')}
        subtitle={t('advertiser:statistics.subtitle')}
        name=""
        showButton={false}
      />

      <div className="flex flex-col-reverse md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <div className="flex flex-col sm:flex-row gap-2 w-full md:w-auto">
          <Select value={campaignFilter} onValueChange={setCampaignFilter}>
            <SelectTrigger className="w-full sm:w-[240px] bg-white">
              <div className="flex items-center gap-2">
                <BarChart3 className="h-4 w-4 text-gray-500" />
                <SelectValue placeholder={t('advertiser:statistics.filters.allCampaigns')} />
              </div>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">{t('advertiser:statistics.filters.allCampaigns')}</SelectItem>
              {campaigns.map((campaign) => (
                <SelectItem key={campaign.campaign_id} value={campaign.campaign_id}>
                  {campaign.title}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className="w-full sm:w-[180px] bg-white">
              <div className="flex items-center gap-2">
                <CalendarIcon className="h-4 w-4 text-gray-500" />
                <SelectValue placeholder={t('advertiser:statistics.dateRanges.last7Days')} />
              </div>
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">{t('advertiser:statistics.dateRanges.last7Days')}</SelectItem>
              <SelectItem value="30d">{t('advertiser:statistics.dateRanges.last30Days')}</SelectItem>
              <SelectItem value="90d">{t('advertiser:statistics.dateRanges.last90Days')}</SelectItem>
              <SelectItem value="year">{t('advertiser:statistics.dateRanges.thisYear')}</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="space-y-8">
        {/* Temel Metrikler */}
        <AnalyticsStats data={performanceData} isLoading={isLoading} />

        {/* Performans ve Cihaz Grafikleri */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          <PerformanceChart className="lg:col-span-3" campaignFilter={campaignFilter} />
          <DeviceDistribution data={[]} className="lg:col-span-1" />
        </div>

        {/* Demografik ve Konum Grafikleri */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <DemographicDistribution />
          <LocationDistribution />
        </div>
      </div>
      {/* Topluluk İstatistik Tablosu */}
      <div className="mt-10 mb-20">
        <CommunityStatsTable campaignFilter={campaignFilter} />
      </div>
    </DashboardLayout>
  )
} 