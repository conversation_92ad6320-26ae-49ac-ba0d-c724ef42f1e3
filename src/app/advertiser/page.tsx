"use client";

import { DashboardLayout } from "@/app/advertiser/components/layout/dashboard-layout";
import { DashboardHeader } from "@/app/advertiser/components/dashboard/header";
import { StatsCards } from "@/app/advertiser/components/dashboard/stats-cards";
import { CampaignPerformanceChart } from "@/app/advertiser/components/dashboard/campaign-performance-chart";
import { CampaignTable } from "./components/dashboard/campaign-table";
import { Button } from "@/components/ui/button";
import { PlusCircle } from "lucide-react";
import Link from "next/link";
import { useLanguage } from "@/shared/hooks/useLanguage";

export default function AdvertiserDashboard() {
  const { t } = useLanguage();

  return (
    <DashboardLayout>
      <DashboardHeader 
        name="John" 
        title={t('advertiser:dashboard.welcome')} 
        subtitle={t('advertiser:dashboard.subtitle')}
      >
        <Button size="lg" className="gap-2">
          <PlusCircle className="h-5 w-5" />
          <span>{t('advertiser:dashboard.sections.campaigns.create')}</span>
        </Button>
      </DashboardHeader>
      
      <div className="space-y-8">
        <StatsCards />
        <div className="grid gap-4 grid-cols-1 md:grid-cols-4">
          <CampaignPerformanceChart />
        </div>
        <CampaignTable />
        <div className="grid gap-8 md:grid-cols-2">
        </div>
      </div>
    </DashboardLayout>
  );
} 