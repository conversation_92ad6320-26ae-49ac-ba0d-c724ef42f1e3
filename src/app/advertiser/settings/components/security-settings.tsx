"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Check, Lock, ShieldCheck, KeyRound, Fingerprint, AlertTriangle, Smartphone, Award } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Separator } from "@/components/ui/separator"
import { Progress } from "@/components/ui/progress"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { useLanguage } from "@/shared/hooks/useLanguage"

export function SecuritySettings() {
  const [saving, setSaving] = useState(false)
  const [currentPassword, setCurrentPassword] = useState("")
  const [newPassword, setNewPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [passwordStrength, setPasswordStrength] = useState(0)
  const { t } = useLanguage()
  
  const handleSave = () => {
    setSaving(true)
    setTimeout(() => {
      setSaving(false)
      setCurrentPassword("")
      setNewPassword("")
      setConfirmPassword("")
      setPasswordStrength(0)
    }, 1000)
  }
  
  // Şifre gücünü hesaplama (basit bir örnek)
  const calculatePasswordStrength = (password: string) => {
    let score = 0
    if (password.length > 0) {
      // Uzunluk için puan
      score += Math.min(password.length * 4, 40)
      
      // Büyük harf için puan
      if (/[A-Z]/.test(password)) score += 10
      
      // Küçük harf için puan
      if (/[a-z]/.test(password)) score += 10
      
      // Sayı için puan
      if (/[0-9]/.test(password)) score += 20
      
      // Özel karakter için puan
      if (/[^A-Za-z0-9]/.test(password)) score += 20
    }
    
    return Math.min(score, 100)
  }
  
  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newPass = e.target.value
    setNewPassword(newPass)
    setPasswordStrength(calculatePasswordStrength(newPass))
  }
  
  const getStrengthLabel = () => {
    if (passwordStrength < 30) return { label: "Zayıf", color: "bg-red-500" }
    if (passwordStrength < 60) return { label: "Orta", color: "bg-yellow-500" }
    if (passwordStrength < 80) return { label: "İyi", color: "bg-green-500" }
    return { label: "Güçlü", color: "bg-green-600" }
  }
  
  const strengthInfo = getStrengthLabel()
  
  const isPasswordsMatch = () => {
    return newPassword === confirmPassword && newPassword !== ""
  }
  
  return (
    <div className="space-y-8">
      <Card>
        <CardHeader>
          <CardTitle>{t('advertiser:settings.security.title')}</CardTitle>
          <CardDescription>
            {t('advertiser:settings.security.subtitle')}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-8">
          <div className="space-y-4">
            <h3 className="text-base font-medium">{t('advertiser:settings.security.password.title')}</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="current-password" className="text-base">
                  {t('advertiser:settings.security.password.current')}
                </Label>
                <Input 
                  id="current-password" 
                  type="password"
                  placeholder={t('advertiser:settings.security.password.current')}
                  className="h-10"
                  value={currentPassword}
                  onChange={(e) => setCurrentPassword(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="new-password" className="text-base">
                  {t('advertiser:settings.security.password.new')}
                </Label>
                <Input 
                  id="new-password" 
                  type="password"
                  placeholder={t('advertiser:settings.security.password.new')}
                  className="h-10"
                  value={newPassword}
                  onChange={handlePasswordChange}
                />
              </div>
              <div className="space-y-2 md:col-span-2">
                <Label htmlFor="confirm-password" className="text-base">
                  {t('advertiser:settings.security.password.confirm')}
                </Label>
                <Input 
                  id="confirm-password" 
                  type="password"
                  placeholder={t('advertiser:settings.security.password.confirm')}
                  className={`h-10 ${confirmPassword && !isPasswordsMatch() ? "border-red-500 focus-visible:ring-red-500" : ""}`}
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                />
                {confirmPassword && !isPasswordsMatch() && (
                  <p className="text-xs text-red-500 mt-1">{t('advertiser:settings.security.password.mismatch')}</p>
                )}
              </div>
            </div>
          </div>

     

        
        </CardContent>
        <CardFooter className="flex justify-between border-t p-6">
          <Button variant="outline">{t('advertiser:settings.buttons.cancel')}</Button>
          <Button onClick={handleSave} disabled={saving || !currentPassword || !newPassword || !confirmPassword || !isPasswordsMatch() || passwordStrength < 50}>
            {saving ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {t('advertiser:settings.buttons.saving')}
              </>
            ) : (
              <>
                <Check className="mr-2 h-4 w-4" />
                {t('advertiser:settings.buttons.save')}
              </>
            )}
          </Button>
        </CardFooter>
      </Card>
      
 
      
            
   
    </div>
  )
}

// Shield ikonu için
function Shield({ className, ...props }: React.ComponentProps<typeof AlertTriangle>) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      stroke="currentColor"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={className}
      {...props}
    >
      <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z" />
    </svg>
  )
} 