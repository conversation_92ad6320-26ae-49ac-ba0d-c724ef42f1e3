"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Check, BellRing, Mail, MailCheck, Smartphone, MessageSquare, DollarSign, Activity, Megaphone, AlertTriangle } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { useLanguage } from "@/shared/hooks/useLanguage"

export function NotificationSettings() {
  const [saving, setSaving] = useState(false)
  const [emailFrequency, setEmailFrequency] = useState("daily")
  const { t } = useLanguage()
  
  const handleSave = () => {
    setSaving(true)
    setTimeout(() => {
      setSaving(false)
    }, 1000)
  }
  
  return (
    <div className="space-y-8">
      <Card>
        <CardHeader>
          <CardTitle>{t('advertiser:settings.notifications.title')}</CardTitle>
          <CardDescription>
            {t('advertiser:settings.notifications.subtitle')}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-8">
          <div className="space-y-4">
            <h3 className="text-base font-medium">{t('advertiser:settings.notifications.email.title')}</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-sm">{t('advertiser:settings.notifications.email.campaign.title')}</Label>
                  <p className="text-xs text-muted-foreground">
                    {t('advertiser:settings.notifications.email.campaign.description')}
                  </p>
                </div>
                <Switch defaultChecked />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-sm">{t('advertiser:settings.notifications.email.performance.title')}</Label>
                  <p className="text-xs text-muted-foreground">
                    {t('advertiser:settings.notifications.email.performance.description')}
                  </p>
                </div>
                <Switch defaultChecked />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-sm">{t('advertiser:settings.notifications.email.billing.title')}</Label>
                  <p className="text-xs text-muted-foreground">
                    {t('advertiser:settings.notifications.email.billing.description')}
                  </p>
                </div>
                <Switch defaultChecked />
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="text-base font-medium">{t('advertiser:settings.notifications.push.title')}</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-sm">{t('advertiser:settings.notifications.push.campaign.title')}</Label>
                  <p className="text-xs text-muted-foreground">
                    {t('advertiser:settings.notifications.push.campaign.description')}
                  </p>
                </div>
                <Switch defaultChecked />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-sm">{t('advertiser:settings.notifications.push.performance.title')}</Label>
                  <p className="text-xs text-muted-foreground">
                    {t('advertiser:settings.notifications.push.performance.description')}
                  </p>
                </div>
                <Switch defaultChecked />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-sm">{t('advertiser:settings.notifications.push.billing.title')}</Label>
                  <p className="text-xs text-muted-foreground">
                    {t('advertiser:settings.notifications.push.billing.description')}
                  </p>
                </div>
                <Switch defaultChecked />
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="text-base font-medium">{t('advertiser:settings.notifications.sms.title')}</h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-sm">{t('advertiser:settings.notifications.sms.campaign.title')}</Label>
                  <p className="text-xs text-muted-foreground">
                    {t('advertiser:settings.notifications.sms.campaign.description')}
                  </p>
                </div>
                <Switch />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-sm">{t('advertiser:settings.notifications.sms.performance.title')}</Label>
                  <p className="text-xs text-muted-foreground">
                    {t('advertiser:settings.notifications.sms.performance.description')}
                  </p>
                </div>
                <Switch />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label className="text-sm">{t('advertiser:settings.notifications.sms.billing.title')}</Label>
                  <p className="text-xs text-muted-foreground">
                    {t('advertiser:settings.notifications.sms.billing.description')}
                  </p>
                </div>
                <Switch />
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between border-t p-6">
          <Button variant="outline">{t('advertiser:settings.buttons.cancel')}</Button>
          <Button onClick={handleSave} disabled={saving}>
            {saving ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {t('advertiser:settings.buttons.saving')}
              </>
            ) : (
              <>
                <Check className="mr-2 h-4 w-4" />
                {t('advertiser:settings.buttons.save')}
              </>
            )}
          </Button>
        </CardFooter>
      </Card>
  
    </div>
  )
} 