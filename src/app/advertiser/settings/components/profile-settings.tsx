"use client"

import { useState } from "react"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { MailCheck, User, Upload, Check, Info } from "lucide-react"
import { Switch } from "@/components/ui/switch"
import { useLanguage, SUPPORTED_LANGUAGES } from '@/shared/hooks/useLanguage'

export function ProfileSettings() {
  const [saving, setSaving] = useState(false)
  const { t, currentLanguage, changeLanguage } = useLanguage()
  
  const handleSave = () => {
    setSaving(true)
    setTimeout(() => {
      setSaving(false)
    }, 1000)
  }
  
  return (
    <div className="space-y-8">
      <Card>
        <CardHeader>
          <CardTitle>{t('advertiser:settings.profile.title')}</CardTitle>
          <CardDescription>
            {t('advertiser:settings.profile.subtitle')}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-8">
      

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="full-name" className="text-base">{t('advertiser:settings.profile.fields.fullName')}</Label>
              <Input 
                id="full-name" 
                placeholder={t('advertiser:settings.profile.fields.fullName')}
                defaultValue="Adnomio" 
                className="h-10"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="job-title" className="text-base">{t('advertiser:settings.profile.fields.jobTitle')}</Label>
              <Input 
                id="job-title" 
                placeholder={t('advertiser:settings.profile.fields.jobTitle')}
                defaultValue="Pazarlama Yöneticisi"
                className="h-10"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="email" className="text-base">{t('advertiser:settings.profile.fields.email')}</Label>
              <div className="relative">
                <Input 
                  id="email" 
                  type="email"
                  placeholder={t('advertiser:settings.profile.fields.email')}
                  defaultValue="<EMAIL>"
                  className="h-10 pr-10"
                  disabled
                />
                <div className="absolute right-3 top-1/2 -translate-y-1/2">
                  <MailCheck className="h-4 w-4 text-green-500" />
                </div>
              </div>
              <p className="text-xs text-muted-foreground">
                {t('advertiser:settings.profile.fields.emailVerified')}
              </p>
            </div>
            <div className="space-y-2">
              <Label htmlFor="phone" className="text-base">{t('advertiser:settings.profile.fields.phone')}</Label>
              <Input 
                id="phone" 
                placeholder={t('advertiser:settings.profile.fields.phone')}
                defaultValue="+90 (*************"
                className="h-10"
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="bio" className="text-base">{t('advertiser:settings.profile.fields.about')}</Label>
            <Textarea 
              id="bio" 
              placeholder={t('advertiser:settings.profile.fields.about')}
              defaultValue="Adnomio Reklamcılık bünyesinde Pazarlama Yöneticisi olarak çalışıyorum. Adnomio ile dijital reklamcılık alanında müşterilerimize hizmet veriyoruz."
              className="min-h-[120px]"
            />
            <p className="text-xs text-muted-foreground">
              {t('advertiser:settings.profile.fields.aboutHint')}
            </p>
          </div>
        
        
        </CardContent>
        <CardFooter className="flex justify-between border-t p-6">
          <Button variant="outline">{t('advertiser:settings.buttons.cancel')}</Button>
          <Button onClick={handleSave} disabled={saving}>
            {saving ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {t('advertiser:settings.buttons.saving')}
              </>
            ) : (
              <>
                <Check className="mr-2 h-4 w-4" />
                {t('advertiser:settings.buttons.save')}
              </>
            )}
          </Button>
        </CardFooter>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>{t('advertiser:settings.profile.language.title')}</CardTitle>
          <CardDescription>
            {t('advertiser:settings.profile.language.subtitle')}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="language" className="text-base">{t('advertiser:settings.profile.language.language')}</Label>
              <Select value={currentLanguage} onValueChange={changeLanguage}>
                <SelectTrigger id="language" className="h-10">
                  <SelectValue placeholder={t('advertiser:settings.profile.language.language')} />
                </SelectTrigger>
                <SelectContent>
                  {Object.values(SUPPORTED_LANGUAGES).map((lang) => (
                    <SelectItem key={lang.code} value={lang.code}>
                      <span className="flex items-center gap-2">
                        <span>{lang.flag}</span>
                        <span>{lang.name}</span>
                      </span>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="timezone" className="text-base">{t('advertiser:settings.profile.language.timezone')}</Label>
              <Select defaultValue="europe-istanbul">
                <SelectTrigger id="timezone" className="h-10">
                  <SelectValue placeholder={t('advertiser:settings.profile.language.timezone')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="europe-istanbul">(GMT+03:00) İstanbul</SelectItem>
                  <SelectItem value="europe-london">(GMT+00:00) Londra</SelectItem>
                  <SelectItem value="america-new_york">(GMT-05:00) New York</SelectItem>
                  <SelectItem value="asia-tokyo">(GMT+09:00) Tokyo</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="date-format" className="text-base">{t('advertiser:settings.profile.language.dateFormat')}</Label>
              <Select defaultValue="dd-mm-yyyy">
                <SelectTrigger id="date-format" className="h-10">
                  <SelectValue placeholder={t('advertiser:settings.profile.language.dateFormat')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="dd-mm-yyyy">GG-AA-YYYY (31-12-2023)</SelectItem>
                  <SelectItem value="mm-dd-yyyy">AA-GG-YYYY (12-31-2023)</SelectItem>
                  <SelectItem value="yyyy-mm-dd">YYYY-AA-GG (2023-12-31)</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="currency" className="text-base">{t('advertiser:settings.profile.language.currency')}</Label>
              <Select defaultValue="try">
                <SelectTrigger id="currency" className="h-10">
                  <SelectValue placeholder={t('advertiser:settings.profile.language.currency')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="try">Türk Lirası (₺)</SelectItem>
                  <SelectItem value="usd">US Dollar ($)</SelectItem>
                  <SelectItem value="eur">Euro (€)</SelectItem>
                  <SelectItem value="gbp">British Pound (£)</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between border-t p-6">
          <Button variant="outline">{t('advertiser:settings.buttons.cancel')}</Button>
          <Button onClick={handleSave} disabled={saving}>
            {saving ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {t('advertiser:settings.buttons.saving')}
              </>
            ) : (
              <>
                <Check className="mr-2 h-4 w-4" />
                {t('advertiser:settings.buttons.save')}
              </>
            )}
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
} 