"use client"

import { useState } from "react"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Check, Building2, Upload, Info, AlertCircle } from "lucide-react"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { useLanguage } from "@/shared/hooks/useLanguage"
import Image from "next/image"

export function CompanySettings() {
  const [saving, setSaving] = useState(false)
  const [companyLogo, setCompanyLogo] = useState("/placeholders/company-logo.svg")
  const { t } = useLanguage()
  
  const handleSave = () => {
    setSaving(true)
    setTimeout(() => {
      setSaving(false)
    }, 1000)
  }
  
  return (
    <div className="space-y-8">
      <Card>
        <CardHeader>
          <CardTitle>{t('advertiser:settings.company.title')}</CardTitle>
          <CardDescription>
            {t('advertiser:settings.company.subtitle')}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-8">
          <div className="space-y-2">
            <Label htmlFor="company-logo" className="text-base">{t('advertiser:settings.company.logo.title')}</Label>
            <div className="flex items-start space-x-6">
            
              <div className="space-y-2 mt-2">
                <Button variant="outline" className="h-9">
                  <Upload className="h-4 w-4 mr-2" />
                  <span>{t('advertiser:settings.company.logo.upload')}</span>
                </Button>
                <p className="text-xs text-muted-foreground">
                  {t('advertiser:settings.company.logo.hint')}
                </p>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label htmlFor="company-name" className="text-base">{t('advertiser:settings.company.fields.name')}</Label>
              <Input 
                id="company-name" 
                placeholder={t('advertiser:settings.company.fields.name')}
                defaultValue="Adnomio Reklamcılık Ltd. Şti." 
                className="h-10"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="vat-id" className="text-base">{t('advertiser:settings.company.fields.vatId')}</Label>
              <Input 
                id="vat-id" 
                placeholder={t('advertiser:settings.company.fields.vatId')}
                defaultValue="1234567890"
                className="h-10"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="industry" className="text-base">{t('advertiser:settings.company.fields.industry')}</Label>
              <Select defaultValue="advertising">
                <SelectTrigger id="industry" className="h-10">
                  <SelectValue placeholder={t('advertiser:settings.company.fields.industry')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="advertising">Reklamcılık ve Pazarlama</SelectItem>
                  <SelectItem value="technology">Bilişim ve Teknoloji</SelectItem>
                  <SelectItem value="finance">Finans ve Bankacılık</SelectItem>
                  <SelectItem value="ecommerce">E-Ticaret</SelectItem>
                  <SelectItem value="education">Eğitim</SelectItem>
                  <SelectItem value="other">Diğer</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label htmlFor="company-size" className="text-base">{t('advertiser:settings.company.fields.size')}</Label>
              <Select defaultValue="11-50">
                <SelectTrigger id="company-size" className="h-10">
                  <SelectValue placeholder={t('advertiser:settings.company.fields.size')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1-10">1-10 çalışan</SelectItem>
                  <SelectItem value="11-50">11-50 çalışan</SelectItem>
                  <SelectItem value="51-200">51-200 çalışan</SelectItem>
                  <SelectItem value="201-500">201-500 çalışan</SelectItem>
                  <SelectItem value="501+">501+ çalışan</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="company-about" className="text-base">{t('advertiser:settings.company.fields.about')}</Label>
            <Textarea 
              id="company-about" 
              placeholder={t('advertiser:settings.company.fields.about')}
              defaultValue="Adnomio Reklamcılık olarak, şirketlerin dijital varlıklarını güçlendirmek ve online görünürlüklerini artırmak için stratejik reklamcılık çözümleri sunuyoruz. Sosyal medya reklamcılığı, arama motoru reklamcılığı ve içerik pazarlama alanlarında uzmanlaşmış ekibimizle müşterilerimizin hedeflerine ulaşmasına yardımcı oluyoruz."
              className="min-h-[120px]"
            />
          </div>
        </CardContent>
        <CardFooter className="flex justify-between border-t p-6">
          <Button variant="outline">{t('advertiser:settings.buttons.cancel')}</Button>
          <Button onClick={handleSave} disabled={saving}>
            {saving ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {t('advertiser:settings.buttons.saving')}
              </>
            ) : (
              <>
                <Check className="mr-2 h-4 w-4" />
                {t('advertiser:settings.buttons.save')}
              </>
            )}
          </Button>
        </CardFooter>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>{t('advertiser:settings.company.address.title')}</CardTitle>
          <CardDescription>
            {t('advertiser:settings.company.address.subtitle')}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2 md:col-span-2">
              <Label htmlFor="address-line1" className="text-base">{t('advertiser:settings.company.address.line1')}</Label>
              <Input 
                id="address-line1" 
                placeholder={t('advertiser:settings.company.address.line1')}
                defaultValue="Adnomio Plaza, Bağdat Caddesi No: 123"
                className="h-10"
              />
            </div>
            <div className="space-y-2 md:col-span-2">
              <Label htmlFor="address-line2" className="text-base">{t('advertiser:settings.company.address.line2')}</Label>
              <Input 
                id="address-line2" 
                placeholder={t('advertiser:settings.company.address.line2')}
                defaultValue="Kat: 5, No: 42"
                className="h-10"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="city" className="text-base">{t('advertiser:settings.company.address.city')}</Label>
              <Input 
                id="city" 
                placeholder={t('advertiser:settings.company.address.city')}
                defaultValue="İstanbul"
                className="h-10"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="state" className="text-base">{t('advertiser:settings.company.address.state')}</Label>
              <Input 
                id="state" 
                placeholder={t('advertiser:settings.company.address.state')}
                defaultValue="Kadıköy"
                className="h-10"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="zip" className="text-base">{t('advertiser:settings.company.address.zip')}</Label>
              <Input 
                id="zip" 
                placeholder={t('advertiser:settings.company.address.zip')}
                defaultValue="34744"
                className="h-10"
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="country" className="text-base">{t('advertiser:settings.company.address.country')}</Label>
              <Select defaultValue="tr">
                <SelectTrigger id="country" className="h-10">
                  <SelectValue placeholder={t('advertiser:settings.company.address.country')} />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="tr">Türkiye</SelectItem>
                  <SelectItem value="us">Amerika Birleşik Devletleri</SelectItem>
                  <SelectItem value="gb">Birleşik Krallık</SelectItem>
                  <SelectItem value="de">Almanya</SelectItem>
                  <SelectItem value="fr">Fransa</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between border-t p-6">
          <Button variant="outline">{t('advertiser:settings.buttons.cancel')}</Button>
          <Button onClick={handleSave} disabled={saving}>
            {saving ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {t('advertiser:settings.buttons.saving')}
              </>
            ) : (
              <>
                <Check className="mr-2 h-4 w-4" />
                {t('advertiser:settings.buttons.save')}
              </>
            )}
          </Button>
        </CardFooter>
      </Card>

    </div>
  )
} 