"use client"

import { useState } from "react"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Check, CreditCard, PlusCircle, Trash2, Download, CreditCardIcon, Landmark, Copy } from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { useLanguage } from "@/shared/hooks/useLanguage"

export function BillingSettings() {
  const [saving, setSaving] = useState(false)
  const [showAddCard, setShowAddCard] = useState(false)
  const [defaultCard, setDefaultCard] = useState("card1")
  const { t } = useLanguage()
  
  const handleSave = () => {
    setSaving(true)
    setTimeout(() => {
      setSaving(false)
      setShowAddCard(false)
    }, 1000)
  }
  
  return (
    <div className="space-y-8">
      <Card>
        <CardHeader>
          <CardTitle>{t('advertiser:billing.paymentMethods')}</CardTitle>
          <CardDescription>
            {t('advertiser:billing.paymentMethodsDescription')}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="text-base font-medium">{t('advertiser:billing.savedCards')}</h3>
              <Button
                variant="outline"
                size="sm"
                className="h-8"
                onClick={() => setShowAddCard(true)}
              >
                <PlusCircle className="h-4 w-4 mr-2" />
                {t('advertiser:billing.addNewCard')}
              </Button>
            </div>

            {showAddCard && (
              <Card className="border-dashed border-2 border-blue-200 bg-blue-50/50">
                <CardContent className="pt-6">
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label htmlFor="card-holder">{t('advertiser:billing.cardHolder')}</Label>
                        <Input
                          id="card-holder"
                          placeholder={t('advertiser:billing.cardHolderPlaceholder')}
                          className="h-10"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="card-number">{t('advertiser:billing.cardNumber')}</Label>
                        <div className="relative">
                          <Input
                            id="card-number"
                            placeholder="XXXX XXXX XXXX XXXX"
                            className="h-10 pl-10"
                          />
                          <div className="absolute left-3 top-1/2 -translate-y-1/2">
                            <CreditCardIcon className="h-4 w-4 text-muted-foreground" />
                          </div>
                        </div>
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="expiry">{t('advertiser:billing.expiryDate')}</Label>
                        <Input
                          id="expiry"
                          placeholder="AA/YY"
                          className="h-10"
                        />
                      </div>
                      <div className="space-y-2">
                        <Label htmlFor="cvc">{t('advertiser:billing.securityCode')}</Label>
                        <Input
                          id="cvc"
                          placeholder="CVC"
                          className="h-10"
                          type="password"
                        />
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Switch id="save-as-default" />
                      <Label htmlFor="save-as-default" className="text-sm">
                        {t('advertiser:billing.saveAsDefault')}
                      </Label>
                    </div>
                  </div>
                </CardContent>
                <CardFooter className="flex justify-end gap-2 pt-2 pb-4 px-6">
                  <Button variant="outline" size="sm" onClick={() => setShowAddCard(false)}>
                    {t('advertiser:settings.buttons.cancel')}
                  </Button>
                  <Button size="sm" onClick={handleSave} disabled={saving}>
                    {saving ? t('advertiser:settings.buttons.saving') : t('advertiser:billing.saveCard')}
                  </Button>
                </CardFooter>
              </Card>
            )}

            <div className="border rounded-md overflow-hidden">
              <RadioGroup value={defaultCard} onValueChange={setDefaultCard} className="divide-y">
                <div className="flex items-center space-x-3 p-4 hover:bg-gray-50">
                  <RadioGroupItem value="card1" id="card1" className="ml-1" />
                  <Label
                    htmlFor="card1"
                    className="flex flex-1 items-center justify-between cursor-pointer py-1 text-base"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="h-10 w-14 bg-gradient-to-br from-blue-600 to-blue-800 rounded-md flex items-center justify-center text-white text-xs font-semibold">
                        VISA
                      </div>
                      <div>
                        <p className="font-medium">•••• •••• •••• 1234</p>
                        <p className="text-sm text-muted-foreground">
                          {t('advertiser:billing.expiry')}: 06/2025
                        </p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="ml-auto bg-green-50 text-green-600 border-green-200">
                        {t('advertiser:billing.default')}
                      </Badge>
                      <Button size="icon" variant="ghost" className="h-8 w-8 rounded-full text-muted-foreground">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </Label>
                </div>
                <div className="flex items-center space-x-3 p-4 hover:bg-gray-50">
                  <RadioGroupItem value="card2" id="card2" className="ml-1" />
                  <Label
                    htmlFor="card2"
                    className="flex flex-1 items-center justify-between cursor-pointer py-1 text-base"
                  >
                    <div className="flex items-center space-x-3">
                      <div className="h-10 w-14 bg-gradient-to-br from-red-500 to-red-700 rounded-md flex items-center justify-center text-white text-xs font-semibold">
                        MASTER
                      </div>
                      <div>
                        <p className="font-medium">•••• •••• •••• 5678</p>
                        <p className="text-sm text-muted-foreground">
                          {t('advertiser:billing.expiry')}: 12/2024
                        </p>
                      </div>
                    </div>
                    <Button size="icon" variant="ghost" className="h-8 w-8 rounded-full text-muted-foreground">
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </Label>
                </div>
              </RadioGroup>
            </div>
          </div>

         
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle>{t('advertiser:billing.billingInfo')}</CardTitle>
          <CardDescription>
            {t('advertiser:billing.billingInfoDescription')}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="billing-email" className="text-base">{t('advertiser:billing.billingEmail')}</Label>
                <Input 
                  id="billing-email" 
                  type="email"
                  placeholder={t('advertiser:billing.billingEmailPlaceholder')} 
                  defaultValue="<EMAIL>" 
                  className="h-10"
                />
                <p className="text-xs text-muted-foreground">
                  {t('advertiser:billing.billingEmailDescription')}
                </p>
              </div>
             
              <div className="space-y-2">
                <Label htmlFor="tax-id" className="text-base">{t('advertiser:billing.taxId')}</Label>
                <Input 
                  id="tax-id" 
                  placeholder={t('advertiser:billing.taxIdPlaceholder')} 
                  defaultValue="1234567890" 
                  className="h-10"
                />
              </div>
            </div>
          </div>
          
          <div className="border-t pt-6 mt-6">
            <h3 className="text-base font-medium mb-4">{t('advertiser:billing.recentInvoices')}</h3>
            <div className="border rounded-md overflow-hidden">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t('advertiser:billing.invoiceNo')}</TableHead>
                    <TableHead>{t('advertiser:billing.date')}</TableHead>
                    <TableHead>{t('advertiser:billing.amount')}</TableHead>
                    <TableHead>{t('advertiser:billing.status')}</TableHead>
                    <TableHead className="w-[100px]">{t('advertiser:billing.action')}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  <TableRow>
                    <TableCell className="font-medium">INV-2023-001</TableCell>
                    <TableCell>12.05.2023</TableCell>
                    <TableCell>₺1,250.00</TableCell>
                    <TableCell>
                      <Badge className="bg-green-50 text-green-600 border-green-200">
                        {t('advertiser:billing.paid')}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <Download className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">INV-2023-002</TableCell>
                    <TableCell>11.04.2023</TableCell>
                    <TableCell>₺950.00</TableCell>
                    <TableCell>
                      <Badge className="bg-green-50 text-green-600 border-green-200">
                        {t('advertiser:billing.paid')}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <Download className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="font-medium">INV-2023-003</TableCell>
                    <TableCell>10.03.2023</TableCell>
                    <TableCell>₺1,450.00</TableCell>
                    <TableCell>
                      <Badge className="bg-green-50 text-green-600 border-green-200">
                        {t('advertiser:billing.paid')}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <Download className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </div>
            <div className="mt-2 text-right">
              <Button variant="link" size="sm" className="h-8 text-blue-500">
                {t('advertiser:billing.viewAllInvoices')}
              </Button>
            </div>
          </div>
        </CardContent>
        <CardFooter className="flex justify-between border-t p-6">
          <Button variant="outline">{t('advertiser:settings.buttons.cancel')}</Button>
          <Button onClick={handleSave} disabled={saving}>
            {saving ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {t('advertiser:settings.buttons.saving')}
              </>
            ) : (
              <>
                <Check className="mr-2 h-4 w-4" />
                {t('advertiser:settings.buttons.save')}
              </>
            )}
          </Button>
        </CardFooter>
      </Card>
    </div>
  )
} 