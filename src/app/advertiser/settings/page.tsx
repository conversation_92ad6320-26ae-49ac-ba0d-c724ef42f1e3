"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import { ProfileSettings } from "./components/profile-settings"
import { BillingSettings } from "./components/billing-settings"
import { NotificationSettings } from "./components/notification-settings"
import { SecuritySettings } from "./components/security-settings"
import { CompanySettings } from "./components/company-settings"
import { DashboardLayout } from "@/app/advertiser/components/layout/dashboard-layout"
import { DashboardHeader } from "@/app/advertiser/components/dashboard/header"
import { useLanguage } from "@/shared/hooks/useLanguage"
import { 
  User, 
  Building2, 
  CreditCard, 
  Bell, 
  ShieldCheck 
} from "lucide-react"

export default function SettingsPage() {
  const { t } = useLanguage();

  return (
    <DashboardLayout>
      <div className="flex justify-between items-center mb-6">
        <DashboardHeader 
          name=""
          title={t('advertiser:settings.title')}
          subtitle={t('advertiser:settings.subtitle')}
          show<PERSON><PERSON>on={false}
        />
      </div>

      <div className="container max-w-6xl mx-auto px-4 sm:px-6">
        <Tabs defaultValue="profile" className="w-full">
          <div className="bg-white/10 backdrop-blur-sm rounded-xl py-2 sm:py-4 mb-8 sm:mb-12 shadow-sm border border-slate-300/30">
            <TabsList className="flex flex-wrap justify-start sm:justify-center w-full max-w-4xl mx-auto h-auto bg-transparent gap-2 px-2 sm:px-4">
              <TabsTrigger 
                value="profile" 
                className="flex-1 sm:flex-none flex items-center justify-center gap-2 py-2 sm:py-3 px-3 sm:px-5 h-auto rounded-lg text-slate-700 hover:bg-slate-200/70 transition-all data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-md min-w-[120px] text-sm sm:text-base"
              >
                <User className="h-4 w-4" />
                <span>{t('advertiser:settings.tabs.profile')}</span>
              </TabsTrigger>
              <TabsTrigger 
                value="company" 
                className="flex-1 sm:flex-none flex items-center justify-center gap-2 py-2 sm:py-3 px-3 sm:px-5 h-auto rounded-lg text-slate-700 hover:bg-slate-200/70 transition-all data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-md min-w-[120px] text-sm sm:text-base"
              >
                <Building2 className="h-4 w-4" />
                <span>{t('advertiser:settings.tabs.company')}</span>
              </TabsTrigger>
              <TabsTrigger 
                value="billing" 
                className="flex-1 sm:flex-none flex items-center justify-center gap-2 py-2 sm:py-3 px-3 sm:px-5 h-auto rounded-lg text-slate-700 hover:bg-slate-200/70 transition-all data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-md min-w-[120px] text-sm sm:text-base"
              >
                <CreditCard className="h-4 w-4" />
                <span>{t('advertiser:settings.tabs.billing')}</span>
              </TabsTrigger>
            {/*  <TabsTrigger 
                value="notifications" 
                className="flex-1 sm:flex-none flex items-center justify-center gap-2 py-2 sm:py-3 px-3 sm:px-5 h-auto rounded-lg text-slate-700 hover:bg-slate-200/70 transition-all data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-md min-w-[120px] text-sm sm:text-base"
              >
                <Bell className="h-4 w-4" />
                <span>{t('advertiser:settings.tabs.notifications')}</span>
              </TabsTrigger>*/}
              <TabsTrigger 
                value="security" 
                className="flex-1 sm:flex-none flex items-center justify-center gap-2 py-2 sm:py-3 px-3 sm:px-5 h-auto rounded-lg text-slate-700 hover:bg-slate-200/70 transition-all data-[state=active]:bg-primary data-[state=active]:text-primary-foreground data-[state=active]:shadow-md min-w-[120px] text-sm sm:text-base"
              >
                <ShieldCheck className="h-4 w-4" />
                <span>{t('advertiser:settings.tabs.security')}</span>
              </TabsTrigger>
            </TabsList>
          </div>
          
          <div className="p-1">
            <TabsContent value="profile" className="mt-0">
              <ProfileSettings />
            </TabsContent>
            
            <TabsContent value="company" className="mt-0">
              <CompanySettings />
            </TabsContent>
            
            <TabsContent value="billing" className="mt-0">
              <BillingSettings />
            </TabsContent>
            
            <TabsContent value="notifications" className="mt-0">
              <NotificationSettings />
            </TabsContent>
            
            <TabsContent value="security" className="mt-0">
              <SecuritySettings />
            </TabsContent>
          </div>
        </Tabs>
      </div>
    </DashboardLayout>
  )
} 