"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { useState, useEffect } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { 
  Bell, 
  LogOut, 
  LayoutDashboard, 
  LineChart, 
  Settings, 
  CreditCard, 
  Target, 
  Check, 
  Globe, 
  ChevronDown, 
  User, 
  HelpCircle,
  Menu as MenuIcon,
  X
} from "lucide-react";
import { useAuth } from "@/shared/hooks/useAuth";
import { Badge } from "@/components/ui/badge";
import { 
  fetchNotificationList,
  markAllNotificationsRead,
  markNotificationRead,
  type Notification as APINotification,
  type NotificationListRequest
} from "@/shared/services/advertiser/advertiser-notifications";
import { useLanguage, SUPPORTED_LANGUAGES } from '@/shared/hooks/useLanguage';

const menuItems = [
  {
    titleKey: "dashboard",
    icon: LayoutDashboard,
    href: "/advertiser"
  },
  {
    titleKey: "campaigns",
    icon: Target,
    href: "/advertiser/campaigns"
  },
  {
    titleKey: "analytics",
    icon: LineChart,
    href: "/advertiser/statistics"
  },
  {
    titleKey: "settings",
    icon: Settings,
    href: "/advertiser/settings"
  }
];

interface MenuItemProps {
  item: {
    titleKey: string;
    icon: React.ElementType;
    href: string;
  };
}

function MenuItem({ item }: MenuItemProps) {
  const pathname = usePathname();
  const { t } = useLanguage();
  const isActive = pathname === item.href;
  const Icon = item.icon;

  return (
    <Link 
      href={item.href}
      className={`flex items-center px-4 py-2 text-sm font-medium rounded-full transition-all duration-200 ${
        isActive 
          ? "text-white bg-gradient-to-r from-blue-600 to-blue-700 shadow-md" 
          : "text-gray-300 hover:bg-gray-800 hover:text-white"
      }`}
    >
      <Icon className={`h-4 w-4 mr-2 ${isActive ? 'text-blue-200' : ''}`} />
      {t(`advertiser:navigation.menu.${item.titleKey}`)}
    </Link>
  );
}

// Local notification interface for header
interface HeaderNotification {
  id: string;
  title: string;
  message: string;
  time: string;
  read: boolean;
  link?: string;
}

export function TopNav() {
  const { t } = useLanguage();
  const { user, logout } = useAuth();
  const { currentLanguage, changeLanguage, getCurrentLanguageInfo } = useLanguage();
  const [showNotifications, setShowNotifications] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);
  const [currentPath, setCurrentPath] = useState("/advertiser");
  
  // API state for notifications
  const [notifications, setNotifications] = useState<HeaderNotification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [isLoadingNotifications, setIsLoadingNotifications] = useState(false);
  
  const selectedLang = getCurrentLanguageInfo();

  // Transform API notification to header format
  const transformAPINotification = (apiNotification: APINotification): HeaderNotification => {
    return {
      id: apiNotification.notification_id,
      title: apiNotification.title,
      message: apiNotification.message,
      time: new Date(apiNotification.created_at).toLocaleString('tr-TR'),
      read: apiNotification.isRead,
      link: "/advertiser/notifications"
    }
  }

  // Load notifications from API
  const loadNotifications = async () => {
    try {
      setIsLoadingNotifications(true);
      const params: NotificationListRequest = {
        limit: 10, // Header'da sadece son 10 bildirimi göster
        skip: 0
      }
      
      const response = await fetchNotificationList(params);
      
      if (response.status && response.result) {
        const transformedNotifications = response.result.data.map(transformAPINotification);
        setNotifications(transformedNotifications);
        setUnreadCount(transformedNotifications.filter(n => !n.read).length);
      }
    } catch (error) {
      console.error("Header notifications load error:", error);
    } finally {
      setIsLoadingNotifications(false);
    }
  }

  // Mark all notifications as read
  const markAllAsRead = async () => {
    try {
      const response = await markAllNotificationsRead();
      if (response.status) {
        setNotifications(prev => prev.map(n => ({ ...n, read: true })));
        setUnreadCount(0);
      }
    } catch (error) {
      console.error("Mark all as read error:", error);
    }
  }

  // Mark single notification as read
  const markAsRead = async (id: string) => {
    try {
      const response = await markNotificationRead({ notification_id: id });
      if (response.status) {
        setNotifications(prev => prev.map(n => 
          n.id === id ? { ...n, read: true } : n
        ));
        setUnreadCount(prev => Math.max(0, prev - 1));
      }
    } catch (error) {
      console.error("Mark as read error:", error);
    }
  }
  
  // Load notifications on component mount
  useEffect(() => {
    loadNotifications();
  }, []);

  // Scroll olduğunda üst çubuğu değiştir
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Bildirim dropdown'ını dışarı tıklayınca kapatma
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Node;
      const notificationPanel = document.getElementById('notification-panel');
      const notificationButton = document.getElementById('notification-button');
      
      if (
        notificationPanel && 
        !notificationPanel.contains(target) && 
        notificationButton && 
        !notificationButton.contains(target)
      ) {
        setShowNotifications(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className={`fixed top-0 left-0 right-0 h-16 ${scrolled ? 'bg-[#0a0a0a] shadow-md' : 'bg-[#111]'} border-b border-gray-800 z-50 transition-all duration-300`}>
      <div className="h-full px-8 flex items-center justify-between max-w-[1920px] mx-auto">
        <div className="flex items-center gap-12">
          <Link href="/advertiser" className="flex items-center gap-2 group">
            <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center transform group-hover:rotate-6 transition-all duration-300 shadow-lg">
              <span className="font-bold text-white">F</span>
            </div>
            <span className="font-medium text-white">Adnomio</span>
          </Link>
          
          {/* Desktop Menü */}
          <nav className="hidden lg:flex items-center gap-4">
            {menuItems.map((item, index) => (
              <MenuItem key={index} item={item} />
            ))}
          </nav>
        </div>
        
        {/* Mobil Menü Butonu */}
        <button 
          className="lg:hidden text-gray-300 hover:text-white"
          onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
        >
          {mobileMenuOpen ? <X className="h-6 w-6" /> : <MenuIcon className="h-6 w-6" />}
        </button>
        
        {/* Sağ Kısım */}
        <div className="hidden lg:flex items-center gap-4">
          {/* Bildirim */}
          <div className="relative">
            <button
              id="notification-button"
              className={`relative p-2 rounded-full ${showNotifications ? 'bg-gray-800' : 'hover:bg-gray-800'} transition-colors duration-200`}
              onClick={() => setShowNotifications(!showNotifications)}
            >
              <Bell className="w-5 h-5 text-gray-300" />
              {unreadCount > 0 && (
                <Badge className="absolute -top-1 -right-1 bg-red-500 hover:bg-red-600 w-5 h-5 flex items-center justify-center p-0 text-[10px]">
                  {unreadCount}
                </Badge>
              )}
            </button>
            
            {/* Bildirimler Dropdown */}
            {showNotifications && (
              <div 
                id="notification-panel"
                className="absolute right-0 top-full mt-2 w-80 bg-[#171717] border border-gray-700 rounded-lg shadow-xl overflow-hidden z-50"
              >
                <div className="p-3 border-b border-gray-700 flex justify-between items-center">
                  <h3 className="font-medium text-white">{t('advertiser:navigation.notifications.title')}</h3>
                  {unreadCount > 0 && (
                    <Button 
                      variant="ghost" 
                      size="sm"
                      className="text-xs text-blue-400 hover:text-blue-300 h-auto py-1"
                      onClick={markAllAsRead}
                    >
                      {t('advertiser:navigation.notifications.markAllRead')}
                    </Button>
                  )}
                </div>
                
                <div className="max-h-[300px] overflow-y-auto">
                  {isLoadingNotifications ? (
                    <div className="p-4 text-center text-gray-400 text-sm">
                      <div className="flex items-center justify-center gap-2">
                        <div className="h-4 w-4 border-2 border-gray-600 border-r-transparent rounded-full animate-spin"></div>
                        Yükleniyor...
                      </div>
                    </div>
                  ) : notifications.length === 0 ? (
                    <div className="p-4 text-center text-gray-400 text-sm">
                      {t('advertiser:navigation.notifications.empty')}
                    </div>
                  ) : (
                    notifications.map((notification) => (
                      <Link
                        key={notification.id}
                        href={notification.link || "/advertiser/notifications"}
                        onClick={() => markAsRead(notification.id)}
                      >
                        <div
                          className={`p-3 border-b border-gray-700 last:border-0 hover:bg-gray-800 transition-colors duration-150 cursor-pointer ${notification.read ? 'opacity-70' : ''}`}
                        >
                          <div className="flex items-start gap-2">
                            <div className={`mt-1 h-2 w-2 rounded-full flex-shrink-0 ${notification.read ? 'bg-gray-500' : 'bg-blue-500'}`} />
                            <div>
                              <h4 className="text-sm font-medium text-white">{notification.title}</h4>
                              <p className="text-xs text-gray-400 mt-1">{notification.message}</p>
                              <p className="text-xs text-gray-500 mt-1">{notification.time}</p>
                            </div>
                          </div>
                        </div>
                      </Link>
                    ))
                  )}
                </div>
                
                <div className="p-2 border-t border-gray-700">
                  <Link href="/advertiser/notifications">
                    <Button 
                      variant="ghost"
                      size="sm"
                      className="w-full justify-center text-blue-400 hover:text-blue-300 hover:bg-gray-800"
                    >
                      {t('advertiser:navigation.notifications.viewAll')}
                    </Button>
                  </Link>
                </div>
              </div>
            )}
          </div>
          
          {/* Bakiye */}
          <div className="group relative">
            <Link href="/advertiser/balance">
              <Button 
                className="bg-gradient-to-r from-indigo-600 to-blue-600 hover:from-indigo-700 hover:to-blue-700 text-white font-medium rounded-full px-6 transition-all duration-200 shadow-md group-hover:shadow-blue-900/20"
              >
                <div className="relative flex items-center gap-2">
                  <CreditCard className="h-4 w-4 text-blue-300" />
                  <span>
                    <span className="opacity-80 mr-1 text-sm">{t('advertiser:navigation.balance.title')}:</span> 
                    <span className="font-semibold">45₺</span>
                  </span>
                </div>
              </Button>
            </Link>
            
            {/* Bakiye Dropdown */}
            <div className="absolute right-0 top-full mt-2 w-56 bg-[#171717] border border-gray-700 rounded-lg shadow-xl overflow-hidden opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
              <div className="p-4">
                <div className="flex justify-between items-center mb-2">
                  <span className="text-sm text-gray-400">{t('advertiser:navigation.balance.current')}</span>
                  <span className="text-lg font-bold text-white">45₺</span>
                </div>
                <div className="flex justify-between items-center mb-4">
                  <span className="text-sm text-gray-400">{t('advertiser:navigation.balance.reserved')}</span>
                  <span className="text-sm text-gray-300">15₺</span>
                </div>
                <Link href="/advertiser/balance">
                  <Button className="w-full bg-gradient-to-r from-indigo-600 to-blue-600 hover:from-indigo-700 hover:to-blue-700">
                    {t('advertiser:navigation.balance.addFunds')}
                  </Button>
                </Link>
              </div>
              <div className="p-3 border-t border-gray-700 bg-gray-800/50">
                <div className="flex items-center gap-2">
                  <div className="w-8 h-8 rounded-full bg-gray-700 flex items-center justify-center">
                    <CreditCard className="h-4 w-4 text-gray-300" />
                  </div>
                  <div>
                    <p className="text-xs text-gray-400">{t('advertiser:navigation.balance.lastTransaction.title')}</p>
                    <p className="text-sm text-white">+100₺ <span className="text-gray-400 text-xs">{t('advertiser:navigation.balance.lastTransaction.daysAgo', { days: 2 })}</span></p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Dil Seçimi */}
          <div className="relative">
            <div className="group inline-block">
              <Button 
                variant="ghost" 
                size="sm" 
                className="text-gray-300 hover:text-white flex items-center gap-1.5 h-8 pl-2 pr-4 hover:bg-gray-800 rounded-full transition-colors duration-200" 
              >
                <Globe className="h-4 w-4 mr-0.5" />
                <span>
                  <span className="mr-1">{selectedLang.flag}</span>
                  <span className="mr-1">{currentLanguage.toUpperCase()}</span>
                </span>
                <ChevronDown className="h-3.5 w-3.5 opacity-70" />
              </Button>
              
              <div className="absolute right-0 z-50 min-w-[180px] invisible opacity-0 group-hover:visible group-hover:opacity-100 transition-all duration-200 transform -translate-y-1 group-hover:translate-y-0">
                <div className="h-2"></div>
                <div className="bg-[#171717] border border-gray-700 rounded-lg shadow-xl overflow-hidden">
                  <div className="p-1">
                    {Object.values(SUPPORTED_LANGUAGES).map((lang) => (
                      <button
                        key={lang.code}
                        className={`w-full text-left px-3 py-2 text-sm flex items-center justify-between rounded-md ${
                          currentLanguage === lang.code
                            ? "bg-gradient-to-r from-blue-600 to-blue-700 text-white font-medium"
                            : "text-gray-300 hover:bg-gray-800 hover:text-white"
                        }`}
                        onClick={() => changeLanguage(lang.code)}
                      >
                        <span className="flex items-center gap-2">
                          <span>{lang.flag}</span>
                          <span>{lang.name}</span>
                        </span>
                        {currentLanguage === lang.code && (
                          <Check className="h-3.5 w-3.5 text-blue-200" />
                        )}
                      </button>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          {/* Profil */}
          <div className="relative group">
            <div className="flex items-center gap-2 p-1.5 rounded-full hover:bg-gray-800 cursor-pointer transition-colors duration-200">
              <Avatar className="h-8 w-8 ring-2 ring-gray-700 group-hover:ring-blue-500 transition-all duration-300">
                <AvatarFallback className="bg-blue-700 text-white">
                  {user?.name?.[0] || "U"}
                </AvatarFallback>
                <AvatarImage src="/avatar.png" />
              </Avatar>
              <div className="hidden sm:block">
                <p className="text-sm text-white font-medium leading-none">{user?.name || t('common:user')}</p>
                <p className="text-xs text-gray-400">{t('advertiser:navigation.profile.role')}</p>
              </div>
              <ChevronDown className="h-3.5 w-3.5 text-gray-400 group-hover:text-white hidden sm:block" />
            </div>
            
            <div className="absolute right-0 top-full mt-2 w-64 bg-[#171717] border border-gray-700 rounded-lg shadow-xl overflow-hidden opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
              <div className="p-4 border-b border-gray-700">
                <div className="flex items-start gap-3">
                  <Avatar className="h-10 w-10">
                    <AvatarFallback className="bg-blue-700 text-white">{user?.name?.[0] || "U"}</AvatarFallback>
                    <AvatarImage src="/avatar.png" />
                  </Avatar>
                  <div>
                    <p className="text-white font-medium">{user?.name || t('common:user')}</p>
                    <p className="text-gray-400 text-xs mt-0.5">{user?.email || "<EMAIL>"}</p>
                    <div className="flex items-center gap-1 mt-1">
                      <Badge className="bg-blue-600/20 text-blue-400 hover:bg-blue-600/30 px-1.5 py-0 text-[10px] font-normal">
                        {t('advertiser:navigation.profile.professional')}
                      </Badge>
                      <Badge className="bg-gray-700/50 text-gray-300 hover:bg-gray-700 px-1.5 py-0 text-[10px] font-normal">
                        {t('advertiser:navigation.profile.id', { id: '24601' })}
                      </Badge>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="p-2 space-y-1">
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="w-full justify-start text-gray-300 hover:text-white hover:bg-gray-800"
                  asChild
                >
                  <Link href="/advertiser/settings">
                    <User className="w-4 h-4 mr-2 text-gray-400" />
                    {t('advertiser:navigation.profile.settings')}
                  </Link>
                </Button>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="w-full justify-start text-gray-300 hover:text-white hover:bg-gray-800"
                  asChild
                >
                  <Link href="#">
                    <HelpCircle className="w-4 h-4 mr-2 text-gray-400" />
                    {t('advertiser:navigation.profile.helpCenter')}
                  </Link>
                </Button>
              </div>
              
              <div className="p-2 border-t border-gray-700">
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="w-full justify-start text-red-400 hover:text-red-300 hover:bg-red-900/20"
                  onClick={logout}
                >
                  <LogOut className="w-4 h-4 mr-2" />
                  {t('advertiser:navigation.profile.logout')}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      {/* Mobil Menü */}
      {mobileMenuOpen && (
        <div className="lg:hidden fixed inset-0 z-40 bg-black/50 backdrop-blur-sm" onClick={() => setMobileMenuOpen(false)}>
          <div className="absolute left-0 right-0 bg-[#171717] border-b border-gray-700 shadow-xl" style={{ top: '56px' }} onClick={(e) => e.stopPropagation()}>
            {/* Mobile Menu Header: Logo (left), X (right) */}
            <div className="flex items-center justify-between px-4 py-3 border-b border-gray-700">
              <Link href="/advertiser" className="flex items-center gap-2 group" onClick={() => setMobileMenuOpen(false)}>
                <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center transform group-hover:rotate-6 transition-all duration-300 shadow-lg">
                  <span className="font-bold text-white">F</span>
                </div>
                <span className="font-medium text-white">Adnomio</span>
              </Link>
              <button
                className="text-gray-300 hover:text-white p-2 ml-2"
                onClick={() => setMobileMenuOpen(false)}
                aria-label="Kapat"
              >
                <X className="h-7 w-7" />
              </button>
            </div>
            <nav className="flex flex-col p-4 space-y-2">
              {menuItems.map((item, index) => {
                // Aktiflik kontrolü: pathname ile tam eşleşme
                const pathname = typeof window !== 'undefined' ? window.location.pathname : currentPath;
                const isActive = pathname === item.href;
                return (
                  <Link 
                    key={index}
                    href={item.href}
                    className={`flex items-center px-3 py-2.5 text-sm font-medium rounded-md transition-colors duration-200
                      ${isActive ? 'bg-gradient-to-r from-blue-600 to-blue-700 text-white shadow-md' : 'text-gray-300 hover:bg-gray-800 hover:text-white'}`}
                    onClick={() => setMobileMenuOpen(false)}
                    aria-current={isActive ? 'page' : undefined}
                  >
                    {item.icon && <item.icon className="h-4 w-4 mr-3 opacity-80" />}
                    {t(`advertiser:navigation.menu.${item.titleKey}`)}
                  </Link>
                );
              })}
            </nav>
            {/* Profil, Dil, Balance ve Bildirimler: UI/UX için mantıklı sıralama */}
            <div className="p-4 border-t border-gray-700 flex flex-col gap-4">
              {/* Profil Bilgisi ve Çıkış Yap Butonu */}
              <div className="flex items-center gap-3 justify-between">
                <div className="flex items-center gap-3">
                  <Avatar className="h-9 w-9 ring-2 ring-gray-700">
                    <AvatarFallback className="bg-blue-700 text-white">
                      {user?.name?.[0] || "U"}
                    </AvatarFallback>
                    <AvatarImage src="/avatar.png" />
                  </Avatar>
                  <div>
                    <p className="text-sm text-white font-medium leading-none">{user?.name || t('common:user')}</p>
                    <p className="text-xs text-gray-400">{t('advertiser:navigation.profile.role')}</p>
                  </div>
                </div>
                {/* Çıkış Yap Butonu */}
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="text-red-400 hover:text-red-300 hover:bg-red-900/20 px-3"
                  onClick={() => { setMobileMenuOpen(false); logout(); }}
                >
                  <LogOut className="w-4 h-4" />
                </Button>
              </div>
              {/* Dil Seçimi */}
              <div className="flex flex-col gap-2">
                <span className="text-xs text-gray-400 mb-1">{t('common:language')}</span>
                <div className="flex gap-2">
                  {Object.values(SUPPORTED_LANGUAGES).map((lang) => (
                    <button
                      key={lang.code}
                      className={`px-3 py-1 rounded-full text-sm font-medium border transition-colors duration-150 ${
                        currentLanguage === lang.code
                          ? 'bg-gradient-to-r from-blue-600 to-blue-700 text-white border-blue-700'
                          : 'text-gray-300 border-gray-700 hover:bg-gray-800 hover:text-white'
                      }`}
                      onClick={() => { changeLanguage(lang.code); setMobileMenuOpen(false); }}
                    >
                      <span className="mr-1">{lang.flag}</span>{lang.code.toUpperCase()}
                    </button>
                  ))}
                </div>
              </div>
            </div>
            {/* Balance ve Bildirimler en alta sabit */}
            <div className="w-full px-4 pb-4 pt-2 flex items-center gap-2 sticky bottom-0 bg-[#171717] z-10">
              <Link href="/advertiser/balance" className="flex-1">
                <Button
                  className="w-full bg-gradient-to-r from-indigo-600 to-blue-600 hover:from-indigo-700 hover:to-blue-700 text-white font-semibold flex items-center justify-center gap-2"
                >
                  <CreditCard className="h-5 w-5" />
                  <span>{t('advertiser:navigation.balance.title')}: 45₺</span>
                </Button>
              </Link>
              <Button
                variant="outline"
                size="icon"
                className="text-gray-300 border-gray-700"
                onClick={() => setShowNotifications(!showNotifications)}
              >
                <Bell className="h-5 w-5" />
              </Button>
            </div>
            {/* Mobil Bildirimler */}
            {showNotifications && (
              <div className="p-4 border-t border-gray-700 max-h-[300px] overflow-y-auto">
                <div className="flex justify-between items-center mb-3">
                  <h3 className="font-medium text-white">{t('advertiser:navigation.notifications.title')}</h3>
                  {unreadCount > 0 && (
                    <Button 
                      variant="ghost" 
                      size="sm"
                      className="text-xs text-blue-400 hover:text-blue-300 h-auto py-1"
                      onClick={markAllAsRead}
                    >
                      {t('advertiser:navigation.notifications.markAllRead')}
                    </Button>
                  )}
                </div>
                <div className="space-y-2">
                  {isLoadingNotifications ? (
                    <div className="p-4 text-center text-gray-400 text-sm">
                      <div className="flex items-center justify-center gap-2">
                        <div className="h-4 w-4 border-2 border-gray-600 border-r-transparent rounded-full animate-spin"></div>
                        Yükleniyor...
                      </div>
                    </div>
                  ) : notifications.length === 0 ? (
                    <div className="p-4 text-center text-gray-400 text-sm">
                      {t('advertiser:navigation.notifications.empty')}
                    </div>
                  ) : (
                    notifications.map((notification) => (
                      <Link
                        key={notification.id}
                        href={notification.link || "/advertiser/notifications"}
                        onClick={() => { markAsRead(notification.id); setMobileMenuOpen(false); }}
                      >
                        <div
                          className={`p-3 border-b border-gray-700 last:border-0 hover:bg-gray-800 transition-colors duration-150 cursor-pointer ${notification.read ? 'opacity-70' : ''}`}
                        >
                          <div className="flex items-start gap-2">
                            <div className={`mt-1 h-2 w-2 rounded-full flex-shrink-0 ${notification.read ? 'bg-gray-500' : 'bg-blue-500'}`} />
                            <div>
                              <h4 className="text-sm font-medium text-white">{notification.title}</h4>
                              <p className="text-xs text-gray-400 mt-1">{notification.message}</p>
                              <p className="text-xs text-gray-500 mt-1">{notification.time}</p>
                            </div>
                          </div>
                        </div>
                      </Link>
                    ))
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}