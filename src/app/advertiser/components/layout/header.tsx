"use client";

import { ReactNode } from "react";
import { Button } from "@/components/ui/button";
import { PlusCircle } from "lucide-react";

interface DashboardHeaderProps {
  title: string;
  description?: string;
  showButton?: boolean;
  buttonText?: string;
  buttonIcon?: ReactNode;
  buttonHref?: string;
  onButtonClick?: () => void;
}

export function DashboardHeader({
  title,
  description,
  showButton = false,
  buttonText = "Yeni O<PERSON>",
  buttonIcon = <PlusCircle className="h-4 w-4 mr-2" />,
  buttonHref,
  onButtonClick,
}: DashboardHeaderProps) {
  return (
    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
      <div>
        <h1 className="text-2xl sm:text-3xl font-bold tracking-tight text-white">{title}</h1>
        {description && (
          <p className="text-gray-400 mt-1 text-sm sm:text-base">{description}</p>
        )}
      </div>
      
      {showButton && (
        buttonHref ? (
          <Button 
            variant="default" 
            size="default"
            className="mt-4 sm:mt-0 bg-gradient-to-r from-indigo-600 to-blue-600 hover:from-indigo-700 hover:to-blue-700"
            asChild
          >
            <a href={buttonHref}>
              {buttonIcon}
              {buttonText}
            </a>
          </Button>
        ) : (
          <Button 
            variant="default" 
            size="default"
            className="mt-4 sm:mt-0 bg-gradient-to-r from-indigo-600 to-blue-600 hover:from-indigo-700 hover:to-blue-700"
            onClick={onButtonClick}
          >
            {buttonIcon}
            {buttonText}
          </Button>
        )
      )}
    </div>
  );
} 