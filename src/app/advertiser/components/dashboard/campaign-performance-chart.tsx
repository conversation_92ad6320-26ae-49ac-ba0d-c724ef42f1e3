"use client";

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Area, AreaChart } from "recharts";
import type { TooltipProps } from 'recharts';
import type { NameType, ValueType } from 'recharts/types/component/DefaultTooltipContent';
import { useLanguage } from "@/shared/hooks/useLanguage";

// Örnek veri - Bu kısım gerçek verilerle değiştirilecek
const last30DaysData = Array.from({ length: 30 }, (_, i) => ({
  date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toLocaleDateString('tr-TR', { day: 'numeric', month: 'short' }),
  clicks: Math.floor(Math.random() * 1000) + 500,
  ctr: (Math.random() * 5 + 2).toFixed(2),
  impressions: Math.floor(Math.random() * 10000) + 5000,
  prevClicks: Math.floor(Math.random() * 800) + 400,
  prevCtr: (Math.random() * 4 + 1.5).toFixed(2),
  prevImpressions: Math.floor(Math.random() * 8000) + 4000,
}));

const CustomTooltip = ({ active, payload, label }: TooltipProps<ValueType, NameType>) => {
  if (active && payload && payload.length) {
    return (
      <div className="bg-white p-4 rounded-xl border shadow-xl">
        <p className="text-gray-600 text-sm font-medium mb-3">{label}</p>
        <div className="space-y-2">
          {payload.map((entry, index: number) => (
            <div key={index} className="flex items-center justify-between gap-4">
              <span className="text-sm text-gray-600">
                {typeof entry.name === 'string' && entry.name.includes('prev') ? 'Önceki Dönem' : 'Bu Dönem'}
              </span>
              <span className="text-sm font-semibold" style={{ color: entry.color }}>
                {typeof entry.name === 'string' && entry.name.includes('ctr') ? `%${entry.value}` : entry.value?.toLocaleString() || '0'}
              </span>
            </div>
          ))}
        </div>
      </div>
    );
  }
  return null;
};

export function CampaignPerformanceChart() {
  const { t } = useLanguage();
  
  return (
    <Card className="col-span-4">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-xl font-semibold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">
              {t('advertiser:dashboard.charts.performanceMetrics.title')}
            </CardTitle>
            <CardDescription className="mt-1">{t('advertiser:dashboard.charts.performanceMetrics.description')}</CardDescription>
          </div>
          <div className="flex items-center gap-4 text-sm">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
              <span className="text-gray-600">{t('advertiser:dashboard.charts.performanceMetrics.currentPeriod')}</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 bg-blue-200 rounded-full"></div>
              <span className="text-gray-600">{t('advertiser:dashboard.charts.performanceMetrics.previousPeriod')}</span>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="clicks" className="space-y-4">
          <TabsList className="bg-gray-50 p-1">
            <TabsTrigger 
              value="clicks"
              className="data-[state=active]:bg-white data-[state=active]:text-blue-600 data-[state=active]:shadow-sm"
            >
              {t('advertiser:dashboard.charts.performanceMetrics.tabs.clicks')}
            </TabsTrigger>
            <TabsTrigger 
              value="ctr"
              className="data-[state=active]:bg-white data-[state=active]:text-blue-600 data-[state=active]:shadow-sm"
            >
              {t('advertiser:dashboard.charts.performanceMetrics.tabs.ctr')}
            </TabsTrigger>
            <TabsTrigger 
              value="impressions"
              className="data-[state=active]:bg-white data-[state=active]:text-blue-600 data-[state=active]:shadow-sm"
            >
              {t('advertiser:dashboard.charts.performanceMetrics.tabs.impressions')}
            </TabsTrigger>
          </TabsList>

          <TabsContent value="clicks" className="h-[400px] mt-0">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={last30DaysData}>
                <defs>
                  <linearGradient id="colorClicks" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#3B82F6" stopOpacity={0.1}/>
                    <stop offset="95%" stopColor="#3B82F6" stopOpacity={0}/>
                  </linearGradient>
                </defs>
                <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" vertical={false} />
                <XAxis 
                  dataKey="date" 
                  stroke="#94a3b8"
                  tick={{ fill: '#64748b', fontSize: 12 }}
                  tickLine={false}
                  axisLine={{ stroke: '#e2e8f0' }}
                />
                <YAxis 
                  stroke="#94a3b8"
                  tick={{ fill: '#64748b', fontSize: 12 }}
                  tickLine={false}
                  axisLine={false}
                />
                <Tooltip content={<CustomTooltip />} />
                <Area
                  type="monotone"
                  dataKey="prevClicks"
                  stroke="#93C5FD"
                  strokeWidth={2}
                  fill="url(#colorClicks)"
                  dot={false}
                />
                <Area
                  type="monotone"
                  dataKey="clicks"
                  stroke="#3B82F6"
                  strokeWidth={2}
                  fill="url(#colorClicks)"
                  dot={false}
                />
              </AreaChart>
            </ResponsiveContainer>
          </TabsContent>

          <TabsContent value="ctr" className="h-[400px] mt-0">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={last30DaysData}>
                <defs>
                  <linearGradient id="colorCtr" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#3B82F6" stopOpacity={0.1}/>
                    <stop offset="95%" stopColor="#3B82F6" stopOpacity={0}/>
                  </linearGradient>
                </defs>
                <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" vertical={false} />
                <XAxis 
                  dataKey="date" 
                  stroke="#94a3b8"
                  tick={{ fill: '#64748b', fontSize: 12 }}
                  tickLine={false}
                  axisLine={{ stroke: '#e2e8f0' }}
                />
                <YAxis 
                  stroke="#94a3b8"
                  tick={{ fill: '#64748b', fontSize: 12 }}
                  tickLine={false}
                  axisLine={false}
                />
                <Tooltip content={<CustomTooltip />} />
                <Area
                  type="monotone"
                  dataKey="prevCtr"
                  stroke="#93C5FD"
                  strokeWidth={2}
                  fill="url(#colorCtr)"
                  dot={false}
                />
                <Area
                  type="monotone"
                  dataKey="ctr"
                  stroke="#3B82F6"
                  strokeWidth={2}
                  fill="url(#colorCtr)"
                  dot={false}
                />
              </AreaChart>
            </ResponsiveContainer>
          </TabsContent>

          <TabsContent value="impressions" className="h-[400px] mt-0">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={last30DaysData}>
                <defs>
                  <linearGradient id="colorImpressions" x1="0" y1="0" x2="0" y2="1">
                    <stop offset="5%" stopColor="#3B82F6" stopOpacity={0.1}/>
                    <stop offset="95%" stopColor="#3B82F6" stopOpacity={0}/>
                  </linearGradient>
                </defs>
                <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" vertical={false} />
                <XAxis 
                  dataKey="date" 
                  stroke="#94a3b8"
                  tick={{ fill: '#64748b', fontSize: 12 }}
                  tickLine={false}
                  axisLine={{ stroke: '#e2e8f0' }}
                />
                <YAxis 
                  stroke="#94a3b8"
                  tick={{ fill: '#64748b', fontSize: 12 }}
                  tickLine={false}
                  axisLine={false}
                />
                <Tooltip content={<CustomTooltip />} />
                <Area
                  type="monotone"
                  dataKey="prevImpressions"
                  stroke="#93C5FD"
                  strokeWidth={2}
                  fill="url(#colorImpressions)"
                  dot={false}
                />
                <Area
                  type="monotone"
                  dataKey="impressions"
                  stroke="#3B82F6"
                  strokeWidth={2}
                  fill="url(#colorImpressions)"
                  dot={false}
                />
              </AreaChart>
            </ResponsiveContainer>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
} 