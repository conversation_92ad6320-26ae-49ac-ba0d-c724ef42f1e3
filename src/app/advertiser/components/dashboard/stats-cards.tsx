"use client";

import { Card, CardContent } from "@/components/ui/card";
import { Zap, Activity, Users, MousePointerClick } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { useEffect, useState } from "react";
import { fetchDashboardStats, DashboardStatsResponse } from "@/shared/services/advertiser/advertiser-dashboard-stats";
import { useLanguage } from "@/shared/hooks/useLanguage";

interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  color: "purple" | "blue" | "pink" | "cyan";
}

const StatCard = ({ title, value, icon, color }: StatCardProps) => {
  const colorStyles = {
    purple: "from-purple-50 to-purple-100 [&_svg]:text-purple-600 [&_.trend]:bg-purple-100 [&_.trend]:text-purple-700",
    blue: "from-blue-50 to-blue-100 [&_svg]:text-blue-600 [&_.trend]:bg-blue-100 [&_.trend]:text-blue-700",
    pink: "from-pink-50 to-pink-100 [&_svg]:text-pink-600 [&_.trend]:bg-pink-100 [&_.trend]:text-pink-700",
    cyan: "from-cyan-50 to-cyan-100 [&_svg]:text-cyan-600 [&_.trend]:bg-cyan-100 [&_.trend]:text-cyan-700"
  };

  return (
    <Card className={`hover:scale-[1.02] transition-all duration-200 shadow-lg bg-gradient-to-br ${colorStyles[color]}`}>
      <CardContent className="p-6">
        <div className="flex justify-between items-start mb-3">
          <div className="h-12 w-12 bg-white/20 rounded-xl flex items-center justify-center">
            {icon}
          </div>
        </div>
        <p className="text-sm font-medium text-gray-900/60 mb-1">{title}</p>
        <div className="flex items-end justify-between">
          <h3 className="text-2xl font-bold text-gray-900">{value}</h3>
        </div>
      </CardContent>
    </Card>
  );
};

export function StatsCards() {
  const { t } = useLanguage();
  const [stats, setStats] = useState<DashboardStatsResponse | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadStats = async () => {
      try {
        const response = await fetchDashboardStats();
        setStats(response);
      } catch (error) {
        console.error("Dashboard istatistikleri yüklenirken hata:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadStats();
  }, []);

  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {Array.from({ length: 4 }).map((_, i) => (
          <div key={i} className="h-[170px] w-full rounded-xl bg-gray-100 animate-pulse" />
        ))}
      </div>
    );
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <StatCard
        title={t('advertiser:dashboard.stats.totalCampaigns')}
        value={stats?.result?.total_campaign || 0}
        icon={<Zap className="h-6 w-6" />}
        color="purple"
      />
      <StatCard
        title={t('advertiser:dashboard.stats.activeCampaigns')}
        value={stats?.result?.active_campaign || 0}
        icon={<Activity className="h-6 w-6" />}
        color="blue"
      />
      <StatCard
        title={t('advertiser:dashboard.stats.totalEngagement')}
        value={stats?.result?.total_view || 0}
        icon={<Users className="h-6 w-6" />}
        color="pink"
      />
      <StatCard
        title={t('advertiser:dashboard.stats.totalClicks')}
        value={stats?.result?.total_click || 0}
        icon={<MousePointerClick className="h-6 w-6" />}
        color="cyan"
      />
    </div>
  );
} 