"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Plus, PlusCircle } from "lucide-react"
import { ReactNode } from "react"
import Link from "next/link";
import { useLanguage } from "@/shared/hooks/useLanguage";

interface DashboardHeaderProps {
  name: string;
  title: string;
  subtitle: string;
  children?: React.ReactNode;
  showButton?: boolean;
}

export function DashboardHeader({
  name,
  title,
  subtitle,
  children,
  showButton = true
}: DashboardHeaderProps) {
  const { t } = useLanguage();
  
  return (
    <div className="flex w-full flex-col gap-4 sm:flex-row sm:items-center sm:justify-between mb-8">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">
          {title ? title : t('advertiser:dashboard.header.welcome')} {name}
        </h1>
        <p className="text-muted-foreground mt-1">
          {subtitle ? subtitle : t('advertiser:dashboard.header.defaultSubtitle')}
        </p>
      </div>
      {showButton && (
        <Link href="/advertiser/campaigns/create">
        {children || (
          <Button size="lg" className="gap-2">
            <PlusCircle className="h-5 w-5" />
            <span>{t('advertiser:dashboard.header.newCampaign')}</span>
          </Button>
        )}
        </Link>
      )}
    </div>
  );
} 