"use client"

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Eye, Pencil, Plus } from "lucide-react"
import Link from "next/link"
import { useEffect, useState } from "react"
import { Skeleton } from "@/components/ui/skeleton"
import { fetchLastCampaigns, LastCampaign } from "@/shared/services/advertiser/advertiser-last-campaigns"
import { useLanguage } from "@/shared/hooks/useLanguage"
import { useRouter } from "next/navigation"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"

const statusMap = {
  ACTIVE: "Aktif",
  STOP: "<PERSON><PERSON><PERSON>ıldı",
  PLANNED: "<PERSON>land<PERSON>",
  FINISHED: "Tamamlandı"
};

const statusColors = {
  ACTIVE: "success" as const,
  STOP: "destructive" as const,
  PLANNED: "secondary" as const,
  FINISHED: "default" as const
};

export function CampaignTable() {
  const { t } = useLanguage();
  const router = useRouter();
  const [loading, setLoading] = useState(true)
  const [campaigns, setCampaigns] = useState<LastCampaign[]>([])
  const [selectedCampaignForDetails, setSelectedCampaignForDetails] = useState<LastCampaign | null>(null)

  useEffect(() => {
    const loadCampaigns = async () => {
      try {
        const response = await fetchLastCampaigns();
        if (response.status && response.result) {
          setCampaigns(response.result);
        }
      } catch (error) {
        console.error("Son kampanyalar yüklenirken hata:", error);
      } finally {
        setLoading(false);
      }
    };

    loadCampaigns();
  }, []);

  // Status değerini Türkçe olarak göster
  const getStatusText = (status: string | undefined | null) => {
    if (!status) return "Bilinmiyor";
    
    const statusStr = String(status).toUpperCase();
    switch (statusStr) {
      case "ACTIVE":
        return "Aktif"
      case "FINISHED":
        return "Tamamlandı"
      case "PLANNED":
        return "Planlandı"
      case "STOP":
        return "Duraklatıldı"
      default:
        return String(status)
    }
  }

  // Durum Badge renkleri
  const getStatusBadgeVariant = (status: string | undefined | null) => {
    if (!status) return "neutral";
    
    const statusStr = String(status).toUpperCase();
    switch (statusStr) {
      case "ACTIVE":
        return "success"
      case "FINISHED":
        return "neutral"
      case "PLANNED":
        return "warning"
      case "STOP":
        return "error"
      default:
        return "neutral"
    }
  }

  const getStatusBadge = (status: string) => {
    return (
      <Badge variant={getStatusBadgeVariant(status)}>
        {getStatusText(status)}
      </Badge>
    );
  };

  const handleViewCampaign = (campaign: LastCampaign) => {
    setSelectedCampaignForDetails(campaign);
  };

  const handleEditCampaign = (campaignId: string) => {
    router.push(`/advertiser/campaigns/edit/${campaignId}`);
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-semibold tracking-tight">
            {t('advertiser:dashboard.sections.campaigns.table.title')}
          </h2>
          <p className="text-sm text-muted-foreground">
            {t('advertiser:dashboard.sections.campaigns.table.description')}
          </p>
        </div>
        
        <Link href="/advertiser/campaigns/create">
          <Button className="flex items-center gap-1">
            <Plus className="h-4 w-4" />
            {t('advertiser:dashboard.sections.campaigns.create')}
          </Button>
        </Link>
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="text-left">{t('advertiser:dashboard.sections.campaigns.title', 'My Campaigns')}</TableHead>
              <TableHead className="text-left">{t('common:status.title', 'Status')}</TableHead>
              <TableHead className="text-left">{t('common:metrics.views', 'Views')}</TableHead>
              <TableHead className="text-left">{t('common:metrics.clicks', 'Clicks')}</TableHead>
              <TableHead className="text-left">{t('common:metrics.ctr', 'CTR')}</TableHead>
              <TableHead className="text-left">{t('advertiser:dashboard.sections.campaigns.budget', 'Budget')}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {loading ? (
              Array.from({ length: 4 }).map((_, i) => (
                <TableRow key={i}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <Skeleton className="h-2 w-2 rounded-full" />
                      <Skeleton className="h-4 w-32" />
                    </div>
                  </TableCell>
                  <TableCell>
                    <Skeleton className="h-6 w-16 rounded-full" />
                  </TableCell>
                  <TableCell className="text-left">
                    <Skeleton className="h-4 w-16 ml-auto" />
                  </TableCell>
                  <TableCell className="text-left">
                    <Skeleton className="h-4 w-16 ml-auto" />
                  </TableCell>
                  <TableCell className="text-left">
                    <Skeleton className="h-4 w-16 ml-auto" />
                  </TableCell>
                  <TableCell className="text-left">
                    <Skeleton className="h-4 w-16 ml-auto" />
                  </TableCell>
                  <TableCell className="text-left">
                    <div className="flex items-center justify-end gap-2">
                      <Skeleton className="h-4 w-8 rounded-md" />
                      <Skeleton className="h-4 w-8 rounded-md" />
                    </div>
                  </TableCell>
                </TableRow>
              ))
            ) : campaigns.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-8">
                  <p className="text-lg font-medium text-gray-600">
                    {t('advertiser:dashboard.sections.campaigns.table.empty')}
                  </p>
                  <p className="text-sm text-gray-500 mt-1">
                    {t('advertiser:dashboard.sections.campaigns.table.createFirst')}
                  </p>
                </TableCell>
              </TableRow>
            ) : (
              campaigns.map((campaign) => (
                <TableRow key={campaign._id}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <div className={`h-2 w-2 rounded-full ${
                        campaign.status === "ACTIVE"
                          ? "bg-green-500"
                          : campaign.status === "FINISHED"
                          ? "bg-blue-500"
                          : "bg-orange-500"
                      }`} />
                      <span className="font-medium">
                        {campaign.title}
                      </span>
                    </div>
                  </TableCell>
                  <TableCell>
                    {getStatusBadge(campaign.status)}
                  </TableCell>
                  <TableCell className="text-left font-medium">
                    {campaign.views ? campaign.views.toLocaleString() : '0'}
                  </TableCell>
                  <TableCell className="text-left font-medium">
                    {campaign.clicks ? campaign.clicks.toLocaleString() : '0'}
                  </TableCell>
                  <TableCell className="text-left font-medium">
                    {campaign.views && campaign.clicks 
                      ? ((campaign.clicks / campaign.views) * 100).toFixed(2) + '%'
                      : '0%'}
                  </TableCell>
                  <TableCell className="text-left font-medium">
                    ₺{campaign.campaign_budget ? campaign.campaign_budget.toLocaleString() : '0'}
                  </TableCell>
                  <TableCell className="text-left">
                    <div className="flex items-center justify-end gap-2">
                      <Button variant="ghost" size="icon" className="h-8 w-8" title={t('advertiser:dashboard.sections.campaigns.table.actions.view')} onClick={() => handleViewCampaign(campaign)}>
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="icon" className="h-8 w-8" title={t('advertiser:dashboard.sections.campaigns.table.actions.edit')} onClick={() => handleEditCampaign(campaign.campaign_id)}>
                        <Pencil className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
      <div className="flex items-center justify-between py-4">
        <p className="text-sm text-muted-foreground">
          Toplam {campaigns.length} kampanya listeleniyor
        </p>
        <Link href="/advertiser/campaigns">
          <Button variant="outline">
            {t('advertiser:dashboard.sections.campaigns.table.viewAll')}
          </Button>
        </Link>
      </div>

      {/* Campaign Details Modal */}
      <Dialog open={!!selectedCampaignForDetails} onOpenChange={() => setSelectedCampaignForDetails(null)}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold">
              {selectedCampaignForDetails?.campaign_title}
            </DialogTitle>
          </DialogHeader>
          
          {selectedCampaignForDetails && (
            <div className="grid gap-6">
              {/* Basic Info */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Kampanya Bilgileri</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <span className="text-sm font-medium text-muted-foreground">Durum</span>
                      <div className="mt-1">
                        {getStatusBadge(selectedCampaignForDetails.status)}
                      </div>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-muted-foreground">Bütçe</span>
                      <p className="text-lg font-semibold">
                        ₺{selectedCampaignForDetails.campaign_budget?.toLocaleString() || '0'}
                      </p>
                    </div>
                  </div>
                  
                  <Separator />
                  
                  <div className="grid grid-cols-3 gap-4">
                    <div>
                      <span className="text-sm font-medium text-muted-foreground">Görüntülenme</span>
                      <p className="text-lg font-semibold">
                        {selectedCampaignForDetails.views?.toLocaleString() || '0'}
                      </p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-muted-foreground">Tıklama</span>
                      <p className="text-lg font-semibold">
                        {selectedCampaignForDetails.clicks?.toLocaleString() || '0'}
                      </p>
                    </div>
                    <div>
                      <span className="text-sm font-medium text-muted-foreground">CTR</span>
                      <p className="text-lg font-semibold">
                        {selectedCampaignForDetails.views && selectedCampaignForDetails.clicks 
                          ? ((selectedCampaignForDetails.clicks / selectedCampaignForDetails.views) * 100).toFixed(2) + '%'
                          : '0%'}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Actions */}
              <div className="flex gap-2 justify-end">
                <Button variant="outline" onClick={() => setSelectedCampaignForDetails(null)}>
                  Kapat
                </Button>
                <Button onClick={() => handleEditCampaign(selectedCampaignForDetails.campaign_id)}>
                  Düzenle
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  )
} 