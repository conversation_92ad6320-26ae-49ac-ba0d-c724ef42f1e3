"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { ArrowDown, ArrowUp, CalendarIcon, ChevronsUpDown, Download, ExternalLink, Filter, Search, SlidersHorizontal, ChevronLeft, ChevronRight, FileDown, CreditCard, Building, Wallet, Eye, Info } from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { Label } from "@/components/ui/label";
import { fetchTransactionList, Transaction, TransactionStatus, TransactionType } from "@/shared/services/advertiser/advertiser-transaction-list";
import { useToast } from "@/components/ui/use-toast";
import { useLanguage } from "@/shared/hooks/useLanguage";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";

export function TransactionHistory() {
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<TransactionStatus | "all">("all");
  const [typeFilter, setTypeFilter] = useState<TransactionType | "all">("all");
  const [dateFilter, setDateFilter] = useState<Date | undefined>(undefined);
  const [pageSize, setPageSize] = useState(5);
  const [currentPage, setCurrentPage] = useState(1);
  const [isExporting, setIsExporting] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [totalTransactions, setTotalTransactions] = useState(0);
  const [selectedTransaction, setSelectedTransaction] = useState<Transaction | null>(null);
  const { toast } = useToast();
  const { t } = useLanguage();

  const fetchTransactions = async () => {
    setIsLoading(true);
    try {
      const response = await fetchTransactionList({
        limit: pageSize,
        skip: (currentPage - 1) * pageSize,
        search: searchQuery || undefined,
        status: statusFilter !== "all" ? statusFilter : undefined,
        type: typeFilter !== "all" ? typeFilter : undefined,
        date: dateFilter ? dateFilter.getTime() : undefined
      });

      console.log('API Response:', response);

      if (response.status && response.result) {
        setTransactions(response.result.data);
        setTotalTransactions(response.result.stats.total);
      } else {
        toast({
          title: "Hata",
          description: response.desc || "İşlem geçmişi alınırken bir hata oluştu",
          variant: "destructive"
        });
      }
    } catch (error) {
      console.error('API Error:', error);
      toast({
        title: "Hata",
        description: "İşlem geçmişi alınırken bir hata oluştu",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchTransactions();
  }, [currentPage, pageSize, searchQuery, statusFilter, typeFilter, dateFilter]);

  // Tarih formatı
  const formatDate = (timestamp: number) => {
    return new Intl.DateTimeFormat("tr-TR", { 
      day: "2-digit", 
      month: "2-digit", 
      year: "numeric", 
      hour: "2-digit", 
      minute: "2-digit" 
    }).format(new Date(timestamp));
  };
  
  // İşlem türüne göre rozet rengi ve metin
  const getTypeBadge = (type: TransactionType) => {
    switch (type) {
      case "BALANCE_ADD":
        return <Badge className="bg-emerald-100 text-emerald-600 hover:bg-emerald-200">
          <ArrowDown className="h-3 w-3 mr-1" /> {t('advertiser:balance.types.deposit')}
        </Badge>;
      case "PAYMENT":
        return <Badge className="bg-blue-100 text-blue-600 hover:bg-blue-200">
          <ExternalLink className="h-3 w-3 mr-1" /> {t('advertiser:balance.types.payment')}
        </Badge>;
      case "RETURN":
        return <Badge className="bg-violet-100 text-violet-600 hover:bg-violet-200">
          <ArrowDown className="h-3 w-3 mr-1" /> {t('advertiser:balance.types.refund')}
        </Badge>;
      default:
        return null;
    }
  };
  
  // İşlem durumuna göre rozet rengi ve metin
  const getStatusBadge = (status: TransactionStatus) => {
    // Status değerini Türkçe olarak göster
    const getStatusText = (status: string) => {
      const statusStr = String(status).toUpperCase();
      switch (statusStr) {
        case "COMPLETED":
          return "Tamamlandı"
        case "PENDING":
          return "Beklemede"
        case "FAILED":
          return "Başarısız"
        case "REJECTED":
          return "Reddedildi"
        case "PROCESSING":
          return "İşlemde"
        default:
          return String(status)
      }
    }

    // Status Badge variant'ı
    const getStatusVariant = (status: string) => {
      const statusStr = String(status).toUpperCase();
      switch (statusStr) {
        case "COMPLETED":
          return "success"
        case "PENDING":
          return "warning"
        case "FAILED":
        case "REJECTED":
          return "error"
        case "PROCESSING":
          return "info"
        default:
          return "neutral"
      }
    }

    return (
      <Badge variant={getStatusVariant(status)}>
        {getStatusText(status)}
      </Badge>
    );
  };
  
  const handleExport = () => {
    setIsExporting(true);
    
    // Gerçek uygulamada, burada CSV veya Excel dosyası oluşturma işlemi yapılır
    setTimeout(() => {
      setIsExporting(false);
      toast({
        title: "Başarılı",
        description: t('advertiser:balance.history.exportSuccess'),
      });
    }, 1500);
  };

  const totalPages = Math.ceil(totalTransactions / pageSize);

  console.log('transactions: ',transactions);
  
  // Ödeme yöntemini Türkçe olarak göster
  const getPaymentMethodText = (method: string) => {
    switch (method) {
      case "CREDIT_CARD":
        return "Kredi Kartı"
      case "DEBIT_CARD":
        return "Banka Kartı"
      case "BANK_TRANSFER":
        return "Banka Havalesi"
      case "WALLET":
        return "Cüzdan"
      case "PAYPAL":
        return "PayPal"
      default:
        return method
    }
  }

  // Ödeme yöntemi ikonu
  const getPaymentMethodIcon = (method: string) => {
    switch (method) {
      case "CREDIT_CARD":
      case "DEBIT_CARD":
        return <CreditCard className="h-4 w-4 text-blue-600" />
      case "BANK_TRANSFER":
        return <Building className="h-4 w-4 text-green-600" />
      case "WALLET":
        return <Wallet className="h-4 w-4 text-purple-600" />
      default:
        return <CreditCard className="h-4 w-4 text-gray-600" />
    }
  }

  // İşlem türü açıklaması
  const getTypeDescription = (type: string) => {
    switch (type) {
      case "BALANCE_ADD":
        return "Hesaba bakiye ekleme işlemi"
      case "PAYMENT":
        return "Kampanya ödemesi"
      case "RETURN":
        return "İade işlemi"
      case "WITHDRAWAL":
        return "Para çekme işlemi"
      default:
        return "İşlem"
    }
  }

  return (
    <Card className="border-gray-200 bg-white shadow-md">
      <CardHeader className="pb-3">
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
          <div>
            <CardTitle className="text-gray-900">{t('advertiser:balance.history.title')}</CardTitle>
            <CardDescription>{t('advertiser:balance.transactionDescription')}</CardDescription>
          </div>
          
          <div className="flex flex-wrap items-center gap-2">
            <Popover>
              <PopoverTrigger asChild>
                <Button variant="outline" size="sm" className="h-8 border-gray-300 text-gray-600 hover:text-gray-900">
                  <SlidersHorizontal className="h-3.5 w-3.5 mr-2" />
                  {t('advertiser:balance.history.actions.filter')}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-80 p-4 bg-white border-gray-200" align="end">
                <div className="space-y-4">
                  <h4 className="font-medium text-sm text-gray-900">{t('advertiser:balance.history.filters.title')}</h4>
                  <div>
                    <Label className="text-xs font-medium text-gray-700">{t('advertiser:balance.history.filters.status')}</Label>
                    <Select value={statusFilter} onValueChange={(value: TransactionStatus | "all") => setStatusFilter(value)}>
                      <SelectTrigger className="bg-white border-gray-200">
                        <SelectValue placeholder={t('advertiser:balance.history.filters.status.all')} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">{t('advertiser:balance.history.filters.status.all')}</SelectItem>
                        <SelectItem value="COMPLETED">{t('advertiser:balance.status.completed')}</SelectItem>
                        <SelectItem value="PENDING">{t('advertiser:balance.status.pending')}</SelectItem>
                        <SelectItem value="FAILED">{t('advertiser:balance.status.failed')}</SelectItem>
                        <SelectItem value="REJECTED">{t('advertiser:balance.status.rejected')}</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label className="text-xs font-medium text-gray-700">{t('advertiser:balance.history.filters.type')}</Label>
                    <Select value={typeFilter} onValueChange={(value: TransactionType | "all") => setTypeFilter(value)}>
                      <SelectTrigger className="bg-white border-gray-200">
                        <SelectValue placeholder={t('advertiser:balance.history.filters.type.all')} />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">{t('advertiser:balance.history.filters.type.all')}</SelectItem>
                        <SelectItem value="BALANCE_ADD">{t('advertiser:balance.types.deposit')}</SelectItem>
                        <SelectItem value="PAYMENT">{t('advertiser:balance.types.payment')}</SelectItem>
                        <SelectItem value="RETURN">{t('advertiser:balance.types.refund')}</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label className="text-xs font-medium text-gray-700">{t('advertiser:balance.date')}</Label>
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button variant="outline" className="w-full justify-start text-left font-normal mt-1 bg-white border-gray-200">
                          <CalendarIcon className="mr-2 h-4 w-4" />
                          {dateFilter ? formatDate(dateFilter.getTime()) : t('advertiser:balance.history.filters.date.selectDate')}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={dateFilter}
                          onSelect={setDateFilter}
                          initialFocus
                          className="rounded-md bg-white"
                        />
                      </PopoverContent>
                    </Popover>
                  </div>
                  <div className="flex justify-between pt-2">
                    <Button 
                      variant="ghost" 
                      onClick={() => {
                        setStatusFilter("all");
                        setTypeFilter("all");
                        setDateFilter(undefined);
                      }}
                      className="text-gray-500 hover:text-gray-700"
                    >
                      {t('advertiser:balance.history.filters.clear')}
                    </Button>
                    <Button className="bg-blue-600 hover:bg-blue-700">{t('advertiser:balance.history.filters.apply')}</Button>
                  </div>
                </div>
              </PopoverContent>
            </Popover>
            
            <Button 
              variant="outline" 
              size="sm" 
              className="h-8 border-gray-300 text-gray-600 hover:text-gray-900"
              onClick={handleExport}
              disabled={isExporting}
            >
              {isExporting ? (
                <>
                  <div className="h-3.5 w-3.5 animate-spin rounded-full border-2 border-gray-500 border-t-transparent mr-2"></div>
                  {t('advertiser:balance.processing')}
                </>
              ) : (
                <>
                  <FileDown className="h-3.5 w-3.5 mr-2" />
                  {t('advertiser:balance.history.actions.export')}
                </>
              )}
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <div className="flex flex-col sm:flex-row justify-between gap-3">
          <div className="flex-1 relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-400" />
            <Input
              type="text"
              placeholder={t('advertiser:balance.history.filters.search')}
              className="pl-9 bg-white border-gray-200 focus:border-blue-500 text-gray-900"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          
          <div className="flex items-center gap-2">
            <Select 
              value={String(pageSize)} 
              onValueChange={(value) => {
                setPageSize(Number(value));
                setCurrentPage(1);
              }}
            >
              <SelectTrigger className="w-[110px] bg-white border-gray-200 h-9">
                <SelectValue placeholder={t('advertiser:balance.pageSize.label')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="5">{t('advertiser:balance.pageSize.5records')}</SelectItem>
                <SelectItem value="10">{t('advertiser:balance.pageSize.10records')}</SelectItem>
                <SelectItem value="20">{t('advertiser:balance.pageSize.20records')}</SelectItem>
                <SelectItem value="50">{t('advertiser:balance.pageSize.50records')}</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        
        {/* Tablo */}
        <div className="rounded-md border border-gray-200 overflow-hidden">
          <Table>
            <TableHeader className="bg-gray-50">
              <TableRow className="hover:bg-gray-100 border-gray-200">
                <TableHead className="text-gray-700 font-medium">İşlem Detayı</TableHead>
                <TableHead className="text-gray-700 font-medium">Tarih & Saat</TableHead>
                <TableHead className="text-gray-700 font-medium">Tür & Açıklama</TableHead>
                <TableHead className="text-gray-700 font-medium text-right">Tutar</TableHead>
                <TableHead className="text-gray-700 font-medium">Durum</TableHead>
                <TableHead className="text-gray-700 font-medium">Ödeme Yöntemi</TableHead>
                <TableHead className="text-gray-700 font-medium text-center">İşlemler</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading ? (
                <TableRow className="hover:bg-gray-50 border-gray-200">
                  <TableCell colSpan={7} className="h-24 text-center">
                    <div className="flex items-center justify-center">
                      <div className="h-6 w-6 animate-spin rounded-full border-2 border-blue-500 border-t-transparent"></div>
                      <span className="ml-2 text-gray-500">{t('advertiser:balance.loading')}</span>
                    </div>
                  </TableCell>
                </TableRow>
              ) : !transactions || transactions.length === 0 ? (
                <TableRow className="hover:bg-gray-50 border-gray-200">
                  <TableCell colSpan={7} className="h-24 text-center text-gray-500">
                    {t('advertiser:balance.history.table.noResults')}
                  </TableCell>
                </TableRow>
              ) : (
                transactions.map((transaction) => (
                  <TableRow key={transaction._id} className="hover:bg-gray-50 border-gray-200">
                    <TableCell className="py-4">
                      <div className="space-y-1">
                        <div className="font-mono text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded inline-block">
                          #{transaction.transaction_id}
                        </div>
                        <div className="text-sm text-gray-900 font-medium">
                          {transaction.name} {transaction.surname}
                        </div>
                        <div className="text-xs text-gray-500">
                          {transaction.email}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="py-4">
                      <div className="space-y-1">
                        <div className="text-sm text-gray-900 font-medium">
                          {new Intl.DateTimeFormat("tr-TR", { 
                            day: "2-digit", 
                            month: "2-digit", 
                            year: "numeric"
                          }).format(new Date(transaction.created_at))}
                        </div>
                        <div className="text-xs text-gray-500">
                          {new Intl.DateTimeFormat("tr-TR", { 
                            hour: "2-digit", 
                            minute: "2-digit",
                            second: "2-digit"
                          }).format(new Date(transaction.created_at))}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="py-4">
                      <div className="space-y-2">
                        {getTypeBadge(transaction.type)}
                        <div className="text-xs text-gray-600">
                          {transaction.description || getTypeDescription(transaction.type)}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="py-4 text-right">
                      <div className="space-y-1">
                        <div className={`font-semibold ${
                          transaction.amount > 0 
                            ? "text-emerald-600" 
                            : "text-red-600"
                        }`}>
                          {transaction.amount > 0 ? "+" : ""}{transaction.amount.toLocaleString('tr-TR')}₺
                        </div>
                        <div className="text-xs text-gray-500">
                          {transaction.amount > 0 ? "Gelen" : "Giden"}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="py-4">
                      {getStatusBadge(transaction.status)}
                    </TableCell>
                    <TableCell className="py-4">
                      <div className="flex items-center gap-2">
                        {getPaymentMethodIcon(transaction.payment_method)}
                        <span className="text-sm text-gray-700">
                          {getPaymentMethodText(transaction.payment_method)}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell className="py-4 text-center">
                      <Button 
                        variant="ghost" 
                        size="sm" 
                        className="h-8 w-8 p-0"
                        onClick={() => setSelectedTransaction(transaction)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
        
        {/* Sayfalama Kontrolleri */}
        {totalPages > 1 && (
          <div className="flex justify-between items-center">
            <div className="text-gray-500 text-sm">
              {t('advertiser:balance.history.table.paginationSummary', {
                totalCount: totalTransactions,
                start: (currentPage - 1) * pageSize + 1,
                end: Math.min(currentPage * pageSize, totalTransactions)
              })}
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="icon"
                className="h-8 w-8 border-gray-200"
                onClick={() => setCurrentPage(currentPage - 1)}
                disabled={currentPage === 1}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
              {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                <Button
                  key={page}
                  variant={page === currentPage ? "default" : "outline"}
                  size="sm"
                  className={`h-8 w-8 p-0 ${
                    page === currentPage
                      ? "bg-blue-600 hover:bg-blue-700 text-white"
                      : "bg-white text-gray-600 border-gray-200"
                  }`}
                  onClick={() => setCurrentPage(page)}
                >
                  {page}
                </Button>
              ))}
              <Button
                variant="outline"
                size="icon"
                className="h-8 w-8 border-gray-200"
                onClick={() => setCurrentPage(currentPage + 1)}
                disabled={currentPage === totalPages}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}
        
        {/* Toplam Bilgisi */}
        <div className="flex justify-between items-center p-3 bg-gray-50 rounded-md border border-gray-200">
          <div className="text-gray-700 text-sm">
            {t('advertiser:balance.history.table.totalCount', { count: totalTransactions })} işlem
          </div>
          
          <div className="flex items-center gap-4">
            <div className="text-sm">
              <span className="text-gray-600 mr-2">{t('advertiser:balance.metrics.totalDeposited')}:</span>
              <span className="text-emerald-600 font-medium">
                +{
                  (transactions || [])
                    .filter(t => t.amount > 0)
                    .reduce((sum, t) => sum + t.amount, 0)
                    .toFixed(2)
                }₺
              </span>
            </div>
            
            <div className="text-sm">
              <span className="text-gray-600 mr-2">{t('advertiser:balance.metrics.spent')}:</span>
              <span className="text-blue-600 font-medium">
                {
                  (transactions || [])
                    .filter(t => t.amount < 0)
                    .reduce((sum, t) => sum + t.amount, 0)
                    .toFixed(2)
                }₺
              </span>
            </div>
          </div>
        </div>
      </CardContent>

      {/* Transaction Detail Modal */}
      <Dialog open={!!selectedTransaction} onOpenChange={() => setSelectedTransaction(null)}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>İşlem Detayları</DialogTitle>
          </DialogHeader>
          
          {selectedTransaction && (
            <div className="space-y-6">
              {/* Header */}
              <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                <div>
                  <div className="text-lg font-semibold">
                    {selectedTransaction.name} {selectedTransaction.surname}
                  </div>
                  <div className="text-sm text-gray-500">
                    {selectedTransaction.email}
                  </div>
                </div>
                <div className="text-right">
                  <div className={`text-2xl font-bold ${
                    selectedTransaction.amount > 0 
                      ? "text-emerald-600" 
                      : "text-red-600"
                  }`}>
                    {selectedTransaction.amount > 0 ? "+" : ""}{selectedTransaction.amount.toLocaleString('tr-TR')}₺
                  </div>
                  {getStatusBadge(selectedTransaction.status)}
                </div>
              </div>

              {/* Transaction Details */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div>
                    <Label className="text-sm font-medium text-gray-700">İşlem ID</Label>
                    <div className="font-mono text-sm bg-gray-100 p-2 rounded mt-1">
                      #{selectedTransaction.transaction_id}
                    </div>
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium text-gray-700">İşlem Türü</Label>
                    <div className="mt-1">
                      {getTypeBadge(selectedTransaction.type)}
                    </div>
                  </div>

                  <div>
                    <Label className="text-sm font-medium text-gray-700">Ödeme Yöntemi</Label>
                    <div className="flex items-center gap-2 mt-1">
                      {getPaymentMethodIcon(selectedTransaction.payment_method)}
                      <span className="text-sm">
                        {getPaymentMethodText(selectedTransaction.payment_method)}
                      </span>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <div>
                    <Label className="text-sm font-medium text-gray-700">İşlem Tarihi</Label>
                    <div className="text-sm mt-1">
                      {new Intl.DateTimeFormat("tr-TR", { 
                        day: "2-digit", 
                        month: "long", 
                        year: "numeric",
                        hour: "2-digit", 
                        minute: "2-digit",
                        second: "2-digit"
                      }).format(new Date(selectedTransaction.created_at))}
                    </div>
                  </div>

                  <div>
                    <Label className="text-sm font-medium text-gray-700">Kullanıcı ID</Label>
                    <div className="font-mono text-sm text-gray-600 mt-1">
                      {selectedTransaction.user_id}
                    </div>
                  </div>

                  <div>
                    <Label className="text-sm font-medium text-gray-700">Durum</Label>
                    <div className="mt-1">
                      {getStatusBadge(selectedTransaction.status)}
                    </div>
                  </div>
                </div>
              </div>

              {/* Description */}
              {selectedTransaction.description && (
                <div>
                  <Label className="text-sm font-medium text-gray-700">Açıklama</Label>
                  <div className="text-sm text-gray-600 bg-gray-50 p-3 rounded mt-1">
                    {selectedTransaction.description}
                  </div>
                </div>
              )}

              {/* Actions */}
              <div className="flex justify-end pt-4 border-t">
                <Button 
                  variant="outline" 
                  onClick={() => setSelectedTransaction(null)}
                >
                  Kapat
                </Button>
              </div>
            </div>
          )}
        </DialogContent>
      </Dialog>
    </Card>
  );
} 