"use client";

import { useState } from "react";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { CreditCard, Wallet, Landmark, PlusCircle, Check, MoreHorizontal, Shield, X, Building, Edit, Trash, CheckCircle, Trash2 } from "lucide-react";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { useLanguage } from "@/shared/hooks/useLanguage";

interface PaymentMethod {
  id: string;
  type: "card" | "bank" | "wallet";
  title: string;
  description: string;
  icon: React.ElementType;
  lastDigits?: string;
  bankName?: string;
  expiryDate?: string;
  isPrimary?: boolean;
}

export function PaymentMethods() {
  const { t } = useLanguage();
  const [isAddingNew, setIsAddingNew] = useState(false);
  const [paymentType, setPaymentType] = useState<"card" | "bank">("card");
  const [cardNumber, setCardNumber] = useState("");
  const [cardName, setCardName] = useState("");
  const [expiryDate, setExpiryDate] = useState("");
  const [cvv, setCvv] = useState("");
  const [bankName, setBankName] = useState("");
  const [ibanNumber, setIbanNumber] = useState("");
  
  // Örnek ödeme yöntemleri
  const [methods, setMethods] = useState<PaymentMethod[]>([
    {
      id: "card1",
      type: "card",
      title: "Visa",
      description: "**** 4582",
      icon: CreditCard,
      lastDigits: "4582",
      expiryDate: "12/25",
      isPrimary: true,
    },
    {
      id: "card2",
      type: "card",
      title: "Mastercard",
      description: "**** 3299",
      icon: CreditCard,
      lastDigits: "3299",
      expiryDate: "09/26",
    },
    {
      id: "bank1",
      type: "bank",
      title: "Garanti Bankası",
      description: "Havale / EFT",
      icon: Landmark,
      bankName: "Garanti",
    },
  ]);
  
  const handleAddNewMethod = () => {
    setIsAddingNew(true);
  };

  const handleCloseDialog = () => {
    setIsAddingNew(false);
    resetForm();
  };

  const resetForm = () => {
    setPaymentType("card");
    setCardNumber("");
    setCardName("");
    setExpiryDate("");
    setCvv("");
    setBankName("");
    setIbanNumber("");
  };

  const handleSubmit = () => {
    // Yeni ödeme yöntemi oluştur
    if (paymentType === "card" && cardNumber && cardName && expiryDate) {
      const lastFourDigits = cardNumber.slice(-4);
      const newMethod: PaymentMethod = {
        id: `card-${Date.now()}`,
        type: "card",
        title: cardName.split(" ")[0], // İlk adı al
        description: `**** ${lastFourDigits}`,
        icon: CreditCard,
        lastDigits: lastFourDigits,
        expiryDate: expiryDate,
        isPrimary: methods.length === 0 // İlk ekleniyorsa varsayılan olarak ayarla
      };
      
      setMethods(prev => [...prev, newMethod]);
    } else if (paymentType === "bank" && bankName && ibanNumber) {
      const newMethod: PaymentMethod = {
        id: `bank-${Date.now()}`,
        type: "bank",
        title: bankName,
        description: "Havale / EFT",
        icon: Landmark,
        bankName: bankName,
        isPrimary: methods.length === 0 // İlk ekleniyorsa varsayılan olarak ayarla
      };
      
      setMethods(prev => [...prev, newMethod]);
    }
    
    // Modal kapatılır ve form sıfırlanır
    handleCloseDialog();
  };

  const handleSetPrimary = (id: string) => {
    setMethods(methods.map(method => ({
      ...method,
      isPrimary: method.id === id
    })));
  };

  const handleDelete = (id: string) => {
    setMethods(methods.filter(method => method.id !== id));
  };

  return (
    <Card className="border-gray-200 bg-white shadow-md">
      <CardHeader className="pb-3">
        <CardTitle className="text-gray-900">{t('advertiser:balance.methods.savedCards')}</CardTitle>
        <CardDescription>{t('advertiser:balance.methods.savedCardsDescription')}</CardDescription>
      </CardHeader>
      
      <CardContent className="px-4 pb-2">
        <div className="space-y-3 max-h-[230px] overflow-y-auto pr-1">
          {methods.map((method) => (
            <div
              key={method.id}
              className={`p-3 rounded-lg border ${
                method.isPrimary 
                  ? "border-blue-200 bg-blue-50" 
                  : "border-gray-200 bg-gray-50"
              } flex items-center justify-between group`}
            >
              <div className="flex items-center gap-3">
                <div className="bg-white p-1.5 rounded-md border border-gray-200">
                  {method.type === "card" ? (
                    <CreditCard className="h-4 w-4 text-blue-600" />
                  ) : (
                    <Building className="h-4 w-4 text-violet-600" />
                  )}
                </div>
                
                <div>
                  <div className="font-medium text-gray-900 text-sm flex items-center gap-2">
                    {method.title}
                    {method.isPrimary && (
                      <span className="bg-blue-100 text-blue-700 text-xs py-0.5 px-2 rounded-full">
                        {t('advertiser:balance.methods.default')}
                      </span>
                    )}
                  </div>
                  <div className="text-xs text-gray-500 flex items-center gap-1.5">
                    <span className="font-mono">{method.description}</span>
                    {method.expiryDate && (
                      <>
                        <span className="text-gray-400">•</span>
                        <span>{method.expiryDate}</span>
                      </>
                    )}
                  </div>
                </div>
              </div>
              
              <div className="flex items-center gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
                {!method.isPrimary && (
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    className="h-7 w-7 p-0 text-gray-500 hover:text-blue-600 hover:bg-blue-50"
                    onClick={() => handleSetPrimary(method.id)}
                  >
                    <CheckCircle className="h-3.5 w-3.5" />
                  </Button>
                )}
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="h-7 w-7 p-0 text-gray-500 hover:text-gray-700 hover:bg-gray-100"
                >
                  <Edit className="h-3.5 w-3.5" />
                </Button>
                {!method.isPrimary && (
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    className="h-7 w-7 p-0 text-gray-500 hover:text-red-600 hover:bg-red-50"
                    onClick={() => handleDelete(method.id)}
                  >
                    <Trash2 className="h-3.5 w-3.5" />
                  </Button>
                )}
              </div>
            </div>
          ))}
        </div>
      </CardContent>
      
      <CardFooter className="pt-2 pb-4 flex justify-center">
        <Dialog open={isAddingNew} onOpenChange={setIsAddingNew}>
          <DialogTrigger asChild>
            <Button 
              variant="outline" 
              className="w-full border-dashed border-gray-300 text-gray-600 hover:text-gray-900 hover:bg-gray-50"
            >
              <PlusCircle className="h-4 w-4 mr-2" />
              {t('advertiser:balance.methods.addNewCard')}
            </Button>
          </DialogTrigger>
          <DialogContent className="bg-white p-6 max-w-md">
            <DialogHeader>
              <DialogTitle className="text-lg font-semibold text-gray-900">
                {t('advertiser:balance.methods.addPaymentMethod')}
              </DialogTitle>
              <DialogDescription className="text-gray-500">
                {t('advertiser:balance.methods.addPaymentMethodDescription')}
              </DialogDescription>
            </DialogHeader>
            
            <div className="space-y-4 py-4">
              <RadioGroup value={paymentType} onValueChange={(v: "card" | "bank") => setPaymentType(v)} className="flex space-x-2">
                <div className="flex items-center space-x-2 flex-1">
                  <RadioGroupItem value="card" id="payment-card" className="text-blue-600" />
                  <Label htmlFor="payment-card" className="flex items-center gap-2 cursor-pointer">
                    <CreditCard className="h-4 w-4 text-blue-600" />
                    {t('advertiser:balance.methods.paymentType.creditCard')}
                  </Label>
                </div>
                <div className="flex items-center space-x-2 flex-1">
                  <RadioGroupItem value="bank" id="payment-bank" className="text-blue-600" />
                  <Label htmlFor="payment-bank" className="flex items-center gap-2 cursor-pointer">
                    <Building className="h-4 w-4 text-violet-600" />
                    {t('advertiser:balance.methods.paymentType.bankAccount')}
                  </Label>
                </div>
              </RadioGroup>
              
              {paymentType === "card" ? (
                <div className="space-y-3">
                  <div>
                    <Label htmlFor="card-number" className="text-gray-700">
                      {t('advertiser:balance.methods.form.cardNumber')}
                    </Label>
                    <Input 
                      id="card-number" 
                      value={cardNumber}
                      onChange={(e) => setCardNumber(e.target.value)}
                      placeholder={t('advertiser:balance.methods.form.cardNumberPlaceholder')}
                      className="mt-1 bg-white border-gray-300" 
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="card-name" className="text-gray-700">
                      {t('advertiser:balance.methods.form.cardName')}
                    </Label>
                    <Input 
                      id="card-name" 
                      value={cardName}
                      onChange={(e) => setCardName(e.target.value)}
                      placeholder={t('advertiser:balance.methods.form.cardNamePlaceholder')}
                      className="mt-1 bg-white border-gray-300" 
                    />
                  </div>
                  
                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <Label htmlFor="expiry-date" className="text-gray-700">
                        {t('advertiser:balance.methods.form.expiryDate')}
                      </Label>
                      <Input 
                        id="expiry-date" 
                        value={expiryDate}
                        onChange={(e) => setExpiryDate(e.target.value)}
                        placeholder={t('advertiser:balance.methods.form.expiryDatePlaceholder')}
                        className="mt-1 bg-white border-gray-300" 
                      />
                    </div>
                    
                    <div>
                      <Label htmlFor="cvv" className="text-gray-700">
                        {t('advertiser:balance.methods.form.cvv')}
                      </Label>
                      <Input 
                        id="cvv" 
                        value={cvv}
                        onChange={(e) => setCvv(e.target.value)}
                        placeholder={t('advertiser:balance.methods.form.cvvPlaceholder')}
                        className="mt-1 bg-white border-gray-300"
                        type="password"
                        maxLength={4}
                      />
                    </div>
                  </div>
                </div>
              ) : (
                <div className="space-y-3">
                  <div>
                    <Label htmlFor="bank-name" className="text-gray-700">
                      {t('advertiser:balance.methods.form.bankName')}
                    </Label>
                    <Input 
                      id="bank-name" 
                      value={bankName}
                      onChange={(e) => setBankName(e.target.value)}
                      placeholder={t('advertiser:balance.methods.form.bankNamePlaceholder')}
                      className="mt-1 bg-white border-gray-300" 
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="iban-number" className="text-gray-700">
                      {t('advertiser:balance.methods.form.iban')}
                    </Label>
                    <Input 
                      id="iban-number" 
                      value={ibanNumber}
                      onChange={(e) => setIbanNumber(e.target.value)}
                      placeholder={t('advertiser:balance.methods.form.ibanPlaceholder')}
                      className="mt-1 bg-white border-gray-300" 
                    />
                  </div>
                </div>
              )}
            </div>
            
            <DialogFooter>
              <Button 
                variant="outline" 
                onClick={handleCloseDialog} 
                className="bg-white text-gray-700 border-gray-300 hover:bg-gray-50"
              >
                {t('advertiser:balance.methods.buttons.cancel')}
              </Button>
              <Button 
                onClick={handleSubmit}
                className="bg-blue-600 hover:bg-blue-700 text-white"
                disabled={
                  (paymentType === "card" && (!cardNumber || !cardName || !expiryDate)) ||
                  (paymentType === "bank" && (!bankName || !ibanNumber))
                }
              >
                {t('advertiser:balance.methods.buttons.add')}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardFooter>
    </Card>
  );
} 