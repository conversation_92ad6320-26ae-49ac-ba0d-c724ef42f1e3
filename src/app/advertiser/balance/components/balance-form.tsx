"use client";

import { useState } from "react";
import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON><PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { CreditCard, Wallet, Landmark, PlusCircle, AlertCircle } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { fetchBalanceAdd, PaymentMethod } from "@/shared/services/advertiser/advertiser-balance-add";
import { useToast } from "@/components/ui/use-toast";
import { useLanguage } from "@/shared/hooks/useLanguage";

interface PaymentMethodUI {
  id: string;
  type: "card" | "bank" | "wallet";
  title: string;
  description: string;
  icon: React.ElementType;
  lastDigits?: string;
  bankName?: string;
  expiryDate?: string;
  apiValue: PaymentMethod;
}

const predefinedAmounts = [
  { value: 100, label: "100₺" },
  { value: 250, label: "250₺" },
  { value: 500, label: "500₺" },
  { value: 1000, label: "1.000₺" },
  { value: 2500, label: "2.500₺" },
];

export function BalanceForm() {
  const [amount, setAmount] = useState<number | "">("");
  const [customAmount, setCustomAmount] = useState<number | "">("");
  const [selectedMethod, setSelectedMethod] = useState<string | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const { toast } = useToast();
  const { t } = useLanguage();

  // Örnek ödeme yöntemleri 
  const paymentMethods: PaymentMethodUI[] = [
    {
      id: "card1",
      type: "card",
      title: "Kredi Kartı",
      description: "**** **** **** 4582",
      icon: CreditCard,
      lastDigits: "4582",
      expiryDate: "12/25",
      apiValue: "CREDIT_CARD"
    },
    {
      id: "bank1",
      type: "bank",
      title: "Banka Havalesi",
      description: "Garanti Bankası",
      icon: Landmark,
      bankName: "Garanti",
      apiValue: "BANK_TRANSFER"
    },
    {
      id: "wallet1",
      type: "wallet",
      title: "PayPal",
      description: "Hızlı Ödeme",
      icon: Wallet,
      apiValue: "PAYPAL"
    },
  ];

  const handleAmountSelect = (value: number) => {
    setAmount(value);
    setCustomAmount("");
  };

  const handleCustomAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value === "" ? "" : Number(e.target.value);
    setCustomAmount(value);
    setAmount(value);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!amount || !selectedMethod) {
      toast({
        title: "Hata",
        description: "Lütfen miktar ve ödeme yöntemi seçin",
        variant: "destructive"
      });
      return;
    }
    
    setIsProcessing(true);
    
    try {
      const selectedPaymentMethod = paymentMethods.find(m => m.id === selectedMethod);
      
      if (!selectedPaymentMethod) {
        throw new Error("Ödeme yöntemi bulunamadı");
      }

      const response = await fetchBalanceAdd({
        amount: Number(amount),
        payment_method: selectedPaymentMethod.apiValue
      });

      if (response.status) {
        toast({
          title: "Başarılı",
          description: "Bakiye ekleme işlemi başarıyla tamamlandı",
        });
        setAmount("");
        setCustomAmount("");
        setSelectedMethod(null);
      } else {
        toast({
          title: "Hata",
          description: response.desc || "Bakiye ekleme işlemi sırasında bir hata oluştu",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Hata",
        description: "Bakiye ekleme işlemi sırasında bir hata oluştu",
        variant: "destructive"
      });
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <Card className="border-gray-200 bg-white shadow-md">
      <CardHeader>
        <CardTitle className="text-gray-900">{t('advertiser:balance.addFunds')}</CardTitle>
        <CardDescription>{t('advertiser:balance.addFundsDescription')}</CardDescription>
      </CardHeader>
      
      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-6">
          {/* Miktar Seçimi */}
          <div className="space-y-3">
            <Label htmlFor="amount" className="text-gray-700">{t('advertiser:balance.amount')}</Label>
            
            <div className="grid grid-cols-3 md:grid-cols-5 gap-3">
              {predefinedAmounts.map((option) => (
                <Button
                  key={option.value}
                  type="button"
                  variant={amount === option.value ? "default" : "outline"}
                  className={`h-12 text-lg font-medium ${
                    amount === option.value
                      ? "bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white border-0"
                      : "bg-white text-gray-700 border-gray-200 hover:text-blue-600 hover:border-blue-300"
                  }`}
                  onClick={() => handleAmountSelect(option.value)}
                >
                  {option.label}
                </Button>
              ))}
            </div>
            
            <div className="flex items-end gap-3">
              <div className="flex-1">
                <Label htmlFor="custom-amount" className="text-gray-700 text-sm">{t('advertiser:balance.customAmount')}</Label>
                <div className="mt-1 relative">
                  <Input
                    id="custom-amount"
                    type="number"
                    placeholder={t('advertiser:balance.customAmountPlaceholder')}
                    className="bg-white border-gray-200 focus:border-blue-500 text-gray-900 pl-10"
                    value={customAmount === "" ? "" : customAmount}
                    onChange={handleCustomAmountChange}
                    min={10}
                    max={10000}
                  />
                  <div className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500">₺</div>
                </div>
              </div>
              
              <Button 
                type="button"
                variant="ghost"
                className="border border-dashed border-gray-300 text-gray-600 hover:text-blue-600 hover:bg-blue-50 hover:border-blue-300"
                onClick={() => setCustomAmount(amount as number || "")}
              >
                <PlusCircle className="h-4 w-4 mr-2" />
                {t('advertiser:balance.customAmountButton')}
              </Button>
            </div>
          </div>
          
          {/* Ödeme Yöntemi Seçimi */}
          <div className="space-y-3">
            <Label className="text-gray-700">{t('advertiser:balance.paymentMethod')}</Label>
            
            <RadioGroup 
              value={selectedMethod || ""}
              onValueChange={setSelectedMethod}
              className="space-y-3"
            >
              {paymentMethods.map((method) => {
                const Icon = method.icon;
                
                return (
                  <div 
                    key={method.id}
                    className={`flex items-center space-x-3 rounded-lg border ${
                      selectedMethod === method.id 
                        ? "border-blue-500 bg-blue-50" 
                        : "border-gray-200 hover:border-gray-300"
                    } p-4 cursor-pointer transition-colors duration-200`}
                    onClick={() => setSelectedMethod(method.id)}
                  >
                    <RadioGroupItem value={method.id} id={method.id} className="text-blue-600" />
                    
                    <div className="flex flex-1 items-center">
                      <div className={`h-10 w-10 rounded-md ${
                        method.type === "card" ? "bg-violet-100" :
                        method.type === "bank" ? "bg-amber-100" :
                        "bg-emerald-100"
                      } flex items-center justify-center mr-3`}>
                        <Icon className={`h-5 w-5 ${
                          method.type === "card" ? "text-violet-500" :
                          method.type === "bank" ? "text-amber-500" :
                          "text-emerald-500"
                        }`} />
                      </div>
                      
                      <div className="flex-1">
                        <Label 
                          htmlFor={method.id} 
                          className="text-gray-900 font-medium cursor-pointer flex items-center"
                        >
                          {method.title}
                          {method.type === "wallet" && (
                            <Badge className="ml-2 bg-emerald-100 text-emerald-600 hover:bg-emerald-200">
                              {t('advertiser:balance.methods.fastest')}
                            </Badge>
                          )}
                        </Label>
                        <p className="text-sm text-gray-500">{method.description}</p>
                      </div>
                    </div>
                    
                    {method.type === "card" && (
                      <div className="hidden md:block text-right text-sm text-gray-500">
                        <p>{t('advertiser:balance.methods.expiryDate')}: {method.expiryDate}</p>
                      </div>
                    )}
                  </div>
                );
              })}
            </RadioGroup>
          </div>
          
          {/* Bilgilendirme */}
          <Alert className="bg-blue-50 border-blue-200 text-blue-800">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle className="text-blue-800">{t('advertiser:balance.infoTitle')}</AlertTitle>
            <AlertDescription className="text-blue-700 text-sm">
              {t('advertiser:balance.infoDescription')}
            </AlertDescription>
          </Alert>
        </CardContent>
        
        <CardFooter className="border-t border-gray-200 pt-4 flex flex-col sm:flex-row gap-3">
          <div className="flex-1 space-y-1">
            <div className="text-sm text-gray-500">{t('advertiser:balance.totalAmount')}:</div>
            <div className="text-xl font-bold text-gray-900">{amount ? `${amount}₺` : "0₺"}</div>
          </div>
          
          <Button 
            type="submit" 
            disabled={!amount || !selectedMethod || isProcessing}
            className="w-full sm:w-auto sm:min-w-[200px] bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-medium shadow-md"
          >
            {isProcessing ? t('advertiser:balance.processing') : t('advertiser:balance.continuePayment')}
          </Button>
        </CardFooter>
      </form>
    </Card>
  );
} 