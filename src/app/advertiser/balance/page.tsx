"use client"

import { DashboardLayout } from "@/app/advertiser/components/layout/dashboard-layout"
import { DashboardHeader } from "@/app/advertiser/components/dashboard/header"
import { BalanceForm } from "./components/balance-form";
import { PaymentMethods } from "./components/payment-methods";
import { TransactionHistory } from "./components/transaction-history";
import { ArrowLeft } from "lucide-react";
import { useLanguage } from "@/shared/hooks/useLanguage";

export default function BalancePage() {
  const { t } = useLanguage();

  return (
    <DashboardLayout>
      <div className="flex mb-4 items-center">
        <DashboardHeader 
          title={t('advertiser:balance.title')}
          subtitle={t('advertiser:balance.subtitle')}
          name=""
          showButton={false}
        />
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        <div className="lg:col-span-1">
          <PaymentMethods />
        </div>
        <div className="lg:col-span-3 space-y-6">
          <BalanceForm />
          <TransactionHistory />
        </div>
      </div>
    </DashboardLayout>
  );
} 