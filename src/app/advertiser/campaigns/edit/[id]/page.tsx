"use client"

import { useEffect, useState } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import { DashboardLayout } from "@/app/advertiser/components/layout/dashboard-layout"
import { Button } from "@/components/ui/button"
import { CampaignStepIndicator } from "../../create/components/campaign-step-indicator"
import { CampaignDetailsForm } from "../../create/components/campaign-details-form"
import { TargetAudienceForm } from "../../create/components/target-audience-form"
import { AdContentForm } from "../../create/components/ad-content-form"
import { BudgetForm } from "../../create/components/budget-form"
import { CampaignDatesForm } from "../../create/components/campaign-dates-form"
import { CampaignAdvancedForm } from "../../create/components/campaign-advanced-form"
import { CampaignPreview } from "../../create/components/campaign-preview"
import { ArrowLeft, X } from "lucide-react"
import Link from "next/link"
import { toast } from "sonner"
import { fetch<PERSON>ampaignGet, CampaignGetResponse } from "@/shared/services/advertiser/advertiser-campaign-list"
import { fetchCampaignUpdate } from "@/shared/services/advertiser/advertiser-campaign-update"
import { Skeleton } from "@/components/ui/skeleton"

// Tip tanımını ekleyelim
interface CampaignPreviewData {
  name: string;
  platform: string;
  objective: string;
  budget: number;
  bidStrategy: string;
  bidAmount: number;
  startDate?: string;
  endDate?: string;
  country: string;
  targetAudience?: string[];
  interests?: string[];
  brandSafety?: string[];
  adContent: {
    headline: string;
    description: string;
    htmlContent?: string;
    image: string | null;
    targetUrl: string;
    ctaText: string;
  };
  [key: string]: unknown;
}

interface CampaignData {
  name?: string;
  platform?: string;
  objective?: string;
  budget?: number;
  bidStrategy?: string;
  bidAmount?: number;
  startDate?: string | null;
  endDate?: string | null;
  country?: string;
  targetAudience?: string[];
  interests?: string[];
  brandSafety?: string[];
  adContent?: {
    headline?: string;
    description?: string;
    htmlContent?: string;
    image?: string | File | null;
    targetUrl?: string;
    ctaText?: string;
    format?: string;
  };
  [key: string]: unknown;
}

export default function EditCampaignPage() {
  const params = useParams()
  const router = useRouter()
  const [currentStep, setCurrentStep] = useState(1)
  const [progress, setProgress] = useState(33)
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({})
  const [showErrors, setShowErrors] = useState(false)
  const [isActiveCampaign, setIsActiveCampaign] = useState(false)
  const [campaignData, setCampaignData] = useState<CampaignData>({
    name: "",
    platform: "",
    objective: "",
    budget: 0,
    bidStrategy: "",
    bidAmount: 0,
    startDate: null,
    endDate: null,
    country: "",
    targetAudience: [],
    interests: [],
    brandSafety: [],
    adContent: {
      headline: "",
      description: "",
      image: null,
      targetUrl: "",
      ctaText: ""
    }
  })

  // Form validasyon fonksiyonları
  const validateStep = (step: number): { isValid: boolean; errors: string[]; fieldErrors: Record<string, string> } => {
    const errors: string[] = [];
    const fieldErrors: Record<string, string> = {};

    switch (step) {
      case 1: // Kampanya Detayları
        if (!campaignData.name?.trim()) {
          errors.push("Kampanya adı zorunludur");
          fieldErrors["name"] = "Kampanya adı zorunludur";
        }
        if (!campaignData.platform) {
          errors.push("Platform seçimi zorunludur");
          fieldErrors["platform"] = "Platform seçimi zorunludur";
        }
        if (!campaignData.objective) {
          errors.push("Kampanya hedefi seçimi zorunludur");
          fieldErrors["objective"] = "Kampanya hedefi seçimi zorunludur";
        }
        break;

      case 2: // Hedef Kitle
        if (!campaignData.country) {
          errors.push("Ülke seçimi zorunludur");
          fieldErrors["country"] = "Ülke seçimi zorunludur";
        }
        if (!campaignData.interests?.length || campaignData.interests.length < 2) {
          errors.push("En az 2 ilgi alanı seçmelisiniz");
          fieldErrors["interests"] = "En az 2 ilgi alanı seçmelisiniz";
        }
        break;

      case 3: // Reklam İçeriği
        if (!campaignData.adContent?.headline?.trim()) {
          errors.push("Reklam başlığı zorunludur");
          fieldErrors["adContent.headline"] = "Reklam başlığı zorunludur";
        }
        if (!campaignData.adContent?.description?.trim()) {
          errors.push("Reklam açıklaması zorunludur");
          fieldErrors["adContent.description"] = "Reklam açıklaması zorunludur";
        }
        if (!campaignData.adContent?.targetUrl?.trim()) {
          errors.push("Hedef URL zorunludur");
          fieldErrors["adContent.targetUrl"] = "Hedef URL zorunludur";
        }
        if (!campaignData.adContent?.image) {
          errors.push("Reklam görseli zorunludur");
          fieldErrors["adContent.image"] = "Reklam görseli zorunludur";
        }
        if (!campaignData.adContent?.ctaText?.trim()) {
          errors.push("Eylem çağrısı metni zorunludur");
          fieldErrors["adContent.ctaText"] = "Eylem çağrısı metni zorunludur";
        }
        break;

      case 4: // Bütçe
        if (!campaignData.budget || campaignData.budget <= 0) {
          errors.push("Geçerli bir bütçe miktarı giriniz");
          fieldErrors["budget"] = "Geçerli bir bütçe miktarı giriniz";
        }
        if (!campaignData.bidStrategy) {
          errors.push("Teklif stratejisi seçimi zorunludur");
          fieldErrors["bidStrategy"] = "Teklif stratejisi seçimi zorunludur";
        }
        if (!campaignData.bidAmount || campaignData.bidAmount <= 0) {
          errors.push("Geçerli bir teklif miktarı giriniz");
          fieldErrors["bidAmount"] = "Geçerli bir teklif miktarı giriniz";
        }
        break;

      case 5: // Kampanya Tarihleri
        if (!campaignData.startDate) {
          errors.push("Başlangıç tarihi zorunludur");
          fieldErrors["startDate"] = "Başlangıç tarihi zorunludur";
        }
        // Başlangıç tarihi bugünden önce olamaz (edit modda daha esnek olabilir)
        if (campaignData.startDate) {
          const startDate = typeof campaignData.startDate === 'number' 
            ? new Date(campaignData.startDate * 1000) 
            : new Date(campaignData.startDate);
          const today = new Date();
          today.setHours(0, 0, 0, 0);
          
          if (startDate < today) {
            errors.push("Başlangıç tarihi bugünden önce olamaz");
            fieldErrors["startDate"] = "Başlangıç tarihi bugünden önce olamaz";
          }
        }
        break;
    }

    return {
      isValid: errors.length === 0,
      errors,
      fieldErrors
    };
  };

  // Tüm form validasyonu (son adım için)
  const validateAllSteps = (): { isValid: boolean; errors: string[]; fieldErrors: Record<string, string> } => {
    const allErrors: string[] = [];
    const allFieldErrors: Record<string, string> = {};
    
    for (let step = 1; step <= 5; step++) {
      const stepValidation = validateStep(step);
      if (!stepValidation.isValid) {
        allErrors.push(`Adım ${step}: ${stepValidation.errors.join(", ")}`);
        Object.assign(allFieldErrors, stepValidation.fieldErrors);
      }
    }

    return {
      isValid: allErrors.length === 0,
      errors: allErrors,
      fieldErrors: allFieldErrors
    };
  };

  // Real-time validasyon için
  const validateCurrentStepOnChange = () => {
    if (showErrors) {
      const validation = validateStep(currentStep);
      setFieldErrors(validation.fieldErrors);
    }
  };

  useEffect(() => {
    const fetchCampaignDetails = async () => {
      try {
        const response = await fetchCampaignGet({ campaign_id: params.id as string })
        
        if (response.status && response.result) {
          const campaign = response.result
          
          // Kampanya status kontrolü
          const isActive = campaign.status === "ACTIVE"
          setIsActiveCampaign(isActive)
          
          if (isActive) {
            toast.error("Bu kampanya şu anda aktif durumda olduğu için düzenlenemez. Kampanyayı duraklatıp tekrar deneyiniz.")
          }
          
          // Platform değerini doğru formata çevir
          const platform = campaign.advertising_platform.toLowerCase() === 'telegram' ? 'Telegram' : 'Discord'
          
          // API'den gelen verileri form state'ine dönüştür
          // Timestamp'leri doğru şekilde dönüştür (Unix timestamp saniye cinsinden geliyor)
          const formatTimestampToDate = (timestamp: number) => {
            return new Date(timestamp * 1000).toISOString().split('T')[0]
          }

          setCampaignData({
            name: campaign.title,
            platform: platform, // Doğru formatta platform değeri
            objective: campaign.campaign_aim,
            budget: campaign.campaign_budget,
            bidStrategy: campaign.bid_strategy.toLowerCase(),
            bidAmount: campaign.bid_strategy.toLowerCase() === 'cpm' ? campaign.cpm_bid_price : campaign.cpc_bid_price,
            startDate: formatTimestampToDate(campaign.starts_at),
            endDate: formatTimestampToDate(campaign.ends_at),
            country: campaign.country,
            interests: campaign.category,
            brandSafety: campaign.brand_safety_check.split(','),
            adContent: {
              headline: campaign.campaign_title,
              description: campaign.campaign_desc,
              image: campaign.campaign_image,
              targetUrl: campaign.destination_url,
              ctaText: campaign.cta_button
            }
          })
        } else {
          throw new Error(response.desc || "Kampanya detayları alınamadı")
        }
      } catch (error) {
        console.error("Kampanya detayları alınamadı:", error)
        toast.error(error instanceof Error ? error.message : "Kampanya detayları alınamadı")
      } finally {
        setIsLoading(false)
      }
    }

    if (params.id) {
      fetchCampaignDetails()
    }
  }, [params.id])

  const updateCampaignData = (data: Partial<CampaignData>) => {
    // Aktif kampanya ise güncellemeyi engelle
    if (isActiveCampaign) {
      toast.error("Aktif kampanya düzenlenemez!")
      return
    }

    // Tarih kontrolü - bitiş tarihi başlangıç tarihinden önce olamaz
    if (data.endDate && campaignData.startDate) {
      const endDate = typeof data.endDate === 'number' ? new Date(data.endDate * 1000) : new Date(data.endDate as string);
      const startDate = typeof campaignData.startDate === 'number' ? new Date(campaignData.startDate * 1000) : new Date(campaignData.startDate as string);
      
      if (endDate < startDate) {
        toast.error("Bitiş tarihi, başlangıç tarihinden önce olamaz.");
        data = { ...data, endDate: campaignData.startDate };
      }
    }

    // Başlangıç tarihi bitiş tarihinden sonra olamaz
    if (data.startDate && campaignData.endDate) {
      const startDate = typeof data.startDate === 'number' ? new Date(data.startDate * 1000) : new Date(data.startDate as string);
      const endDate = typeof campaignData.endDate === 'number' ? new Date(campaignData.endDate * 1000) : new Date(campaignData.endDate as string);
      
      if (startDate > endDate) {
        data = { ...data, endDate: null };
      }
    }

    setCampaignData((prev) => {
      const newData = { ...prev, ...data }
      return newData
    })

    // Real-time validasyon
    setTimeout(validateCurrentStepOnChange, 0);
  }

  const handleNext = () => {
    // Aktif kampanya kontrolü
    if (isActiveCampaign) {
      toast.error("Aktif kampanya düzenlenemez!")
      return
    }

    // Mevcut adımı validate et
    const validation = validateStep(currentStep);
    
    if (!validation.isValid) {
      setShowErrors(true);
      setFieldErrors(validation.fieldErrors);
      
      toast.error(validation.errors.join("\n"));
      return;
    }

    // Hata göstermeyi kapat ve bir sonraki adıma geç
    setShowErrors(false);
    setFieldErrors({});

    if (currentStep < 5) {
      setCurrentStep((prev) => prev + 1)
      setProgress(Math.min(100, progress + (100 / 5)))
    }
  }

  const handlePrevious = () => {
    // Aktif kampanya için de geri gidebilir (sadece görüntüleme)
    // Geri giderken hataları temizle
    setShowErrors(false);
    setFieldErrors({});
    
    if (currentStep > 1) {
      setCurrentStep((prev) => prev - 1)
      setProgress(Math.max(0, progress - (100 / 5)))
    }
  }

  const handleSubmit = async () => {
    // Aktif kampanya kontrolü
    if (isActiveCampaign) {
      toast.error("Aktif kampanya düzenlenemez!")
      return
    }

    // Tüm adımları validate et
    const validation = validateAllSteps();
    
    if (!validation.isValid) {
      setShowErrors(true);
      setFieldErrors(validation.fieldErrors);
      
      toast.error("Lütfen tüm gerekli alanları doldurun:\n\n" + validation.errors.join("\n"));
      return;
    }

    try {
      setIsSubmitting(true)
      
      // Convert dates to Unix timestamp - improved conversion
      let startDate: number | null = null;
      let endDate: number | null = null;

      if (campaignData.startDate) {
        if (typeof campaignData.startDate === 'number') {
          // Zaten timestamp ise
          startDate = campaignData.startDate;
        } else {
          // String ise (YYYY-MM-DD formatında) Date objesine çevir
          const dateObj = new Date(campaignData.startDate);
          if (!isNaN(dateObj.getTime())) {
            startDate = Math.floor(dateObj.getTime() / 1000);
          }
        }
      }

      if (campaignData.endDate) {
        if (typeof campaignData.endDate === 'number') {
          // Zaten timestamp ise
          endDate = campaignData.endDate;
        } else {
          // String ise (YYYY-MM-DD formatında) Date objesine çevir ve gün sonuna ayarla
          const dateObj = new Date(campaignData.endDate);
          if (!isNaN(dateObj.getTime())) {
            dateObj.setHours(23, 59, 59, 999);
            endDate = Math.floor(dateObj.getTime() / 1000);
          }
        }
      }

      // Prepare the update data
      const updateData = {
        campaign_id: params.id as string,
        title: campaignData.name || "",
        campaign_aim: campaignData.objective || "",
        advertising_platform: campaignData.platform || "",
        country: campaignData.country || "",
        category: campaignData.interests || [],
        campaign_title: campaignData.adContent?.headline || "",
        campaign_desc: campaignData.adContent?.description || "",
        destination_url: campaignData.adContent?.targetUrl || "",
        campaign_image: campaignData.adContent?.image as string || "",
        cta_button: campaignData.adContent?.ctaText || "",
        campaign_budget: campaignData.budget?.toString() || "",
        bid_strategy: campaignData.bidStrategy || "",
        cpm_bid_price: campaignData.bidStrategy === "cpm" ? campaignData.bidAmount?.toString() || "" : "0",
        cpc_bid_price: campaignData.bidStrategy === "cpc" ? campaignData.bidAmount?.toString() || "" : "0",
        brand_safety_check: campaignData.brandSafety?.join(",") || "",
        starts_at: startDate?.toString() || "",
        ends_at: endDate?.toString() || "",
        status: "ACTIVE",
        language: "tr"
      }

      console.log("Gönderilen tarih verileri:", {
        originalStartDate: campaignData.startDate,
        originalEndDate: campaignData.endDate,
        convertedStartDate: startDate,
        convertedEndDate: endDate,
        starts_at: updateData.starts_at,
        ends_at: updateData.ends_at
      });

      const response = await fetchCampaignUpdate(updateData)

      if (response.status) {
        toast.success("Kampanya başarıyla güncellendi!");
        router.push("/advertiser/campaigns")
      } else {
        throw new Error(response.desc || "Kampanya güncellenirken bir hata oluştu")
      }
    } catch (error) {
      console.error("Kampanya güncelleme hatası:", error)
      toast.error(error instanceof Error ? error.message : "Kampanya güncellenirken bir hata oluştu");
    } finally {
      setIsSubmitting(false)
    }
  }

  // Adıma göre bileşen gösterme
  const renderStepContent = () => {
    if (isLoading) {
      return <Skeleton className="h-[400px] w-full" />
    }

    const commonProps = {
      data: campaignData,
      updateData: updateCampaignData,
      fieldErrors,
      showErrors,
      disabled: isActiveCampaign
    };

    switch (currentStep) {
      case 1:
        return <CampaignDetailsForm {...commonProps} />
      case 2:
        return <TargetAudienceForm {...commonProps} />
      case 3:
        return <AdContentForm {...commonProps} />
      case 4:
        return <BudgetForm {...commonProps} />
      case 5:
        return <CampaignDatesForm {...commonProps} />
      case 6:
        return <CampaignAdvancedForm {...commonProps} />
      default:
        return <CampaignDetailsForm {...commonProps} />
    }
  }

  const isLastStep = currentStep === 5

  return (
    <DashboardLayout>
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center gap-2">
          <Link href="/advertiser/campaigns">
            <Button variant="ghost" size="icon" className="rounded-full">
              <ArrowLeft className="h-5 w-5" />
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Kampanya {isActiveCampaign ? "Görüntüle" : "Düzenle"}
            </h1>
            {isActiveCampaign && (
              <p className="text-sm text-amber-600 mt-1">
                Bu kampanya aktif durumda olduğu için düzenlenemez
              </p>
            )}
          </div>
        </div>
        <Link href="/advertiser/campaigns">
          <Button variant="ghost" size="icon" className="rounded-full">
            <X className="h-5 w-5" />
          </Button>
        </Link>
      </div>

      {/* Aktif kampanya uyarısı */}
      {isActiveCampaign && (
        <div className="bg-amber-50 border border-amber-200 rounded-lg p-4 mb-6">
          <div className="flex items-start">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-amber-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-amber-800">
                Aktif Kampanya Düzenlenemez
              </h3>
              <div className="mt-2 text-sm text-amber-700">
                <p>
                  Bu kampanya şu anda aktif durumda olduğu için düzenlenemez. 
                  Kampanyayı düzenlemek için önce kampanyayı duraklattıktan sonra tekrar deneyiniz.
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Sol panel - Form */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-xl border shadow-sm p-6 mb-6">
            <CampaignStepIndicator currentStep={currentStep} />
            
            <div className="mt-6">
              {renderStepContent()}
            </div>
            
            <div className="flex justify-between mt-8 pt-4 border-t">
              <Button 
                variant="outline" 
                onClick={handlePrevious}
                disabled={currentStep === 1 || isSubmitting}
              >
                Geri
              </Button>
              <Button 
                onClick={isLastStep ? handleSubmit : handleNext}
                disabled={isSubmitting || isActiveCampaign}
              >
                {isActiveCampaign 
                  ? "Düzenleme Yapılamaz" 
                  : isLastStep 
                    ? (isSubmitting ? "İşleniyor..." : "Değişiklikleri Kaydet") 
                    : "İleri"
                }
              </Button>
            </div>
          </div>
        </div>

        {/* Sağ panel - Önizleme */}
        <div className="lg:col-span-1">
          {isLoading ? (
            <Skeleton className="h-[600px] w-full" />
          ) : (
            <CampaignPreview data={campaignData as CampaignPreviewData} />
          )}
        </div>
      </div>
    </DashboardLayout>
  )
} 