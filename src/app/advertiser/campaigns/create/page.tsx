"use client"

import { useState } from "react"
import { DashboardLayout } from "@/app/advertiser/components/layout/dashboard-layout"
import { Button } from "@/components/ui/button"
import { CampaignStepIndicator } from "./components/campaign-step-indicator"
import { CampaignDetailsForm } from "./components/campaign-details-form"
import { TargetAudienceForm } from "./components/target-audience-form"
import { AdContentForm } from "./components/ad-content-form"
import { BudgetForm } from "./components/budget-form"
import { CampaignDatesForm } from "./components/campaign-dates-form"
import { CampaignAdvancedForm } from "./components/campaign-advanced-form"
import { CampaignPreview } from "./components/campaign-preview"
import { ArrowLeft, X } from "lucide-react"
import Link from "next/link"
import { toast } from "sonner"
import { fetchCampaignCreate, CampaignCreateParams } from "@/shared/services/advertiser/advertiser-campaign-create"
import { useRouter } from "next/navigation"

// Tip tanımını ekleyelim
interface CampaignPreviewData {
  name: string;
  platform: string;
  objective: string;
  budget: number;
  bidStrategy: string;
  bidAmount: number;
  startDate?: string;
  endDate?: string;
  country: string;
  targetAudience?: string[];
  interests?: string[];
  brandSafety?: string[];
  adContent: {
    headline: string;
    description: string;
    htmlContent?: string;
    image: string | null;
    targetUrl: string;
    ctaText: string;
  };
  [key: string]: unknown;
}

interface CampaignData {
  name?: string;
  platform?: string;
  objective?: string;
  budget?: number;
  bidStrategy?: string;
  bidAmount?: number;
  startDate?: string | null;
  endDate?: string | null;
  country?: string;
  targetAudience?: string[];
  interests?: string[];
  brandSafety?: string[];
  adContent?: {
    headline?: string;
    description?: string;
    htmlContent?: string;
    image?: string | File | null;
    targetUrl?: string;
    ctaText?: string;
    format?: string;
  };
  [key: string]: unknown;
}

export default function CreateCampaignPage() {
  const [currentStep, setCurrentStep] = useState(1)
  const [progress, setProgress] = useState(33)
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [fieldErrors, setFieldErrors] = useState<Record<string, string>>({})
  const [showErrors, setShowErrors] = useState(false)
  const [campaignData, setCampaignData] = useState<CampaignData>({
    name: "",
    platform: "Telegram", // Telegram, Discord
    objective: "awareness", // awareness, traffic, engagement, conversions, app_installs, reach
    budget: 5000,
    bidStrategy: "cpm", // cpm, cpc
    bidAmount: 10,
    startDate: null, // null olarak başlat, string yerine
    endDate: null,   // null olarak başlat, string yerine
    country: "TR",
    targetAudience: [],
    interests: [],
    brandSafety: ["adult"],
    adContent: {
      headline: "",
      description: "",
      image: null,
      targetUrl: "",
      ctaText: "Daha Fazla Bilgi"
    }
  })

  // Form validasyon fonksiyonları
  const validateStep = (step: number): { isValid: boolean; errors: string[]; fieldErrors: Record<string, string> } => {
    const errors: string[] = [];
    const fieldErrors: Record<string, string> = {};

    switch (step) {
      case 1: // Kampanya Detayları
        if (!campaignData.name?.trim()) {
          errors.push("Kampanya adı zorunludur");
          fieldErrors["name"] = "Kampanya adı zorunludur";
        }
        if (!campaignData.platform) {
          errors.push("Platform seçimi zorunludur");
          fieldErrors["platform"] = "Platform seçimi zorunludur";
        }
        if (!campaignData.objective) {
          errors.push("Kampanya hedefi seçimi zorunludur");
          fieldErrors["objective"] = "Kampanya hedefi seçimi zorunludur";
        }
        break;

      case 2: // Hedef Kitle
        if (!campaignData.country) {
          errors.push("Ülke seçimi zorunludur");
          fieldErrors["country"] = "Ülke seçimi zorunludur";
        }
        if (!campaignData.interests?.length || campaignData.interests.length < 2) {
          errors.push("En az 2 ilgi alanı seçmelisiniz");
          fieldErrors["interests"] = "En az 2 ilgi alanı seçmelisiniz";
        }
        break;

      case 3: // Reklam İçeriği
        if (!campaignData.adContent?.headline?.trim()) {
          errors.push("Reklam başlığı zorunludur");
          fieldErrors["adContent.headline"] = "Reklam başlığı zorunludur";
        }
        if (!campaignData.adContent?.description?.trim()) {
          errors.push("Reklam açıklaması zorunludur");
          fieldErrors["adContent.description"] = "Reklam açıklaması zorunludur";
        }
        if (!campaignData.adContent?.targetUrl?.trim()) {
          errors.push("Hedef URL zorunludur");
          fieldErrors["adContent.targetUrl"] = "Hedef URL zorunludur";
        }
        if (!campaignData.adContent?.image) {
          errors.push("Reklam görseli zorunludur");
          fieldErrors["adContent.image"] = "Reklam görseli zorunludur";
        }
        if (!campaignData.adContent?.ctaText?.trim()) {
          errors.push("Eylem çağrısı metni zorunludur");
          fieldErrors["adContent.ctaText"] = "Eylem çağrısı metni zorunludur";
        }
        break;

      case 4: // Bütçe
        if (!campaignData.budget || campaignData.budget <= 0) {
          errors.push("Geçerli bir bütçe miktarı giriniz");
          fieldErrors["budget"] = "Geçerli bir bütçe miktarı giriniz";
        }
        if (!campaignData.bidStrategy) {
          errors.push("Teklif stratejisi seçimi zorunludur");
          fieldErrors["bidStrategy"] = "Teklif stratejisi seçimi zorunludur";
        }
        if (!campaignData.bidAmount || campaignData.bidAmount <= 0) {
          errors.push("Geçerli bir teklif miktarı giriniz");
          fieldErrors["bidAmount"] = "Geçerli bir teklif miktarı giriniz";
        }
        break;

      case 5: // Kampanya Tarihleri
        if (!campaignData.startDate) {
          errors.push("Başlangıç tarihi zorunludur");
          fieldErrors["startDate"] = "Başlangıç tarihi zorunludur";
        }
        // Başlangıç tarihi bugünden önce olamaz
        if (campaignData.startDate) {
          const startDate = typeof campaignData.startDate === 'number' 
            ? new Date(campaignData.startDate * 1000) 
            : new Date(campaignData.startDate);
          const today = new Date();
          today.setHours(0, 0, 0, 0);
          
          if (startDate < today) {
            errors.push("Başlangıç tarihi bugünden önce olamaz");
            fieldErrors["startDate"] = "Başlangıç tarihi bugünden önce olamaz";
          }
        }
        break;

      case 6: // Gelişmiş Ayarlar
        if (!campaignData.brandSafety?.length) {
          errors.push("En az bir marka güvenliği seçeneği seçmelisiniz");
          fieldErrors["brandSafety"] = "En az bir marka güvenliği seçeneği seçmelisiniz";
        }
        break;
    }

    return {
      isValid: errors.length === 0,
      errors,
      fieldErrors
    };
  };

  // Tüm form validasyonu (son adım için)
  const validateAllSteps = (): { isValid: boolean; errors: string[]; fieldErrors: Record<string, string> } => {
    const allErrors: string[] = [];
    const allFieldErrors: Record<string, string> = {};
    
    for (let step = 1; step <= 6; step++) {
      const stepValidation = validateStep(step);
      if (!stepValidation.isValid) {
        allErrors.push(`Adım ${step}: ${stepValidation.errors.join(", ")}`);
        Object.assign(allFieldErrors, stepValidation.fieldErrors);
      }
    }

    return {
      isValid: allErrors.length === 0,
      errors: allErrors,
      fieldErrors: allFieldErrors
    };
  };

  // Real-time validasyon için
  const validateCurrentStepOnChange = () => {
    if (showErrors) {
      const validation = validateStep(currentStep);
      setFieldErrors(validation.fieldErrors);
    }
  };

  // CampaignData güncellendiğinde real-time validasyon yap
  const updateCampaignData = (data: Partial<CampaignData>) => {
    // Tarih kontrolü - konsola debugging için yazdır
    if (data.startDate || data.endDate) {
      console.log("Tarih güncellemesi:", {
        currentStartDate: campaignData.startDate,
        newStartDate: data.startDate,
        currentEndDate: campaignData.endDate,
        newEndDate: data.endDate,
        type: data.startDate ? typeof data.startDate : 'undefined'
      });
    }

    // Tarih kontrolü - bitiş tarihi başlangıç tarihinden önce olamaz
    if (data.endDate && campaignData.startDate) {
      const endDate = typeof data.endDate === 'number' ? new Date(data.endDate * 1000) : new Date(data.endDate as string);
      const startDate = typeof campaignData.startDate === 'number' ? new Date(campaignData.startDate * 1000) : new Date(campaignData.startDate as string);
      
      if (endDate < startDate) {
        toast.error("Bitiş tarihi, başlangıç tarihinden önce olamaz.");
        // Bitiş tarihini başlangıç tarihine eşit olarak ayarla
        data = { ...data, endDate: campaignData.startDate };
      }
    }

    // Başlangıç tarihi bitiş tarihinden sonra olamaz
    if (data.startDate && campaignData.endDate) {
      const startDate = typeof data.startDate === 'number' ? new Date(data.startDate * 1000) : new Date(data.startDate as string);
      const endDate = typeof campaignData.endDate === 'number' ? new Date(campaignData.endDate * 1000) : new Date(campaignData.endDate as string);
      
      if (startDate > endDate) {
        // Eğer başlangıç tarihi değiştiyse ve bitiş tarihinden sonra ise, bitiş tarihini temizle
        data = { ...data, endDate: null };
      }
    }

    setCampaignData((prev) => {
      const newData = { ...prev, ...data };
      return newData;
    });

    // Real-time validasyon
    setTimeout(validateCurrentStepOnChange, 0);
  }

  const handleNext = () => {
    // Mevcut adımı validate et
    const validation = validateStep(currentStep);
    
    if (!validation.isValid) {
      setShowErrors(true);
      setFieldErrors(validation.fieldErrors);
      
      toast.error("Eksik Bilgiler", {
        description: validation.errors.join("\n")
      });
      return;
    }

    // Hata göstermeyi kapat ve bir sonraki adıma geç
    setShowErrors(false);
    setFieldErrors({});

    if (currentStep < 6) {
      setCurrentStep((prev) => prev + 1)
      setProgress(Math.min(100, progress + (100 / 6)))
    }
  }

  const handlePrevious = () => {
    // Geri giderken hataları temizle
    setShowErrors(false);
    setFieldErrors({});
    
    if (currentStep > 1) {
      setCurrentStep((prev) => prev - 1)
      setProgress(Math.max(0, progress - (100 / 6)))
    }
  }

  const handleSubmit = async () => {
    // Tüm adımları validate et
    const validation = validateAllSteps();
    
    if (!validation.isValid) {
      setShowErrors(true);
      setFieldErrors(validation.fieldErrors);
      
      toast.error("Eksik Bilgiler", {
        description: "Lütfen tüm gerekli alanları doldurun:\n\n" + validation.errors.join("\n")
      });
      return;
    }

    try {
      setIsSubmitting(true)
      
      // Convert dates to Unix timestamp - improved conversion
      let startDate: string = "";
      let endDate: string = "";

      if (campaignData.startDate) {
        if (typeof campaignData.startDate === 'number') {
          // Zaten timestamp ise
          startDate = (campaignData.startDate as number).toString();
        } else {
          // String ise (YYYY-MM-DD formatında) Date objesine çevir
          const dateObj = new Date(campaignData.startDate as string);
          if (!isNaN(dateObj.getTime())) {
            startDate = Math.floor(dateObj.getTime() / 1000).toString();
          }
        }
      }

      if (campaignData.endDate) {
        if (typeof campaignData.endDate === 'number') {
          // Zaten timestamp ise
          endDate = (campaignData.endDate as number).toString();
        } else if (campaignData.endDate) {
          // String ise (YYYY-MM-DD formatında) Date objesine çevir ve gün sonuna ayarla
          const dateObj = new Date(campaignData.endDate as string);
          if (!isNaN(dateObj.getTime())) {
            dateObj.setHours(23, 59, 59, 999);
            endDate = Math.floor(dateObj.getTime() / 1000).toString();
          }
        }
      }
      
      // Form verilerini API formatına dönüştürme
      const apiData: CampaignCreateParams = {
        title: campaignData.name || "",
        campaign_aim: campaignData.objective || "awareness",
        advertising_platform: campaignData.platform || "Instagram",
        country: campaignData.country || "TR",
        category: campaignData.interests || [],
        campaign_title: campaignData.adContent?.headline || "",
        campaign_desc: campaignData.adContent?.description || "",
        destination_url: campaignData.adContent?.targetUrl || "",
        campaign_image: campaignData.adContent?.image as string || "",
        cta_button: campaignData.adContent?.ctaText || "Daha Fazla Bilgi",
        campaign_budget: typeof campaignData.budget === 'number' ? campaignData.budget.toString() : (campaignData.budget as unknown as string || "5000"),
        bid_strategy: campaignData.bidStrategy || "cpm",
        cpm_bid_price: campaignData.bidStrategy === "cpm" 
          ? (typeof campaignData.bidAmount === 'number' ? campaignData.bidAmount.toString() : (campaignData.bidAmount as unknown as string || "10"))
          : "0",
        cpc_bid_price: campaignData.bidStrategy === "cpc" 
          ? (typeof campaignData.bidAmount === 'number' ? campaignData.bidAmount.toString() : (campaignData.bidAmount as unknown as string || "2"))
          : "0",
        brand_safety_check: campaignData.brandSafety?.join(',') || "",
        starts_at: startDate || Math.floor(new Date().getTime() / 1000).toString(),
        ends_at: endDate,
        status: "active",
        language: "tr"
      }
      
      console.log("Gönderilen tarih verileri:", {
        originalStartDate: campaignData.startDate,
        originalEndDate: campaignData.endDate,
        convertedStartDate: startDate,
        convertedEndDate: endDate,
        starts_at: apiData.starts_at,
        ends_at: apiData.ends_at
      });
      
      console.log("API'ye gönderilecek kampanya verileri:", apiData)
      
      // API çağrısı yap
      const response = await fetchCampaignCreate(apiData)
      
      if (response.status) {
        toast.success("Kampanyanız başarıyla oluşturuldu!");
        
        // Kampanya listesine yönlendir
        router.push("/advertiser/campaigns")
      } else {
        throw new Error(response.desc || "Kampanya oluşturulurken bir hata oluştu")
      }
    } catch (error) {
      console.error("Kampanya oluşturma hatası:", error)
      
      toast.error(error instanceof Error ? error.message : "Kampanya oluşturulurken bir hata oluştu");
    } finally {
      setIsSubmitting(false)
    }
  }

  // Adıma göre bileşen gösterme
  const renderStepContent = () => {
    const commonProps = {
      data: campaignData,
      updateData: updateCampaignData,
      fieldErrors,
      showErrors
    };

    switch (currentStep) {
      case 1:
        return <CampaignDetailsForm {...commonProps} />
      case 2:
        return <TargetAudienceForm {...commonProps} />
      case 3:
        return <AdContentForm {...commonProps} />
      case 4:
        return <BudgetForm {...commonProps} />
      case 5:
        return <CampaignDatesForm {...commonProps} />
      case 6:
        return <CampaignAdvancedForm {...commonProps} />
      default:
        return <CampaignDetailsForm {...commonProps} />
    }
  }

  const isLastStep = currentStep === 6

  return (
    <DashboardLayout>
      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center gap-2">
          <Link href="/advertiser/campaigns">
            <Button variant="ghost" size="icon" className="rounded-full">
              <ArrowLeft className="h-5 w-5" />
            </Button>
          </Link>
          <h1 className="text-3xl font-bold tracking-tight">Yeni Kampanya Oluştur</h1>
        </div>
        <Link href="/advertiser/campaigns">
          <Button variant="ghost" size="icon" className="rounded-full">
            <X className="h-5 w-5" />
          </Button>
        </Link>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Sol panel - Form */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-xl border shadow-sm p-6 mb-6">
            <CampaignStepIndicator currentStep={currentStep} />
            
            <div className="mt-6">
              {renderStepContent()}
            </div>
            
            <div className="flex justify-between mt-8 pt-4 border-t">
              <Button 
                variant="outline" 
                onClick={handlePrevious}
                disabled={currentStep === 1 || isSubmitting}
              >
                Geri
              </Button>
              <Button 
                onClick={isLastStep ? handleSubmit : handleNext}
                disabled={isSubmitting}
              >
                {isLastStep ? (isSubmitting ? "İşleniyor..." : "Kampanyayı Başlat") : "İleri"}
              </Button>
            </div>
          </div>
        </div>

        {/* Sağ panel - Önizleme */}
        <div className="lg:col-span-1">
          <CampaignPreview data={campaignData as CampaignPreviewData} />
        </div>
      </div>
    </DashboardLayout>
  )
} 