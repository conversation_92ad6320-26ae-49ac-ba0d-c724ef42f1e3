"use client"

import { useState, useRef, useEffect } from "react"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { cn } from "@/lib/utils"
import { Image, Upload, Info, FileImage, AlertCircle, Bold, Italic, Underline, Type, Link, Edit, Check, ChevronRight, ExternalLink, Radio, User, Smartphone, Globe, Smile, Calendar as CalendarIcon, Clock } from "lucide-react"
import { Card, CardContent } from "@/components/ui/card"
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import NextImage from "next/image"
import { Calendar } from "@/components/ui/calendar"
import { format } from "date-fns"
import { CampaignInput } from "@/components/ui/campaign-input"
import { Link as LinkIcon, AlignLeft } from "lucide-react"

// CampaignData tipini içe aktarma
interface CampaignData {
  name?: string;
  platform?: string;
  objective?: string;
  budget?: number;
  bidStrategy?: string;
  bidAmount?: number;
  startDate?: string | null;
  endDate?: string | null;
  country?: string;
  targetAudience?: string[];
  interests?: string[];
  brandSafety?: string[];
  adContent?: {
    headline?: string;
    description?: string;
    htmlContent?: string;
    image?: string | File | null;
    targetUrl?: string;
    ctaText?: string;
    format?: string;
  };
  [key: string]: unknown;
}

// Emoji verileri
const emojiData = [
  { emoji: "😀", category: "Yüzler" },
  { emoji: "😂", category: "Yüzler" },
  { emoji: "🥰", category: "Yüzler" },
  { emoji: "👍", category: "Jestler" },
  { emoji: "👏", category: "Jestler" },
  { emoji: "🎉", category: "Nesneler" },
  { emoji: "🎁", category: "Nesneler" },
  { emoji: "⭐", category: "Semboller" },
  { emoji: "❤️", category: "Semboller" },
  { emoji: "🚀", category: "Nesneler" },
  { emoji: "💯", category: "Semboller" },
  { emoji: "💪", category: "Jestler" },
  { emoji: "🔥", category: "Nesneler" },
  { emoji: "✨", category: "Nesneler" },
  { emoji: "👋", category: "Jestler" },
  { emoji: "💫", category: "Nesneler" },
]

interface AdContentFormProps {
  data: CampaignData;
  updateData: (data: Partial<CampaignData>) => void;
}

export function AdContentForm({ data, updateData }: AdContentFormProps) {
  const [adFormat, setAdFormat] = useState("image")
  const [previewImage, setPreviewImage] = useState<string | null>(null)
  
  // Artık data doğrudan CampaignData tipinde olduğu için dönüştürmeye gerek yok
  const [headline, setHeadline] = useState(data.adContent?.headline || "")
  const [description, setDescription] = useState(data.adContent?.description || "")
  const [htmlContent, setHtmlContent] = useState(data.adContent?.htmlContent || "")
  const [targetUrl, setTargetUrl] = useState(data.adContent?.targetUrl || "")
  const [ctaText, setCtaText] = useState(data.adContent?.ctaText || "Daha Fazla Bilgi")
  const [customCtaText, setCustomCtaText] = useState("")
  const [isCustomCta, setIsCustomCta] = useState(false)
  const [previewTab, setPreviewTab] = useState<"preview" | "details">("preview")
  const fileInputRef = useRef<HTMLInputElement>(null)
  const textareaRef = useRef<HTMLTextAreaElement>(null)
  const [activeFormats, setActiveFormats] = useState<string[]>([])
  const [startDate, setStartDate] = useState<Date | null>(() => {
    if (!data.startDate) return null;
    try {
      if (typeof data.startDate === 'number') {
        return new Date(data.startDate * 1000);
      }
      if (typeof data.startDate === 'string') {
        return new Date(data.startDate);
      }
      return null;
    } catch (e) {
      return null;
    }
  });
  const [startTime, setStartTime] = useState<string>(() => {
    if (!data.startDate) return "";
    try {
      let dateObj;
      if (typeof data.startDate === 'number') {
        dateObj = new Date(data.startDate * 1000);
      } else if (typeof data.startDate === 'string') {
        dateObj = new Date(data.startDate);
      } else {
        return "";
      }
      return `${dateObj.getHours().toString().padStart(2, '0')}:${dateObj.getMinutes().toString().padStart(2, '0')}`;
    } catch (e) {
      return "";
    }
  });
  const [endDate, setEndDate] = useState<Date | null>(() => {
    if (!data.endDate) return null;
    try {
      if (typeof data.endDate === 'number') {
        return new Date(data.endDate * 1000);
      }
      if (typeof data.endDate === 'string') {
        return new Date(data.endDate);
      }
      return null;
    } catch (e) {
      return null;
    }
  });

  // Formatları track etmek için useEffect
  useEffect(() => {
    const editor = textareaRef.current;
    if (!editor) return;

    // Seçim değiştiğinde aktif formatları güncelle
    const handleSelectionChange = () => {
      if (!editor) return;
      
      const selection = window.getSelection();
      if (!selection || selection.rangeCount === 0) return;
      
      // Şu anda aktif formatları tespit et
      const newActiveFormats = [];
      if (document.queryCommandState('bold')) newActiveFormats.push('bold');
      if (document.queryCommandState('italic')) newActiveFormats.push('italic');
      if (document.queryCommandState('underline')) newActiveFormats.push('underline');
      
      // Type/code formatı için özel kontrol yapılabilir
      
      setActiveFormats(newActiveFormats);
    };

    document.addEventListener('selectionchange', handleSelectionChange);
    return () => {
      document.removeEventListener('selectionchange', handleSelectionChange);
    };
  }, []);

  // HTML içeriğini başlangıçta ayarla
  useEffect(() => {
    if (textareaRef.current) {
      // data'nın tipini kontrol et (unknown olduğu için)
      const adData = data as { adContent?: { htmlContent?: string } } | null | undefined;
      if (adData?.adContent?.htmlContent) {
        // Kaydedilmiş HTML içeriği varsa onu kullan
        textareaRef.current.innerHTML = adData.adContent.htmlContent;
      } else if (description) {
        // Yoksa açıklamayı format vererek göster
        textareaRef.current.innerHTML = formatTextToHtml(description);
      }
    }
  }, [data, description]);

  // Tarih ve saat güncellemeleri
  useEffect(() => {
    if (!startDate || !startTime) return;

    try {
      const [hours, minutes] = startTime.split(":");
      const dateWithTime = new Date(startDate);
      dateWithTime.setHours(Number(hours));
      dateWithTime.setMinutes(Number(minutes));
      
      if (!isNaN(dateWithTime.getTime())) {
        const timestamp = Math.floor(dateWithTime.getTime() / 1000);
        if (data.startDate !== timestamp) {
          updateData({ startDate: timestamp });
        }
      }
    } catch (error) {
      console.error("Tarih dönüşüm hatası:", error);
    }
  }, [startDate, startTime, data.startDate, updateData]);
  
  useEffect(() => {
    if (!endDate) return;

    try {
      const dateWithTime = new Date(endDate);
      dateWithTime.setHours(23, 59, 59, 999);
      
      if (!isNaN(dateWithTime.getTime())) {
        const timestamp = Math.floor(dateWithTime.getTime() / 1000);
        if (data.endDate !== timestamp) {
          updateData({ endDate: timestamp });
        }
      }
    } catch (error) {
      console.error("Tarih dönüşüm hatası:", error);
    }
  }, [endDate, data.endDate, updateData]);

  const ctaOptions = [
    "Daha Fazla Bilgi",
    "Satın Al",
    "Kaydol",
    "İletişime Geç",
    "İndir",
    "Şimdi Keşfet",
    "Ücretsiz Dene",
    "Rezervasyon Yap",
    "custom"
  ]

  const handleFormatChange = (value: string) => {
    setAdFormat("image");
    updateData({ adContent: { ...data.adContent, format: "image" } });
  }

  const handleHeadlineChange = (value: string) => {
    setHeadline(value)
    updateData({
      adContent: {
        ...data.adContent,
        headline: value
      }
    })
  }

  // HTML içeriğinden plain metin elde etme
  const htmlToPlainText = (html: string): string => {
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;
    return tempDiv.textContent || tempDiv.innerText || '';
  };

  // Html içeriğinden markdown benzeri formata dönüştürme
  const htmlToMarkdown = (html: string): string => {
    let markdown = html;
    markdown = markdown.replace(/<strong>(.*?)<\/strong>/g, '**$1**');
    markdown = markdown.replace(/<em>(.*?)<\/em>/g, '_$1_');
    markdown = markdown.replace(/<u>(.*?)<\/u>/g, '<u>$1</u>');
    markdown = markdown.replace(/<code>(.*?)<\/code>/g, '`$1`');
    
    // HTML etiketlerini temizle
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = markdown;
    return tempDiv.textContent || tempDiv.innerText || '';
  };

  // Markdown formatını HTML'e dönüştürme
  const formatTextToHtml = (text: string): string => {
    let formattedText = text;
    
    // Markdown benzeri işaretlemeleri HTML'e dönüştür
    formattedText = formattedText
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      .replace(/_(.*?)_/g, '<em>$1</em>')
      .replace(/<u>(.*?)<\/u>/g, '<u>$1</u>')
      .replace(/`(.*?)`/g, '<code>$1</code>');
    
    return formattedText;
  };

  const handleEditorChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const value = e.target.value;
    setDescription(value);
    setHtmlContent(formatTextToHtml(value));
    
    updateData({
      adContent: {
        ...data.adContent,
        description: value,
        htmlContent: formatTextToHtml(value)
      }
    });
  };

  const handleTargetUrlChange = (value: string) => {
    setTargetUrl(value)
    updateData({
      adContent: {
        ...data.adContent,
        targetUrl: value
      }
    })
  }

  const handleCtaTextChange = (value: string) => {
    if (value === "custom") {
      setIsCustomCta(true)
      return
    }
    
    setIsCustomCta(false)
    setCtaText(value)
    updateData({
      adContent: {
        ...data.adContent,
        ctaText: value
      }
    })
  }

  const handleCustomCtaTextChange = (value: string) => {
    setCustomCtaText(value)
  }

  const applyCustomCtaText = () => {
    if (customCtaText.trim()) {
      setCtaText(customCtaText)
      updateData({
        adContent: {
          ...data.adContent,
          ctaText: customCtaText
        }
      })
    }
  }

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onloadend = () => {
        const imageUrl = reader.result as string
        setPreviewImage(imageUrl)
        updateData({
          adContent: {
            ...data.adContent,
            image: imageUrl
          }
        })
      }
      reader.readAsDataURL(file)
    }
  }

  const triggerFileInput = () => {
    fileInputRef.current?.click()
  }

  // Format uygulama fonksiyonu
  const applyTextFormat = (format: string) => {
    if (!textareaRef.current) return;
    
    // Editöre odaklan
    textareaRef.current.focus();
    
    // Format komutlarını çalıştır
    switch(format) {
      case 'bold':
        document.execCommand('bold', false);
        break;
      case 'italic':
        document.execCommand('italic', false);
        break;
      case 'underline':
        document.execCommand('underline', false);
        break;
      case 'type':
        // Kod formatı için <code> etiketini uygulamak için
        const selection = window.getSelection();
        if (selection && selection.rangeCount > 0) {
          const range = selection.getRangeAt(0);
          const selectedText = range.toString();
          
          if (selectedText) {
            // Seçili metni <code> etiketi ile değiştir
            const codeElement = document.createElement('code');
            codeElement.textContent = selectedText;
            range.deleteContents();
            range.insertNode(codeElement);
          }
        }
        break;
      default:
        break;
    }
    
    // Editör içeriği değişti, state'i güncelle
    handleEditorChange({ target: { value: textareaRef.current.value } } as React.ChangeEvent<HTMLTextAreaElement>);
  };

  const insertEmoji = (emoji: string) => {
    if (!textareaRef.current) return;
    
    // Editöre odaklan
    textareaRef.current.focus();
    
    // Emojinin pozisyonunu ayarla
    document.execCommand('insertText', false, emoji);
    
    // Editör içeriği değişti, state'i güncelle
    handleEditorChange({ target: { value: textareaRef.current.value } } as React.ChangeEvent<HTMLTextAreaElement>);
  };

  return (
    <div className="space-y-8">
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 flex items-start gap-3 mb-8">
        <Info className="h-5 w-5 text-blue-600 mt-0.5" />
        <div>
          <p className="text-sm font-medium text-blue-800">Etkili reklam içeriği oluşturun</p>
          <p className="text-sm text-blue-700 mt-1">
            Net ve dikkat çekici başlıklar, açıklayıcı metinler ve kaliteli görseller kullanarak
            reklamlarınızın performansını artırabilirsiniz.
          </p>
        </div>
      </div>

      <div className="space-y-8">
        <div className="pt-6 space-y-8">
          <div className="grid grid-cols-1 gap-8">
            <div className="space-y-6">
              <div className="space-y-4 text-lg font-semibold">
                <CampaignInput
                  id="ad-headline"
                  label="Reklam Başlığı"
                  placeholder="Başlık girin"
                  value={headline}
                  onChange={e => handleHeadlineChange(e.target.value)}
                  icon={<Type className="h-5 w-5" />}
                />
              </div>

              <div className="space-y-4 mt-6 md:col-span-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="description" className=" text-lg">Reklam Açıklaması</Label>
                  <div className="flex items-center gap-2">
                    <ToggleGroup type="multiple" value={activeFormats} className="border rounded-md bg-white shadow-sm">
                      <ToggleGroupItem 
                        value="bold" 
                        size="sm" 
                        onClick={() => applyTextFormat('bold')} 
                        className="data-[state=on]:bg-blue-50 data-[state=on]:text-blue-600"
                        aria-label="Kalın"
                      >
                        <Bold className="h-4 w-4" />
                      </ToggleGroupItem>
                      <ToggleGroupItem 
                        value="italic" 
                        size="sm" 
                        onClick={() => applyTextFormat('italic')} 
                        className="data-[state=on]:bg-blue-50 data-[state=on]:text-blue-600"
                        aria-label="İtalik"
                      >
                        <Italic className="h-4 w-4" />
                      </ToggleGroupItem>
                      <ToggleGroupItem 
                        value="underline" 
                        size="sm" 
                        onClick={() => applyTextFormat('underline')} 
                        className="data-[state=on]:bg-blue-50 data-[state=on]:text-blue-600"
                        aria-label="Altı Çizili"
                      >
                        <Underline className="h-4 w-4" />
                      </ToggleGroupItem>
                      <ToggleGroupItem 
                        value="type" 
                        size="sm" 
                        onClick={() => applyTextFormat('type')} 
                        className="data-[state=on]:bg-blue-50 data-[state=on]:text-blue-600"
                        aria-label="Kod"
                      >
                        <Type className="h-4 w-4" />
                      </ToggleGroupItem>
                    </ToggleGroup>
                    
                    <Popover>
                      <PopoverTrigger asChild>
                        <Button 
                          variant="outline" 
                          size="sm" 
                          className="h-8 w-8 p-0 border rounded-md bg-white shadow-sm"
                          aria-label="Emoji Ekle"
                        >
                          <Smile className="h-4 w-4" />
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-64 p-2">
                        <div className="space-y-2">
                          <h3 className="text-sm font-medium">Emoji Ekle</h3>
                          <div className="grid grid-cols-6 gap-1">
                            {emojiData.map((item, index) => (
                              <button
                                key={index}
                                className="text-xl p-1 hover:bg-gray-100 rounded"
                                onClick={() => insertEmoji(item.emoji)}
                                aria-label={`${item.emoji} Emoji`}
                              >
                                {item.emoji}
                              </button>
                            ))}
                          </div>
                        </div>
                      </PopoverContent>
                    </Popover>
                  </div>
                </div>
                <p className="text-xs text-muted-foreground">
                  Ürün veya hizmetinizin açıklamasını girin (Maks. 150 karakter)
                </p>
                <div className="relative">
                  <span className="absolute top-5 left-3 text-blue-400 pointer-events-none">
                    <AlignLeft className="h-5 w-5" />
                  </span>
                  <textarea
                    ref={textareaRef}
                    value={description}
                    onChange={handleEditorChange}
                    maxLength={150}
                    className={`
                      w-full
                      pl-10 pr-4 py-4
                      min-h-[120px]
                      bg-slate-50
                      border-2 border-slate-200
                      rounded-xl
                      text-base
                      focus:border-blue-500
                      focus:ring-2 focus:ring-blue-100
                      focus:bg-white
                      transition-all duration-200
                      placeholder:text-slate-400
                      shadow-sm
                      hover:border-blue-300
                      disabled:bg-slate-100 disabled:cursor-not-allowed
                      resize-y
                    `}
                    style={{ minHeight: '112px' }}
                    placeholder="Ürün veya hizmetinizin açıklamasını girin"
                  />
                  <div className="absolute right-3 bottom-3 text-xs text-muted-foreground bg-white px-1">
                    {description.length}/150
                  </div>
                </div>
              </div>

              <div className="space-y-4 mt-6">
                <CampaignInput
                  id="ad-target-url"
                  label="Hedef URL"
                  placeholder="https://ornek.com"
                  value={targetUrl}
                  onChange={e => {
                    setTargetUrl(e.target.value);
                    updateData({ adContent: { ...data.adContent, targetUrl: e.target.value } });
                  }}
                  icon={<LinkIcon className="h-5 w-5" />}
                />
                {targetUrl && !targetUrl.match(/^https?:\/\/.+/i) && (
                  <div className="text-xs text-red-500 mt-1 flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" />
                    <span>URL 'http://' veya 'https://' ile başlamalıdır</span>
                  </div>
                )}
              </div>

              <div className="space-y-4 mt-6">
                <CampaignInput
                  id="ad-cta"
                  label="Call-to-Action (CTA) Butonu"
                  placeholder="Örn: Satın Al, Daha Fazla Bilgi"
                  value={ctaText}
                  onChange={e => handleCtaTextChange(e.target.value)}
                  icon={<Check className="h-5 w-5" />}
                />
                <div className="flex flex-wrap gap-2 mt-2">
                  {ctaOptions.filter(opt => opt !== "custom").map(option => (
                    <Button
                      key={option}
                      type="button"
                      variant={ctaText === option ? "default" : "outline"}
                      size="sm"
                      className="text-xs h-8"
                      onClick={() => handleCtaTextChange(option)}
                    >
                      {option}
                    </Button>
                  ))}
                </div>
              </div>

              <div className="space-y-4 mt-6 md:col-span-2">
                <Label className="text-lg">Reklam Görseli</Label>
                <p className="text-xs text-muted-foreground">
                  Yüksek kaliteli bir görsel ekleyin (Önerilen boyut: 1200x628 piksel)
                </p>

                <input
                  type="file"
                  ref={fileInputRef}
                  onChange={handleImageUpload}
                  accept="image/*"
                  style={{ display: 'none' }}
                />

                {!previewImage ? (
                  <div 
                    onClick={triggerFileInput}
                    className="w-60 h-60 border-2 border-dashed border-gray-300 rounded-lg p-8 flex flex-col items-center justify-center h-56 cursor-pointer hover:border-blue-400 transition-colors bg-gray-50"
                  >
                    <Upload className="h-10 w-10 text-gray-400 mb-3" />
                    <p className="text-sm font-medium mb-1">Görsel Yüklemek İçin Tıklayın</p>
                    <p className="text-xs text-muted-foreground">
                      PNG, JPG veya GIF, maks. 5MB
                    </p>
                  </div>
                ) : (
                  <div className="w-60 h-60 relative rounded-lg overflow-hidden border border-gray-200">
                    <NextImage 
                      src={previewImage}
                      alt="Reklam Önizleme"
                      width={240}
                      height={240}
                      className="object-cover w-full h-full"
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-40 opacity-0 hover:opacity-100 transition-opacity flex items-center justify-center">
                      <Button
                        size="sm"
                        variant="secondary"
                        className="bg-white hover:bg-gray-100"
                        onClick={triggerFileInput}
                      >
                        Görseli Değiştir
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 

