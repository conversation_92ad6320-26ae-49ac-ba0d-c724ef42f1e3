"use client"

import { useState } from "react"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { cn } from "@/lib/utils"
import { Info, ShieldCheck, ChevronDown, ChevronUp } from "lucide-react"
import { Button } from "@/components/ui/button"

interface CampaignAdvancedFormProps {
  data: Record<string, unknown>
  updateData: (data: Record<string, unknown>) => void
}

export function CampaignAdvancedForm({ data, updateData }: CampaignAdvancedFormProps) {
  const [showContentOptions, setShowContentOptions] = useState(true)
  const [brandSafety, setBrandSafety] = useState<string[]>(data?.brandSafety as string[] || ["adult"])
  
  const handleBrandSafetyChange = (values: string[]) => {
    setBrandSafety(values)
    updateData({ brandSafety: values })
  }
  
  const toggleBrandSafetyOption = (option: string) => {
    const updated = brandSafety.includes(option)
      ? brandSafety.filter(item => item !== option)
      : [...brandSafety, option]
    
    handleBrandSafetyChange(updated)
  }
  
  return (
    <div className="space-y-8">
      <div>
        <h2 className="text-2xl font-semibold">Gelişmiş Ayarlar</h2>
        <p className="text-sm text-muted-foreground mt-1 mb-6">
          Kampanyanız için gelişmiş ayarları yapılandırın
        </p>
      </div>
      
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 flex items-start gap-3 mb-8">
        <Info className="h-5 w-5 text-blue-600 mt-0.5" />
        <div>
          <p className="text-sm font-medium text-blue-800">Markanız için güvenli bir ortam sağlayın</p>
          <p className="text-sm text-blue-700 mt-1">
            Reklamlarınızın gösterileceği içerik türlerini kontrol ederek marka itibarınızı koruyun.
          </p>
        </div>
      </div>
      
      <div className="space-y-6">
        <div 
          className="border rounded-xl p-6 space-y-6"
        >
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <ShieldCheck className="h-5 w-5 text-blue-600" />
              <Label className="text-lg font-medium">Marka Güvenliği Kontrolü</Label>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowContentOptions(!showContentOptions)}
              className="h-8 w-8 p-0 rounded-full"
            >
              {showContentOptions ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </Button>
          </div>
          
          <p className="text-sm text-muted-foreground">
            Reklamlarınızın uygunsuz içeriklerle gösterilmesini engeller.
          </p>
          
          {showContentOptions && (
            <div className="space-y-4 pt-4 border-t">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="adult"
                  checked={brandSafety.includes("adult")}
                  onCheckedChange={() => toggleBrandSafetyOption("adult")}
                />
                <Label htmlFor="adult" className="font-normal cursor-pointer">
                  Yetişkin içeriği hariç tut
                </Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="political"
                  checked={brandSafety.includes("political")}
                  onCheckedChange={() => toggleBrandSafetyOption("political")}
                />
                <Label htmlFor="political" className="font-normal cursor-pointer">
                  Politik içeriği hariç tut
                </Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="gaming"
                  checked={brandSafety.includes("gaming")}
                  onCheckedChange={() => toggleBrandSafetyOption("gaming")}
                />
                <Label htmlFor="gaming" className="font-normal cursor-pointer">
                  Oyun içeriğini hariç tut
                </Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="offensive"
                  checked={brandSafety.includes("offensive")}
                  onCheckedChange={() => toggleBrandSafetyOption("offensive")}
                />
                <Label htmlFor="offensive" className="font-normal cursor-pointer">
                  Kaba dil içeren içeriği hariç tut
                </Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="sensitive"
                  checked={brandSafety.includes("sensitive")}
                  onCheckedChange={() => toggleBrandSafetyOption("sensitive")}
                />
                <Label htmlFor="sensitive" className="font-normal cursor-pointer">
                  Hassas konular içeren içeriği hariç tut
                </Label>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
} 