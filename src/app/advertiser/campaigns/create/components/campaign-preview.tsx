"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { ChevronLeft, ChevronRight, Image as ImageIcon, MessageSquare, Info, Calendar, Target, DollarSign, Clock, ExternalLink, Check, Globe } from "lucide-react"
import Image from "next/image"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"

// Kampanya verileri için interface
interface AdContentData {
  headline: string;
  description: string;
  htmlContent?: string;
  image: string | null;
  targetUrl: string;
  ctaText: string;
}

interface CampaignData {
  name: string;
  platform: string;
  objective: string;
  budget: number;
  bidStrategy: string;
  bidAmount: number;
  startDate?: string; // Date string (ISO format)
  endDate?: string;   // Date string (ISO format)
  country: string;
  targetAudience?: string[]; // Veya daha spesifik bir tip
  interests?: string[];
  brandSafety?: string[];
  adContent: AdContentData;
}

interface CampaignPreviewProps {
  data: CampaignData;
}

// Ülke kodlarını ülke adlarına çeviren yardımcı fonksiyon
const getCountryName = (countryCode: string) => {
  const countries: Record<string, string> = {
    "TR": "Türkiye",
    "US": "Amerika Birleşik Devletleri",
    "GB": "Birleşik Krallık",
    "DE": "Almanya",
    "FR": "Fransa",
    "IT": "İtalya",
    "ES": "İspanya"
  }
  return countries[countryCode] || countryCode
}

// Markdown formatını HTML'e dönüştürme
const formatTextToHtml = (text: string): string => {
  let formattedText = text;
  
  // Markdown benzeri işaretlemeleri HTML'e dönüştür
  formattedText = formattedText
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/_(.*?)_/g, '<em>$1</em>')
    .replace(/<u>(.*?)<\/u>/g, '<u>$1</u>')
    .replace(/`(.*?)`/g, '<code>$1</code>');
  
  return formattedText;
};

export function CampaignPreview({ data }: CampaignPreviewProps) {
  const [activeTab, setActiveTab] = useState<"preview" | "info">("preview")

  return (
    <div className="sticky top-6">
      <Card className="border-gray-200">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <CardTitle className="text-xl">Önizleme</CardTitle>
            <div className="flex items-center gap-2">
              <Button 
                variant={activeTab === "preview" ? "default" : "outline"}
                size="sm"
                onClick={() => setActiveTab("preview")}
              >
                Önizleme
              </Button>
              <Button 
                variant={activeTab === "info" ? "default" : "outline"}
                size="sm"
                onClick={() => setActiveTab("info")}
              >
                Detaylar
              </Button>
            </div>
          </div>
          <CardDescription>
            {activeTab === "preview" 
              ? "Kampanyanızın nasıl görüneceğini önizleyin"
              : "Kampanya ayarlarınızın detaylarını görüntüleyin"
            }
          </CardDescription>
        </CardHeader>
        <CardContent>
          {activeTab === "preview" ? (
            <div className="space-y-4">
              {/* Önizleme Alanı */}
              <div className="bg-gray-50 overflow-hidden rounded-lg border border-gray-200 mx-auto">
                <div className="bg-white">
                  <div className="bg-gray-100 rounded-lg p-3 overflow-hidden">
                    <div className="flex items-center justify-between pb-3">
                      <div className="flex items-center gap-2">
                        <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold">
                          F
                        </div>
                        <div>
                          <p className="text-sm font-medium">Adnomio Ads</p>
                          {data.platform && (
                            <p className="text-xs text-gray-500">{data.platform} için reklam</p>
                          )}
                        </div>
                      </div>
                      <div className="flex">
                        <button className="h-8 w-8 rounded-full flex items-center justify-center text-gray-500 hover:bg-gray-200">
                          •••
                        </button>
                      </div>
                    </div>
                    
                    <div className="mb-3">
                      <h3 className="text-base font-semibold mb-1">
                        {data.adContent.headline || "Kampanya Başlığı"}
                      </h3>
                      <p 
                        className="text-sm text-gray-600"
                        dangerouslySetInnerHTML={{ 
                          __html: data.adContent?.htmlContent 
                            ? data.adContent.htmlContent
                            : data.adContent?.description 
                              ? formatTextToHtml(data.adContent.description)
                              : "Kampanyanızın açıklaması burada görünecek. Bu kısım reklam içeriğinizi tanımlar." 
                        }}
                      />
                    </div>
                    
                    <div className="w-full h-48 relative aspect-video bg-gray-200 rounded-md mb-3 flex items-center justify-center">
                      {data.adContent.image ? (
                        <Image 
                          src={data.adContent.image} 
                          alt="Ad preview" 
                          fill 
                          className="object-cover rounded-md" 
                        />
                      ) : (
                        <div className="flex flex-col items-center text-gray-400">
                          <ImageIcon className="h-10 w-10 mb-2" />
                          <span className="text-sm">Görsel burada görünecek</span>
                        </div>
                      )}
                    </div>
                    
                    <div className="mb-3 flex">
                      <Button 
                        size="sm" 
                        className="text-xs h-8 px-3 bg-blue-600 hover:bg-blue-700 text-white"
                      >
                        {data.adContent.ctaText || "Daha Fazla Bilgi"} <ChevronRight className="h-3 w-3 ml-1" />
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="w-full bg-blue-50 rounded-lg p-3 text-center">
                <p className="text-sm text-blue-700 flex items-center justify-center gap-1.5">
                  <Info className="h-4 w-4" />
                  <span>
                    Bu önizleme, reklamınızın kullanıcılara nasıl görüneceğine dair bir fikir verir.
                  </span>
                </p>
              </div>

              <div className="flex justify-center gap-2 mt-2">
                <Button variant="outline" size="icon" className="h-8 w-8 rounded-full">
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <Button variant="outline" size="icon" className="h-8 w-8 rounded-full">
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          ) : (
            <div className="space-y-6">
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-3 flex items-center gap-1.5">
                  <Target className="h-4 w-4" />
                  <span>Kampanya Bilgileri</span>
                </h3>
                <div className="space-y-3">
                  <div className="flex justify-between items-center py-2 border-b border-gray-100">
                    <span className="text-sm">Kampanya Adı:</span>
                    <span className="text-sm font-medium">{data.name || "Belirtilmedi"}</span>
                  </div>
                  <div className="flex justify-between items-center py-2 border-b border-gray-100">
                    <span className="text-sm">Platform:</span>
                    <Badge variant="outline" className="bg-blue-50 text-blue-600 border-blue-200 text-xs">
                      {data.platform || "Belirtilmedi"}
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center py-2 border-b border-gray-100">
                    <span className="text-sm">Hedef Ülke:</span>
                    <div className="flex items-center gap-1.5">
                      <Globe className="h-3.5 w-3.5 text-gray-500" />
                      <span className="text-sm font-medium">
                        {data.country ? getCountryName(data.country) : "Belirtilmedi"}
                      </span>
                    </div>
                  </div>
                  <div className="flex justify-between items-center py-2 border-b border-gray-100">
                    <span className="text-sm">Amaç:</span>
                    <div className="flex items-center gap-1.5">
                      {data.objective && (
                        <Badge 
                          variant="outline" 
                          className={cn(
                            "text-xs",
                            data.objective === "awareness" && "bg-blue-50 text-blue-600 border-blue-200",
                            data.objective === "traffic" && "bg-purple-50 text-purple-600 border-purple-200",
                            data.objective === "conversions" && "bg-green-50 text-green-600 border-green-200",
                          )}
                        >
                          {data.objective === "awareness" && "Marka Bilinirliği"}
                          {data.objective === "traffic" && "Site Trafiği"}
                          {data.objective === "conversions" && "Dönüşümler"}
                        </Badge>
                      )}
                      {!data.objective && (
                        <span className="text-sm font-medium">Belirtilmedi</span>
                      )}
                    </div>
                  </div>
                  <div className="flex justify-between items-center py-2 border-b border-gray-100">
                    <span className="text-sm">Reklam Biçimi:</span>
                    <span className="text-sm font-medium">
                      Görsel Reklam
                    </span>
                  </div>
                </div>
              </div>
              
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-3 flex items-center gap-1.5">
                  <DollarSign className="h-4 w-4" />
                  <span>Bütçe ve Ücretlendirme</span>
                </h3>
                <div className="space-y-3">
                  <div className="flex justify-between items-center py-2 border-b border-gray-100">
                    <span className="text-sm">Toplam Bütçe:</span>
                    <span className="text-sm font-medium">₺{data.budget?.toLocaleString() || "0"}</span>
                  </div>
                  <div className="flex justify-between items-center py-2 border-b border-gray-100">
                    <span className="text-sm">Teklif Stratejisi:</span>
                    <Badge variant="outline" className={cn(
                      "text-xs",
                      data.bidStrategy === "cpm" ? "bg-blue-50 text-blue-600 border-blue-200" : "bg-green-50 text-green-600 border-green-200"
                    )}>
                      {data.bidStrategy === "cpm" ? "Gösterim Başı (CPM)" : "Tıklama Başı (CPC)"}
                    </Badge>
                  </div>
                  <div className="flex justify-between items-center py-2 border-b border-gray-100">
                    <span className="text-sm">Teklif Tutarı:</span>
                    <span className="text-sm font-medium">
                      ₺{data.bidAmount || "0"} {data.bidStrategy === "cpm" ? "/ 1000 gösterim" : "/ tıklama"}
                    </span>
                  </div>
                </div>
              </div>
              
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-3 flex items-center gap-1.5">
                  <Calendar className="h-4 w-4" />
                  <span>Yayın Tarihleri</span>
                </h3>
                <div className="space-y-3">
                  <div className="flex justify-between items-center py-2 border-b border-gray-100">
                    <span className="text-sm">Başlangıç:</span>
                    <span className="text-sm font-medium">
                      {data.startDate ? (
                        typeof data.startDate === 'number' 
                          ? new Date(data.startDate * 1000).toLocaleDateString("tr-TR", {
                              day: "numeric",
                              month: "numeric",
                              year: "numeric"
                            })
                          : new Date(data.startDate).toLocaleDateString("tr-TR", {
                              day: "numeric",
                              month: "numeric",
                              year: "numeric"
                            })
                      ) : "Belirtilmedi"}
                    </span>
                  </div>
                  <div className="flex justify-between items-center py-2 border-b border-gray-100">
                    <span className="text-sm">Bitiş:</span>
                    <span className="text-sm font-medium">
                      {data.endDate ? (
                        typeof data.endDate === 'number' 
                          ? new Date(data.endDate * 1000).toLocaleDateString("tr-TR", {
                              day: "numeric",
                              month: "numeric",
                              year: "numeric"
                            })
                          : new Date(data.endDate).toLocaleDateString("tr-TR", {
                              day: "numeric",
                              month: "numeric",
                              year: "numeric"
                            })
                      ) : "Belirtilmedi"}
                    </span>
                  </div>
                  <div className="flex justify-between items-center py-2 border-b border-gray-100">
                    <span className="text-sm">Süre:</span>
                    <span className="text-sm font-medium">
                      {data.startDate && data.endDate ? (
                        (() => {
                          const start = typeof data.startDate === 'number' 
                            ? new Date(data.startDate * 1000) 
                            : new Date(data.startDate);
                          const end = typeof data.endDate === 'number' 
                            ? new Date(data.endDate * 1000) 
                            : new Date(data.endDate);
                          
                          if (isNaN(start.getTime()) || isNaN(end.getTime())) {
                            return "Geçersiz tarih";
                          }
                          
                          const diffTime = Math.abs(end.getTime() - start.getTime());
                          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                          return `${diffDays} gün`;
                        })()
                      ) : "Belirtilmedi"}
                    </span>
                  </div>
                </div>
              </div>
              
              <div>
                <h3 className="text-sm font-medium text-muted-foreground mb-3 flex items-center gap-1.5">
                  <ExternalLink className="h-4 w-4" />
                  <span>Reklam İçeriği</span>
                </h3>
                <div className="space-y-3">
                  <div className="flex justify-between items-center py-2 border-b border-gray-100">
                    <span className="text-sm">Başlık:</span>
                    <span className="text-sm font-medium truncate max-w-[220px]">
                      {data.adContent.headline || "Belirtilmedi"}
                    </span>
                  </div>
                  <div className="flex justify-between items-center py-2 border-b border-gray-100">
                    <span className="text-sm">Açıklama:</span>
                    <div className="text-sm font-medium truncate max-w-[220px]">
                      {data.adContent?.htmlContent ? (
                        <span dangerouslySetInnerHTML={{ 
                          __html: data.adContent.htmlContent.length > 60 
                            ? data.adContent.htmlContent.substring(0, 60) + "..." 
                            : data.adContent.htmlContent
                        }} />
                      ) : data.adContent?.description ? (
                        <span dangerouslySetInnerHTML={{ 
                          __html: data.adContent.description.length > 30 
                            ? formatTextToHtml(data.adContent.description.substring(0, 30) + "...") 
                            : formatTextToHtml(data.adContent.description)
                        }} />
                      ) : "Belirtilmedi"}
                    </div>
                  </div>
                  <div className="flex justify-between items-center py-2 border-b border-gray-100">
                    <span className="text-sm">Görsel:</span>
                    <div className="flex items-center gap-1.5">
                      {data.adContent.image ? (
                        <Badge variant="outline" className="bg-green-50 text-green-600 border-green-200 text-xs flex items-center gap-1">
                          <Check className="h-3 w-3" />
                          <span>Yüklendi</span>
                        </Badge>
                      ) : (
                        <span className="text-sm font-medium">Yüklenmedi</span>
                      )}
                    </div>
                  </div>
                  <div className="flex justify-between items-center py-2 border-b border-gray-100">
                    <span className="text-sm">CTA:</span>
                    <span className="text-sm font-medium">
                      {data.adContent.ctaText || "Daha Fazla Bilgi"}
                    </span>
                  </div>
                  <div className="flex justify-between items-center py-2 border-b border-gray-100">
                    <span className="text-sm">Hedef URL:</span>
                    <span className="text-sm font-medium truncate max-w-[220px]">
                      {data.adContent.targetUrl || "Belirtilmedi"}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
} 