"use client"

import { useState, useEffect } from "react"
import { Label } from "@/components/ui/label"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { addDays, format } from "date-fns"
import { Calendar as CalendarIcon, Clock } from "lucide-react"
import { tr } from "date-fns/locale"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface CampaignDatesFormProps {
  data: Record<string, unknown>
  updateData: (data: Record<string, unknown>) => void
}

export function CampaignDatesForm({ data, updateData }: CampaignDatesFormProps) {
  const today = new Date()
  const [startDate, setStartDate] = useState<Date | undefined>(() => {
    if (!data.startDate) return undefined;
    try {
      const date = typeof data.startDate === 'number' 
        ? new Date(data.startDate * 1000) 
        : new Date(data.startDate);
      return isNaN(date.getTime()) ? undefined : date;
    } catch (e) {
      return undefined;
    }
  });
  
  const [endDate, setEndDate] = useState<Date | undefined>(() => {
    if (!data.endDate) return undefined;
    try {
      const date = typeof data.endDate === 'number' 
        ? new Date(data.endDate * 1000) 
        : new Date(data.endDate);
      return isNaN(date.getTime()) ? undefined : date;
    } catch (e) {
      return undefined;
    }
  });
  
  const [dateError, setDateError] = useState<string | null>(null)
  const [startTime, setStartTime] = useState<string>(() => {
    if (!data.startDate) return "12:00";
    try {
      const date = typeof data.startDate === 'number' 
        ? new Date(data.startDate * 1000) 
        : new Date(data.startDate);
      return isNaN(date.getTime()) 
        ? "12:00" 
        : `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`;
    } catch (e) {
      return "12:00";
    }
  });

  // Önceden tanımlanmış saat listesi
  const predefinedTimes = [
    "08:00", "08:30", "09:00", "09:30", "10:00", "10:30", "11:00", "11:30",
    "12:00", "12:30", "13:00", "13:30", "14:00", "14:30", "15:00", "15:30",
    "16:00", "16:30", "17:00", "17:30", "18:00", "18:30", "19:00", "19:30",
    "20:00", "20:30", "21:00", "21:30", "22:00", "22:30", "23:00", "23:30"
  ];

  const [hour, setHour] = useState<string>(() => startTime ? startTime.split(":")[0] : "12");
  const [minute, setMinute] = useState<string>(() => startTime ? startTime.split(":")[1] : "00");

  // Tarih değişikliklerini izle ve güncelle
  useEffect(() => {
    if (!startDate) {
      updateData({ startDate: null });
      return;
    }

    try {
      const [hours, minutes] = startTime.split(":");
      const dateWithTime = new Date(startDate);
      dateWithTime.setHours(Number(hours));
      dateWithTime.setMinutes(Number(minutes));
      
      if (!isNaN(dateWithTime.getTime())) {
        const timestamp = Math.floor(dateWithTime.getTime() / 1000);
        if (data.startDate !== timestamp) {
          updateData({ startDate: timestamp });
        }
      }
    } catch (error) {
      console.error("Tarih dönüşüm hatası:", error);
    }
  }, [startDate, startTime]);

  useEffect(() => {
    if (!endDate) {
      updateData({ endDate: null });
      return;
    }

    try {
      const dateWithTime = new Date(endDate);
      dateWithTime.setHours(23, 59, 59, 999);
      
      if (!isNaN(dateWithTime.getTime())) {
        const timestamp = Math.floor(dateWithTime.getTime() / 1000);
        if (data.endDate !== timestamp) {
          updateData({ endDate: timestamp });
        }
      }
    } catch (error) {
      console.error("Tarih dönüşüm hatası:", error);
    }
  }, [endDate]);

  const handleStartDateChange = (date: Date | undefined) => {
    setStartDate(date)
    setDateError(null)
    
    if (!date) {
      updateData({ startDate: null })
      return
    }
    
    if (date && endDate && endDate < date) {
      setEndDate(undefined)
      updateData({ endDate: null })
    }
  }

  const handleEndDateChange = (date: Date | undefined) => {
    if (date && startDate && date < startDate) {
      setDateError("Bitiş tarihi, başlangıç tarihinden önce olamaz.")
      return
    }
    
    setEndDate(date)
    setDateError(null)
  }

  const handleTimeChange = (time: string) => {
    setStartTime(time);
  };

  return (
    <div className="space-y-6 bg-white p-6 rounded-lg shadow-sm border border-gray-100">
      <div>
        <h2 className="text-xl font-semibold text-gray-800">Kampanya Tarihleri</h2>
        <p className="text-sm text-gray-500 mt-1">
          Kampanyanızın yayın süresini belirleyin
        </p>
      </div>

      {dateError && (
        <Alert variant="destructive" className="py-2">
          <AlertDescription>{dateError}</AlertDescription>
        </Alert>
      )}

      <div className="grid md:grid-cols-2 gap-6">
        <div className="space-y-3">
          <Label htmlFor="startDate" className="text-sm font-medium">Başlangıç Tarihi</Label>
          <div className="flex">
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  id="startDate"
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal rounded-r-none border-r-0",
                    !startDate && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {startDate ? (
                    format(startDate, "d MMMM yyyy", { locale: tr })
                  ) : (
                    <span>Tarih seçin</span>
                  )}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={startDate}
                  onSelect={handleStartDateChange}
                  initialFocus
                  disabled={(date) => date < today}
                />
              </PopoverContent>
            </Popover>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  id="startTime"
                  variant="outline"
                  className="rounded-l-none flex items-center font-normal"
                >
                  <Clock className="mr-2 h-4 w-4" />
                  {startTime || "Saat"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="p-0 w-auto" align="start">
                <div className="grid grid-cols-3 gap-1 p-2 max-h-[200px] overflow-y-auto">
                  {predefinedTimes.map((time) => (
                    <Button
                      key={time}
                      variant={startTime === time ? "default" : "ghost"}
                      size="sm"
                      className={`text-sm ${startTime === time ? "bg-blue-500" : ""}`}
                      onClick={() => handleTimeChange(time)}
                    >
                      {time}
                    </Button>
                  ))}
                </div>
              </PopoverContent>
            </Popover>
          </div>
        </div>

        <div className="space-y-3">
          <Label htmlFor="endDate" className="text-sm font-medium">Bitiş Tarihi</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                id="endDate"
                variant="outline"
                className={cn(
                  "w-full justify-start text-left font-normal",
                  !endDate && "text-muted-foreground"
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {endDate ? (
                  format(endDate, "d MMMM yyyy", { locale: tr })
                ) : (
                  <span>Tarih seçin</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={endDate}
                onSelect={handleEndDateChange}
                initialFocus
                disabled={(date) => 
                  (startDate ? date < startDate : date < today) || 
                  date < addDays(today, 1)
                }
              />
            </PopoverContent>
          </Popover>
        </div>
      </div>

      {startDate && endDate && (
        <div className="mt-4 p-3 bg-blue-50 rounded-md border border-blue-100 text-center">
          <p className="text-sm text-blue-800">
            Kampanyanız <span className="font-medium">{format(startDate, "d MMMM", { locale: tr })}</span> - 
            <span className="font-medium"> {format(endDate, "d MMMM yyyy", { locale: tr })}</span> tarihleri arasında 
            <span className="font-medium"> {Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))} gün</span> yayında olacak
          </p>
        </div>
      )}

      <p className="text-xs text-gray-500 italic">
        Not: Belirlediğiniz bütçe limitine ulaşılması durumunda kampanyanız bitiş tarihinden önce durdurulabilir.
      </p>
    </div>
  )
} 