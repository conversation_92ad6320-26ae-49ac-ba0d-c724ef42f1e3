"use client"

import { cn } from "@/lib/utils"

interface CampaignStepIndicatorProps {
  currentStep: number
}

export function CampaignStepIndicator({ currentStep }: CampaignStepIndicatorProps) {
  const steps = [
    { id: 1, name: "<PERSON><PERSON><PERSON><PERSON>ay<PERSON>" },
    { id: 2, name: "<PERSON><PERSON><PERSON>" },
    { id: 3, name: "<PERSON><PERSON><PERSON> İçeriği" },
    { id: 4, name: "<PERSON>ütç<PERSON> ve Teklif" },
    { id: 5, name: "<PERSON><PERSON><PERSON>" },
    { id: 6, name: "Gelişmiş Ayarlar" }
  ]

  const progress = Math.min(100, ((currentStep - 1) / (steps.length - 1)) * 100)

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-xs font-medium text-gray-600">Adım {currentStep}/6</p>
          <p className="font-medium">{steps.find(step => step.id === currentStep)?.name}</p>
        </div>
        <div className="text-sm font-medium">
          {progress === 100 ? "Tamamlandı" : "Devam Ediyor"}
        </div>
      </div>
      
      <div className="relative h-2 w-full bg-gray-100 rounded-full overflow-hidden">
        <div 
          className="absolute h-full bg-blue-600 transition-all duration-300 ease-in-out"
          style={{ width: `${progress}%` }}
        />
      </div>

      <div className="flex items-center justify-between text-xs text-muted-foreground">
        {currentStep === 1 && "Kampanya ile ilgili temel bilgileri girin"}
        {currentStep === 2 && "Reklamınızın hedef kitlesini belirleyin"}
        {currentStep === 3 && "Reklam içeriğini oluşturun"}
        {currentStep === 4 && "Kampanya bütçenizi ve teklif stratejinizi belirleyin"}
        {currentStep === 5 && "Kampanyanın başlangıç ve bitiş tarihlerini seçin"}
        {currentStep === 6 && "Kampanya için ek güvenlik ve kontrol ayarlarını yapın"}
      </div>
    </div>
  )
} 