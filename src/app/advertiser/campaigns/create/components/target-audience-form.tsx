"use client"

import { useState } from "react"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { Info, Users, MapPin, Globe, ChevronDown, ChevronUp, Plus } from "lucide-react"
import { CampaignSelect, CampaignSelectItem } from "@/components/ui/campaign-select"

interface TargetAudienceFormProps {
  data: Record<string, unknown>
  updateData: (data: Record<string, unknown>) => void
}

interface Country {
  code: string
  name: string
  flag: string
}

interface InterestCategory {
  id: string
  name: string
}

export function TargetAudienceForm({ data, updateData }: TargetAudienceFormProps) {
  const [showAdvanced, setShowAdvanced] = useState(false)
  const [selectedCountry, setSelectedCountry] = useState<string>(() => {
    const country = data.country;
    return typeof country === 'string' ? country : "TR";
  })
  const [selectedInterests, setSelectedInterests] = useState<string[]>(() => {
    if (Array.isArray(data.interests)) {
      return data.interests as string[];
    }
    return [];
  })

  const handleCountryChange = (value: string) => {
    setSelectedCountry(value)
    updateData({ country: value })
  }

  const toggleInterest = (interest: string) => {
    const updated = selectedInterests.includes(interest)
      ? selectedInterests.filter(item => item !== interest)
      : [...selectedInterests, interest]
    
    setSelectedInterests(updated)
    updateData({ interests: updated })
  }

  const countries: Country[] = [
    { code: "TR", name: "Türkiye", flag: "🇹🇷" },
    { code: "US", name: "Amerika Birleşik Devletleri", flag: "🇺🇸" },
    { code: "GB", name: "Birleşik Krallık", flag: "🇬🇧" },
    { code: "DE", name: "Almanya", flag: "🇩🇪" },
    { code: "FR", name: "Fransa", flag: "🇫🇷" },
    { code: "IT", name: "İtalya", flag: "🇮🇹" },
    { code: "ES", name: "İspanya", flag: "🇪🇸" },
    { code: "CA", name: "Kanada", flag: "🇨🇦" },
    { code: "AU", name: "Avustralya", flag: "🇦🇺" },
    { code: "JP", name: "Japonya", flag: "🇯🇵" },
    { code: "NL", name: "Hollanda", flag: "🇳🇱" },
    { code: "BE", name: "Belçika", flag: "🇧🇪" },
    { code: "SE", name: "İsveç", flag: "🇸🇪" },
    { code: "NO", name: "Norveç", flag: "🇳🇴" },
    { code: "DK", name: "Danimarka", flag: "🇩🇰" },
    { code: "FI", name: "Finlandiya", flag: "🇫🇮" },
    { code: "PT", name: "Portekiz", flag: "🇵🇹" },
    { code: "GR", name: "Yunanistan", flag: "🇬🇷" },
    { code: "PL", name: "Polonya", flag: "🇵🇱" },
    { code: "AT", name: "Avusturya", flag: "🇦🇹" },
    { code: "HU", name: "Macaristan", flag: "🇭🇺" },
    { code: "CH", name: "İsviçre", flag: "🇨🇭" },
    { code: "RU", name: "Rusya", flag: "🇷🇺" },
    { code: "CN", name: "Çin", flag: "🇨🇳" },
    { code: "IN", name: "Hindistan", flag: "🇮🇳" },
    { code: "BR", name: "Brezilya", flag: "🇧🇷" },
    { code: "MX", name: "Meksika", flag: "🇲🇽" },
    { code: "AR", name: "Arjantin", flag: "🇦🇷" },
    { code: "ZA", name: "Güney Afrika", flag: "🇿🇦" },
    { code: "AE", name: "Birleşik Arap Emirlikleri", flag: "🇦🇪" },
    { code: "SA", name: "Suudi Arabistan", flag: "🇸🇦" },
    { code: "KR", name: "Güney Kore", flag: "🇰🇷" },
    { code: "SG", name: "Singapur", flag: "🇸🇬" },
    { code: "MY", name: "Malezya", flag: "🇲🇾" },
    { code: "ID", name: "Endonezya", flag: "🇮🇩" },
    { code: "TH", name: "Tayland", flag: "🇹🇭" },
    { code: "VN", name: "Vietnam", flag: "🇻🇳" },
    { code: "PH", name: "Filipinler", flag: "🇵🇭" },
    { code: "EG", name: "Mısır", flag: "🇪🇬" },
    { code: "IL", name: "İsrail", flag: "🇮🇱" },
  ]

  const interestCategories: InterestCategory[] = [
    { id: "tech", name: "Teknoloji" },
    { id: "sports", name: "Spor" },
    { id: "fashion", name: "Moda" },
    { id: "music", name: "Müzik" },
    { id: "movies", name: "Film" },
    { id: "books", name: "Kitap" },
    { id: "travel", name: "Seyahat" },
    { id: "food", name: "Yemek" },
    { id: "health", name: "Sağlık" },
    { id: "fitness", name: "Fitness" },
    { id: "business", name: "İş" },
    { id: "education", name: "Eğitim" },
    { id: "gaming", name: "Oyun" },
    { id: "beauty", name: "Güzellik" },
    { id: "decoration", name: "Dekorasyon" },
  ]

  return (
    <div className="space-y-8">
     
      
      <div className="space-y-6">
        <div>
          <div className="flex items-center gap-2 mb-4">
            <Globe className="h-5 w-5 text-gray-700" />
            <Label className="text-lg font-medium">Konum</Label>
          </div>
          
          <div className="space-y-4">
            <Label htmlFor="country">Reklamlarınızın gösterileceği ülkeyi seçin</Label>
            
         
            
            <CampaignSelect
              value={selectedCountry}
              onValueChange={handleCountryChange}
              placeholder="Ülke seçin"
              icon={<Globe className="h-5 w-5" />}
            >
              {countries.map((country) => (
                <CampaignSelectItem key={country.code} value={country.code} className="flex items-center gap-2">
                  <span className="mr-2">{country.flag}</span> {country.name}
                </CampaignSelectItem>
              ))}
            </CampaignSelect>

            <div className="bg-blue-50 border border-blue-100 rounded-lg p-2 mb-6">
              <div className="flex items-start gap-3">
                <Info className="h-5 w-5 text-blue-500 mt-0.5" />
                <div>
                  <p className="text-sm text-blue-500 mt-1">
                    Reklamınız şu an için sadece tek bir ülkede gösterilebilir.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div className="pt-6 border-t">
          <div className="flex items-center gap-2 mb-4">
            <Globe className="h-5 w-5 text-gray-700" />
            <Label className="text-lg font-medium">İlgi Alanları</Label>
          </div>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <p className="text-sm text-muted-foreground">
                Reklamlarınızın gösterileceği ilgi alanlarını seçin
              </p>
              <p className="text-sm font-medium">
                Seçilen: <span className={cn(
                  selectedInterests.length < 3 ? "text-red-500" : "text-green-600"
                )}>{selectedInterests.length}</span> <span className="text-gray-500">(En az 3 seçim yapılmalı)</span>
              </p>
            </div>
            
            <div className="border rounded-lg p-6">
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-3">
                {interestCategories.map((interest) => (
                  <div 
                    key={interest.id}
                    onClick={() => toggleInterest(interest.id)}
                    className={cn(
                      "border rounded-lg p-3 cursor-pointer flex items-center gap-2 transition-colors",
                      selectedInterests.includes(interest.id) 
                        ? "border-blue-500 bg-blue-50" 
                        : "border-gray-200 hover:border-gray-300 hover:bg-gray-50"
                    )}
                  >
                    <div className={cn(
                      "w-6 h-6 rounded-full flex items-center justify-center",
                      selectedInterests.includes(interest.id) 
                        ? "bg-blue-500 text-white" 
                        : "bg-gray-100 text-gray-400"
                    )}>
                      {selectedInterests.includes(interest.id) ? (
                        <span className="text-xs font-bold">+</span>
                      ) : (
                        <Plus className="h-3 w-3" />
                      )}
                    </div>
                    <span className={cn(
                      "font-medium",
                      selectedInterests.includes(interest.id) ? "text-blue-700" : "text-gray-700"
                    )}>
                      {interest.name}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
        
        <Button
          variant="outline"
          onClick={() => setShowAdvanced(!showAdvanced)}
          className="w-full flex items-center justify-center gap-2"
        >
          {showAdvanced ? "Gelişmiş Ayarları Gizle" : "Gelişmiş Ayarlar"}
          {showAdvanced ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
        </Button>
        
        {showAdvanced && (
          <div className="pt-6 border-t space-y-6">
            <div className="flex items-center gap-2 mb-4">
              <Users className="h-5 w-5 text-gray-700" />
              <Label className="text-lg font-medium">Davranışsal Hedefleme</Label>
            </div>
            
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <p className="text-sm text-yellow-800">
                Davranışsal hedefleme seçenekleri yakında gelecek. Şimdilik diğer hedefleme seçeneklerini kullanabilirsiniz.
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
} 