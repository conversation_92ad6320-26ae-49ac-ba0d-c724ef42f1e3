"use client"

import { useState, useEffect } from "react"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { cn } from "@/lib/utils"
import { Calculator, Coins, DollarSign, Eye, Info, MousePointer, TrendingUp } from "lucide-react"
import { Slider } from "@/components/ui/slider"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { CampaignInput } from "@/components/ui/campaign-input"

interface BudgetFormProps {
  data: Record<string, unknown>
  updateData: (data: Record<string, unknown>) => void
}

export function BudgetForm({ data, updateData }: BudgetFormProps) {
  const [bidStrategy, setBidStrategy] = useState(() => {
    const strategy = data.bidStrategy;
    return typeof strategy === 'string' ? strategy : "cpm";
  });
  const [budget, setBudget] = useState(Number(data.budget) || 5000)
  const [bidAmount, setBidAmount] = useState(Number(data.bidAmount) || 10)
  const [currency, setCurrency] = useState("TL")
  const [estimatedResults, setEstimatedResults] = useState({ impressions: 0, clicks: 0 })

  // Tahmini sonuçları güncelle
  useEffect(() => {
    let impressions = 0;
    let clicks = 0;
    
    const numericBudget = Number(budget);
    const numericBidAmount = Number(bidAmount);
    
    if (bidStrategy === "cpm") {
      // CPM için tahmini gösterim sayısı (basit hesaplama)
      impressions = Math.floor(numericBudget / numericBidAmount * 1000);
      // Ortalama %1 tıklama oranı ile tahmini tıklama
      clicks = Math.floor(impressions * 0.01);
    } else {
      // CPC için tahmini tıklama sayısı
      clicks = Math.floor(numericBudget / numericBidAmount);
      // Ortalama %1 tıklama oranı ile tahmini gösterim
      impressions = Math.floor(clicks * 100);
    }
    
    setEstimatedResults({ impressions, clicks });
  }, [budget, bidAmount, bidStrategy]);

  const handleBidStrategyChange = (value: string) => {
    setBidStrategy(value)
    // Teklif stratejisi değiştiğinde varsayılan teklif tutarını güncelle
    const defaultBidAmount = value === "cpm" ? 10 : 2
    setBidAmount(defaultBidAmount)
    updateData({ 
      bidStrategy: value,
      bidAmount: defaultBidAmount
    })
  }

  const handleBudgetChange = (value: number | string) => {
    // Eğer string bir değer gelirse (biçimlendirilmiş sayı) işleme alıyoruz
    let numericValue: number;
    
    if (typeof value === 'string') {
      // Noktaları kaldırıp sayıya çeviriyoruz (Tr formatı için)
      numericValue = Number(value.replace(/\./g, ''));
    } else {
      numericValue = value;
    }
    
    // Sayısal değeri güncelliyoruz
    setBudget(numericValue);
    
    // Backend'e gönderilecek veri için değeri string olarak kaydediyoruz
    updateData({ budget: numericValue.toString() });
  }

  const handleBidAmountChange = (value: number) => {
    setBidAmount(value)
    updateData({ bidAmount: value })
  }

  const formatNumber = (num: number) => {
    return isNaN(num) ? "0" : num.toLocaleString('tr-TR');
  }

  return (
    <div className="space-y-8">
      <div className="bg-gradient-to-r from-blue-50 to-blue-100 p-4 rounded-xl border border-blue-200">
       
        <p className="text-sm text-blue-700  max-w-1xl">
          Kampanya bütçenizi ve teklif stratejinizi belirleyin. Doğru strateji ve bütçe planlaması, reklamlarınızın hedef kitlenize etkili şekilde ulaşmasını sağlar.
        </p>
      </div>

      <div className="space-y-8 bg-white rounded-xl p-6 border shadow-sm">
        <div>
          <div className="flex justify-between items-start mb-3">
            <div>
              <Label htmlFor="budget" className="text-lg font-medium">Toplam Kampanya Bütçesi</Label>
              <p className="text-sm text-muted-foreground">
                Kampanya süresince harcanacak toplam tutar
              </p>
            </div>
          </div>
          
          <div className="mt-5">
            <div className="flex items-center justify-between">
              <div className="flex items-end gap-2">
                <span className="text-3xl font-bold text-blue-700">₺{formatNumber(budget)}</span>
                <span className="text-gray-500 mb-1">toplam bütçe</span>
              </div>
              
              <div className="flex items-end gap-1 w-auto flex-shrink-0">
                <CampaignInput
                  id="budget"
                  type="text"
                  label="Toplam Kampanya Bütçesi"
                  value={formatNumber(budget)}
                  onChange={e => handleBudgetChange(e.target.value)}
                  icon={<TrendingUp className="h-5 w-5" />}
                  min={1000}
                  max={100000}
                  className="max-w-xs w-full h-10 text-lg font-medium text-left"
                />
                <span className="text-lg font-medium text-gray-600 pb-1">₺</span>
              </div>
            </div>
            
            
            
            <div className="grid grid-cols-5 gap-3 mt-8">
              {[
                { value: 5000, label: "₺5.000" },
                { value: 10000, label: "₺10.000" },
                { value: 20000, label: "₺20.000" },
                { value: 30000, label: "₺30.000" },
                { value: 50000, label: "₺50.000" }
              ].map((item) => (
                <button
                  key={item.value}
                  onClick={() => handleBudgetChange(item.value)}
                  className={cn(
                    "py-3 px-2 rounded-lg text-sm font-medium transition-all",
                    budget === item.value
                      ? "bg-blue-100 text-blue-700 border-2 border-blue-500"
                      : "bg-white text-gray-700 border border-gray-300 hover:border-blue-300 hover:bg-blue-50"
                  )}
                >
                  {item.label}
                </button>
              ))}
            </div>
            
            
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6 p-4 bg-slate-50 rounded-lg border border-slate-200">
            <div className="space-y-1">
              <p className="text-sm text-slate-500 flex items-center gap-1.5">
                <Eye className="h-4 w-4 text-slate-400" />
                Tahmini gösterim sayısı
              </p>
              <p className="text-xl font-semibold text-slate-800">{formatNumber(estimatedResults.impressions)}</p>
            </div>
            <div className="space-y-1">
              <p className="text-sm text-slate-500 flex items-center gap-1.5">
                <MousePointer className="h-4 w-4 text-slate-400" />
                Tahmini tıklama sayısı
              </p>
              <p className="text-xl font-semibold text-slate-800">{formatNumber(estimatedResults.clicks)}</p>
            </div>
          </div>
        </div>

        <div className="space-y-4 pt-8 border-t">
          <div className="flex justify-between items-start">
            <div>
              <Label className="text-lg font-medium">Teklif Stratejisi</Label>
              <p className="text-sm text-muted-foreground">
                Reklam gösterimleriniz için nasıl ücretlendirme yapılacağını seçin
              </p>
            </div>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger>
                  <Info className="h-5 w-5 text-blue-500" />
                </TooltipTrigger>
                <TooltipContent side="left" className="max-w-[300px] p-4">
                  <p className="text-sm">
                    <strong>CPM (Gösterim Başı Maliyet):</strong> Her 1000 gösterim için ödeme yaparsınız. Marka bilinirliği hedefleri için uygundur.
                  </p>
                  <p className="text-sm mt-2">
                    <strong>CPC (Tıklama Başı Maliyet):</strong> Yalnızca reklamınıza tıklandığında ödeme yaparsınız. Trafik ve dönüşüm hedefleri için uygundur.
                  </p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>

          <RadioGroup value={bidStrategy} onValueChange={handleBidStrategyChange} className="mt-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div 
                className={cn(
                  "border rounded-xl p-6 cursor-pointer hover:border-blue-200 hover:bg-blue-50 transition-colors relative",
                  bidStrategy === "cpm" ? "border-blue-500 bg-blue-50" : ""
                )}
                onClick={() => handleBidStrategyChange("cpm")}
              >
                <div className="flex items-start gap-3">
                  <RadioGroupItem 
                    value="cpm" 
                    id="cpm" 
                    className="mt-1"
                  />
                  <div>
                    <Label
                      htmlFor="cpm"
                      className="font-bold text-blue-700 text-lg"
                    >
                      Gösterim Başı Maliyet (CPM)
                    </Label>
                    <div className="flex items-center gap-2 mb-2 mt-1">
                      <Eye className="h-4 w-4 text-blue-500" />
                      <p className="text-sm text-gray-600">
                        1000 gösterim başına ücretlendirme
                      </p>
                    </div>
                    
                    <ul className="mt-4 space-y-2">
                      <li className="text-sm text-gray-600 flex items-start gap-2">
                        <span className="text-blue-500 text-lg leading-5">•</span>
                        <span>Marka bilinirliği için ideal</span>
                      </li>
                      <li className="text-sm text-gray-600 flex items-start gap-2">
                        <span className="text-blue-500 text-lg leading-5">•</span>
                        <span>Geniş kitleye ulaşmak isteyenler için</span>
                      </li>
                    </ul>
                    
                    <div className="text-sm text-gray-600 mt-4 flex items-center gap-2">
                      <span className="font-medium">Önerilen teklif:</span>
                      <Badge variant="outline" className="bg-blue-100 text-blue-800 border-blue-200">₺10 - ₺20 / 1000 gösterim</Badge>
                    </div>
                  </div>
                </div>
                
                {bidStrategy === "cpm" && (
                  <div className="absolute top-3 right-3">
                    <Badge className="bg-blue-500">Seçili</Badge>
                  </div>
                )}
              </div>

              <div 
                className={cn(
                  "border rounded-xl p-6 cursor-pointer hover:border-indigo-200 hover:bg-indigo-50 transition-colors relative",
                  bidStrategy === "cpc" ? "border-indigo-500 bg-indigo-50" : ""
                )}
                onClick={() => handleBidStrategyChange("cpc")}
              >
                <div className="flex items-start gap-3">
                  <RadioGroupItem 
                    value="cpc" 
                    id="cpc" 
                    className="mt-1"
                  />
                  <div>
                    <Label 
                      htmlFor="cpc" 
                      className="font-bold text-indigo-700 text-lg"
                    >
                      Tıklama Başı Maliyet (CPC)
                    </Label>
                    <div className="flex items-center gap-2 mb-2 mt-1">
                      <MousePointer className="h-4 w-4 text-indigo-500" />
                      <p className="text-sm text-gray-600">
                        Tıklama başına ücretlendirme
                      </p>
                    </div>
                    
                    <ul className="mt-4 space-y-2">
                      <li className="text-sm text-gray-600 flex items-start gap-2">
                        <span className="text-indigo-500 text-lg leading-5">•</span>
                        <span>Trafik ve dönüşüm için ideal</span>
                      </li>
                      <li className="text-sm text-gray-600 flex items-start gap-2">
                        <span className="text-indigo-500 text-lg leading-5">•</span>
                        <span>Sonuç odaklı kampanyalar için</span>
                      </li>
                    </ul>
                    
                    <div className="text-sm text-gray-600 mt-4 flex items-center gap-2">
                      <span className="font-medium">Önerilen teklif:</span>
                      <Badge variant="outline" className="bg-indigo-100 text-indigo-800 border-indigo-200">₺2 - ₺5 / tıklama</Badge>
                    </div>
                  </div>
                </div>
                
                {bidStrategy === "cpc" && (
                  <div className="absolute top-3 right-3">
                    <Badge className="bg-indigo-500">Seçili</Badge>
                  </div>
                )}
              </div>
            </div>
          </RadioGroup>
        </div>

        <div className="space-y-4 pt-8 border-t">
          <div className="flex justify-between items-start">
            <div>
              <Label htmlFor="bidAmount" className="text-lg font-medium">
                {bidStrategy === "cpm" ? "CPM Teklif Tutarı" : "CPC Teklif Tutarı"}
              </Label>
              <p className="text-sm text-muted-foreground">
                {bidStrategy === "cpm" 
                  ? "Her 1000 gösterim için ödemeyi istediğiniz tutar" 
                  : "Her tıklama için ödemeyi istediğiniz tutar"}
              </p>
            </div>
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-1">
                <Calculator className="h-4 w-4 text-slate-500" />
                <span className="text-sm text-slate-500">
                  {bidStrategy === "cpm" 
                    ? `${formatNumber(Math.floor(Number(budget) / Number(bidAmount) * 1000))} tahmini gösterim`
                    : `${formatNumber(Math.floor(Number(budget) / Number(bidAmount)))} tahmini tıklama`}
                </span>
              </div>
            </div>
          </div>
          
          <div className="flex items-end gap-1 mt-4">
            <CampaignInput
              id="bidAmount"
              type="number"
              value={bidAmount}
              onChange={e => handleBidAmountChange(Number(e.target.value))}
              icon={<Calculator className="h-5 w-5" />}
              className="text-lg h-10"
            />
            <span className="w-10 h-10 rounded-md bg-gray-100 flex items-center justify-center text-gray-700 font-medium">₺</span>
          </div>
          
          <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 flex items-start gap-3">
              <Coins className="h-5 w-5 text-blue-600 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-blue-800">Önerilen teklif aralığı</p>
                <p className="text-sm text-blue-700 mt-1">
                  {bidStrategy === "cpm" ? "₺10 - ₺20 / 1000 gösterim" : "₺2 - ₺5 / tıklama"}
                </p>
                <p className="text-xs text-blue-600 mt-2">
                  Bu aralıkta teklif vermek, reklamınızın rekabetçi kalmasını sağlar
                </p>
              </div>
            </div>
            
            <div className="bg-green-50 border border-green-200 rounded-lg p-4 flex items-start gap-3">
              <TrendingUp className="h-5 w-5 text-green-600 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-green-800">Tahmini bütçe kullanımı</p>
                <div className="flex flex-col text-sm text-green-700 mt-1">
                  <span className="flex justify-between">
                    <span>Günlük tahmini harcama:</span>
                    <span className="font-medium">₺{formatNumber(Math.round(budget / 30))}</span>
                  </span>
                  <span className="flex justify-between mt-1">
                    <span>Haftalık tahmini harcama:</span>
                    <span className="font-medium">₺{formatNumber(Math.round(budget / 4))}</span>
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 