"use client"

import { useState } from "react"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { BeakerIcon, ShoppingCart, BarChart, MessageCircle, Hash } from "lucide-react"
import { cn } from "@/lib/utils"
import { CampaignInput } from "@/components/ui/campaign-input"

interface CampaignDetailsFormProps {
  data: Record<string, unknown>
  updateData: (data: Record<string, unknown>) => void
  fieldErrors?: Record<string, string>
  showErrors?: boolean
  disabled?: boolean
}

export function CampaignDetailsForm({ data, updateData, fieldErrors, showErrors, disabled = false }: CampaignDetailsFormProps) {
  const [objective, setObjective] = useState<string>(data?.objective as string || "awareness")
  const [platform, setPlatform] = useState<string>(data?.platform as string || "Telegram")

  const handleObjectiveChange = (value: string) => {
    if (disabled) return
    setObjective(value)
    updateData({ objective: value })
  }

  const handlePlatformChange = (value: string) => {
    if (disabled) return
    setPlatform(value)
    updateData({ platform: value })
  }

  return (
    <div className="space-y-8">
      <div className="space-y-4">
        <div>
          <CampaignInput
            id="campaign-name"
            label="Kampanya Adı"
            placeholder="Örn: Yaz Sezonu Promosyonu"
            value={data?.name as string || ""}
            onChange={(e) => !disabled && updateData({ name: e.target.value })}
            disabled={disabled}
            error={fieldErrors?.["name"]}
          />
        </div>
      </div>

      <div className="space-y-4">
        <div>
          <Label className="text-lg font-semibold">Reklam Platformu</Label>
          <p className="text-sm text-muted-foreground mb-6">
            Reklamınızın gösterileceği platformu seçin
          </p>
          {fieldErrors?.["platform"] && showErrors && (
            <p className="text-sm text-red-600 mb-2">{fieldErrors["platform"]}</p>
          )}

          <RadioGroup value={platform || "Telegram"} onValueChange={handlePlatformChange} disabled={disabled}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div 
                className={cn(
                  "border rounded-xl p-6 transition-colors",
                  disabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer hover:border-blue-200 hover:bg-blue-50",
                  platform === "Telegram" ? "border-blue-500 bg-blue-50 ring-2 ring-blue-200" : ""
                )}
                onClick={() => !disabled && handlePlatformChange("Telegram")}
              >
                <div className="flex items-start gap-3">
                  <RadioGroupItem 
                    value="Telegram" 
                    id="telegram" 
                    className="mt-1"
                  />
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-3">
                      <Label 
                        htmlFor="telegram" 
                        className="font-bold text-lg text-blue-700"
                      >
                        Telegram
                      </Label>
                      <div className="bg-blue-500 rounded-lg p-2 text-white">
                        <MessageCircle className="h-5 w-5" />
                      </div>
                    </div>
                    <div className="border-t border-blue-100 pt-3">
                      <p className="text-sm text-gray-600 mb-2">
                        Telegram reklamları ile:
                      </p>
                      <ul className="text-xs text-gray-600 space-y-1">
                        <li className="flex items-center gap-1">
                          <div className="h-1.5 w-1.5 rounded-full bg-blue-500"></div>
                          <span>Yüksek etkileşimli kanal ve gruplara erişin</span>
                        </li>
                        <li className="flex items-center gap-1">
                          <div className="h-1.5 w-1.5 rounded-full bg-blue-500"></div>
                          <span>Teknoloji ve kripto meraklılarına ulaşın</span>
                        </li>
                        <li className="flex items-center gap-1">
                          <div className="h-1.5 w-1.5 rounded-full bg-blue-500"></div>
                          <span>Özelleştirilmiş mesajlar gönderebilirsiniz</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              <div 
                className={cn(
                  "border rounded-xl p-6 transition-colors",
                  disabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer hover:border-indigo-200 hover:bg-indigo-50",
                  platform === "Discord" ? "border-indigo-500 bg-indigo-50 ring-2 ring-indigo-200" : ""
                )}
                onClick={() => !disabled && handlePlatformChange("Discord")}
              >
                <div className="flex items-start gap-3">
                  <RadioGroupItem 
                    value="Discord" 
                    id="discord" 
                    className="mt-1"
                  />
                  <div className="flex-1">
                    <div className="flex items-center justify-between mb-3">
                      <Label 
                        htmlFor="discord" 
                        className="font-bold text-lg text-indigo-700"
                      >
                        Discord
                      </Label>
                      <div className="bg-indigo-600 rounded-lg p-2 text-white">
                        <Hash className="h-5 w-5" />
                      </div>
                    </div>
                    <div className="border-t border-indigo-100 pt-3">
                      <p className="text-sm text-gray-600 mb-2">
                        Discord reklamları ile:
                      </p>
                      <ul className="text-xs text-gray-600 space-y-1">
                        <li className="flex items-center gap-1">
                          <div className="h-1.5 w-1.5 rounded-full bg-indigo-500"></div>
                          <span>Oyun ve hobi topluluklarına erişin</span>
                        </li>
                        <li className="flex items-center gap-1">
                          <div className="h-1.5 w-1.5 rounded-full bg-indigo-500"></div>
                          <span>Genç ve teknoloji odaklı kitlelere ulaşın</span>
                        </li>
                        <li className="flex items-center gap-1">
                          <div className="h-1.5 w-1.5 rounded-full bg-indigo-500"></div>
                          <span>Özel topluluk ve sunucularda reklam yapın</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </RadioGroup>
        </div>
      </div>

      <div className="space-y-4">
        <div>
          <Label className="text-lg font-semibold">Kampanya Hedefi</Label>
          <p className="text-sm text-muted-foreground mb-6">
            Kampanyanızın öncelikli hedefini seçin
          </p>
          {fieldErrors?.["objective"] && showErrors && (
            <p className="text-sm text-red-600 mb-2">{fieldErrors["objective"]}</p>
          )}

          <RadioGroup value={objective || "awareness"} onValueChange={handleObjectiveChange} disabled={disabled}>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              <div
                className={cn(
                  "border rounded-lg p-4 transition-colors",
                  disabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer hover:border-blue-200 hover:bg-blue-50",
                  objective === "awareness" ? "border-blue-500 bg-blue-50" : ""
                )}
                onClick={() => !disabled && handleObjectiveChange("awareness")}
              >
                <div className="flex flex-col items-center text-center gap-2">
                  <div className={cn(
                    "w-10 h-10 rounded-full flex items-center justify-center",
                    objective === "awareness" ? "bg-blue-100 text-blue-700" : "bg-gray-100 text-gray-600"
                  )}>
                    <BeakerIcon className="h-5 w-5" />
                  </div>
                  <RadioGroupItem
                    value="awareness"
                    id="awareness"
                    className="sr-only"
                  />
                  <Label
                    htmlFor="awareness"
                    className={cn(
                      "font-medium cursor-pointer",
                      objective === "awareness" ? "text-blue-700" : "text-gray-700"
                    )}
                  >
                    Marka Bilinirliği
                  </Label>
                  <p className="text-xs text-gray-500">
                    Markanızı daha fazla kişiye tanıtın
                  </p>
                </div>
              </div>

              <div
                className={cn(
                  "border rounded-lg p-4 transition-colors",
                  disabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer hover:border-green-200 hover:bg-green-50",
                  objective === "traffic" ? "border-green-500 bg-green-50" : ""
                )}
                onClick={() => !disabled && handleObjectiveChange("traffic")}
              >
                <div className="flex flex-col items-center text-center gap-2">
                  <div className={cn(
                    "w-10 h-10 rounded-full flex items-center justify-center",
                    objective === "traffic" ? "bg-green-100 text-green-700" : "bg-gray-100 text-gray-600"
                  )}>
                    <BarChart className="h-5 w-5" />
                  </div>
                  <RadioGroupItem
                    value="traffic"
                    id="traffic"
                    className="sr-only"
                  />
                  <Label
                    htmlFor="traffic"
                    className={cn(
                      "font-medium cursor-pointer",
                      objective === "traffic" ? "text-green-700" : "text-gray-700"
                    )}
                  >
                    Site Trafiği
                  </Label>
                  <p className="text-xs text-gray-500">
                    Web sitenize ziyaretçi çekin
                  </p>
                </div>
              </div>

              <div
                className={cn(
                  "border rounded-lg p-4 transition-colors",
                  disabled ? "opacity-50 cursor-not-allowed" : "cursor-pointer hover:border-orange-200 hover:bg-orange-50",
                  objective === "conversions" ? "border-orange-500 bg-orange-50" : ""
                )}
                onClick={() => !disabled && handleObjectiveChange("conversions")}
              >
                <div className="flex flex-col items-center text-center gap-2">
                  <div className={cn(
                    "w-10 h-10 rounded-full flex items-center justify-center",
                    objective === "conversions" ? "bg-orange-100 text-orange-700" : "bg-gray-100 text-gray-600"
                  )}>
                    <ShoppingCart className="h-5 w-5" />
                  </div>
                  <RadioGroupItem
                    value="conversions"
                    id="conversions"
                    className="sr-only"
                  />
                  <Label
                    htmlFor="conversions"
                    className={cn(
                      "font-medium cursor-pointer",
                      objective === "conversions" ? "text-orange-700" : "text-gray-700"
                    )}
                  >
                    Dönüşümler
                  </Label>
                  <p className="text-xs text-gray-500">
                    Satışları veya kayıtları artırın
                  </p>
                </div>
              </div>
            </div>
          </RadioGroup>
        </div>
      </div>
    </div>
  )
} 