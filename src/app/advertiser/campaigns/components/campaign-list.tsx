"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { 
  Pagination, 
  PaginationContent, 
  PaginationItem, 
  PaginationLink, 
  PaginationNext, 
  PaginationPrevious 
} from "@/components/ui/pagination"
import { 
  ChevronRight,
  FileEdit,
  MoreHorizontal,
  Pause,
  Play,
  Trash2,
  TrendingUp,
  BarChart4,
  AlertCircle,
  ChevronDown,
  ChevronUp,
  Plus,
  Search,
  ChevronLeft,
  Calendar,
  Users,
  Eye,
  MousePointerClick,
  Target,
  Clock,
  DollarSign
} from "lucide-react"
import { fetchCampaignList, fetchCampaignDelete, Campaign as APICampaign } from "@/shared/services/advertiser/advertiser-campaign-list"
import { fetchCampaignResume } from "@/shared/services/advertiser/advertiser-campaign-resume"
import { fetchCampaignPause } from "@/shared/services/advertiser/advertiser-campaign-pause"
import { fetchCampaignExtend } from "@/shared/services/advertiser/advertiser-campaign-extend"
import { fetchCampaignBudgetIncrease } from "@/shared/services/advertiser/advertiser-campaign-budget-increase"
import { authService } from "@/shared/services/auth-service"
import { useToast } from "@/components/ui/use-toast"
import { Skeleton } from "@/components/ui/skeleton"
import Link from "next/link"
import { useLanguage } from "@/shared/hooks/useLanguage"
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "@/components/ui/card"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

interface CampaignListProps {
  searchQuery: string
  statusFilter: string
  setSearchQuery: (query: string) => void
}

interface CampaignListParams {
  limit: number
  skip: number
  campaign_status?: string
  status?: string
  search?: string
}

export function CampaignList({ searchQuery, statusFilter, setSearchQuery }: CampaignListProps) {
  const [selectedCampaigns, setSelectedCampaigns] = useState<string[]>([])
  const [currentPage, setCurrentPage] = useState(1)
  const [itemsPerPage, setItemsPerPage] = useState(10)
  const [campaigns, setCampaigns] = useState<APICampaign[]>([])
  const [totalCount, setTotalCount] = useState(0)
  const [loading, setLoading] = useState(false)
  const [authError, setAuthError] = useState(false)
  const [deletingCampaign, setDeletingCampaign] = useState<string | null>(null)
  const { toast } = useToast()
  const router = useRouter()
  const { t } = useLanguage()
  const [sort, setSort] = useState<{key: keyof APICampaign, desc: boolean}>({key: 'starts_at', desc: true})
  const [selectedCampaignForDetails, setSelectedCampaignForDetails] = useState<APICampaign | null>(null)
  const [updatingCampaign, setUpdatingCampaign] = useState<string | null>(null)
  const [isDropdownOpen, setIsDropdownOpen] = useState<string | null>(null)
  const [extendModalOpen, setExtendModalOpen] = useState(false)
  const [selectedCampaignForExtend, setSelectedCampaignForExtend] = useState<APICampaign | null>(null)
  const [newEndDate, setNewEndDate] = useState("")
  const [extendDuration, setExtendDuration] = useState(7)
  const [budgetIncreaseModalOpen, setBudgetIncreaseModalOpen] = useState(false)
  const [selectedCampaignForBudgetIncrease, setSelectedCampaignForBudgetIncrease] = useState<APICampaign | null>(null)
  const [additionalBudget, setAdditionalBudget] = useState("")

  // API'den kampanyaları çek
  useEffect(() => {
    const fetchCampaigns = async () => {
      setLoading(true);
      try {
        // Oturum kontrolü
        const user = authService.getCurrentUser();
        if (!user || !authService.getAuthToken()) {
          console.error("Kampanya listesi - Geçerli bir oturum bulunamadı");
          setAuthError(true);
          
          toast({
            title: "Oturum Hatası",
            description: "Oturumunuz sona ermiş. Lütfen tekrar giriş yapın.",
            variant: "destructive"
          });
          
          setTimeout(() => {
            router.push("/auth/login");
          }, 1500);
          
          setLoading(false);
          return;
        }
        
        const params: CampaignListParams = {
          limit: itemsPerPage,
          skip: (currentPage - 1) * itemsPerPage,
        };
        
        // Status filtresi ekle (API'nin beklediği formatta)
        if (statusFilter !== "all") {
          // statusFilter değerlerini backend'in beklediği formata çevir
          let statusValue = "";
          switch (statusFilter.toLowerCase()) {
            case "active":
              statusValue = "ACTIVE";
              break;
            case "paused":
              statusValue = "STOP";
              break;
            case "planned":
              statusValue = "PLANNED";
              break;
            case "completed":
              statusValue = "FINISHED";
              break;
            default:
              // Eğer statusFilter direkt API formatında geliyorsa (büyük harfle)
              statusValue = statusFilter.toUpperCase();
          }
          
          // Try both possible parameter names for status filtering
          params.campaign_status = statusValue;
          (params as CampaignListParams).status = statusValue; // Alternative parameter name
        }

        // Arama filtresi ekle
        if (searchQuery && searchQuery.trim() !== "") {
          params.search = searchQuery;
        }

        console.log("API Request Params:", params); // Debug için
        console.log("Original statusFilter:", statusFilter); // Debug için
        console.log("Final campaign_status param:", params.campaign_status); // Debug için
        console.log("Request body being sent:", JSON.stringify(params)); // Debug için

        const response = await fetchCampaignList(params);
        console.log("API Response:", response); // Debug için
        console.log("Response data count:", response.result?.data?.length); // Debug için
        console.log("Response stats:", response.result?.stats); // Debug için
        
        if (response.status && response.result?.data) {
          setCampaigns(response.result.data);
          setTotalCount(response.result.stats?.total || 0);
        } else {
          setCampaigns([]);
          setTotalCount(0);
        }
      } catch (error) {
        console.error("Kampanya listesi alınamadı:", error);
        setCampaigns([]);
        setTotalCount(0);
      } finally {
        setLoading(false);
      }
    };

    fetchCampaigns();
  }, [currentPage, itemsPerPage, statusFilter, searchQuery, authError, toast, router]);

  // Filtreleme ve sıralama işlemleri
  const filteredCampaigns = campaigns;

  const totalPages = Math.ceil(totalCount / itemsPerPage);

  const sortedCampaigns = [...filteredCampaigns].sort((a, b) => {
    const aVal = a[sort.key]
    const bVal = b[sort.key]
    if (sort.key === 'campaign_budget') {
        const numA = parseFloat(aVal as string || "0");
        const numB = parseFloat(bVal as string || "0");
        return sort.desc ? numB - numA : numA - numB;
    }
    if (typeof aVal === 'number' && typeof bVal === 'number') {
      return sort.desc ? bVal - aVal : aVal - bVal
    }
    if (typeof aVal === 'string' && typeof bVal === 'string') {
      return sort.desc ? bVal.localeCompare(aVal) : aVal.localeCompare(bVal)
    }
    return 0
  })

  const paginatedCampaigns = sortedCampaigns;

  // Checkbox işlemleri
  const toggleSelectAll = () => {
    if (selectedCampaigns.length === campaigns.length) {
      setSelectedCampaigns([])
    } else {
      setSelectedCampaigns(campaigns.map(campaign => campaign.campaign_id))
    }
  }

  const toggleSelectCampaign = (id: string) => {
    if (selectedCampaigns.includes(id)) {
      setSelectedCampaigns(selectedCampaigns.filter(campaignId => campaignId !== id))
    } else {
      setSelectedCampaigns([...selectedCampaigns, id])
    }
  }

  // Durum Badge renkleri (API'den gelen status değerlerine göre)
  const getStatusBadgeVariant = (status: string | undefined | null) => {
    if (!status) return "neutral";
    
    const statusStr = String(status).toUpperCase();
    switch (statusStr) {
      case "ACTIVE":
        return "success"
      case "FINISHED":
        return "neutral"
      case "PLANNED":
        return "warning"
      case "STOP":
        return "error"
      default:
        return "neutral"
    }
  }

  // Status değerini Türkçe olarak göster (API'den gelen değerlere göre)
  const getStatusText = (status: string | undefined | null) => {
    if (!status) return "Bilinmiyor";
    
    const statusStr = String(status).toUpperCase();
    switch (statusStr) {
      case "ACTIVE":
        return t('advertiser:campaigns.status.active');
      case "FINISHED":
        return t('advertiser:campaigns.status.completed');
      case "PLANNED":
        return t('advertiser:campaigns.status.planned');
      case "STOP":
        return t('advertiser:campaigns.status.paused');
      default:
        return String(status);
    }
  }

  // API'den dönen verilerle eksik alanlar için varsayılan değerleri sağla
  const getCampaignDetail = (campaign: APICampaign) => {
    // campaign_type alanı API'de olmadığı için campaign_aim ile doldur
    const campaignType = campaign.campaign_aim || "Diğer";
    
    // Bütçe bilgileri API'den geliyor - spending alanını kullan
    const budget = {
      total: Number(campaign.campaign_budget) || 0,
      spent: Number(campaign.spending) || 0, // API'den gelen spending değerini kullan
    };
    
    // Metrik bilgileri API'den geliyor
    const metrics = {
      impressions: campaign.views || 0,
      clicks: campaign.clicks || 0,
      ctr: campaign.ctr || 0,
      conversions: campaign.conversions || 0
    };
    
    return {
      type: campaignType,
      budget,
      metrics
    };
  };

  // Kampanya silme işlemi
  const handleDeleteCampaign = async (campaignId: string) => {
    if (confirm("Bu kampanyayı silmek istediğinize emin misiniz? Bu işlem geri alınamaz.")) {
      try {
        setDeletingCampaign(campaignId);
        
        const response = await fetchCampaignDelete({ campaign_id: campaignId });
        
        if (response.status) {
          toast({
            title: "Başarılı",
            description: "Kampanya başarıyla silindi.",
            variant: "default"
          });
          
          // Kampanya listesini güncelle
          setCampaigns(prevCampaigns => 
            prevCampaigns.filter(campaign => campaign.campaign_id !== campaignId)
          );
          
          // Seçili kampanyalardan da kaldır
          if (selectedCampaigns.includes(campaignId)) {
            setSelectedCampaigns(prevSelected => 
              prevSelected.filter(id => id !== campaignId)
            );
          }
        } else {
          // 401 hata kontrolü
          if (response.errorCode === "AUTH_FAILED") {
            console.error("Kampanya silme - Yetkilendirme hatası");
            setAuthError(true);
            
            toast({
              title: "Oturum Hatası",
              description: "Oturumunuz sona ermiş. Lütfen tekrar giriş yapın.",
              variant: "destructive"
            });
            
            setTimeout(() => {
              router.push("/auth/login");
            }, 1500);
            
            return;
          }
          
          throw new Error(response.desc || "Kampanya silinirken bir hata oluştu");
        }
      } catch (error) {
        console.error("Kampanya silme hatası:", error);
        
        toast({
          title: "Hata",
          description: error instanceof Error ? error.message : "Kampanya silinirken bir hata oluştu",
          variant: "destructive"
        });
      } finally {
        setDeletingCampaign(null);
      }
    }
  };

  // Kampanya durum değiştirme işlemi
  const handleStatusChange = async (campaignId: string, action: 'resume' | 'pause') => {
    try {
      setUpdatingCampaign(campaignId);
      
      const response = action === 'resume' 
        ? await fetchCampaignResume({ campaign_id: campaignId })
        : await fetchCampaignPause({ campaign_id: campaignId });
      
      if (response.status) {
        toast({
          title: "Başarılı",
          description: action === 'resume' 
            ? "Kampanya başarıyla aktifleştirildi."
            : "Kampanya başarıyla duraklatıldı.",
          variant: "default"
        });
        
        // Kampanya listesini güncelle
        setCampaigns(prevCampaigns => 
          prevCampaigns.map(campaign => 
            campaign.campaign_id === campaignId
              ? { ...campaign, status: action === 'resume' ? 'ACTIVE' : 'STOP' }
              : campaign
          )
        );
      } else {
        // 401 hata kontrolü
        if (response.errorCode === "AUTH_FAILED") {
          console.error("Kampanya durum değiştirme - Yetkilendirme hatası");
          setAuthError(true);
          
          toast({
            title: "Oturum Hatası",
            description: "Oturumunuz sona ermiş. Lütfen tekrar giriş yapın.",
            variant: "destructive"
          });
          
          setTimeout(() => {
            router.push("/auth/login");
          }, 1500);
          
          return;
        }
        
        throw new Error(response.desc || "Kampanya durumu değiştirilirken bir hata oluştu");
      }
    } catch (error) {
      console.error("Kampanya durum değiştirme hatası:", error);
      
      toast({
        title: "Hata",
        description: error instanceof Error ? error.message : "Kampanya durumu değiştirilirken bir hata oluştu",
        variant: "destructive"
      });
    } finally {
      setUpdatingCampaign(null);
    }
  };

  const handleExtendCampaign = async (campaignId: string) => {
    try {
      setUpdatingCampaign(campaignId);
      
      // Calculate new end date from duration
      const currentEndDate = selectedCampaignForExtend?.ends_at;
      if (!currentEndDate) {
        throw new Error("Kampanya bitiş tarihi bulunamadı");
      }
      
      const currentEndTimestamp = typeof currentEndDate === 'string' ? Number(currentEndDate) : currentEndDate;
      const newEndTimestamp = currentEndTimestamp + (extendDuration * 24 * 60 * 60); // Add duration in seconds
      
      const response = await fetchCampaignExtend({ 
        campaign_id: campaignId, 
        new_end_date: newEndTimestamp.toString() 
      });
      
      if (response.status) {
        toast({
          title: "Başarılı",
          description: "Kampanya başarıyla uzatıldı.",
          variant: "default"
        });
        
        // Kampanya listesini güncelle
        setCampaigns(prevCampaigns => 
          prevCampaigns.map(campaign => 
            campaign.campaign_id === campaignId
              ? { ...campaign, ends_at: newEndTimestamp }
              : campaign
          )
        );
      } else {
        throw new Error(response.desc || "Kampanya uzatılırken bir hata oluştu");
      }
    } catch (error) {
      console.error("Kampanya uzatma hatası:", error);
      
      toast({
        title: "Hata",
        description: error instanceof Error ? error.message : "Kampanya uzatılırken bir hata oluştu",
        variant: "destructive"
      });
    } finally {
      setUpdatingCampaign(null);
    }
  };

  const handleBudgetIncrease = async (campaignId: string) => {
    try {
      setUpdatingCampaign(campaignId);
      
      if (!additionalBudget || parseFloat(additionalBudget) <= 0) {
        throw new Error("Geçerli bir bütçe miktarı girin");
      }
      
      const response = await fetchCampaignBudgetIncrease({ 
        campaign_id: campaignId, 
        additional_budget: additionalBudget 
      });
      
      if (response.status) {
        toast({
          title: "Başarılı",
          description: "Kampanya bütçesi başarıyla artırıldı.",
          variant: "default"
        });
        
        // Kampanya listesini güncelle
        setCampaigns(prevCampaigns => 
          prevCampaigns.map(campaign => 
            campaign.campaign_id === campaignId
              ? { ...campaign, campaign_budget: Number(campaign.campaign_budget) + parseFloat(additionalBudget) }
              : campaign
          )
        );
      } else {
        throw new Error(response.desc || "Kampanya bütçesi artırılırken bir hata oluştu");
      }
    } catch (error) {
      console.error("Kampanya bütçesi artırma hatası:", error);
      
      toast({
        title: "Hata",
        description: error instanceof Error ? error.message : "Kampanya bütçesi artırılırken bir hata oluştu",
        variant: "destructive"
      });
    } finally {
      setUpdatingCampaign(null);
    }
  };

  const handleDropdownItemClick = (campaignId: string, action: () => void) => {
    action();
    setIsDropdownOpen(null);
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="border rounded-md">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[50px]">
                  <Skeleton className="h-5 w-5 rounded" />
                </TableHead>
                <TableHead><Skeleton className="h-4 w-24" /></TableHead>
                <TableHead><Skeleton className="h-4 w-16" /></TableHead>
                <TableHead><Skeleton className="h-4 w-16" /></TableHead>
                <TableHead><Skeleton className="h-4 w-20" /></TableHead>
                <TableHead className="text-right"><Skeleton className="h-4 w-16" /></TableHead>
                <TableHead className="text-right"><Skeleton className="h-4 w-16" /></TableHead>
                <TableHead className="text-right"><Skeleton className="h-4 w-16" /></TableHead>
                <TableHead className="text-right"><Skeleton className="h-4 w-12" /></TableHead>
                <TableHead className="text-right"><Skeleton className="h-4 w-16" /></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {Array.from({ length: 6 }).map((_, i) => (
                <TableRow key={i}>
                  <TableCell className="w-[50px]"><Skeleton className="h-5 w-5 rounded" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-32 mb-2" /><Skeleton className="h-3 w-20" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                  <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                  <TableCell className="text-right"><Skeleton className="h-4 w-16" /></TableCell>
                  <TableCell className="text-right"><Skeleton className="h-4 w-16" /></TableCell>
                  <TableCell className="text-right"><Skeleton className="h-4 w-16" /></TableCell>
                  <TableCell className="text-right"><Skeleton className="h-4 w-12" /></TableCell>
                  <TableCell className="text-right"><Skeleton className="h-4 w-16" /></TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    );
  }

  if (campaigns.length === 0) {
    return <div className="py-24 text-center text-muted-foreground">Gösterilecek kampanya bulunamadı.</div>;
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>{t('advertiser:campaigns.myCampaigns')}</CardTitle>
            <CardDescription>{t('advertiser:campaigns.campaignListDescription')}</CardDescription>
          </div>
          
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead 
                  className="cursor-pointer"
                  onClick={() => setSort(s => ({key: 'title', desc: s.key === 'title' ? !s.desc : true}))}
                >
                  <div className="flex items-center gap-1">
                    {t('advertiser:campaigns.name')}
                    {sort.key === 'title' && (
                      sort.desc ? <ChevronDown className="h-4 w-4" /> : <ChevronUp className="h-4 w-4" />
                    )}
                  </div>
                </TableHead>
                <TableHead>{t('advertiser:campaigns.statusList')}</TableHead>
                <TableHead 
                  className="text-right cursor-pointer"
                  onClick={() => setSort(s => ({key: 'starts_at', desc: s.key === 'starts_at' ? !s.desc : true}))}
                >
                  <div className="flex items-center justify-end gap-1">
                    {t('advertiser:campaigns.startDate')}
                    {sort.key === 'starts_at' && (
                      sort.desc ? <ChevronDown className="h-4 w-4" /> : <ChevronUp className="h-4 w-4" />
                    )}
                  </div>
                </TableHead>
                <TableHead 
                  className="text-right cursor-pointer"
                  onClick={() => setSort(s => ({key: 'ends_at', desc: s.key === 'ends_at' ? !s.desc : true}))}
                >
                  <div className="flex items-center justify-end gap-1">
                    {t('advertiser:campaigns.endDate')}
                    {sort.key === 'ends_at' && (
                      sort.desc ? <ChevronDown className="h-4 w-4" /> : <ChevronUp className="h-4 w-4" />
                    )}
                  </div>
                </TableHead>
                <TableHead 
                  className="text-right cursor-pointer"
                  onClick={() => setSort(s => ({key: 'campaign_budget', desc: s.key === 'campaign_budget' ? !s.desc : true}))}
                >
                  <div className="flex items-center justify-end gap-1">
                    {t('advertiser:campaigns.metrics.budget')}
                    {sort.key === 'campaign_budget' && (
                      sort.desc ? <ChevronDown className="h-4 w-4" /> : <ChevronUp className="h-4 w-4" />
                    )}
                  </div>
                </TableHead>
                <TableHead 
                  className="text-right cursor-pointer"
                  onClick={() => setSort(s => ({key: 'campaign_budget', desc: s.key === 'campaign_budget' ? !s.desc : true}))}
                >
                  <div className="flex items-center justify-end gap-1">
                    {t('advertiser:campaigns.metrics.spent')}
                    {sort.key === 'campaign_budget' && (
                      sort.desc ? <ChevronDown className="h-4 w-4" /> : <ChevronUp className="h-4 w-4" />
                    )}
                  </div>
                </TableHead>
                <TableHead className="text-right">{t('advertiser:campaigns.actions')}</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {paginatedCampaigns.map((campaign) => (
                <TableRow key={campaign.campaign_id} className="group">
                  <TableCell>
                    <div className="flex flex-col gap-1 max-w-[200px]">
                      <span className="font-medium truncate">{campaign.title}</span>
                      <span className="text-xs text-muted-foreground truncate">{campaign.campaign_aim}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant={getStatusBadgeVariant(campaign.status)}>
                      {getStatusText(campaign.status)}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    {campaign.starts_at ? (() => {
                      const timestamp = typeof campaign.starts_at === 'string' ? parseFloat(campaign.starts_at) : campaign.starts_at;
                      const date = new Date(timestamp > 10000000000 ? timestamp : timestamp * 1000);
                      return date.toLocaleDateString("tr-TR", {
                        day: "numeric",
                        month: "short",
                        year: "numeric"
                      });
                    })() : "-"}
                  </TableCell>
                  <TableCell className="text-right">
                    {campaign.ends_at ? (() => {
                      const timestamp = typeof campaign.ends_at === 'string' ? parseFloat(campaign.ends_at) : campaign.ends_at;
                      const date = new Date(timestamp > 10000000000 ? timestamp : timestamp * 1000);
                      return date.toLocaleDateString("tr-TR", {
                        day: "numeric",
                        month: "short",
                        year: "numeric"
                      });
                    })() : "-"}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="font-medium">
                      ₺{getCampaignDetail(campaign).budget.total.toLocaleString()}
                    </div>
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="font-medium">
                      ₺{getCampaignDetail(campaign).budget.spent.toLocaleString()}
                    </div>
                    {campaign.status !== "PLANNED" && (
                      <div className="w-full bg-gray-200 rounded-full h-1 mt-1">
                        <div 
                          className="bg-primary h-1 rounded-full" 
                          style={{ 
                            width: `${Math.min(100, Math.max(0, (getCampaignDetail(campaign).budget.spent / getCampaignDetail(campaign).budget.total) * 100))}%` 
                          }}
                        ></div>
                      </div>
                    )}
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex justify-end items-center space-x-1 opacity-0 group-hover:opacity-100 transition-opacity">
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        className="h-8 w-8"
                        onClick={() => setSelectedCampaignForDetails(campaign)}
                      >
                        <BarChart4 className="h-4 w-4" />
                      </Button>
                      <Link href={`/advertiser/campaigns/edit/${campaign.campaign_id}`}>
                        <Button variant="ghost" size="icon" className="h-8 w-8">
                          <FileEdit className="h-4 w-4" />
                        </Button>
                      </Link>
                      <DropdownMenu 
                        open={isDropdownOpen === campaign.campaign_id} 
                        onOpenChange={(open) => setIsDropdownOpen(open ? campaign.campaign_id : null)}
                      >
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Kampanya İşlemleri</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem 
                            className="flex items-center gap-2"
                            onClick={() => handleDropdownItemClick(campaign.campaign_id, () => {})}
                          >
                            <TrendingUp className="h-4 w-4" />
                            <span>Analitik</span>
                          </DropdownMenuItem>
                          <Link href={`/advertiser/campaigns/edit/${campaign.campaign_id}`}>
                            <DropdownMenuItem 
                              className="flex items-center gap-2"
                              onClick={() => handleDropdownItemClick(campaign.campaign_id, () => {})}
                            >
                              <FileEdit className="h-4 w-4" />
                              <span>Düzenle</span>
                            </DropdownMenuItem>
                          </Link>
                          {campaign.status === "ACTIVE" ? (
                            <>
                              <DropdownMenuItem 
                                className="flex items-center gap-2"
                                onClick={() => handleDropdownItemClick(campaign.campaign_id, () => handleStatusChange(campaign.campaign_id, 'pause'))}
                                disabled={updatingCampaign === campaign.campaign_id}
                              >
                                {updatingCampaign === campaign.campaign_id ? (
                                  <>
                                    <Pause className="h-4 w-4 animate-pulse" />
                                    <span>Duraklatılıyor...</span>
                                  </>
                                ) : (
                                  <>
                                    <Pause className="h-4 w-4" />
                                    <span>Duraklat</span>
                                  </>
                                )}
                              </DropdownMenuItem>
                              <DropdownMenuItem 
                                className="flex items-center gap-2"
                                onClick={() => {
                                  setSelectedCampaignForExtend(campaign);
                                  setExtendModalOpen(true);
                                }}
                                disabled={updatingCampaign === campaign.campaign_id}
                              >
                                {updatingCampaign === campaign.campaign_id ? (
                                  <>
                                    <Clock className="h-4 w-4 animate-pulse" />
                                    <span>Süreyi Uzat</span>
                                  </>
                                ) : (
                                  <>
                                    <Clock className="h-4 w-4" />
                                    <span>Süreyi Uzat</span>
                                  </>
                                )}
                              </DropdownMenuItem>
                              <DropdownMenuItem 
                                className="flex items-center gap-2"
                                onClick={() => {
                                  setSelectedCampaignForBudgetIncrease(campaign);
                                  setBudgetIncreaseModalOpen(true);
                                }}
                                disabled={updatingCampaign === campaign.campaign_id}
                              >
                                {updatingCampaign === campaign.campaign_id ? (
                                  <>
                                    <DollarSign className="h-4 w-4 animate-pulse" />
                                    <span>Bütçeyi Arttır</span>
                                  </>
                                ) : (
                                  <>
                                    <DollarSign className="h-4 w-4" />
                                    <span>Bütçeyi Arttır</span>
                                  </>
                                )}
                              </DropdownMenuItem>
                            </>
                          ) : (
                            <DropdownMenuItem 
                              className="flex items-center gap-2"
                              onClick={() => handleDropdownItemClick(campaign.campaign_id, () => handleStatusChange(campaign.campaign_id, 'resume'))}
                              disabled={updatingCampaign === campaign.campaign_id}
                            >
                              {updatingCampaign === campaign.campaign_id ? (
                                <>
                                  <Play className="h-4 w-4 animate-pulse" />
                                  <span>Aktifleştiriliyor...</span>
                                </>
                              ) : (
                                <>
                                  <Play className="h-4 w-4" />
                                  <span>Aktifleştir</span>
                                </>
                              )}
                            </DropdownMenuItem>
                          )}
                          <DropdownMenuSeparator />
                          <DropdownMenuItem 
                            className="flex items-center gap-2 text-destructive focus:text-destructive"
                            onClick={(e) => {
                              e.preventDefault();
                              e.stopPropagation();
                              handleDeleteCampaign(campaign.campaign_id);
                            }}
                            disabled={deletingCampaign === campaign.campaign_id}
                          >
                            {deletingCampaign === campaign.campaign_id ? (
                              <>
                                <Trash2 className="h-4 w-4 animate-pulse" />
                                <span>Siliniyor...</span>
                              </>
                            ) : (
                              <>
                                <Trash2 className="h-4 w-4" />
                                <span>Sil</span>
                              </>
                            )}
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
        {paginatedCampaigns.length === 0 && (
          <div className="flex flex-col items-center justify-center gap-4 py-8 border-t border-border text-center">
            <div className="flex h-16 w-16 items-center justify-center rounded-full bg-muted">
              <Search className="h-8 w-8 text-muted-foreground" />
            </div>
            <div>
              <p className="text-lg font-medium">{t('advertiser:campaigns.noCampaignsFound')}</p>
              <p className="mt-1 text-sm text-muted-foreground">{t('advertiser:campaigns.noCampaignsFoundDescription')}</p>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" onClick={() => setSearchQuery('')}>{t('advertiser:campaigns.clearSearch')}</Button>
              <Link href="/advertiser/campaigns/create">
              </Link>
            </div>
          </div>
        )}
      </CardContent>

      {/* Campaign Details Modal */}
      <Dialog open={!!selectedCampaignForDetails} onOpenChange={() => setSelectedCampaignForDetails(null)}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold">
              {selectedCampaignForDetails?.title}
            </DialogTitle>
          </DialogHeader>
          
          {selectedCampaignForDetails && (
            <div className="grid gap-6">
              {/* Status and Basic Info */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <Badge variant={getStatusBadgeVariant(selectedCampaignForDetails.status)}>
                    {getStatusText(selectedCampaignForDetails.status)}
                  </Badge>
                  <span className="text-sm text-muted-foreground">
                    Platform: {selectedCampaignForDetails.advertising_platform}
                  </span>
                  <span className="text-sm text-muted-foreground">
                    Ülke: {selectedCampaignForDetails.country}
                  </span>
                </div>
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <Calendar className="h-4 w-4" />
                    <span>
                      {new Date(typeof selectedCampaignForDetails.starts_at === 'string' ? Number(selectedCampaignForDetails.starts_at) * 1000 : selectedCampaignForDetails.starts_at * 1000).toLocaleDateString("tr-TR")} - 
                      {new Date(typeof selectedCampaignForDetails.ends_at === 'string' ? Number(selectedCampaignForDetails.ends_at) * 1000 : selectedCampaignForDetails.ends_at * 1000).toLocaleDateString("tr-TR")}
                    </span>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Campaign Details Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Basic Campaign Info */}
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Kampanya Bilgileri</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Kampanya ID</span>
                      <span className="font-mono text-xs">{selectedCampaignForDetails.campaign_id}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Kampanya Amacı</span>
                      <span className="text-sm">{selectedCampaignForDetails.campaign_aim}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Dil</span>
                      <span className="text-sm">{selectedCampaignForDetails.language?.toUpperCase()}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Kategoriler</span>
                      <div className="flex flex-wrap gap-1">
                        {selectedCampaignForDetails.category?.map((cat, index) => (
                          <Badge key={index} variant="outline" className="text-xs">
                            {cat}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">CTA Butonu</span>
                      <span className="text-sm">{selectedCampaignForDetails.cta_button}</span>
                    </div>
                  </CardContent>
                </Card>

                {/* Budget and Bidding */}
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Bütçe ve Teklif Bilgileri</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Toplam Bütçe</span>
                      <span className="font-medium">₺{Number(selectedCampaignForDetails.campaign_budget).toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Teklif Stratejisi</span>
                      <span className="text-sm uppercase">{selectedCampaignForDetails.bid_strategy}</span>
                    </div>
                    {selectedCampaignForDetails.bid_strategy === 'cpm' && (
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-muted-foreground">CPM Teklifi</span>
                        <span className="text-sm">₺{selectedCampaignForDetails.cmp_bid_price}</span>
                      </div>
                    )}
                    {selectedCampaignForDetails.bid_strategy === 'cpc' && (
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-muted-foreground">CPC Teklifi</span>
                        <span className="text-sm">₺{selectedCampaignForDetails.cpc_bid_price}</span>
                      </div>
                    )}
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Marka Güvenliği</span>
                      <span className="text-sm">{selectedCampaignForDetails.brand_safety_check}</span>
                    </div>
                  </CardContent>
                </Card>

                {/* Performance Metrics */}
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Performans Metrikleri</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-1">
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <Eye className="h-4 w-4" />
                          <span>Görüntülenme</span>
                        </div>
                        <div className="text-lg font-semibold">
                          {selectedCampaignForDetails.views?.toLocaleString() || '0'}
                        </div>
                      </div>
                      <div className="space-y-1">
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <MousePointerClick className="h-4 w-4" />
                          <span>Tıklama</span>
                        </div>
                        <div className="text-lg font-semibold">
                          {selectedCampaignForDetails.clicks?.toLocaleString() || '0'}
                        </div>
                      </div>
                      <div className="space-y-1">
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <Target className="h-4 w-4" />
                          <span>CTR</span>
                        </div>
                        <div className="text-lg font-semibold">
                          {selectedCampaignForDetails.ctr || '0'}%
                        </div>
                      </div>
                      <div className="space-y-1">
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <TrendingUp className="h-4 w-4" />
                          <span>Dönüşüm</span>
                        </div>
                        <div className="text-lg font-semibold">
                          {selectedCampaignForDetails.conversions?.toLocaleString() || '0'}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Earnings */}
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-sm font-medium">Kazanç Bilgileri</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Toplam Kazanç</span>
                      <span className="font-medium text-green-600">₺{selectedCampaignForDetails.earnings?.toLocaleString() || '0'}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Oluşturulma Tarihi</span>
                      <span className="text-sm">
                        {new Date(selectedCampaignForDetails.created_at).toLocaleDateString("tr-TR", {
                          day: "numeric",
                          month: "long",
                          year: "numeric",
                          hour: "2-digit",
                          minute: "2-digit"
                        })}
                      </span>
                    </div>
                    {selectedCampaignForDetails.updated_at && (
                      <div className="flex justify-between items-center">
                        <span className="text-sm text-muted-foreground">Son Güncelleme</span>
                        <span className="text-sm">
                          {new Date(selectedCampaignForDetails.updated_at).toLocaleDateString("tr-TR", {
                            day: "numeric",
                            month: "long",
                            year: "numeric",
                            hour: "2-digit",
                            minute: "2-digit"
                          })}
                        </span>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>

              {/* Campaign Content */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Kampanya İçeriği</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div>
                    <span className="text-sm text-muted-foreground">Kampanya Başlığı:</span>
                    <p className="text-sm mt-1">{selectedCampaignForDetails.campaign_title}</p>
                  </div>
                  <div>
                    <span className="text-sm text-muted-foreground">Kampanya Açıklaması:</span>
                    <p className="text-sm mt-1">{selectedCampaignForDetails.campaign_desc || "Açıklama bulunmuyor."}</p>
                  </div>
                  <div>
                    <span className="text-sm text-muted-foreground">Hedef URL:</span>
                    <a 
                      href={selectedCampaignForDetails.destination_url} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-sm text-blue-600 hover:underline mt-1 block"
                    >
                      {selectedCampaignForDetails.destination_url}
                    </a>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {/* Extend Campaign Modal */}
      <Dialog open={extendModalOpen} onOpenChange={() => setExtendModalOpen(false)}>
        <DialogContent className="max-w-md max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold">Kampanya Süresini Uzat</DialogTitle>
          </DialogHeader>
          
          <div className="grid gap-4">
            <div className="flex items-center gap-4">
              <Label htmlFor="duration">Uzatılacak Süre (Gün)</Label>
              <Input
                id="duration"
                type="number"
                value={extendDuration}
                onChange={(e) => setExtendDuration(Number(e.target.value))}
              />
            </div>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setExtendModalOpen(false)}
              >
                İptal
              </Button>
              <Button
                variant="destructive"
                onClick={() => {
                  if (selectedCampaignForExtend) {
                    handleExtendCampaign(selectedCampaignForExtend.campaign_id);
                    setExtendModalOpen(false);
                  }
                }}
              >
                Uzat
              </Button>
            </DialogFooter>
          </div>
        </DialogContent>
      </Dialog>

      {/* Budget Increase Modal */}
      <Dialog open={budgetIncreaseModalOpen} onOpenChange={() => setBudgetIncreaseModalOpen(false)}>
        <DialogContent className="max-w-md max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold">Kampanya Bütçesini Arttır</DialogTitle>
          </DialogHeader>
          
          <div className="grid gap-4">
            {selectedCampaignForBudgetIncrease && (
              <div className="space-y-2">
                <div className="text-sm text-muted-foreground">
                  <strong>{selectedCampaignForBudgetIncrease.title}</strong>
                </div>
                <div className="text-sm text-muted-foreground">
                  Mevcut Bütçe: <span className="font-medium">₺{Number(selectedCampaignForBudgetIncrease.campaign_budget).toLocaleString()}</span>
                </div>
              </div>
            )}
            <div className="space-y-2">
              <Label htmlFor="additionalBudget">Eklenecek Bütçe (₺)</Label>
              <Input
                id="additionalBudget"
                type="number"
                placeholder="Örn: 1000"
                value={additionalBudget}
                onChange={(e) => setAdditionalBudget(e.target.value)}
                min="1"
                step="0.01"
              />
              {additionalBudget && parseFloat(additionalBudget) > 0 && selectedCampaignForBudgetIncrease && (
                <div className="text-sm text-green-600">
                  Yeni Toplam Bütçe: ₺{(Number(selectedCampaignForBudgetIncrease.campaign_budget) + parseFloat(additionalBudget)).toLocaleString()}
                </div>
              )}
            </div>
          </div>
          
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setBudgetIncreaseModalOpen(false);
                setAdditionalBudget("");
              }}
            >
              İptal
            </Button>
            <Button
              onClick={() => {
                if (selectedCampaignForBudgetIncrease) {
                  handleBudgetIncrease(selectedCampaignForBudgetIncrease.campaign_id);
                  setBudgetIncreaseModalOpen(false);
                  setAdditionalBudget("");
                }
              }}
              disabled={!additionalBudget || parseFloat(additionalBudget) <= 0}
            >
              Bütçeyi Arttır
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {paginatedCampaigns.length > 0 && (
        <CardFooter className="flex justify-between border-t p-6">
          <div className="flex items-center gap-4">
            <div className="text-sm text-muted-foreground">
              {t('advertiser:campaigns.showing')} {((currentPage - 1) * itemsPerPage) + 1} {t('advertiser:campaigns.to')} {Math.min(currentPage * itemsPerPage, totalCount)} {t('advertiser:campaigns.of')} {totalCount} {t('advertiser:campaigns.campaigns')}
            </div>
            <select
              value={itemsPerPage}
              onChange={(e) => {
                const newItemsPerPage = Number(e.target.value);
                setItemsPerPage(newItemsPerPage);
                setCurrentPage(1); // Sayfa değiştiğinde ilk sayfaya dön
              }}
              className="h-8 rounded-md border border-input bg-background px-2 text-sm"
            >
              <option value={5}>5 per page</option>
              <option value={10}>10 per page</option>
              <option value={20}>20 per page</option>
              <option value={50}>50 per page</option>
            </select>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(p => Math.max(1, p - 1))}
              disabled={currentPage === 1}
            >
              <ChevronLeft className="h-4 w-4" />
              {t('advertiser:campaigns.previous')}
            </Button>
            <div className="flex items-center gap-1">
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                const pageNumber = i + 1;
                return (
                  <Button
                    key={pageNumber}
                    variant={currentPage === pageNumber ? "default" : "outline"}
                    size="sm"
                    onClick={() => setCurrentPage(pageNumber)}
                  >
                    {pageNumber}
                  </Button>
                );
              })}
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setCurrentPage(p => Math.min(totalPages, p + 1))}
              disabled={currentPage === totalPages}
            >
              {t('advertiser:campaigns.next')}
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </CardFooter>
      )}
    </Card>
  )
} 