"use client"

import { <PERSON><PERSON><PERSON><PERSON>, Clock, DollarSign, Zap } from "lucide-react"
import { cn } from "@/lib/utils"
import { useEffect, useState } from "react"
import { useToast } from "@/components/ui/use-toast"
import { useRouter } from "next/navigation"
import { authService } from "@/shared/services/auth-service"
import { fetchCampaignStatistics } from "@/shared/services/advertiser/advertiser-campaign-list"
import { Card, CardContent } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { useLanguage } from "@/shared/hooks/useLanguage"
import type { DashboardStatsResponse } from "@/shared/services/advertiser/advertiser-dashboard-stats"
import { Skeleton } from "@/components/ui/skeleton"

interface StatsCardProps {
  title: string
  value: string | number
  subValue: string
  icon: React.ReactNode
  percentage?: number
  colorClassName: string
  bgColorClassName: string
  textColorClassName: string
  gradientFrom: string
  gradientTo: string
}

function StatsCard({ 
  title, 
  value, 
  subValue, 
  icon, 
  percentage, 
  colorClassName,
  bgColorClassName,
  textColorClassName,
  gradientFrom,
  gradientTo
}: StatsCardProps) {
  const [animate, setAnimate] = useState(false)
  
  useEffect(() => {
    // İlk render'dan sonra animasyonu tetikle
    const timer = setTimeout(() => {
      setAnimate(true)
    }, 100)
    
    return () => clearTimeout(timer)
  }, [])

  return (
    <div className="bg-white rounded-xl border shadow-sm overflow-hidden hover:shadow-md transition-all duration-300 group">
      <div className="p-6 relative">
        {/* Dekoratif arkaplan elementi */}
        <div 
          className="absolute top-0 right-0 w-24 h-24 opacity-5 transform translate-x-8 -translate-y-8 group-hover:scale-110 transition-transform duration-500"
          style={{
            background: `radial-gradient(circle, ${gradientFrom}, ${gradientTo})`,
            borderRadius: '50%'
          }}
        />
        
        <div className="flex items-center">
          <div className="flex items-center gap-3">
            <div className={cn(
              "flex items-center justify-center w-11 h-11 rounded-xl transition-transform duration-300 group-hover:scale-110",
              bgColorClassName
            )}>
              <span className={textColorClassName}>
                {icon}
              </span>
            </div>
            <div className="text-sm font-medium tracking-wide">
              {title}
            </div>
          </div>
        </div>
        
        <div className="mt-6 flex items-baseline">
          <p className="text-3xl font-bold tracking-tight">{value}</p>
          <p className="ml-2 text-sm text-muted-foreground font-medium">{subValue}</p>
        </div>
        
        {/* İlerleme çubuğu container */}
        <div className="mt-5">
          <div className="h-3 bg-gray-100 rounded-full overflow-hidden">
            <div 
              className="h-full rounded-full transition-all duration-1000 ease-out"
              style={{ 
                width: animate ? `${percentage || 100}%` : '0%',
                background: `linear-gradient(90deg, ${gradientFrom}, ${gradientTo})`,
                boxShadow: `0 1px 3px ${gradientTo}`
              }}
            />
          </div>
        </div>
      </div>
    </div>
  )
}

export function StatsOverview() {
  const [loading, setLoading] = useState(false)
  const [authError, setAuthError] = useState(false)
  const [stats, setStats] = useState({
    activeCampaigns: 0,
    totalBudget: 0,
    spentPercentage: 0,
    totalCampaigns: 0,
    totalEarnings: 0
  })
  const { toast } = useToast()
  const router = useRouter()
  const { t } = useLanguage()

  useEffect(() => {
    const fetchCampaignStats = async () => {
      try {
        // Oturum kontrolü
        const user = authService.getCurrentUser();
        if (!user || !authService.getAuthToken()) {
          console.error("Kampanya istatistikleri - Geçerli bir oturum bulunamadı");
          setAuthError(true);
          
          toast({
            title: "Oturum Hatası",
            description: "Oturumunuz sona ermiş. Lütfen tekrar giriş yapın.",
            variant: "destructive"
          });
          
          setTimeout(() => {
            router.push("/auth/login");
          }, 1500);
          
          return;
        }
        
        // Yeni istatistik servisini kullan
        const response = await fetchCampaignStatistics();
        
        // 401 hata kontrolü
        if (response.errorCode === "AUTH_FAILED") {
          console.error("Kampanya istatistikleri - Yetkilendirme hatası");
          setAuthError(true);
          
          toast({
            title: "Oturum Hatası",
            description: "Oturumunuz sona ermiş. Lütfen tekrar giriş yapın.",
            variant: "destructive"
          });
          
          setTimeout(() => {
            router.push("/auth/login");
          }, 1500);
          return;
        }
        
        if (response.status && response.result) {
          const { active_campaign_count, total_budget, total_campaign, total_earnings } = response.result;
          // Harcama yüzdesi: aktif kampanya varsa %60, yoksa %0
          const spentPercentage = active_campaign_count > 0 ? 60 : 0;
          setStats({
            activeCampaigns: active_campaign_count,
            totalBudget: total_budget,
            spentPercentage,
            totalCampaigns: total_campaign,
            totalEarnings: total_earnings
          });
        }
      } catch (error) {
        console.error("Kampanya istatistikleri alınamadı:", error);
      }
    };
    
    if (!authError) {
      fetchCampaignStats();
    }
  }, [authError, toast, router]);

  return (
    <div className="grid gap-5 grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
      <StatsCard
        title={t('advertiser:dashboard.stats.activeCampaigns')}
        value={stats.activeCampaigns}
        subValue={t('advertiser:campaigns.status.active')}
        icon={<CheckCircle className="h-5 w-5" />}
        percentage={stats.activeCampaigns > 0 ? (stats.activeCampaigns / stats.totalCampaigns) * 100 : 0}
        colorClassName="bg-blue-500" 
        bgColorClassName="bg-blue-50"
        textColorClassName="text-blue-700"
        gradientFrom="#3b82f6"
        gradientTo="#1d4ed8"
      />
      <StatsCard
        title={t('advertiser:campaigns.metrics.budget')}
        value={`₺${stats.totalBudget.toLocaleString('tr-TR', { maximumFractionDigits: 2 })}`}
        subValue={t('advertiser:campaigns.metrics.spent')}
        icon={<DollarSign className="h-5 w-5" />}
        percentage={stats.spentPercentage}
        colorClassName="bg-indigo-500"
        bgColorClassName="bg-indigo-50"
        textColorClassName="text-indigo-700"
        gradientFrom="#6366f1"
        gradientTo="#4338ca"
      />
      <StatsCard
        title={t('advertiser:dashboard.stats.totalSpent')}
        value={`${stats.totalEarnings.toLocaleString('tr-TR', { maximumFractionDigits: 2 })}`}
        subValue="₺"
        icon={<Zap className="h-5 w-5" />}
        colorClassName="bg-green-500"
        bgColorClassName="bg-green-50"
        textColorClassName="text-green-700"
        gradientFrom="#10b981"
        gradientTo="#047857"
      />
      <StatsCard
        title={t('advertiser:dashboard.stats.totalCampaigns')}
        value={stats.totalCampaigns}
        subValue={t('advertiser:campaigns.metrics.total')}
        icon={<Clock className="h-5 w-5" />}
        percentage={100}
        colorClassName="bg-gray-500"
        bgColorClassName="bg-gray-50"
        textColorClassName="text-gray-700"
        gradientFrom="#6b7280"
        gradientTo="#374151"
      />
    </div>
  )
}