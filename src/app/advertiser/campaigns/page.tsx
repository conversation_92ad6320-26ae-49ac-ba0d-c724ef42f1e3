"use client"

import { useState } from "react"
import { DashboardLayout } from "@/app/advertiser/components/layout/dashboard-layout"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select"
import { PlusCircle, Search, SlidersHorizontal } from "lucide-react"
import { CampaignList } from "./components/campaign-list"
import { StatsOverview } from "./components/stats-overview"
import { DashboardHeader } from "@/app/advertiser/components/dashboard/header"
import { useLanguage } from "@/shared/hooks/useLanguage"

export default function CampaignsPage() {
  const [searchQuery, setSearchQuery] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const { t } = useLanguage()

  return (
    <DashboardLayout>
      <DashboardHeader 
        name=""
        title={t('advertiser:campaigns.title')}
        subtitle={t('advertiser:campaigns.subtitle')}
        
      />
      
      <div className="space-y-6">
        <StatsOverview />

        <div className="flex flex-col md:flex-row gap-4 items-end">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder={t('advertiser:campaigns.search')}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          <div className="flex gap-2 items-center">
            <div className="flex items-center gap-2">
              <SlidersHorizontal className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">{t('advertiser:campaigns.filter')}</span>
            </div>
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder={t('advertiser:campaigns.status.all')} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t('advertiser:campaigns.status.all')}</SelectItem>
                <SelectItem value="active">{t('advertiser:campaigns.status.active')}</SelectItem>
                <SelectItem value="completed">{t('advertiser:campaigns.status.completed')}</SelectItem>
                <SelectItem value="planned">{t('advertiser:campaigns.status.planned')}</SelectItem>
                <SelectItem value="paused">{t('advertiser:campaigns.status.paused')}</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <CampaignList 
          searchQuery={searchQuery}
          statusFilter={statusFilter}
          setSearchQuery={setSearchQuery}
        />
      </div>
    </DashboardLayout>
  )
} 