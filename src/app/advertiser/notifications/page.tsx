"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Check, AlertTriangle } from "lucide-react"
import { DashboardLayout } from "@/app/advertiser/components/layout/dashboard-layout"
import { DashboardHeader } from "@/app/advertiser/components/dashboard/header"
import { NotificationList } from "./components/notification-list"
import { useToast } from "@/components/ui/use-toast"
import { useLanguage } from "@/shared/hooks/useLanguage"
import { 
  fetchNotificationList,
  markAllNotificationsRead,
  markNotificationRead,
  deleteNotification,
  type Notification as APINotification,
  type NotificationListRequest
} from "@/shared/services/advertiser/advertiser-notifications"

// Component için notification interface
interface Notification {
  id: string;
  title: string;
  message: string;
  time: string;
  read: boolean;
  type?: "campaign" | "payment" | "system" | "report";
  link?: string;
}

export default function AdvertiserNotificationsPage() {
  const { t } = useLanguage();
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [unreadCount, setUnreadCount] = useState(0)
  const [activeTab, setActiveTab] = useState<"all" | "unread">("all")
  const [isLoading, setIsLoading] = useState(false)
  const [isInitialLoading, setIsInitialLoading] = useState(true)
  const { toast } = useToast()

  // API'den gelen notification'ı component formatına çevir
  const transformAPINotification = (apiNotification: APINotification): Notification => {
    return {
      id: apiNotification.notification_id,
      title: apiNotification.title,
      message: apiNotification.message,
      time: new Date(apiNotification.created_at).toLocaleString('tr-TR'),
      read: apiNotification.isRead,
      type: apiNotification.type as "campaign" | "payment" | "system" | "report",
    }
  }

  // Bildirimleri yükle
  const loadNotifications = async () => {
    try {
      const params: NotificationListRequest = {
        limit: 50,
        skip: 0
      }
      
      const response = await fetchNotificationList(params)
      
      if (response.status && response.result) {
        const transformedNotifications = response.result.data.map(transformAPINotification)
        setNotifications(transformedNotifications)
        setUnreadCount(transformedNotifications.filter(n => !n.read).length)
      } else {
        throw new Error(response.desc || "Bildirimler yüklenemedi")
      }
    } catch (error) {
      console.error("Bildirimler yüklenirken hata:", error)
      toast({
        title: "Hata",
        description: error instanceof Error ? error.message : "Bildirimler yüklenemedi",
        variant: "destructive"
      })
    } finally {
      setIsInitialLoading(false)
    }
  }

  // Sayfa yüklendiğinde bildirimleri getir
  useEffect(() => {
    loadNotifications()
  }, [])

  // Tüm bildirimleri okundu olarak işaretle
  const handleMarkAllAsRead = async () => {
    setIsLoading(true)
    
    try {
      const response = await markAllNotificationsRead()
      
      if (response.status) {
        // Local state'i güncelle
        setNotifications(prev => prev.map(n => ({ ...n, read: true })))
        setUnreadCount(0)
        
        toast({
          title: t('advertiser:notifications.toast.allRead'),
          description: t('advertiser:notifications.toast.allReadDesc', { count: unreadCount }),
        })
      } else {
        throw new Error(response.desc || "Bildirimler okundu olarak işaretlenemedi")
      }
    } catch (error) {
      console.error("Tüm bildirimler okundu olarak işaretlenirken hata:", error)
      toast({
        title: "Hata",
        description: error instanceof Error ? error.message : "İşlem başarısız",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Tek bir bildirimi okundu olarak işaretle
  const handleMarkAsRead = async (id: string) => {
    try {
      const response = await markNotificationRead({ notification_id: id })
      
      if (response.status) {
        // Local state'i güncelle
        setNotifications(prev => prev.map(n => 
          n.id === id ? { ...n, read: true } : n
        ))
        setUnreadCount(prev => Math.max(0, prev - 1))
        
        toast({
          title: t('advertiser:notifications.toast.oneRead'),
          variant: "default",
        })
      } else {
        throw new Error(response.desc || "Bildirim okundu olarak işaretlenemedi")
      }
    } catch (error) {
      console.error("Bildirim okundu olarak işaretlenirken hata:", error)
      toast({
        title: "Hata",
        description: error instanceof Error ? error.message : "İşlem başarısız",
        variant: "destructive"
      })
    }
  }

  // Bildirimi sil
  const handleDeleteNotification = async (id: string) => {
    try {
      const response = await deleteNotification({ notification_id: id })
      
      if (response.status) {
        // Local state'i güncelle
        const deletedNotification = notifications.find(n => n.id === id)
        setNotifications(prev => prev.filter(n => n.id !== id))
        
        if (deletedNotification && !deletedNotification.read) {
          setUnreadCount(prev => Math.max(0, prev - 1))
        }
        
        toast({
          title: t('advertiser:notifications.toast.deleted'),
          variant: "default",
        })
      } else {
        throw new Error(response.desc || "Bildirim silinemedi")
      }
    } catch (error) {
      console.error("Bildirim silinirken hata:", error)
      toast({
        title: "Hata",
        description: error instanceof Error ? error.message : "İşlem başarısız",
        variant: "destructive"
      })
    }
  }

  // Sekme değişikliğini işle
  const handleTabChange = (value: string) => {
    setActiveTab(value as "all" | "unread")
  }

  // Filtrelenmiş bildirimler
  const filteredNotifications = notifications.filter(notification =>
    activeTab === "unread" ? !notification.read : true
  )

  if (isInitialLoading) {
    return (
      <DashboardLayout>
        <DashboardHeader 
          name=""
          title={t('advertiser:notifications.title')}
          subtitle={t('advertiser:notifications.subtitle')}
          showButton={false}
        />
        <Card>
          <CardContent className="px-6 py-5">
            <div className="space-y-4">
              <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
              <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
              <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
            </div>
          </CardContent>
        </Card>
      </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <DashboardHeader 
        name=""
        title={t('advertiser:notifications.title')}
        subtitle={t('advertiser:notifications.subtitle')}
        showButton={false}
      />

      {/* Uyarı banner'ı - okunmamış bildirim varsa */}
      {unreadCount > 0 && (
        <div className="bg-amber-50 border border-amber-200 rounded-lg p-3 mb-6 flex items-center gap-3">
          <div className="bg-amber-100 rounded-full p-1.5">
            <AlertTriangle className="h-5 w-5 text-amber-600" />
          </div>
          <div className="flex-grow">
            <p className="text-amber-800 text-sm">
              {unreadCount} okunmamış bildiriminiz var.
            </p>
          </div>
          {unreadCount > 0 && (
            <Button 
              variant="outline" 
              size="sm" 
              onClick={handleMarkAllAsRead} 
              disabled={isLoading}
              className="border-amber-300 text-amber-700 hover:text-amber-900 hover:bg-amber-100 hover:border-amber-400"
            >
              {isLoading ? (
                <span className="h-4 w-4 border-2 border-amber-700 border-r-transparent rounded-full animate-spin mr-2" />
              ) : (
                <Check className="mr-2 h-4 w-4" />
              )}
              {t('advertiser:notifications.markAllRead')}
            </Button>
          )}
        </div>
      )}

      <Card>
        <CardContent className="px-6 py-5">
          <Tabs defaultValue={activeTab} onValueChange={handleTabChange} className="w-full">
            <TabsList className="grid w-full grid-cols-2 mb-4">
              <TabsTrigger value="all">
                Tümü ({notifications.length})
              </TabsTrigger>
              <TabsTrigger value="unread">
                Okunmamış ({unreadCount})
              </TabsTrigger>
            </TabsList>
            
            <TabsContent value="all" className="mt-4 space-y-4">
              <NotificationList 
                notifications={filteredNotifications} 
                onMarkAsRead={handleMarkAsRead}
                onDelete={handleDeleteNotification}
              />
            </TabsContent>
            
            <TabsContent value="unread" className="mt-4 space-y-4">
              <NotificationList 
                notifications={filteredNotifications}
                onMarkAsRead={handleMarkAsRead}
                onDelete={handleDeleteNotification}
              />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </DashboardLayout>
  )
} 