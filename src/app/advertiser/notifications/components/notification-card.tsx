"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { Check, Trash2, CreditCard, Bell, FileText, BarChart4, MessageSquare, ExternalLink } from "lucide-react"
// Local notification interface
interface Notification {
  id: string;
  title: string;
  message: string;
  time: string;
  read: boolean;
  type?: "campaign" | "payment" | "system" | "report";
  link?: string;
}
import Link from "next/link"
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { useLanguage } from "@/shared/hooks/useLanguage"

interface NotificationCardProps {
  notification: Notification
  onMarkAsRead: (id: string) => void
  onDelete: (id: string) => void
}

export function NotificationCard({ notification, onMarkAsRead, onDelete }: NotificationCardProps) {
  const [isHovered, setIsHovered] = useState(false)
  const { t } = useLanguage()
  
  // <PERSON><PERSON><PERSON><PERSON> tür<PERSON><PERSON> göre simge belirle
  const getNotificationIcon = (type?: string) => {
    switch (type) {
      case "campaign":
        return <MessageSquare className="h-4 w-4 text-blue-500" />
      case "payment":
        return <CreditCard className="h-4 w-4 text-green-500" />
      case "report":
        return <BarChart4 className="h-4 w-4 text-purple-500" />
      case "system":
        return <Bell className="h-4 w-4 text-amber-500" />
      default:
        return <FileText className="h-4 w-4 text-gray-500" />
    }
  }

  // Bildirim içeriği için wrapper
  const ContentWrapper = ({ children }: { children: React.ReactNode }) => {
    if (notification.link) {
      return (
        <Link 
          href={notification.link} 
          className="flex-grow cursor-pointer"
        >
          {children}
        </Link>
      )
    }
    return <div className="flex-grow">{children}</div>
  }

  return (
    <div
      className={cn(
        "flex items-start gap-4 rounded-lg border p-4 transition-colors relative",
        notification.read 
          ? "border-border/50 bg-muted/20 text-muted-foreground" 
          : "border-border bg-background shadow-sm",
        isHovered && "bg-muted/50"
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <div className="flex mt-1 items-center">
        <div className={cn(
          "h-2.5 w-2.5 flex-shrink-0 rounded-full mr-2",
          notification.read ? "bg-muted-foreground/50" : "bg-primary animate-pulse"
        )} />
        {getNotificationIcon(notification.type)}
      </div>
      
      <ContentWrapper>
        <h4 className={cn("text-sm font-semibold flex items-center gap-1", 
          !notification.read && "text-foreground"
        )}>
          {notification.title}
          {notification.link && isHovered && (
            <ExternalLink className="h-3 w-3 inline ml-1 text-muted-foreground" />
          )}
        </h4>
        <p className={cn("text-sm mt-1", 
          notification.read ? "text-muted-foreground/80" : "text-muted-foreground"
        )}>
          {notification.message}
        </p>
        <p className={cn("mt-1 text-xs", 
          notification.read ? "text-muted-foreground/60" : "text-muted-foreground/80"
        )}>
          {notification.time}
        </p>
      </ContentWrapper>

      <div className={cn(
        "flex gap-1 transition-opacity",
        (isHovered || !notification.read) ? "opacity-100" : "opacity-0"
      )}>
        <TooltipProvider>
          {!notification.read && (
            <Tooltip>
              <TooltipTrigger asChild>
                <Button 
                  variant="ghost" 
                  size="icon" 
                  className="h-7 w-7 flex-shrink-0" 
                  onClick={() => onMarkAsRead(notification.id)}
                >
                  <Check className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>{t('advertiser:notifications.markAsRead')}</p>
              </TooltipContent>
            </Tooltip>
          )}
          
          <Tooltip>
            <TooltipTrigger asChild>
              <Button 
                variant="ghost" 
                size="icon" 
                className="h-7 w-7 flex-shrink-0 text-destructive hover:text-destructive/90" 
                onClick={() => onDelete(notification.id)}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>{t('advertiser:notifications.deleteNotification')}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
    </div>
  )
} 