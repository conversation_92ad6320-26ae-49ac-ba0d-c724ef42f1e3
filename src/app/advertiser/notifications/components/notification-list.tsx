"use client"

import { Bell<PERSON><PERSON>, Filter } from "lucide-react"
import { NotificationCard } from "./notification-card"
import { But<PERSON> } from "@/components/ui/button"
import { useState } from "react"
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuLabel, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu"
import { cn } from "@/lib/utils"
// Local notification interface
interface Notification {
  id: string;
  title: string;
  message: string;
  time: string;
  read: boolean;
  type?: "campaign" | "payment" | "system" | "report";
  link?: string;
}
import { useLanguage } from "@/shared/hooks/useLanguage"

interface NotificationListProps {
  notifications: Notification[]
  onMarkAsRead: (id: string) => void
  onDelete: (id: string) => void
}

export function NotificationList({ notifications, onMarkAsRead, onDelete }: NotificationListProps) {
  const [filter, setFilter] = useState<string | null>(null)
  const { t } = useLanguage()
  
  const filteredNotifications = filter 
    ? notifications.filter(n => n.type === filter)
    : notifications

  if (notifications.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center gap-4 rounded-lg border border-dashed border-border bg-background p-8 text-center">
        <div className="rounded-full border border-border bg-muted p-3">
          <BellRing className="h-8 w-8 text-muted-foreground" />
        </div>
        <p className="text-lg font-medium text-muted-foreground">
          {t('advertiser:notifications.noNotifications')}
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-4">
      {notifications.length > 0 && (
        <div className="flex justify-end">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" size="sm" className="gap-2">
                <Filter className="h-4 w-4" />
                {filter ? `${t('advertiser:notifications.filter')}: ${filter}` : t('advertiser:notifications.filter')}
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-48">
              <DropdownMenuLabel>{t('advertiser:notifications.notificationType')}</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                className={cn(filter === null && "font-medium")}
                onClick={() => setFilter(null)}
              >
                {t('advertiser:notifications.all')}
              </DropdownMenuItem>
              <DropdownMenuItem 
                className={cn(filter === "campaign" && "font-medium")}
                onClick={() => setFilter("campaign")}
              >
                {t('advertiser:notifications.campaigns')}
              </DropdownMenuItem>
              <DropdownMenuItem 
                className={cn(filter === "payment" && "font-medium")}
                onClick={() => setFilter("payment")}
              >
                {t('advertiser:notifications.payments')}
              </DropdownMenuItem>
              <DropdownMenuItem 
                className={cn(filter === "report" && "font-medium")}
                onClick={() => setFilter("report")}
              >
                {t('advertiser:notifications.reports')}
              </DropdownMenuItem>
              <DropdownMenuItem 
                className={cn(filter === "system" && "font-medium")}
                onClick={() => setFilter("system")}
              >
                {t('advertiser:notifications.system')}
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      )}
      
      {filteredNotifications.length === 0 ? (
        <div className="flex flex-col items-center justify-center gap-3 rounded-lg border border-dashed border-border bg-background py-6 text-center">
          <p className="text-sm text-muted-foreground">
            {t('advertiser:notifications.noFilteredNotifications')}
          </p>
          <Button variant="outline" size="sm" onClick={() => setFilter(null)}>
            {t('advertiser:notifications.showAllNotifications')}
          </Button>
        </div>
      ) : (
        filteredNotifications.map((notification) => (
          <NotificationCard
            key={notification.id}
            notification={notification}
            onMarkAsRead={onMarkAsRead}
            onDelete={onDelete}
          />
        ))
      )}
    </div>
  )
} 