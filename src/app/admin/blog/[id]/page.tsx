"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"

import { BlogForm } from "../components/blog-form"
import { DashboardLayout } from "@/app/layout/dashboard-layout"
import { DashboardHeader } from "@/app/admin/components/dashboard/header"
import { AlertCircle, ArrowLeft, Loader2 } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { BlogFormTypes } from "../components/types"

// Örnek kategori verileri
const MOCK_CATEGORIES: BlogFormTypes.Category[] = [
  { id: "1", name: "<PERSON><PERSON><PERSON><PERSON>" },
  { id: "2", name: "Sosyal <PERSON>" },
  { id: "3", name: "SEO" },
  { id: "4", name: "E-Ticaret" },
  { id: "5", name: "<PERSON>k<PERSON><PERSON><PERSON>" }
]

// Örnek yazar verileri
const MOCK_AUTHORS: BlogFormTypes.Author[] = [
  { id: "1", name: "<PERSON>" },
  { id: "2", name: "<PERSON><PERSON><PERSON><PERSON>" },
  { id: "3", name: "Mehmet Demir" }
]

// Örnek veri modeli (dosya içine eklenmiştir, gerçek projede tip tanımları ayrı bir dosyada olabilir)
interface BlogPost {
  id: string
  title: {
    tr: string
    en: string
    de: string
  }
  slug: {
    tr: string
    en: string
    de: string
  }
  content: {
    tr: string
    en: string
    de: string
  }
  excerpt: {
    tr: string
    en: string
    de: string
  }
  image: {
    tr: string | null
    en: string | null
    de: string | null
  }
  author: {
    id: string
    name: string
  }
  category: {
    id: string
    name: string
  }
  created_at: string
  updated_at: string
  status: "published" | "draft"
  featured: boolean
  published_at: string | null
}

// Örnek veri (gerçek uygulamada API'den gelecek)
const MOCK_POSTS: BlogPost[] = [
  {
    id: "1",
    title: {
      tr: "Dijital Pazarlama Trendleri",
      en: "Digital Marketing Trends",
      de: "Digitale Marketing-Trends"
    },
    slug: {
      tr: "dijital-pazarlama-trendleri",
      en: "digital-marketing-trends",
      de: "digitale-marketing-trends"
    },
    content: {
      tr: "İçerik Türkçe...",
      en: "Content in English...",
      de: "Inhalt auf Deutsch..."
    },
    excerpt: {
      tr: "2023 yılında öne çıkan dijital pazarlama trendleri...",
      en: "Digital marketing trends that stand out in 2023...",
      de: "Digitale Marketing-Trends, die sich 2023 auszeichnen..."
    },
    image: {
      tr: "/images/blog/tr/digital-marketing.jpg",
      en: "/images/blog/en/digital-marketing.jpg",
      de: "/images/blog/de/digital-marketing.jpg"
    },
    author: {
      id: "1",
      name: "Ali Yılmaz"
    },
    category: {
      id: "1",
      name: "Pazarlama"
    },
    created_at: "2023-05-12T10:30:00",
    updated_at: "2023-05-15T14:20:00",
    status: "published",
    featured: true,
    published_at: "2023-05-15T14:20:00"
  },
  {
    id: "2",
    title: {
      tr: "Sosyal Medya Stratejileri",
      en: "Social Media Strategies",
      de: "Social-Media-Strategien"
    },
    slug: {
      tr: "sosyal-medya-stratejileri",
      en: "social-media-strategies",
      de: "social-media-strategien"
    },
    content: {
      tr: "İçerik Türkçe...",
      en: "Content in English...",
      de: "Inhalt auf Deutsch..."
    },
    excerpt: {
      tr: "Etkili sosyal medya stratejileri...",
      en: "Effective social media strategies...",
      de: "Effektive Social-Media-Strategien..."
    },
    image: {
      tr: "/images/blog/tr/social-media.jpg",
      en: "/images/blog/en/social-media.jpg",
      de: "/images/blog/de/social-media.jpg"
    },
    author: {
      id: "2",
      name: "Ayşe Kaya"
    },
    category: {
      id: "2",
      name: "Sosyal Medya"
    },
    created_at: "2023-06-18T09:45:00",
    updated_at: "2023-06-20T11:30:00",
    status: "published",
    featured: false,
    published_at: "2023-06-20T11:30:00"
  }
]

export default function EditBlogPage() {
  const router = useRouter()
  const params = useParams()
  const id = params.id as string
  
  const [post, setPost] = useState<BlogPost | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [saving, setSaving] = useState(false)
  
  // Veri yüklemesini simüle et
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true)
        setError(null)
        
        // API çağrısını simüle et (gerçek uygulamada)
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        const foundPost = MOCK_POSTS.find(post => post.id === id)
        
        if (foundPost) {
          setPost(foundPost)
        } else {
          setError("Blog yazısı bulunamadı")
        }
      } catch (err) {
        setError("Blog yazısı yüklenirken bir hata oluştu")
        console.error(err)
      } finally {
        setLoading(false)
      }
    }
    
    fetchData()
  }, [id])
  
  const handleSubmit = async (data: BlogFormTypes.BlogFormData) => {
    try {
      setSaving(true)
      
      // API çağrısını simüle et (gerçek uygulamada)
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      // Başarılı sonucu simüle et
      console.log("Güncellendi:", data)
      
      // Listeye yönlendir
      router.push("/admin/blog")
    } catch (err) {
      console.error("Güncelleme hatası:", err)
      setError("Blog yazısı güncellenirken bir hata oluştu")
    } finally {
      setSaving(false)
    }
  }
  
  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <DashboardHeader 
            title="Blog Yazısı Düzenle" 
            description="Blog içeriğini düzenleyin"
          />
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => router.push("/admin/blog")}
            className="h-9"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Geri Dön
          </Button>
        </div>
        
        {loading ? (
          <div className="flex justify-center items-center h-64">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-3">Yükleniyor...</span>
          </div>
        ) : error ? (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Hata</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        ) : post ? (
          <div className="relative">
            {saving && (
              <div className="absolute inset-0 bg-white/60 flex items-center justify-center z-10 rounded-lg">
                <div className="flex items-center space-x-2">
                  <Loader2 className="h-5 w-5 animate-spin text-primary" />
                  <span>Kaydediliyor...</span>
                </div>
              </div>
            )}
            <BlogForm
              categories={MOCK_CATEGORIES}
              authors={MOCK_AUTHORS}
              initialData={{
                title: post.title,
                slug: post.slug,
                content: post.content,
                excerpt: post.excerpt,
                image: post.image,
                category_id: post.category.id,
                author_id: post.author.id,
                status: post.status,
                featured: post.featured,
                published_at: post.published_at
              }}
              onSubmit={handleSubmit}
              isEdit={true}
            />
          </div>
        ) : null}
      </div>
    </DashboardLayout>
  )
} 