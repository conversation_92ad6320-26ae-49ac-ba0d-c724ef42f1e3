"use client"

import { useState, useEffect } from "react"
import { DashboardLayout } from "@/app/layout/dashboard-layout"
import { DashboardHeader } from "@/app/admin/components/dashboard/header"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from "@/components/ui/table"
import { 
  Search, PlusCircle, Edit, Trash, Eye, 
  FileText, Globe, Calendar, User as UserIcon,
  Settings as SettingsIcon
} from "lucide-react"
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import Link from "next/link"
import { useRouter } from "next/navigation"

// Örnek veri modeli
interface BlogPost {
  id: string
  title: {
    tr: string
    en: string
    de: string
  }
  slug: {
    tr: string
    en: string
    de: string
  }
  content: {
    tr: string
    en: string
    de: string
  }
  excerpt: {
    tr: string
    en: string
    de: string
  }
  image: {
    tr: string
    en: string
    de: string
  }
  author: {
    id: string
    name: string
  }
  category: {
    id: string
    name: string
  }
  created_at: string
  updated_at: string
  status: "published" | "draft"
  featured: boolean
}

// Örnek veri
const MOCK_POSTS: BlogPost[] = [
  {
    id: "1",
    title: {
      tr: "Dijital Pazarlama Trendleri",
      en: "Digital Marketing Trends",
      de: "Digitale Marketing-Trends"
    },
    slug: {
      tr: "dijital-pazarlama-trendleri",
      en: "digital-marketing-trends",
      de: "digitale-marketing-trends"
    },
    content: {
      tr: "İçerik Türkçe...",
      en: "Content in English...",
      de: "Inhalt auf Deutsch..."
    },
    excerpt: {
      tr: "2023 yılında öne çıkan dijital pazarlama trendleri...",
      en: "Digital marketing trends that stand out in 2023...",
      de: "Digitale Marketing-Trends, die sich 2023 auszeichnen..."
    },
    image: {
      tr: "/images/blog/tr/digital-marketing.jpg",
      en: "/images/blog/en/digital-marketing.jpg",
      de: "/images/blog/de/digital-marketing.jpg"
    },
    author: {
      id: "1",
      name: "Ali Yılmaz"
    },
    category: {
      id: "1",
      name: "Pazarlama"
    },
    created_at: "2023-05-12T10:30:00",
    updated_at: "2023-05-15T14:20:00",
    status: "published",
    featured: true
  },
  {
    id: "2",
    title: {
      tr: "Sosyal Medya Stratejileri",
      en: "Social Media Strategies",
      de: "Social-Media-Strategien"
    },
    slug: {
      tr: "sosyal-medya-stratejileri",
      en: "social-media-strategies",
      de: "social-media-strategien"
    },
    content: {
      tr: "İçerik Türkçe...",
      en: "Content in English...",
      de: "Inhalt auf Deutsch..."
    },
    excerpt: {
      tr: "Etkili sosyal medya stratejileri...",
      en: "Effective social media strategies...",
      de: "Effektive Social-Media-Strategien..."
    },
    image: {
      tr: "/images/blog/tr/social-media.jpg",
      en: "/images/blog/en/social-media.jpg",
      de: "/images/blog/de/social-media.jpg"
    },
    author: {
      id: "2",
      name: "Ayşe Kaya"
    },
    category: {
      id: "2",
      name: "Sosyal Medya"
    },
    created_at: "2023-06-18T09:45:00",
    updated_at: "2023-06-20T11:30:00",
    status: "published",
    featured: false
  },
  {
    id: "3",
    title: {
      tr: "SEO İpuçları",
      en: "SEO Tips",
      de: "SEO-Tipps"
    },
    slug: {
      tr: "seo-ipuclari",
      en: "seo-tips",
      de: "seo-tipps"
    },
    content: {
      tr: "İçerik Türkçe...",
      en: "Content in English...",
      de: "Inhalt auf Deutsch..."
    },
    excerpt: {
      tr: "Web sitenizi optimize etmek için SEO ipuçları...",
      en: "SEO tips to optimize your website...",
      de: "SEO-Tipps zur Optimierung Ihrer Website..."
    },
    image: {
      tr: "/images/blog/tr/seo.jpg",
      en: "/images/blog/en/seo.jpg",
      de: "/images/blog/de/seo.jpg"
    },
    author: {
      id: "3",
      name: "Mehmet Demir"
    },
    category: {
      id: "3",
      name: "SEO"
    },
    created_at: "2023-07-05T14:20:00",
    updated_at: "2023-07-08T16:15:00",
    status: "draft",
    featured: false
  }
]

// Ana bileşen
export default function BlogPage() {
  const router = useRouter()
  const [posts, setPosts] = useState<BlogPost[]>(MOCK_POSTS)
  const [searchQuery, setSearchQuery] = useState("")
  const [currentPage, setCurrentPage] = useState(1)
  const [postsPerPage] = useState(10)
  const [categoryFilter, setCategoryFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [languageFilter, setLanguageFilter] = useState("tr")
  
  // Filtreleme işlemi
  const filteredPosts = posts.filter(post => {
    // Arama sorgusu filtreleme
    const searchLower = searchQuery.toLowerCase()
    const matchesSearch = post.title[languageFilter as keyof typeof post.title].toLowerCase().includes(searchLower) ||
                         post.author.name.toLowerCase().includes(searchLower) ||
                         post.category.name.toLowerCase().includes(searchLower)
    
    // Kategori filtreleme
    const matchesCategory = categoryFilter === "all" || post.category.id === categoryFilter
    
    // Durum filtreleme
    const matchesStatus = statusFilter === "all" || post.status === statusFilter
    
    return matchesSearch && matchesCategory && matchesStatus
  })
  
  // Sayfalama için
  const indexOfLastPost = currentPage * postsPerPage
  const indexOfFirstPost = indexOfLastPost - postsPerPage
  const currentPosts = filteredPosts.slice(indexOfFirstPost, indexOfLastPost)
  const totalPages = Math.ceil(filteredPosts.length / postsPerPage)
  
  // Sayfa değişim işleyici
  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }
  
  // Tarihi formatla
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat('tr-TR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    }).format(date)
  }

  // Blog görüntüleme işlemi
  const handleViewBlog = (id: string) => {
    router.push(`/admin/blog/${id}`)
  }

  // Blog düzenleme işlemi
  const handleEditBlog = (id: string) => {
    router.push(`/admin/blog/${id}`)
  }

  // Blog silme işlemi
  const handleDeleteBlog = (id: string) => {
    // Gerçek uygulamada bir onay iletişim kutusu gösterilir
    if (confirm('Bu blog yazısını silmek istediğinizden emin misiniz?')) {
      // API çağrısı yapılır
      setPosts(posts.filter(post => post.id !== id))
    }
  }
  
  return (
    <DashboardLayout>
      <div className="space-y-6">
        <DashboardHeader 
          title="Blog Yönetimi" 
          description="Blog yazılarını görüntüleyin, düzenleyin ve yönetin"
        />
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <Card>
            <CardContent className="p-4 flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Toplam İçerik</p>
                <h3 className="text-2xl font-bold">{posts.length}</h3>
              </div>
              <FileText className="h-8 w-8 text-blue-500" />
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Yayımlanmış</p>
                <h3 className="text-2xl font-bold">
                  {posts.filter(post => post.status === "published").length}
                </h3>
              </div>
              <Globe className="h-8 w-8 text-green-500" />
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Taslak</p>
                <h3 className="text-2xl font-bold">
                  {posts.filter(post => post.status === "draft").length}
                </h3>
              </div>
              <FileText className="h-8 w-8 text-gray-500" />
            </CardContent>
          </Card>
        </div>
        
        <div className="flex flex-col md:flex-row gap-4 items-end justify-between mb-6">
          <div className="flex flex-col md:flex-row gap-4 w-full md:w-auto">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
              <Input
                placeholder="Blog içeriği ara..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 w-full md:w-[300px]"
              />
            </div>
            
            <div className="flex gap-3">
              <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Kategori" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tüm Kategoriler</SelectItem>
                  <SelectItem value="1">Pazarlama</SelectItem>
                  <SelectItem value="2">Sosyal Medya</SelectItem>
                  <SelectItem value="3">SEO</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Durum" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tüm Durumlar</SelectItem>
                  <SelectItem value="published">Yayımlanmış</SelectItem>
                  <SelectItem value="draft">Taslak</SelectItem>
                </SelectContent>
              </Select>
              
              <Select value={languageFilter} onValueChange={setLanguageFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Dil" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="tr">Türkçe</SelectItem>
                  <SelectItem value="en">İngilizce</SelectItem>
                  <SelectItem value="de">Almanca</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => router.push("/admin/blog/settings")}
            >
              <SettingsIcon className="h-4 w-4 mr-2" />
              Ayarlar
            </Button>
            <Button 
              onClick={() => router.push("/admin/blog/create")}
              className="shrink-0"
            >
              <PlusCircle className="h-4 w-4 mr-2" />
              Yeni Blog Yazısı
            </Button>
          </div>
        </div>
        
        <div className="border rounded-lg">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Başlık</TableHead>
                <TableHead>Yazar</TableHead>
                <TableHead>Kategori</TableHead>
                <TableHead>Tarih</TableHead>
                <TableHead>Durum</TableHead>
                <TableHead className="text-right">İşlemler</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {currentPosts.length > 0 ? (
                currentPosts.map((post) => (
                  <TableRow key={post.id}>
                    <TableCell className="font-medium">
                      {post.title[languageFilter as keyof typeof post.title]}
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <UserIcon className="h-4 w-4" />
                        {post.author.name}
                      </div>
                    </TableCell>
                    <TableCell>{post.category.name}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4" />
                        {formatDate(post.created_at)}
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge 
                        className={post.status === "published" 
                          ? "bg-green-100 text-green-800" 
                          : "bg-yellow-100 text-yellow-800"}
                      >
                        {post.status === "published" ? "Yayında" : "Taslak"}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex items-center justify-end gap-2">
                        <Button 
                          variant="ghost" 
                          size="icon" 
                          onClick={() => handleViewBlog(post.id)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="icon"
                          onClick={() => handleEditBlog(post.id)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="icon" 
                          className="text-red-500"
                          onClick={() => handleDeleteBlog(post.id)}
                        >
                          <Trash className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={6} className="h-24 text-center">
                    Sonuç bulunamadı.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
        
        {filteredPosts.length > postsPerPage && (
          <Pagination className="mt-4">
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious 
                  onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                  className={currentPage === 1 ? "pointer-events-none opacity-50" : ""}
                />
              </PaginationItem>
              
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                const pageNum = i + 1
                return (
                  <PaginationItem key={pageNum}>
                    <PaginationLink
                      onClick={() => handlePageChange(pageNum)}
                      isActive={currentPage === pageNum}
                    >
                      {pageNum}
                    </PaginationLink>
                  </PaginationItem>
                )
              })}
              
              {totalPages > 5 && (
                <>
                  <PaginationItem>
                    <span className="flex h-9 w-9 items-center justify-center">
                      ...
                    </span>
                  </PaginationItem>
                  <PaginationItem>
                    <PaginationLink
                      onClick={() => handlePageChange(totalPages)}
                      isActive={currentPage === totalPages}
                    >
                      {totalPages}
                    </PaginationLink>
                  </PaginationItem>
                </>
              )}
              
              <PaginationItem>
                <PaginationNext 
                  onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
                  className={currentPage === totalPages ? "pointer-events-none opacity-50" : ""}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        )}
      </div>
    </DashboardLayout>
  )
} 