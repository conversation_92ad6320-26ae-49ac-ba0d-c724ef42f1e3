"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { BlogForm } from "../components/blog-form"
import { DashboardLayout } from "@/app/layout/dashboard-layout"
import { DashboardHeader } from "@/app/admin/components/dashboard/header"
import { ArrowLeft, Loader2 } from "lucide-react"
import { Button } from "@/components/ui/button"
import { BlogFormTypes } from "../components/types"

// Örnek kategori verileri
const MOCK_CATEGORIES: BlogFormTypes.Category[] = [
  { id: "1", name: "<PERSON><PERSON><PERSON><PERSON>" },
  { id: "2", name: "Sosyal Medy<PERSON>" },
  { id: "3", name: "SEO" },
  { id: "4", name: "E-Ticaret" },
  { id: "5", name: "<PERSON>k<PERSON><PERSON><PERSON>" }
]

// Örnek yazar verileri
const MOCK_AUTHORS: BlogFormTypes.Author[] = [
  { id: "1", name: "<PERSON>" },
  { id: "2", name: "<PERSON><PERSON><PERSON><PERSON>" },
  { id: "3", name: "<PERSON><PERSON><PERSON>" }
]

export default function CreateBlogPage() {
  const router = useRouter()
  const [saving, setSaving] = useState(false)
  
  const handleSubmit = async (data: BlogFormTypes.BlogFormData) => {
    try {
      setSaving(true)
      
      // API çağrısını simüle et (gerçek uygulamada)
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      // Başarılı sonucu simüle et
      console.log("Oluşturuldu:", data)
      
      // Başarı mesajı göster (eğer toast bileşeni varsa)
      // toast({
      //   title: "Blog yazısı oluşturuldu",
      //   description: "Blog yazısı başarıyla oluşturuldu.",
      // })
      
      // Listeye yönlendir
      router.push("/admin/blog")
    } catch (err) {
      console.error("Oluşturma hatası:", err)
      // toast({
      //   variant: "destructive",
      //   title: "Hata",
      //   description: "Blog yazısı oluşturulurken bir hata oluştu.",
      // })
    } finally {
      setSaving(false)
    }
  }
  
  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <DashboardHeader 
            title="Yeni Blog Yazısı" 
            description="Yeni bir blog içeriği oluşturun"
          />
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => router.push("/admin/blog")}
            className="h-9"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Geri Dön
          </Button>
        </div>
        
        <div className="relative">
          {saving && (
            <div className="absolute inset-0 bg-white/60 flex items-center justify-center z-10 rounded-lg">
              <div className="flex items-center space-x-2">
                <Loader2 className="h-5 w-5 animate-spin text-primary" />
                <span>Kaydediliyor...</span>
              </div>
            </div>
          )}
          
          <BlogForm 
            onSubmit={handleSubmit}
            isEdit={false}
            categories={MOCK_CATEGORIES}
            authors={MOCK_AUTHORS}
          />
        </div>
      </div>
    </DashboardLayout>
  )
} 