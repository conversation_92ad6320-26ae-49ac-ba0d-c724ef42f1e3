"use client"

import { BlogFormTypes } from "./types"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { ImagePlus, Trash2 } from "lucide-react"
import Image from 'next/image'

export function LanguageTab({
  activeLanguage,
  setActiveLanguage,
  formData,
  setFormData,
  handleTextChange,
  generateSlug,
  handleImageAction,
  isEdit,
}: BlogFormTypes.LanguageTabProps) {
  const languageLabels = {
    tr: "Türkçe",
    en: "İngilizce",
    de: "Almanca",
  }

  // Doğrudan değer değiştirme fonksiyonu
  const handleInputChange = (
    field: "title" | "slug" | "content" | "excerpt",
    language: BlogFormTypes.Language,
    value: string
  ) => {
    setFormData(prevData => ({
      ...prevData,
      [field]: {
        ...(prevData[field] as BlogFormTypes.LanguageContent),
        [language]: value
      }
    }))
  }

  // Başlık değişikliğinde özel işlem
  const handleTitleChange = (language: BlogFormTypes.Language, value: string) => {
    handleInputChange("title", language, value)
    generateSlug(value, language)
  }

  return (
    <Tabs
      defaultValue={activeLanguage}
      value={activeLanguage}
      onValueChange={(value) => setActiveLanguage(value as BlogFormTypes.Language)}
      className="w-full"
    >
      <TabsList className="grid w-full grid-cols-3">
        <TabsTrigger value="tr">{languageLabels.tr}</TabsTrigger>
        <TabsTrigger value="en">{languageLabels.en}</TabsTrigger>
        <TabsTrigger value="de">{languageLabels.de}</TabsTrigger>
      </TabsList>

      {(["tr", "en", "de"] as const).map((lang) => (
        <TabsContent key={lang} value={lang} className="space-y-6 py-4">
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor={`title-${lang}`}>Başlık ({languageLabels[lang]})</Label>
                <Input
                  id={`title-${lang}`}
                  placeholder={`Blog başlığı ${languageLabels[lang]}`}
                  value={formData.title[lang]}
                  onChange={(e) => handleTitleChange(lang, e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor={`slug-${lang}`}>SEO URL ({languageLabels[lang]})</Label>
                <Input
                  id={`slug-${lang}`}
                  placeholder={`Blog SEO URL ${languageLabels[lang]}`}
                  value={formData.slug[lang]}
                  onChange={(e) => handleInputChange("slug", lang, e.target.value)}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor={`excerpt-${lang}`}>Özet ({languageLabels[lang]})</Label>
              <Textarea
                id={`excerpt-${lang}`}
                placeholder={`Blog özeti ${languageLabels[lang]}`}
                value={formData.excerpt[lang]}
                onChange={(e) => handleInputChange("excerpt", lang, e.target.value)}
                rows={3}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor={`content-${lang}`}>İçerik ({languageLabels[lang]})</Label>
              <Textarea
                id={`content-${lang}`}
                placeholder={`Blog içeriği ${languageLabels[lang]}`}
                value={formData.content[lang]}
                onChange={(e) => handleInputChange("content", lang, e.target.value)}
                rows={10}
                className="min-h-[200px]"
              />
            </div>

            <div className="space-y-2">
              <Label>Blog Görseli ({languageLabels[lang]})</Label>
              {formData.image[lang] ? (
                <Card>
                  <CardContent className="p-4">
                    <div className="aspect-video relative rounded-md overflow-hidden border">
                      <Image 
                        src={formData.image[lang] || "/placeholders/image-placeholder.jpg"}
                        alt={`Blog görseli ${languageLabels[lang]}`}
                        width={500}
                        height={300}
                        className="w-full h-full object-cover"
                      />
                      <Button
                        variant="destructive"
                        size="icon"
                        className="absolute top-2 right-2"
                        onClick={() => handleImageAction(lang, 'remove')}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                <Button 
                  variant="outline" 
                  className="w-full py-8 border-dashed"
                  onClick={() => handleImageAction(lang, 'upload')}
                >
                  <ImagePlus className="h-4 w-4 mr-2" />
                  Görsel Yükle ({languageLabels[lang]})
                </Button>
              )}
            </div>
          </div>
        </TabsContent>
      ))}
    </Tabs>
  )
} 