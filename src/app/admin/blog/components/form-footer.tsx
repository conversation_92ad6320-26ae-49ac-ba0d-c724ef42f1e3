"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { BlogFormTypes } from "./types"
import { AlertTriangle, Check, Save } from "lucide-react"

export function FormFooter({ isEdit }: BlogFormTypes.FormFooterProps) {
  return (
    <div className="flex items-center justify-between pt-6 border-t">
      <div className="flex items-center text-yellow-600">
        <AlertTriangle className="h-4 w-4 mr-2" />
        <span className="text-sm">
          {isEdit 
            ? "Değişiklikleri kaydetmek için 'Güncelle' butonuna tıklayın."
            : "Yeni blog yazısını kaydetmek için 'Kaydet' butonuna tıklayın."}
        </span>
      </div>
      
      <div className="flex gap-3">
        <Button variant="outline" type="reset">
          İptal
        </Button>
        <Button type="submit" className="gap-2">
          {isEdit ? (
            <>
              <Check className="h-4 w-4" />
              <PERSON><PERSON><PERSON><PERSON>
            </>
          ) : (
            <>
              <Save className="h-4 w-4" />
              Kaydet
            </>
          )}
        </Button>
      </div>
    </div>
  )
} 