"use client"

import { BlogFormTypes } from "./types"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Calendar } from "@/components/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Button } from "@/components/ui/button"
import { CalendarIcon } from "lucide-react"
import { format } from "date-fns"
import { tr } from "date-fns/locale"

export function DetailsPanel({
  formData,
  setFormData,
  categories,
  authors,
}: BlogFormTypes.DetailsPanelProps) {
  // <PERSON><PERSON><PERSON>
  const handleDateChange = (date: Date | undefined) => {
    setFormData({
      ...formData,
      published_at: date ? date.toISOString() : null,
    })
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Blog Detayları</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Kategori Seçimi */}
        <div className="space-y-2">
          <Label htmlFor="category">Kategori</Label>
          <Select
            value={formData.category_id}
            onValueChange={(value) => setFormData({ ...formData, category_id: value })}
          >
            <SelectTrigger id="category">
              <SelectValue placeholder="Kategori seçin" />
            </SelectTrigger>
            <SelectContent>
              {categories.map((category) => (
                <SelectItem key={category.id} value={category.id}>
                  {category.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Yazar Seçimi */}
        <div className="space-y-2">
          <Label htmlFor="author">Yazar</Label>
          <Select
            value={formData.author_id}
            onValueChange={(value) => setFormData({ ...formData, author_id: value })}
          >
            <SelectTrigger id="author">
              <SelectValue placeholder="Yazar seçin" />
            </SelectTrigger>
            <SelectContent>
              {authors.map((author) => (
                <SelectItem key={author.id} value={author.id}>
                  {author.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Durum Seçimi */}
        <div className="space-y-2">
          <Label htmlFor="status">Durum</Label>
          <Select
            value={formData.status}
            onValueChange={(value: "published" | "draft") => setFormData({ ...formData, status: value })}
          >
            <SelectTrigger id="status">
              <SelectValue placeholder="Durum seçin" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="published">Yayımlanmış</SelectItem>
              <SelectItem value="draft">Taslak</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Tarih Seçimi */}
        <div className="space-y-2">
          <Label>Yayın Tarihi</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="w-full justify-start text-left font-normal"
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {formData.published_at ? (
                  format(new Date(formData.published_at), "PPP", { locale: tr })
                ) : (
                  <span>Tarih seçin</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0">
              <Calendar
                mode="single"
                selected={formData.published_at ? new Date(formData.published_at) : undefined}
                onSelect={handleDateChange}
                locale={tr}
              />
            </PopoverContent>
          </Popover>
        </div>

        {/* Öne Çıkan İçerik Seçimi */}
        <div className="flex items-center justify-between">
          <Label htmlFor="featured" className="flex-1">
            Öne Çıkan İçerik
          </Label>
          <Switch
            id="featured"
            checked={formData.featured}
            onCheckedChange={(checked) => setFormData({ ...formData, featured: checked })}
          />
        </div>
      </CardContent>
    </Card>
  )
} 