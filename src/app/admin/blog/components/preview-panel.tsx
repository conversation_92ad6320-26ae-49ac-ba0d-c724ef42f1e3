"use client"

import { BlogFormTypes } from "./types"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { format } from "date-fns"
import { tr } from "date-fns/locale"
import { CalendarIcon, UserCircle, TagIcon } from "lucide-react"
import { cn } from "@/lib/utils"
import Image from 'next/image'

export function PreviewPanel({
  formData,
  categories,
  authors,
  activeLanguage,
}: BlogFormTypes.PreviewPanelProps) {
  // Kategori adını bul
  const categoryName = categories.find(cat => cat.id === formData.category_id)?.name || "Kategori seçilmedi"
  
  // Yazar adını bul
  const authorName = authors.find(author => author.id === formData.author_id)?.name || "Yazar seçilmedi"
  
  // Varsayılan resim
  const defaultImage = "/placeholders/image-placeholder.jpg"
  
  // Tarih formatla
  const formatDate = (dateString: string | null) => {
    if (!dateString) return "Tarih belirlenmedi"
    try {
      return format(new Date(dateString), "d MMMM yyyy", { locale: tr })
    } catch (error) {
      return "Geçersiz tarih"
    }
  }

  // Durum bilgisi
  const getStatusBadge = () => {
    switch (formData.status) {
      case "published":
        return <Badge className="bg-green-100 text-green-800">Yayımlanmış</Badge>
      case "draft":
        return <Badge className="bg-yellow-100 text-yellow-800">Taslak</Badge>
      default:
        return null
    }
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Önizleme</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Görsel Önizleme */}
        <div className="aspect-video w-full rounded-md overflow-hidden bg-gray-100 border">
          {formData.image[activeLanguage] ? (
            <Image 
              src={formData.image[activeLanguage] || defaultImage}
              alt={formData.title[activeLanguage] || "Blog görseli"}
              width={800}
              height={450}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <span className="text-gray-400">Görsel eklenmedi</span>
            </div>
          )}
        </div>

        {/* İçerik Önizleme */}
        <div className="space-y-4">
          {/* Başlık */}
          <h3 className={cn(
            "text-xl font-bold",
            !formData.title[activeLanguage] && "text-gray-400"
          )}>
            {formData.title[activeLanguage] || "Başlık eklenmedi"}
          </h3>

          {/* Meta Bilgileri */}
          <div className="flex flex-wrap gap-4 text-sm text-gray-500">
            <div className="flex items-center">
              <CalendarIcon className="h-4 w-4 mr-1" />
              <span>{formatDate(formData.published_at)}</span>
            </div>
            <div className="flex items-center">
              <UserCircle className="h-4 w-4 mr-1" />
              <span>{authorName}</span>
            </div>
            <div className="flex items-center">
              <TagIcon className="h-4 w-4 mr-1" />
              <span>{categoryName}</span>
            </div>
            {getStatusBadge()}
            {formData.featured && (
              <Badge className="bg-blue-100 text-blue-800">Öne Çıkan</Badge>
            )}
          </div>

          {/* Özet */}
          <p className={cn(
            "text-sm text-gray-600 border-l-4 border-gray-200 pl-4 py-2",
            !formData.excerpt[activeLanguage] && "text-gray-400"
          )}>
            {formData.excerpt[activeLanguage] || "Özet eklenmedi"}
          </p>

          {/* İçerik */}
          <div className="mt-4 pt-4 border-t">
            <h4 className="text-sm font-medium mb-2">İçerik önizleme:</h4>
            <div className={cn(
              "prose prose-sm max-w-none",
              !formData.content[activeLanguage] && "text-gray-400"
            )}>
              {formData.content[activeLanguage] ? (
                <p>{formData.content[activeLanguage].substring(0, 300)}
                  {formData.content[activeLanguage].length > 300 ? "..." : ""}
                </p>
              ) : (
                <p>İçerik eklenmedi</p>
              )}
            </div>
          </div>
        </div>

        {/* SEO URL */}
        <div className="p-3 bg-gray-50 rounded-md text-sm">
          <p className="text-gray-500 mb-1">SEO URL:</p>
          <code className="text-gray-800 break-all">
            {formData.slug[activeLanguage] ? 
              `/blog/${formData.slug[activeLanguage]}` : 
              "/blog/[henüz-url-oluşturulmadı]"
            }
          </code>
        </div>
      </CardContent>
    </Card>
  )
} 