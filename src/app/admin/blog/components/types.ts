// eslint-disable-next-line @typescript-eslint/no-namespace
export namespace BlogFormTypes {
  // Language tipini export ediyoruz
  export type Language = "tr" | "en" | "de";
  
  // Dil içeriği türü
  export type LanguageContent = {
    tr: string;
    en: string;
    de: string;
  };
  
  // Resim içeriği türü
  export type ImageContent = {
    tr: string | null;
    en: string | null;
    de: string | null;
  };
  
  // Form verisi türü
  export interface BlogFormData {
    title: LanguageContent;
    slug: LanguageContent;
    content: LanguageContent;
    excerpt: LanguageContent;
    image: ImageContent;
    category_id: string;
    author_id: string;
    status: "published" | "draft";
    featured: boolean;
    published_at: string | null;
  }
  
  // Kategori türü
  export interface Category {
    id: string;
    name: string;
  }
  
  // Yazar türü
  export interface Author {
    id: string;
    name: string;
  }
  
  // BlogForm bileşeni props türü
  export interface BlogFormProps {
    initialData?: Partial<BlogFormData>;
    categories?: Category[];
    authors?: Author[];
    onSubmit: (data: BlogFormData) => void;
    isEdit?: boolean;
  }
  
  // LanguageTab bileşeni props türü
  export interface LanguageTabProps {
    activeLanguage: Language;
    setActiveLanguage: (lang: Language) => void;
    formData: BlogFormData;
    setFormData: React.Dispatch<React.SetStateAction<BlogFormData>>;
    handleTextChange: (field: keyof BlogFormData, value: string, language?: Language) => void;
    generateSlug: (title: string, language: Language) => void;
    handleImageAction: (language: Language, action: 'upload' | 'remove') => void;
    isEdit: boolean;
  }
  
  // DetailsPanel bileşeni props türü
  export interface DetailsPanelProps {
    formData: BlogFormData;
    setFormData: React.Dispatch<React.SetStateAction<BlogFormData>>;
    categories: Category[];
    authors: Author[];
    activeLanguage: Language;
  }
  
  // PreviewPanel bileşeni props türü
  export interface PreviewPanelProps {
    formData: BlogFormData;
    categories: Category[];
    authors: Author[];
    activeLanguage: Language;
  }
  
  // FormFooter bileşeni props türü
  export interface FormFooterProps {
    isEdit: boolean;
  }
}

// Ayrıca eski Language tipini kapatıyorum ama geriye dönük uyumluluğu koruyorum
export type Language = BlogFormTypes.Language;

// BlogForm bileşeni props türü
export interface BlogFormProps {
  initialData?: Partial<BlogFormTypes.BlogFormData>;
  categories?: BlogFormTypes.Category[];
  authors?: BlogFormTypes.Author[];
  onSubmit: (data: BlogFormTypes.BlogFormData) => void;
  isEdit?: boolean;
}

// LanguageTab bileşeni props türü
export interface LanguageTabProps {
  activeLanguage: BlogFormTypes.Language;
  setActiveLanguage: (lang: BlogFormTypes.Language) => void;
  formData: BlogFormTypes.BlogFormData;
  setFormData: React.Dispatch<React.SetStateAction<BlogFormTypes.BlogFormData>>;
  handleImageAction: (language: BlogFormTypes.Language, action: 'upload' | 'remove') => void;
  isEdit: boolean;
}

// DetailsPanel bileşeni props türü
export interface DetailsPanelProps {
  formData: BlogFormTypes.BlogFormData;
  setFormData: React.Dispatch<React.SetStateAction<BlogFormTypes.BlogFormData>>;
  categories: BlogFormTypes.Category[];
  authors: BlogFormTypes.Author[];
  activeLanguage: BlogFormTypes.Language;
}

// PreviewPanel bileşeni props türü
export interface PreviewPanelProps {
  formData: BlogFormTypes.BlogFormData;
  categories: BlogFormTypes.Category[];
  authors: BlogFormTypes.Author[];
  activeLanguage: BlogFormTypes.Language;
}

// FormFooter bileşeni props türü
export interface FormFooterProps {
  isEdit: boolean;
} 