"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger 
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import { Edit, MoreVertical, Plus, Trash } from "lucide-react"

// Kategori tipi
interface Category {
  id: string
  name: string
  slug: string
  postCount: number
  createdAt: string
}

interface CategoryManagerProps {
  onRefresh?: () => void
}

// Örnek veri
const INITIAL_CATEGORIES: Category[] = [
  {
    id: "1",
    name: "<PERSON><PERSON><PERSON><PERSON>",
    slug: "p<PERSON><PERSON><PERSON>",
    postCount: 12,
    createdAt: "2023-01-15"
  },
  {
    id: "2",
    name: "Sosyal Medy<PERSON>",
    slug: "sosyal-medya",
    postCount: 8,
    createdAt: "2023-02-20"
  },
  {
    id: "3",
    name: "SEO",
    slug: "seo",
    postCount: 5,
    createdAt: "2023-03-10"
  },
  {
    id: "4",
    name: "E-Ticaret",
    slug: "e-ticaret",
    postCount: 7,
    createdAt: "2023-04-05"
  },
  {
    id: "5",
    name: "Teknoloji",
    slug: "teknoloji",
    postCount: 9,
    createdAt: "2023-05-12"
  }
]

export function CategoryManager({ onRefresh }: CategoryManagerProps) {
  const [categories, setCategories] = useState<Category[]>(INITIAL_CATEGORIES)
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false)
  const [currentCategory, setCurrentCategory] = useState<Category | null>(null)
  const [newCategoryName, setNewCategoryName] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [dropdownOpen, setDropdownOpen] = useState<string | null>(null)
  
  // Slug oluştur
  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^\w\sğüşıöçĞÜŞİÖÇ]/g, "")
      .replace(/\s+/g, "-")
      .replace(/ğ/g, "g")
      .replace(/ü/g, "u")
      .replace(/ş/g, "s")
      .replace(/ı/g, "i")
      .replace(/ö/g, "o")
      .replace(/ç/g, "c")
      .replace(/Ğ/g, "G")
      .replace(/Ü/g, "U")
      .replace(/Ş/g, "S")
      .replace(/İ/g, "I")
      .replace(/Ö/g, "O")
      .replace(/Ç/g, "C")
      .replace(/-+/g, "-")
      .trim()
  }
  
  // Yeni kategori ekle
  const handleAddCategory = () => {
    if (newCategoryName.trim()) {
      const newCategory: Category = {
        id: Date.now().toString(),
        name: newCategoryName,
        slug: generateSlug(newCategoryName),
        postCount: 0,
        createdAt: new Date().toISOString().split("T")[0]
      }
      
      setCategories([...categories, newCategory])
      setNewCategoryName("")
      setIsAddDialogOpen(false)
    }
  }
  
  // Kategori düzenle
  const handleEditCategory = () => {
    if (currentCategory && newCategoryName.trim()) {
      const updatedCategories = categories.map(cat => 
        cat.id === currentCategory.id
          ? {
              ...cat,
              name: newCategoryName,
              slug: generateSlug(newCategoryName)
            }
          : cat
      )
      
      setCategories(updatedCategories)
      setNewCategoryName("")
      setIsEditDialogOpen(false)
      setCurrentCategory(null)
    }
  }
  
  // Kategori sil
  const handleDeleteCategory = () => {
    if (currentCategory) {
      const updatedCategories = categories.filter(cat => cat.id !== currentCategory.id)
      setCategories(updatedCategories)
      setIsDeleteDialogOpen(false)
      setCurrentCategory(null)
    }
  }
  
  // Düzenleme için kategori seç
  const selectCategoryForEdit = (category: Category) => {
    setCurrentCategory(category)
    setNewCategoryName(category.name)
    setIsEditDialogOpen(true)
  }
  
  // Silme için kategori seç
  const selectCategoryForDelete = (category: Category) => {
    setCurrentCategory(category)
    setIsDeleteDialogOpen(true)
  }
  
  // Tarih formatla
  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return new Intl.DateTimeFormat('tr-TR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    }).format(date)
  }
  
  const handleDropdownItemClick = (categoryId: string, action: () => void) => {
    action();
    setDropdownOpen(null);
  };
  
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-bold">Kategoriler</h2>
        <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
          <DialogTrigger asChild>
            <Button size="sm">
              <Plus className="h-4 w-4 mr-2" />
              Yeni Kategori
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Yeni Kategori Ekle</DialogTitle>
              <DialogDescription>
                Blog yazıları için yeni bir kategori oluşturun.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="name">Kategori Adı</Label>
                <Input
                  id="name"
                  value={newCategoryName}
                  onChange={(e) => setNewCategoryName(e.target.value)}
                  placeholder="Kategori adı girin"
                />
              </div>
              <div className="space-y-2">
                <Label>Otomatik Slug</Label>
                <div className="p-2 bg-gray-50 border rounded-md text-sm text-gray-500">
                  {newCategoryName ? generateSlug(newCategoryName) : "kategori-slug"}
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
                İptal
              </Button>
              <Button onClick={handleAddCategory}>Ekle</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
      
      <div className="border rounded-lg overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Kategori Adı</TableHead>
              <TableHead>Slug</TableHead>
              <TableHead className="text-center">Yazı Sayısı</TableHead>
              <TableHead>Oluşturulma Tarihi</TableHead>
              <TableHead className="text-right">İşlemler</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {categories.length > 0 ? (
              categories.map((category) => (
                <TableRow key={category.id}>
                  <TableCell className="font-medium">{category.name}</TableCell>
                  <TableCell>{category.slug}</TableCell>
                  <TableCell className="text-center">
                    <Badge variant="outline">{category.postCount}</Badge>
                  </TableCell>
                  <TableCell>{formatDate(category.createdAt)}</TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu 
                      open={dropdownOpen === category.id} 
                      onOpenChange={(open) => setDropdownOpen(open ? category.id : null)}
                    >
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreVertical className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem 
                          onClick={() => handleDropdownItemClick(category.id, () => selectCategoryForEdit(category))}
                        >
                          <Edit className="h-4 w-4 mr-2" />
                          Düzenle
                        </DropdownMenuItem>
                        <DropdownMenuItem 
                          onClick={() => handleDropdownItemClick(category.id, () => selectCategoryForDelete(category))}
                          className="text-red-600"
                          disabled={category.postCount > 0}
                        >
                          <Trash className="h-4 w-4 mr-2" />
                          Sil
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-4">
                  Henüz kategori bulunmuyor
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      
      {/* Düzenleme İletişim Kutusu */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Kategori Düzenle</DialogTitle>
            <DialogDescription>
              Kategori bilgilerini güncelleyin.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="editName">Kategori Adı</Label>
              <Input
                id="editName"
                value={newCategoryName}
                onChange={(e) => setNewCategoryName(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label>Otomatik Slug</Label>
              <div className="p-2 bg-gray-50 border rounded-md text-sm text-gray-500">
                {newCategoryName ? generateSlug(newCategoryName) : "kategori-slug"}
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsEditDialogOpen(false)}>
              İptal
            </Button>
            <Button onClick={handleEditCategory}>Güncelle</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Silme İletişim Kutusu */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Kategori Sil</DialogTitle>
            <DialogDescription>
              Bu işlem geri alınamaz. Kategoriyi silmek istediğinizden emin misiniz?
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <p className="text-sm">
              <span className="font-semibold">{currentCategory?.name}</span> kategorisi silinecek.
            </p>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              İptal
            </Button>
            <Button variant="destructive" onClick={handleDeleteCategory}>
              Sil
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  )
} 