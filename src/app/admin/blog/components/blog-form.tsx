"use client"

import { useState } from "react"
import { LanguageTab } from "./language-tab"
import { BlogFormTypes } from "./types"
import { DetailsPanel } from "./details-panel"
import { PreviewPanel } from "./preview-panel"
import { FormFooter } from "./form-footer"

/**
 * Ana BlogForm bileşeni
 * Tüm blog yazısı form işlevlerini yönetir
 */
export function BlogForm({
  initialData,
  categories = [],
  authors = [],
  onSubmit,
  isEdit = false,
}: BlogFormTypes.BlogFormProps) {
  // Form state
  const [formData, setFormData] = useState<BlogFormTypes.BlogFormData>({
    title: initialData?.title || { tr: "", en: "", de: "" },
    slug: initialData?.slug || { tr: "", en: "", de: "" },
    content: initialData?.content || { tr: "", en: "", de: "" },
    excerpt: initialData?.excerpt || { tr: "", en: "", de: "" },
    image: initialData?.image || { tr: null, en: null, de: null },
    category_id: initialData?.category_id || "",
    author_id: initialData?.author_id || "",
    status: initialData?.status || "draft",
    featured: initialData?.featured || false,
    published_at: initialData?.published_at || null,
  })

  // Aktif dil sekmesi
  const [activeLanguage, setActiveLanguage] = useState<BlogFormTypes.Language>("tr")

  // Form gönderi işlemi
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSubmit(formData)
  }

  // Metin alanlarını güncelleme işlevi
  const handleTextChange = (
    field: keyof BlogFormTypes.BlogFormData,
    value: string,
    language?: BlogFormTypes.Language
  ) => {
    if (language && (field === "title" || field === "slug" || field === "content" || field === "excerpt")) {
      // Dil bazlı alanlar (çoklu dil desteği olan alanlar)
      setFormData((prevData) => ({
        ...prevData,
        [field]: {
          ...prevData[field] as BlogFormTypes.LanguageContent,
          [language]: value,
        },
      }))
    } else {
      // Tekil alanlar
      setFormData((prevData) => ({
        ...prevData,
        [field]: value,
      }))
    }
  }

  // Başlık değiştiğinde slug'ı otomatik oluştur
  const generateSlug = (title: string, language: BlogFormTypes.Language) => {
    const slug = title
      .toLowerCase()
      .replace(/[^\w\sğüşıöçĞÜŞİÖÇ]/g, "")
      .replace(/\s+/g, "-")
      .replace(/ğ/g, "g")
      .replace(/ü/g, "u")
      .replace(/ş/g, "s")
      .replace(/ı/g, "i")
      .replace(/ö/g, "o")
      .replace(/ç/g, "c")
      .replace(/Ğ/g, "G")
      .replace(/Ü/g, "U")
      .replace(/Ş/g, "S")
      .replace(/İ/g, "I")
      .replace(/Ö/g, "O")
      .replace(/Ç/g, "C")
      .replace(/-+/g, "-")
      .trim()

    setFormData((prevData) => ({
      ...prevData,
      slug: {
        ...prevData.slug,
        [language]: slug,
      },
    }))
  }

  // Resim işlemleri
  const handleImageAction = (language: BlogFormTypes.Language, action: 'upload' | 'remove') => {
    if (action === 'upload') {
      // Gerçek uygulamada bir dosya seçici açılır ve sunucuya yükleme işlemi yapılır
      const mockImageUrl = `/images/blog/${language}/image-${Math.floor(Math.random() * 1000)}.jpg`
      
      setFormData((prevData) => ({
        ...prevData,
        image: {
          ...prevData.image,
          [language]: mockImageUrl,
        },
      }))
    } else {
      setFormData((prevData) => ({
        ...prevData,
        image: {
          ...prevData.image,
          [language]: null,
        },
      }))
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-8">
      <LanguageTab
        activeLanguage={activeLanguage}
        setActiveLanguage={setActiveLanguage}
        formData={formData}
        setFormData={setFormData}
        handleTextChange={handleTextChange}
        generateSlug={generateSlug}
        handleImageAction={handleImageAction}
        isEdit={isEdit}
      />

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <DetailsPanel
          formData={formData}
          setFormData={setFormData}
          categories={categories}
          authors={authors}
          activeLanguage={activeLanguage}
        />
        
        <PreviewPanel
          formData={formData}
          categories={categories}
          authors={authors}
          activeLanguage={activeLanguage}
        />
      </div>

      <FormFooter isEdit={isEdit} />
    </form>
  )
} 