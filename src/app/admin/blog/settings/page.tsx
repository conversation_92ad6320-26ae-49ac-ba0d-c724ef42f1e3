"use client"

import { useState } from "react"
import { DashboardLayout } from "@/app/layout/dashboard-layout"
import { DashboardHeader } from "@/app/admin/components/dashboard/header"
import { CategoryManager } from "../components/category-manager"
import { Button } from "@/components/ui/button"
import { ArrowLeft, Save } from "lucide-react"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { useRouter } from "next/navigation"

export default function BlogSettingsPage() {
  const router = useRouter()
  
  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <DashboardHeader 
            title="Blog Ayarları" 
            description="Blog kategorilerini yönetin"
          />
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => router.push("/admin/blog")}
            className="h-9"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            <PERSON><PERSON>
          </Button>
        </div>
        
        <Card>
          <CardHeader>
            <CardTitle>Kate<PERSON>i <PERSON>ö<PERSON></CardTitle>
            <CardDescription>
              Blog kategorilerini ekleyin, düzenleyin ve yönetin
            </CardDescription>
          </CardHeader>
          <CardContent>
            <CategoryManager />
          </CardContent>
        </Card>
        
        <div className="flex justify-end">
          <Button className="px-6" onClick={() => router.push("/admin/blog")}>
            <Save className="mr-2 h-4 w-4" />
            Kaydet
          </Button>
        </div>
      </div>
    </DashboardLayout>
  )
} 