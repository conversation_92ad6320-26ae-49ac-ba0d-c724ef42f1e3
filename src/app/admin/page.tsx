"use client"

import { 
  Users, 
  Megaphone,
  Target,
  Eye,
  Share2,
  DollarSign,
  TrendingUp,
  ArrowUp,
  ArrowDown,
  Calendar,
  BarChart3,
  <PERSON><PERSON><PERSON>
} from "lucide-react"

import { DashboardLayout } from "@/app/layout/dashboard-layout"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  AreaChart,
  Area,
  Legend,
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ResponsiveContainer,
  CartesianGrid,
  XAxis,
  YAxis,
  Tooltip,
  Bar,
  BarChart
} from 'recharts'
import * as React from "react"
import {
  ChartConfig
} from "@/components/ui/chart"
import { WorldMap } from "@/app/admin/components/dashboard/world-map"
import { DashboardHeader } from "@/app/admin/components/dashboard/header"
import { fetchCampaignStatistics, CampaignStatistics } from "@/shared/services/admin/campaign-stats"
import { fetchMonthlyPerformance, MonthlyPerformance } from "@/shared/services/admin/monthly-performance"

export default function Home() {
  // Aylık performans verileri
  const [monthlyData, setMonthlyData] = React.useState<MonthlyPerformance[]>([]);

  // Platform performans verileri
  const platformPerformanceData = [
    { name: 'Instagram', takipçi: 850000, etkileşim: 68, gelir: 125000 },
    { name: 'Facebook', takipçi: 620000, etkileşim: 45, gelir: 85000 },
    { name: 'Twitter', takipçi: 450000, etkileşim: 52, gelir: 65000 },
    { name: 'YouTube', takipçi: 320000, etkileşim: 73, gelir: 95000 },
    { name: 'TikTok', takipçi: 280000, etkileşim: 82, gelir: 72000 },
  ]

  // Reklam türü performansı
  const adTypeData = [
    { name: 'Story', views: 2800, engagement: 1200, conversion: 450 },
    { name: 'Post', views: 2200, engagement: 900, conversion: 380 },
    { name: 'Reels', views: 3500, engagement: 1800, conversion: 620 },
    { name: 'IGTV', views: 1800, engagement: 750, conversion: 280 },
  ]

  // Son aktiviteler state'i
  const initialActivities = [
    {
      id: "1",
      title: "Yeni influencer kampanyası başlatıldı",
      user: "Nike Türkiye",
      time: "5 dk önce",
      status: "active" as const,
      amount: "₺45,000"
    },
    {
      id: "2",
      title: "Story kampanyası tamamlandı",
      user: "Adidas",
      time: "25 dk önce",
      status: "completed" as const,
      amount: "₺32,500"
    },
    {
      id: "3",
      title: "Yeni reklam teklifi",
      user: "Puma",
      time: "1 saat önce",
      status: "pending" as const,
      amount: "₺28,750"
    },
    {
      id: "4",
      title: "Reels kampanyası başlatıldı",
      user: "Under Armour",
      time: "2 saat önce",
      status: "active" as const,
      amount: "₺37,200"
    }
  ];
  const [recentActivities, setRecentActivities] = React.useState(initialActivities);
  const [isRefreshing, setIsRefreshing] = React.useState(false);
  const [isUpdated, setIsUpdated] = React.useState(false);

  // Saatlik tıklama verileri
  const clickData = React.useMemo(() => [
    { date: new Date("2024-04-01").toISOString(), desktop: 222, mobile: 150 },
    { date: new Date("2024-04-02").toISOString(), desktop: 97, mobile: 180 },
    { date: new Date("2024-04-03").toISOString(), desktop: 167, mobile: 120 },
    { date: new Date("2024-04-04").toISOString(), desktop: 242, mobile: 260 },
    { date: new Date("2024-04-05").toISOString(), desktop: 373, mobile: 290 },
    { date: new Date("2024-04-06").toISOString(), desktop: 301, mobile: 340 },
    { date: new Date("2024-04-07").toISOString(), desktop: 245, mobile: 180 },
    { date: new Date("2024-04-08").toISOString(), desktop: 409, mobile: 320 },
    { date: new Date("2024-04-09").toISOString(), desktop: 59, mobile: 110 },
    { date: new Date("2024-04-10").toISOString(), desktop: 261, mobile: 190 }
  ], []);

  const chartConfig = {
    views: {
      label: "Sayfa Görüntülenmeleri",
      color: "hsl(var(--chart-3))",
    },
    desktop: {
      label: "Masaüstü",
      color: "hsl(var(--chart-1))",
    },
    mobile: {
      label: "Mobil",
      color: "hsl(var(--chart-2))",
    },
  } satisfies ChartConfig

  const [activeChart, setActiveChart] = React.useState<keyof typeof chartConfig>("desktop")

  const total = React.useMemo(
    () => ({
      desktop: clickData.reduce((acc, curr) => acc + curr.desktop, 0),
      mobile: clickData.reduce((acc, curr) => acc + curr.mobile, 0),
    }),
    [clickData]
  )

  const formatNumber = (value: number) => {
    return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".")
  }

  const [stats, setStats] = React.useState<CampaignStatistics>({
    total_view: 0,
    total_click: 0,
    total_conversion: 0,
    total_earnings: 0
  });

  // Fetch campaign statistics
  React.useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await fetchCampaignStatistics();
        if (response.status && response.result) {
          setStats(response.result);
        }
      } catch (error) {
        console.error("Failed to fetch campaign statistics:", error);
      }
    };

    fetchStats();
  }, []);

  // Fetch monthly performance data
  React.useEffect(() => {
    const fetchMonthlyStats = async () => {
      try {
        const response = await fetchMonthlyPerformance();
        if (response.status && response.result) {
          setMonthlyData(response.result);
        }
      } catch (error) {
        console.error("Failed to fetch monthly performance:", error);
      }
    };

    fetchMonthlyStats();
  }, []);

  // Transform API data for the chart
  const chartData = React.useMemo(() => {
    // Eğer veri yoksa veya boşsa, son 6 ay için 0 değerleri döndür
    if (!monthlyData || monthlyData.length === 0) {
      const months = ['Oca', 'Şub', 'Mar', 'Nis', 'May', 'Haz'];
      return months.map(month => ({
        name: month,
        görüntülenme: 0,
        etkileşim: 0,
        dönüşüm: 0
      }));
    }

    // Veri varsa, her ay için toplamları hesapla
    return monthlyData.map(month => {
      const totalViews = month.advertisers.reduce((sum, adv) => sum + (adv.views || 0), 0);
      const totalClicks = month.advertisers.reduce((sum, adv) => sum + (adv.clicks || 0), 0);
      const totalConversions = month.advertisers.reduce((sum, adv) => sum + (adv.conversions || 0), 0);

      return {
        name: month.month.split(' ')[0], // Sadece ay adını al
        görüntülenme: totalViews,
        etkileşim: totalClicks,
        dönüşüm: totalConversions
      };
    });
  }, [monthlyData]);

  return (
    <DashboardLayout>

      <DashboardHeader
        title="Reklam Yönetim Paneli"
        description="Kampanya performansları ve reklam istatistikleri"
      />
      
      {/* Ana İstatistikler */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4 mb-8">
        <Card className="hover:scale-[1.02] transition-all duration-200 shadow-lg bg-gradient-to-br from-purple-50 to-purple-100">
          <CardContent className="p-6">
            <div className="flex justify-between items-start mb-3">
              <div className="h-12 w-12 bg-purple-500/10 rounded-xl flex items-center justify-center">
                <Eye className="h-6 w-6 text-purple-600" />
              </div>
            </div>
            <p className="text-sm font-medium text-purple-900/60 mb-1">Toplam Görüntülenme</p>
            <div className="flex items-end justify-between">
              <h3 className="text-2xl font-bold text-purple-900">{stats.total_view.toLocaleString('tr-TR')}</h3>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:scale-[1.02] transition-all duration-200 shadow-lg bg-gradient-to-br from-blue-50 to-blue-100">
          <CardContent className="p-6">
            <div className="flex justify-between items-start mb-3">
              <div className="h-12 w-12 bg-blue-500/10 rounded-xl flex items-center justify-center">
                <Share2 className="h-6 w-6 text-blue-600" />
              </div>
            </div>
            <p className="text-sm font-medium text-blue-900/60 mb-1">Toplam Tıklama</p>
            <div className="flex items-end justify-between">
              <h3 className="text-2xl font-bold text-blue-900">{stats.total_click.toLocaleString('tr-TR')}</h3>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:scale-[1.02] transition-all duration-200 shadow-lg bg-gradient-to-br from-green-50 to-green-100">
          <CardContent className="p-6">
            <div className="flex justify-between items-start mb-3">
              <div className="h-12 w-12 bg-green-500/10 rounded-xl flex items-center justify-center">
                <DollarSign className="h-6 w-6 text-green-600" />
              </div>
            </div>
            <p className="text-sm font-medium text-green-900/60 mb-1">Toplam Kazanç</p>
            <div className="flex items-end justify-between">
              <h3 className="text-2xl font-bold text-green-900">₺{stats.total_earnings.toLocaleString('tr-TR')}</h3>
            </div>
          </CardContent>
        </Card>

        <Card className="hover:scale-[1.02] transition-all duration-200 shadow-lg bg-gradient-to-br from-amber-50 to-amber-100">
          <CardContent className="p-6">
            <div className="flex justify-between items-start mb-3">
              <div className="h-12 w-12 bg-amber-500/10 rounded-xl flex items-center justify-center">
                <Target className="h-6 w-6 text-amber-600" />
              </div>
            </div>
            <p className="text-sm font-medium text-amber-900/60 mb-1">Toplam Dönüşüm</p>
            <div className="flex items-end justify-between">
              <h3 className="text-2xl font-bold text-amber-900">{stats.total_conversion.toLocaleString('tr-TR')}</h3>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Grafikler ve İstatistikler */}
      <div className="grid gap-6 grid-cols-1 lg:grid-cols-2 mb-6">
        <Card className="shadow-lg">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg font-semibold">Aylık Performans Analizi</CardTitle>
              <Badge variant="outline" className="font-normal">
                <BarChart3 className="w-4 h-4 mr-1" />
                Son 6 Ay
              </Badge>
            </div>
          </CardHeader>
          <CardContent className="p-6">
            <div className="h-[300px] w-full">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart data={chartData}>
                  <defs>
                    <linearGradient id="görüntülenmeGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#8b5cf6" stopOpacity={0.1}/>
                      <stop offset="95%" stopColor="#8b5cf6" stopOpacity={0}/>
                    </linearGradient>
                    <linearGradient id="etkileşimGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.1}/>
                      <stop offset="95%" stopColor="#3b82f6" stopOpacity={0}/>
                    </linearGradient>
                    <linearGradient id="dönüşümGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#22c55e" stopOpacity={0.1}/>
                      <stop offset="95%" stopColor="#22c55e" stopOpacity={0}/>
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" className="stroke-slate-200" />
                  <XAxis dataKey="name" className="text-xs" />
                  <YAxis className="text-xs" />
                  <Tooltip />
                  <Area
                    type="monotone"
                    dataKey="görüntülenme"
                    stroke="#8b5cf6"
                    fillOpacity={1}
                    fill="url(#görüntülenmeGradient)"
                  />
                  <Area
                    type="monotone"
                    dataKey="etkileşim"
                    stroke="#3b82f6"
                    fillOpacity={1}
                    fill="url(#etkileşimGradient)"
                  />
                  <Area
                    type="monotone"
                    dataKey="dönüşüm"
                    stroke="#22c55e"
                    fillOpacity={1}
                    fill="url(#dönüşümGradient)"
                  />
                </AreaChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-lg">
          <CardHeader className="flex flex-col items-stretch space-y-0 border-b p-0 sm:flex-row">
            <div className="flex flex-1 flex-col justify-center gap-1 px-6 py-5 sm:py-6">
              <CardTitle className="text-lg font-semibold">Tıklama Analizi</CardTitle>
              <CardDescription className="text-sm text-slate-500">
                Son 10 günün saatlik tıklama verileri
              </CardDescription>
            </div>
            <div className="flex">
              {["desktop", "mobile"].map((key) => {
                const chart = key as keyof typeof chartConfig
                return (
                  <button
                    key={chart}
                    data-active={activeChart === chart}
                    className="relative z-30 flex flex-1 flex-col justify-center gap-1 border-t px-6 py-4 text-left even:border-l data-[active=true]:bg-muted/50 sm:border-l sm:border-t-0 sm:px-8 sm:py-6"
                    onClick={() => setActiveChart(chart)}
                  >
                    <span className="text-xs text-muted-foreground">
                      {chartConfig[chart].label}
                    </span>
                    <span className="text-lg font-bold leading-none sm:text-3xl">
                      {total[key as keyof typeof total].toLocaleString()}
                    </span>
                  </button>
                )
              })}
            </div>
          </CardHeader>
          <CardContent className="px-2 sm:p-6">
            <ResponsiveContainer width="100%" height={300}>
              <BarChart
                data={clickData}
                margin={{
                  left: 40,
                  right: 40,
                  top: 20,
                  bottom: 20
                }}
              >
                <CartesianGrid vertical={false} className="stroke-slate-200" />
                <XAxis
                  dataKey="date"
                  tickLine={false}
                  axisLine={false}
                  tickMargin={8}
                  minTickGap={32}
                  tickFormatter={(value) => {
                    const date = new Date(value)
                    return date.toLocaleDateString("tr-TR", {
                      month: "short",
                      day: "numeric",
                    })
                  }}
                />
                <YAxis
                  tickLine={false}
                  axisLine={false}
                  tickMargin={8}
                  tickFormatter={(value) => `${value}`}
                />
                <Tooltip
                  content={({ active, payload, label }) => {
                    if (active && payload && payload.length) {
                      const date = new Date(label)
                      return (
                        <div className="bg-white p-2 rounded-lg shadow-lg border border-slate-200">
                          <p className="font-medium text-slate-900 mb-1">
                            {date.toLocaleDateString("tr-TR", {
                              day: "numeric",
                              month: "long",
                              year: "numeric"
                            })}
                          </p>
                          <p className="text-sm text-purple-600">
                            Masaüstü: {payload[0].value}
                          </p>
                          <p className="text-sm text-blue-600">
                            Mobil: {payload[1].value}
                          </p>
                        </div>
                      )
                    }
                    return null
                  }}
                />
                <Bar
                  dataKey="desktop"
                  name="Masaüstü"
                  fill="#7c3aed" // 
                  radius={[4, 4, 0, 0]}
                />
                <Bar
                  dataKey="mobile"
                  name="Mobil"
                  fill="#0ea5e9"
                  radius={[4, 4, 0, 0]}
                />
                <Legend />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* Alt Grid */}
      <div className="grid gap-6 grid-cols-1 lg:grid-cols-2">
        <Card className="shadow-lg">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg font-semibold">Reklam Türü Performansı</CardTitle>
              <Badge variant="outline" className="font-normal">
                <BarChart3 className="w-4 h-4 mr-1" />
                Tüm Türler
              </Badge>
            </div>
          </CardHeader>
          <CardContent className="p-6">
            <div className="h-[300px] w-full">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={adTypeData}>
                  <CartesianGrid strokeDasharray="3 3" className="stroke-slate-200" />
                  <XAxis dataKey="name" className="text-xs" />
                  <YAxis className="text-xs" />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="views" name="Görüntülenme" fill="#8b5cf6" radius={[4, 4, 0, 0]} />
                  <Bar dataKey="engagement" name="Etkileşim" fill="#3b82f6" radius={[4, 4, 0, 0]} />
                  <Bar dataKey="conversion" name="Dönüşüm" fill="#22c55e" radius={[4, 4, 0, 0]} />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        <Card className="shadow-lg">
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <CardTitle className="text-lg font-semibold">Son Aktiviteler</CardTitle>
              <div className="flex items-center gap-2">
                <Badge variant="outline" className="font-normal">Canlı Akış</Badge>
                <button
                  onClick={() => {
                    setIsRefreshing(true);
                    setTimeout(() => {
                      setRecentActivities([...initialActivities]);
                      setIsRefreshing(false);
                      setIsUpdated(true);
                      setTimeout(() => setIsUpdated(false), 1200);
                    }, 700);
                  }}
                  className="p-1 rounded hover:bg-slate-100 border border-slate-200 transition flex items-center justify-center min-w-[32px] min-h-[32px]"
                  title="Yenile"
                  disabled={isRefreshing}
                >
                  {isRefreshing ? (
                    <svg className="animate-spin h-4 w-4 text-slate-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle><path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v4a4 4 0 00-4 4H4z"></path></svg>
                  ) : (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-slate-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582M20 20v-5h-.581M5.582 9A7.003 7.003 0 0112 5c3.314 0 6.065 2.686 6.418 6.004M18.418 15A7.003 7.003 0 0112 19c-3.314 0-6.065-2.686-6.418-6.004" /></svg>
                  )}
                </button>
                {isUpdated && (
                  <span className="ml-2 text-xs text-green-600 animate-fade-in">Güncellendi</span>
                )}
              </div>
            </div>
          </CardHeader>
          <CardContent className="p-4">
            <div className="space-y-4 max-h-72 overflow-y-auto pr-2">
              {recentActivities.map((activity) => (
                <div key={activity.id} 
                  className="flex items-start gap-4 p-3 rounded-lg hover:bg-slate-50 border border-transparent hover:border-slate-200 transition-all duration-200">
                  <div className="h-10 w-10 flex items-center justify-center rounded-xl bg-gradient-to-br from-blue-100 to-blue-200 text-blue-700 font-semibold text-sm">
                    {activity.user.charAt(0)}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium">{activity.title}</p>
                        <div className="flex items-center gap-2 mt-1">
                          <span className="text-xs font-medium text-blue-600">{activity.user}</span>
                          <span className="text-xs text-slate-400">•</span>
                          <span className="text-xs text-slate-500">{activity.time}</span>
                        </div>
                      </div>
                      <div className="text-right">
                        <Badge
                          variant="outline"
                          className={
                            activity.status === "completed"
                              ? "border-green-500 text-green-700 bg-green-50"
                              : activity.status === "active"
                              ? "border-blue-500 text-blue-700 bg-blue-50"
                              : "border-amber-500 text-amber-700 bg-amber-50"
                          }
                        >
                          {activity.status === "completed" ? "Tamamlandı" : 
                           activity.status === "active" ? "Aktif" : "Bekliyor"}
                        </Badge>
                        <p className="text-sm font-semibold mt-1 text-slate-600">{activity.amount}</p>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
        <WorldMap />
      </div>

    </DashboardLayout>
  )
}
