import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface Activity {
  id: string
  title: string
  user?: string
  time: string
  status?: "completed" | "pending"
  icon?: React.ReactNode
}

interface ActivityLogProps {
  title: string
  activities: Activity[]
  showFilter?: boolean
}

export function ActivityLog({ title, activities, showFilter = false }: ActivityLogProps) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle className="text-lg font-medium">{title}</CardTitle>
        {showFilter && (
          <Select defaultValue="all">
            <SelectTrigger className="w-[150px]">
              <SelectValue placeholder="Select filter" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Tüm Aktiviteler</SelectItem>
              <SelectItem value="completed">Tamamlanan</SelectItem>
              <SelectItem value="pending">Bekleyen</SelectItem>
            </SelectContent>
          </Select>
        )}
      </CardHeader>
      <CardContent className="space-y-4">
        {activities.map((activity) => (
          <div key={activity.id} className="flex items-start gap-4 p-2 rounded-lg hover:bg-muted/50">
            {activity.icon ? (
              <div className="h-8 w-8 flex items-center justify-center rounded-full bg-blue-100">
                {activity.icon}
              </div>
            ) : (
              <Avatar className="h-8 w-8">
                <AvatarFallback>{activity.user ? activity.user[0] : "S"}</AvatarFallback>
              </Avatar>
            )}
            <div className="flex-1 space-y-1">
              <div className="flex items-center justify-between">
                <p className="text-sm font-medium">{activity.title}</p>
                <p className="text-xs text-muted-foreground">{activity.time}</p>
              </div>
              {activity.user && (
                <p className="text-xs text-muted-foreground">{activity.user}</p>
              )}
            </div>
            {activity.status && (
              <Badge
                variant="outline"
                className={activity.status === "completed" ? "border-green-500 text-green-500" : ""}
              >
                {activity.status === "completed" ? "Tamamlandı" : "Bekliyor"}
              </Badge>
            )}
          </div>
        ))}
      </CardContent>
    </Card>
  )
}
