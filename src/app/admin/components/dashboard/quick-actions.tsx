import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { LucideIcon, UserPlus, PieChart, CreditCard, FileText } from "lucide-react"
import Link from "next/link"

interface QuickAction {
  title: string
  icon: LucideIcon
  href: string
  color: string
}

interface QuickActionsProps {
  title: string
  actions: QuickAction[]
}

export function QuickActions({ title, actions }: QuickActionsProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-medium">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 gap-4">
          {actions.map((action, index) => {
            const Icon = action.icon
            return (
              <Link
                key={index}
                href={action.href}
                className="flex flex-col items-center justify-center p-4 rounded-lg border hover:bg-muted/50 transition-colors"
              >
                <div className={`p-2 rounded-full ${action.color} mb-2`}>
                  <Icon className="h-5 w-5 text-white" />
                </div>
                <span className="text-sm font-medium text-center">{action.title}</span>
              </Link>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}
