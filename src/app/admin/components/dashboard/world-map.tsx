"use client"

import { useState } from "react"
import {
  ComposableMap,
  Geographies,
  Geography,
  ZoomableGroup
} from "react-simple-maps"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Globe2 } from "lucide-react"

const geoUrl = "https://unpkg.com/world-atlas@2.0.2/countries-110m.json"

// ISO ülke kodları ile eşleştirme
const countryMapping = {
  "Turkey": "TUR",
  "United States of America": "USA",
  "United Kingdom": "GBR",
  "Germany": "DEU",
  "France": "FRA",
  "Spain": "ESP",
  "Italy": "ITA",
  "Russia": "RUS",
  "China": "CHN",
  "Japan": "JPN",
  "India": "IND",
  "Brazil": "BRA",
  "Canada": "CAN",
  "Australia": "AUS",
  "South Korea": "KOR",
  "Indonesia": "IDN",
  "Saudi Arabia": "SAU",
  "South Africa": "ZAF",
  "Mexico": "MEX",
  "Egypt": "EGY"
}

// Tıklama verileri
const clickData = {
  TUR: { clicks: 1250000, name: "Türkiye", change: 12.5 },
  USA: { clicks: 2850000, name: "Amerika Birleşik Devletleri", change: 8.3 },
  GBR: { clicks: 950000, name: "Birleşik Krallık", change: -2.1 },
  DEU: { clicks: 1150000, name: "Almanya", change: 5.7 },
  FRA: { clicks: 890000, name: "Fransa", change: 3.2 },
  ESP: { clicks: 750000, name: "İspanya", change: 1.8 },
  ITA: { clicks: 820000, name: "İtalya", change: -1.5 },
  RUS: { clicks: 1350000, name: "Rusya", change: 15.4 },
  CHN: { clicks: 3250000, name: "Çin", change: 25.8 },
  JPN: { clicks: 1050000, name: "Japonya", change: 4.2 },
  IND: { clicks: 2250000, name: "Hindistan", change: 18.9 },
  BRA: { clicks: 950000, name: "Brezilya", change: 7.6 },
  CAN: { clicks: 780000, name: "Kanada", change: 2.3 },
  AUS: { clicks: 650000, name: "Avustralya", change: 6.1 },
  KOR: { clicks: 920000, name: "Güney Kore", change: 9.4 },
  IDN: { clicks: 1150000, name: "Endonezya", change: 14.2 },
  SAU: { clicks: 580000, name: "Suudi Arabistan", change: 11.8 },
  ZAF: { clicks: 420000, name: "Güney Afrika", change: 5.9 },
  MEX: { clicks: 850000, name: "Meksika", change: 8.7 },
  EGY: { clicks: 680000, name: "Mısır", change: 13.5 }
}

const maxClicks = Math.max(...Object.values(clickData).map(d => d.clicks))
const totalClicks = Object.values(clickData).reduce((sum, d) => sum + d.clicks, 0)
const averageChange = Object.values(clickData).reduce((sum, d) => sum + d.change, 0) / Object.keys(clickData).length

// Renk skalası için aralıklar
const ranges = [
  { min: 0, max: 500000, label: "0-500K" },
  { min: 500000, max: 1000000, label: "500K-1M" },
  { min: 1000000, max: 2000000, label: "1M-2M" },
  { min: 2000000, max: Infinity, label: "2M+" }
]

export function WorldMap() {
  const [tooltipContent, setTooltipContent] = useState("")
  const [position, setPosition] = useState<{
    coordinates: [number, number];
    zoom: number;
  }>({
    coordinates: [20, 0],
    zoom: 1
  })

  function handleZoomIn() {
    if (position.zoom >= 4) return
    setPosition(pos => ({ ...pos, zoom: pos.zoom * 1.5 }))
  }

  function handleZoomOut() {
    if (position.zoom <= 1) return
    setPosition(pos => ({ ...pos, zoom: pos.zoom / 1.5 }))
  }

  function handleMoveEnd(position: { coordinates: [number, number]; zoom: number }) {
    setPosition(position)
  }

  function getColor(clicks: number) {
    if (clicks > 1000000) return "#15803d"; // koyu yeşil
    if (clicks > 500000) return "#22c55e"; // yeşil
    if (clicks > 100000) return "#fde047"; // sarı
    return "#e5e7eb"; // açık gri
  }

  return (
    <Card className="shadow-lg">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg font-semibold">Küresel Tıklama Analizi</CardTitle>
            <CardDescription className="mt-1">
              Toplam {(totalClicks / 1000000).toFixed(1)}M tıklama, ortalama %{averageChange.toFixed(1)} artış
            </CardDescription>
          </div>
          <Badge variant="outline" className="font-normal">
            <Globe2 className="w-4 h-4 mr-1" />
            Dünya Geneli
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <div className="relative h-[400px] w-full overflow-hidden">
          <ComposableMap
            projection="geoMercator"
            projectionConfig={{
              scale: 120,
              center: [0, 30]
            }}
          >
            <ZoomableGroup
              zoom={position.zoom}
              center={position.coordinates}
              onMoveEnd={handleMoveEnd}
              maxZoom={5}
            >
              <Geographies geography={geoUrl}>
                {({ geographies }) =>
                  geographies.map((geo) => {
                    const countryCode = countryMapping[geo.properties.name as keyof typeof countryMapping]
                    const countryData = countryCode ? clickData[countryCode as keyof typeof clickData] : undefined
                    return (
                      <Geography
                        key={geo.rsmKey}
                        geography={geo}
                        fill={countryData ? getColor(countryData.clicks) : "hsl(var(--muted))"}
                        stroke="hsl(var(--border))"
                        strokeWidth={0.5}
                        style={{
                          default: { outline: "none" },
                          hover: {
                            fill: countryData ? "#15803d" : "hsl(var(--muted))",
                            outline: "none",
                            cursor: countryData ? "pointer" : "default"
                          },
                          pressed: { outline: "none" },
                        }}
                        onMouseEnter={() => {
                          if (countryData) {
                            const changeText = countryData.change > 0
                              ? `↑ %${countryData.change}`
                              : `↓ %${Math.abs(countryData.change)}`
                            setTooltipContent(
                              `${countryData.name}\n${(countryData.clicks / 1000000).toFixed(2)}M tıklama\n${changeText}`
                            )
                          } else {
                            setTooltipContent(geo.properties.name)
                          }
                        }}
                        onMouseLeave={() => {
                          setTooltipContent("")
                        }}
                      />
                    )
                  })
                }
              </Geographies>
            </ZoomableGroup>
          </ComposableMap>
          {tooltipContent && (
            <div
              className="absolute bg-white/90 px-3 py-2 rounded-md shadow-lg border border-border text-sm"
              style={{
                left: "50%",
                bottom: "1rem",
                transform: "translateX(-50%)",
                zIndex: 1000,
                backdropFilter: "blur(8px)"
              }}
            >
              {tooltipContent.split('\n').map((text, i) => (
                <p key={i} className={i === 0 ? "font-medium" : "text-muted-foreground"}>
                  {text}
                </p>
              ))}
            </div>
          )}
          <div className="absolute right-2 bottom-2 flex flex-col gap-2">
            <button
              onClick={handleZoomIn}
              className="bg-background hover:bg-accent p-1 rounded-md border border-border"
            >
              +
            </button>
            <button
              onClick={handleZoomOut}
              className="bg-background hover:bg-accent p-1 rounded-md border border-border"
            >
              -
            </button>
          </div>
          <div className="absolute left-2 bottom-2 bg-white/90 p-2 rounded-md border border-border backdrop-blur">
            <div className="text-xs font-medium mb-1">Tıklama Yoğunluğu</div>
            {ranges.map((range, i) => (
              <div key={i} className="flex items-center gap-2 text-xs">
                <div 
                  className="w-3 h-3 rounded-sm"
                  style={{
                    backgroundColor: getColor((range.min + range.max) / 2)
                  }}
                />
                <span>{range.label}</span>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  )
} 