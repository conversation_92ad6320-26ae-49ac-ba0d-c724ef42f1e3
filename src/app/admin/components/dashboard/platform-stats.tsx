import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"

interface PlatformStat {
  name: string
  value: number
  color: string
}

interface PlatformStatsProps {
  title: string
  stats: PlatformStat[]
}

export function PlatformStats({ title, stats }: PlatformStatsProps) {
  const totalValue = stats.reduce((sum, stat) => sum + stat.value, 0)

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-medium">{title}</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {stats.map((stat, index) => (
          <div key={index} className="space-y-1">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">{stat.name}</span>
              <span className="text-sm text-muted-foreground">{stat.value.toFixed(1)}M</span>
            </div>
            <Progress
              value={(stat.value / totalValue) * 100}
              className="h-2"
              indicatorClassName={stat.color}
            />
          </div>
        ))}
      </CardContent>
    </Card>
  )
}
