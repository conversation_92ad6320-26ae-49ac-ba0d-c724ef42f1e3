import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"

interface PaymentItem {
  label: string
  value: string | number
  color: string
}

interface PaymentSummaryProps {
  title: string
  items: PaymentItem[]
  total?: {
    label: string
    value: string | number
  }
}

export function PaymentSummary({ title, items, total }: PaymentSummaryProps) {
  // Helper function to format numbers consistently
  const formatNumber = (value: string | number) => {
    if (typeof value === 'number') {
      // Using a period as decimal separator for consistency
      return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".")
    }
    return value
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-medium">{title}</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {items.map((item, index) => (
          <div key={index} className="flex items-center justify-between">
            <div className="flex items-center">
              <div className={`h-3 w-3 rounded-full mr-2 ${item.color}`} />
              <span className="text-sm font-medium">{item.label}</span>
            </div>
            <span className="text-sm font-bold">£{formatNumber(item.value)}</span>
          </div>
        ))}

        {total && (
          <>
            <div className="border-t pt-4 mt-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">{total.label}</span>
                <span className="text-sm font-bold">£{formatNumber(total.value)}</span>
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  )
}
