import { cn } from "@/lib/utils"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { LucideIcon } from "lucide-react"

interface StatCardProps {
  title: string
  value: string | number
  icon: LucideIcon
  secondaryValue?: string
  change?: {
    value: number
    type: "increase" | "decrease"
  }
  className?: string
  iconColor?: string
}

export function StatCard({
  title,
  value,
  icon: Icon,
  secondaryValue,
  change,
  className,
  iconColor = "text-blue-500"
}: StatCardProps) {
  return (
    <Card className={cn("", className)}>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-sm font-medium text-muted-foreground">
          {title}
        </CardTitle>
        <Icon className={cn("h-4 w-4", iconColor)} />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{value}</div>
        {change && (
          <p className={cn(
            "text-xs",
            change.type === "increase" ? "text-green-500" : "text-red-500"
          )}>
            <span>{change.type === "increase" ? "↑" : "↓"} {Math.abs(change.value)}% artış</span>
          </p>
        )}
        {secondaryValue && (
          <p className="text-xs text-muted-foreground">
            Aktif: {secondaryValue}
          </p>
        )}
      </CardContent>
    </Card>
  )
}
