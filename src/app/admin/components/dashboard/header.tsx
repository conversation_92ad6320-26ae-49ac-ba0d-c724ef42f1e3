"use client"

import { Bell, <PERSON> } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { useAuth } from "@/shared/hooks/useAuth"

interface DashboardHeaderProps {
  title: string
  description?: string
}

export function DashboardHeader({ title, description }: DashboardHeaderProps) {
  const { user } = useAuth()

  return (
    <div className="flex mt-5 mb-6 flex-col gap-4 md:flex-row md:items-center md:justify-between">
      <div>
        <h1 className="text-2xl font-bold tracking-tight">{title}</h1>

        {description && (
          <p className="text-sm text-gray-500">{description}</p>
        )}

      </div>
      <div className="flex items-center gap-4">
        
      
        
      </div>
    </div>
  )
}
