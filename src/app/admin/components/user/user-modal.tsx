import { useState } from "react"
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON>alog<PERSON>eader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { 
  User, Calendar, MapPin, Phone, Mail, Shield, Wallet,
  Clock, Lock, Briefcase, FileText, BarChart2, AlertCircle
} from "lucide-react"
import { User as UserType } from "./types"

interface UserModalProps {
  user: UserType | null
  open: boolean
  mode: "view" | "edit" | "delete"
  onClose: () => void
  onDelete?: (userId: number) => void
}

export function UserModal({ user, open, mode, onClose, onDelete }: UserModalProps) {
  const [activeTab, setActiveTab] = useState("profile")

  if (!user && mode !== "view") {
    return null
  }

  const getStatusColor = (status: string | undefined) => {
    switch (status) {
      case "Aktif": return "bg-green-100 text-green-700"
      case "Beklemede": return "bg-amber-100 text-amber-700"
      case "Engelli": return "bg-red-100 text-red-700"
      default: return "bg-gray-100 text-gray-700"
    }
  }

  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return "Bilgi yok"
    return new Date(dateString).toLocaleDateString("tr-TR", {
      day: "numeric",
      month: "long",
      year: "numeric"
    })
  }
  
  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className={mode !== "delete" ? "sm:max-w-[700px] max-h-[85vh] overflow-y-auto" : "sm:max-w-[425px]"}>
        <DialogHeader>
          <DialogTitle>
            {mode === "view" ? "Kullanıcı Detayları" :
             mode === "edit" ? "Kullanıcı Düzenle" :
             "Kullanıcı Sil"}
          </DialogTitle>
          <DialogDescription>
            {mode === "view" ? "Kullanıcı bilgilerini görüntüleyin" :
             mode === "edit" ? "Kullanıcı bilgilerini düzenleyin" :
             "Bu kullanıcıyı silmek istediğinizden emin misiniz?"}
          </DialogDescription>
        </DialogHeader>

        {mode === "delete" ? (
          <div className="py-4">
            <div className="flex items-center gap-4 mb-4">
              <div className="h-16 w-16 rounded-full bg-gray-100 flex items-center justify-center text-2xl font-bold">
                {user?.name[0]}
              </div>
              <div>
                <h3 className="font-medium">{user?.name}</h3>
                <p className="text-sm text-gray-500">{user?.email}</p>
                <Badge className={getStatusColor(user?.status)}>{user?.status}</Badge>
              </div>
            </div>
            <div className="bg-red-50 p-4 rounded-md border border-red-200 mb-4">
              <div className="flex gap-2">
                <AlertCircle className="h-5 w-5 text-red-500" />
                <div>
                  <h4 className="font-medium text-red-700">Dikkat!</h4>
                  <p className="text-sm text-red-600">Bu işlem geri alınamaz. Kullanıcı silindikten sonra tüm verilerine erişiminiz kaybolacaktır.</p>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <Tabs defaultValue="profile" className="w-full" value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid grid-cols-5 mb-6">
              <TabsTrigger value="profile">Profil</TabsTrigger>
              <TabsTrigger value="contact">İletişim</TabsTrigger>
              <TabsTrigger value="security">Güvenlik</TabsTrigger>
              <TabsTrigger value="activity">Aktivite</TabsTrigger>
              <TabsTrigger value="finance">Finans</TabsTrigger>
            </TabsList>
            
            <TabsContent value="profile" className="space-y-4">
              <div className="flex items-center gap-4 mb-4">
                <div className="h-20 w-20 rounded-full bg-gray-100 flex items-center justify-center text-3xl font-bold">
                  {user?.name[0]}
                </div>
                <div>
                  <h3 className="text-xl font-medium">{user?.name}</h3>
                  <div className="flex items-center gap-2 mt-1">
                    <Badge variant="outline">{user?.role}</Badge>
                    <Badge className={getStatusColor(user?.status)}>{user?.status}</Badge>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base flex items-center gap-2">
                      <Briefcase className="h-4 w-4" />
                      İş Bilgileri
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div>
                      <p className="text-sm font-medium text-gray-500">Rol</p>
                      <p>{user?.role || "Bilgi yok"}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500">Kampanya Sayısı</p>
                      <p>{user?.campaigns !== undefined ? user.campaigns : "Bilgi yok"}</p>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base flex items-center gap-2">
                      <User className="h-4 w-4" />
                      Kişisel Bilgiler
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-2">
                    <div>
                      <p className="text-sm font-medium text-gray-500">Doğum Tarihi</p>
                      <p>{user?.birthDate ? formatDate(user?.birthDate) : "Bilgi yok"}</p>
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-500">Hakkında</p>
                      <p className="text-sm">{user?.bio || "Kullanıcı hakkında bilgi bulunmuyor."}</p>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="contact" className="space-y-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base flex items-center gap-2">
                    <Mail className="h-4 w-4" />
                    İletişim Bilgileri
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-start gap-3">
                    <Mail className="h-5 w-5 text-gray-500 mt-0.5" />
                    <div>
                      <p className="text-sm font-medium text-gray-500">E-posta Adresi</p>
                      <p>{user?.email || "Bilgi yok"}</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <Phone className="h-5 w-5 text-gray-500 mt-0.5" />
                    <div>
                      <p className="text-sm font-medium text-gray-500">Telefon</p>
                      <p>{user?.phone || "Bilgi yok"}</p>
                    </div>
                  </div>
                  <div className="flex items-start gap-3">
                    <MapPin className="h-5 w-5 text-gray-500 mt-0.5" />
                    <div>
                      <p className="text-sm font-medium text-gray-500">Adres</p>
                      {user?.address ? (
                        <>
                          <p>{user.address.street}</p>
                          <p>{`${user.address.city}, ${user.address.state} ${user.address.zipCode}`}</p>
                          <p>{user.address.country}</p>
                        </>
                      ) : (
                        <p>Bilgi yok</p>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base">Sosyal Medya</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  {user?.socialMedia?.twitter && (
                    <div>
                      <p className="text-sm font-medium text-gray-500">Twitter</p>
                      <p className="text-blue-600">@{user.socialMedia.twitter}</p>
                    </div>
                  )}
                  {user?.socialMedia?.linkedin && (
                    <div>
                      <p className="text-sm font-medium text-gray-500">LinkedIn</p>
                      <p className="text-blue-600">{user.socialMedia.linkedin}</p>
                    </div>
                  )}
                  {user?.socialMedia?.instagram && (
                    <div>
                      <p className="text-sm font-medium text-gray-500">Instagram</p>
                      <p className="text-blue-600">@{user.socialMedia.instagram}</p>
                    </div>
                  )}
                  {(!user?.socialMedia?.twitter && !user?.socialMedia?.linkedin && !user?.socialMedia?.instagram) && (
                    <p className="text-sm text-gray-500">Sosyal medya bilgisi bulunmuyor</p>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="security" className="space-y-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base flex items-center gap-2">
                    <Shield className="h-4 w-4" />
                    Güvenlik Ayarları
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">İki Faktörlü Kimlik Doğrulama</p>
                      <p className="text-sm text-gray-500">Hesabınızı daha güvenli hale getirin</p>
                    </div>
                    <Badge variant={user?.twoFactorEnabled ? "default" : "outline"}>
                      {user?.twoFactorEnabled ? "Aktif" : "Pasif"}
                    </Badge>
                  </div>
                  <div className="border-t pt-3">
                    <p className="text-sm font-medium text-gray-500">Son Şifre Değişikliği</p>
                    <p>10 Mayıs 2023</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base flex items-center gap-2">
                    <Lock className="h-4 w-4" />
                    Yetkiler
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {user?.permissions?.length ? (
                      <div className="flex flex-wrap gap-2">
                        {user.permissions.map((permission, index) => (
                          <Badge key={index} variant="secondary">{permission}</Badge>
                        ))}
                      </div>
                    ) : (
                      <p className="text-sm text-gray-500">Özel yetki tanımlanmamış</p>
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="activity" className="space-y-4">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base flex items-center gap-2">
                    <Clock className="h-4 w-4" />
                    Hesap Aktivitesi
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div>
                    <p className="text-sm font-medium text-gray-500">Kayıt Tarihi</p>
                    <p>{user?.registrationDate ? formatDate(user.registrationDate) : "Bilgi yok"}</p>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-gray-500">Son Giriş</p>
                    <p>{user?.lastLogin ? formatDate(user.lastLogin) : "Bilgi yok"}</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base flex items-center gap-2">
                    <FileText className="h-4 w-4" />
                    Son Hareketler
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="border-l-2 border-green-500 pl-4 relative">
                      <div className="absolute w-2 h-2 bg-green-500 rounded-full -left-[5px] top-1.5"></div>
                      <p className="font-medium">Profil güncellendi</p>
                      <p className="text-sm text-gray-500">10 Haziran 2023, 14:23111</p>
                    </div>
                    <div className="border-l-2 border-blue-500 pl-4 relative">
                      <div className="absolute w-2 h-2 bg-blue-500 rounded-full -left-[5px] top-1.5"></div>
                      <p className="font-medium">Yeni kampanya oluşturuldu</p>
                      <p className="text-sm text-gray-500">5 Haziran 2023, 09:15</p>
                    </div>
                    <div className="border-l-2 border-gray-300 pl-4 relative">
                      <div className="absolute w-2 h-2 bg-gray-300 rounded-full -left-[5px] top-1.5"></div>
                      <p className="font-medium">Oturum açıldı</p>
                      <p className="text-sm text-gray-500">1 Haziran 2023, 10:03</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="finance" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base flex items-center gap-2">
                      <Wallet className="h-4 w-4" />
                      Finansal Özet
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {user?.role === "Reklamveren" ? (
                      <>
                        <div>
                          <p className="text-sm font-medium text-gray-500">Toplam Harcama</p>
                          <p className="text-xl font-bold">{user?.spends !== undefined ? `${user.spends.toLocaleString('tr-TR')} ₺` : "Bilgi yok"}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-500">Bu Ay</p>
                          <p className="text-lg">2.850 ₺</p>
                        </div>
                      </>
                    ) : user?.role === "Yayıncı" ? (
                      <>
                        <div>
                          <p className="text-sm font-medium text-gray-500">Toplam Kazanç</p>
                          <p className="text-xl font-bold">{user?.earnings !== undefined ? `${user.earnings.toLocaleString('tr-TR')} ₺` : "Bilgi yok"}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-500">Bu Ay</p>
                          <p className="text-lg">1.235 ₺</p>
                        </div>
                      </>
                    ) : (
                      <p className="text-sm text-gray-500">Finansal bilgiler bu kullanıcı rolü için uygulanabilir değil</p>
                    )}
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base flex items-center gap-2">
                      <BarChart2 className="h-4 w-4" />
                      Performans
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {user?.role === "Reklamveren" ? (
                      <div className="space-y-3">
                        <div>
                          <p className="text-sm font-medium text-gray-500">Kampanya Sayısı</p>
                          <p>{user?.campaigns || 0}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-500">Toplam İzlenme</p>
                          <p>135.842</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-500">Ortalama CTR</p>
                          <p>2.8%</p>
                        </div>
                      </div>
                    ) : user?.role === "Yayıncı" ? (
                      <div className="space-y-3">
                        <div>
                          <p className="text-sm font-medium text-gray-500">Kampanya Sayısı</p>
                          <p>{user?.campaigns || 0}</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-500">Toplam Gösterim</p>
                          <p>284.521</p>
                        </div>
                        <div>
                          <p className="text-sm font-medium text-gray-500">Ortalama RPM</p>
                          <p>4.32 ₺</p>
                        </div>
                      </div>
                    ) : (
                      <p className="text-sm text-gray-500">Performans bilgileri bu kullanıcı rolü için uygulanabilir değil</p>
                    )}
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={onClose}>
            {mode === "delete" ? "İptal" : "Kapat"}
          </Button>
          {mode === "delete" && onDelete && (
            <Button
              variant="destructive"
              onClick={() => {
                onDelete(user!.id)
                onClose()
              }}
            >
              Sil
            </Button>
          )}
          {mode === "edit" && (
            <Button>Kaydet</Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
} 