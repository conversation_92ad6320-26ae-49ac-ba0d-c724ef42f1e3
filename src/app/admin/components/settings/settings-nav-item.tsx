import { Badge } from "@/components/ui/badge"

interface SettingsNavItemProps {
  icon: React.ReactNode
  label: string
  active: boolean
  badge?: string
  onClick: () => void
}

export function SettingsNavItem({
  icon,
  label,
  active,
  badge,
  onClick
}: SettingsNavItemProps) {
  return (
    <button
      onClick={onClick}
      className={`flex items-center justify-between w-full px-3 py-2 text-sm rounded-md transition-colors ${
        active
          ? "bg-blue-50 text-blue-700 font-medium"
          : "text-gray-700 hover:bg-gray-50"
      }`}
    >
      <div className="flex items-center gap-2">
        {icon}
        <span>{label}</span>
      </div>
      {badge && (
        <Badge variant="info" size="sm">{badge}</Badge>
      )}
    </button>
  )
} 