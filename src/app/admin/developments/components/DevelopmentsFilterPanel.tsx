import React from "react";
import { Select, SelectContent, SelectItem, SelectTrigger } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { FilterIcon } from "lucide-react";

interface DevelopmentsFilterPanelProps {
  dateRange: string;
  setDateRange: (v: string) => void;
  userType: string;
  setUserType: (v: string) => void;
  actionType: string;
  setActionType: (v: string) => void;
  search: string;
  setSearch: (v: string) => void;
}

export const DevelopmentsFilterPanel: React.FC<DevelopmentsFilterPanelProps> = ({
  dateRange,
  setDateRange,
  userType,
  setUserType,
  actionType,
  setActionType,
  search,
  setSearch,
}) => (
  <div className="mb-4 bg-muted/40 p-3 rounded-lg">
    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-3">
      {/* Date range select */}
      <Select value={dateRange} onValueChange={setDateRange}>
        <SelectTrigger className="w-full">
          {dateRange === "all" ? (
            <span className="text-muted-foreground italic">Date</span>
          ) : (
            <span>{
              dateRange === "bugun" ? "Bugün" : dateRange === "hafta" ? "Bu Hafta" : dateRange === "ay" ? "Bu Ay" : "Özel"
            }</span>
          )}
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="bugun">Bugün</SelectItem>
          <SelectItem value="hafta">Bu hafta</SelectItem>
          <SelectItem value="ay">Bu ay</SelectItem>
          <SelectItem value="ozel">Özel</SelectItem>
        </SelectContent>
      </Select>
      
      {/* User type select */}
      <Select value={userType} onValueChange={setUserType}>
        <SelectTrigger className="w-full">
          {userType === "all" ? (
            <span className="text-muted-foreground italic">Kullanıcı Tipi</span>
          ) : (
            <span>{userType}</span>
          )}
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">Tümü</SelectItem>
          <SelectItem value="system">User</SelectItem>
          <SelectItem value="publisher">Publisher</SelectItem>
          <SelectItem value="advertiser">Advertiser</SelectItem>
        </SelectContent>
      </Select>
      
      {/* Action type select */}
      <Select value={actionType} onValueChange={setActionType}>
        <SelectTrigger className="w-full">
          {actionType === "all" ? (
            <span className="text-muted-foreground italic">İşlem Tipi</span>
          ) : (
            <span>{actionType}</span>
          )}
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">Tümü</SelectItem>
          <SelectItem value="campaign">Kampanya</SelectItem>
          <SelectItem value="payment">Ödeme</SelectItem>
          <SelectItem value="user">Kullanıcı</SelectItem>
          <SelectItem value="system">Sistem</SelectItem>
        </SelectContent>
      </Select>
      
      {/* Search input */}
      <Input
        type="text"
        placeholder="Arama yap..."
        className="w-full"
        value={search}
        onChange={e => setSearch(e.target.value)}
      />
    </div>
    
    <div className="mt-3 flex justify-end">
      <Button variant="outline" className="w-full sm:w-auto flex items-center gap-2">
        <FilterIcon className="h-4 w-4" />
        <span>Filtrele</span>
      </Button>
    </div>
  </div>
) 