"use client"

import React from "react"
import { Card } from "@/components/ui/card"
import { <PERSON><PERSON><PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import Link from "next/link"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Bell, ChevronLeft, ChevronRight, Circle, User } from "lucide-react"
import { Toaster } from "sonner"
import { cn } from "@/lib/utils"
import { Badge } from "@/components/ui/badge"
import { ScrollArea } from "@/components/ui/scroll-area"
import { format, parseISO } from "date-fns"
import { tr } from "date-fns/locale"
import { Button } from "@/components/ui/button"

interface DevelopmentNotification {
  id: number
  type: string
  actionIcon: React.ReactNode
  title: string
  content: string
  date: string
  status: string
  publisher?: { name: string; avatar: string; id: number } | null
  advertiser?: { name: string; id: number } | null
  campaign?: { name: string; id: number } | null
  userType: string
  note?: string | null
}

interface DevelopmentsListProps {
  notifications: DevelopmentNotification[]
  onPageChange?: (page: number) => void
  totalPages?: number
  currentPage?: number
  pageSize?: number
  isLoading?: boolean
}

export const DevelopmentsList: React.FC<DevelopmentsListProps> = ({ 
  notifications,
  onPageChange = () => {},
  totalPages = 1,
  currentPage = 1,
  pageSize = 10,
  isLoading = false
}) => {
  // Reverse the current order
  const sortedNotifications = [...notifications].reverse()

  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= totalPages) {
      onPageChange(newPage)
    }
  }

  return (
    <div className="relative flex flex-col h-full">
      <Toaster position="top-right" expand={true} richColors />
      
      {/* Notifications List */}
      <ScrollArea className="flex-1 pr-4 h-[calc(100vh-16rem)]">
        <div className="space-y-2">
          {isLoading ? (
            // Loading state
            Array.from({ length: 3 }).map((_, idx) => (
              <Card key={idx} className="flex items-center gap-3 p-3 animate-pulse">
                <div className="w-8 h-8 rounded-full bg-muted" />
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-muted rounded w-1/4" />
                  <div className="h-3 bg-muted rounded w-3/4" />
                  <div className="h-3 bg-muted rounded w-1/2" />
                </div>
              </Card>
            ))
          ) : sortedNotifications.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-48 text-muted-foreground">
              <Bell className="w-8 h-8 mb-2 opacity-20" />
              <p className="text-sm">Henüz bildirim bulunmuyor.</p>
            </div>
          ) : (
            sortedNotifications.map((item) => (
              <NotificationCard key={item.id} item={item} />
            ))
          )}
        </div>
      </ScrollArea>

      {/* Pagination */}
      {!isLoading && sortedNotifications.length > 0 && (
        <div className="flex items-center justify-between border-t mt-4 pt-4 px-1">
          <div className="text-sm text-muted-foreground">
            Toplam <span className="font-medium text-foreground">{pageSize * (totalPages - 1) + sortedNotifications.length}</span> bildirim
          </div>
          <div className="flex items-center gap-1">
            <Button
              variant="outline"
              size="icon"
              className="h-8 w-8"
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <div className="flex items-center gap-1 px-2">
              {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (
                <Button
                  key={page}
                  variant={page === currentPage ? "default" : "ghost"}
                  size="icon"
                  className={cn(
                    "h-8 w-8",
                    page === currentPage && "pointer-events-none"
                  )}
                  onClick={() => handlePageChange(page)}
                >
                  {page}
                </Button>
              ))}
            </div>
            <Button
              variant="outline"
              size="icon"
              className="h-8 w-8"
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}

const NotificationCard: React.FC<{ item: DevelopmentNotification }> = ({ item }) => {
  const isUnread = item.status === "Okunmadı"
  
  return (
    <Card className={cn(
      "flex items-center gap-3 p-3 transition-all hover:shadow-sm cursor-pointer group",
      isUnread ? "bg-blue-50/30 dark:bg-blue-950/10 border-l-[3px] border-l-blue-500" : "hover:bg-slate-50/50 dark:hover:bg-slate-800/30"
    )}>
      {/* Status Indicator */}
      {isUnread && (
        <div className="w-1.5 h-1.5 rounded-full bg-blue-500 absolute -left-3" />
      )}

      {/* Avatar */}
      <Avatar className="w-8 h-8 border border-border/50">
        <AvatarImage src={item.publisher?.avatar} />
        <AvatarFallback>
          {item.userType === "System" ? (
            <Bell className="w-3.5 h-3.5" />
          ) : (
            <User className="w-3.5 h-3.5" />
          )}
        </AvatarFallback>
      </Avatar>

      {/* Content */}
      <div className="flex-1 min-w-0">
        <div className="flex items-center gap-2 mb-0.5">
          <span className="text-sm font-medium truncate">
            {item.publisher?.name || item.advertiser?.name || "System"}
          </span>
          <span className="text-xs text-muted-foreground">•</span>
          <span className="text-xs text-muted-foreground">
            {format(parseISO(item.date), "d MMM yyyy HH:mm", { locale: tr })}
          </span>
        </div>
        <p className="text-sm line-clamp-2 text-muted-foreground group-hover:text-foreground/90 transition-colors">
          {item.content}
        </p>
        
        {/* Tags */}
        <div className="flex flex-wrap items-center gap-1.5 mt-1.5">
          {item.campaign && (
            <Link href={`/admin/campaigns/${item.campaign.id}`}>
              <Badge variant="secondary" className="px-1.5 py-0 text-xs font-normal hover:bg-secondary/80">
                {item.campaign.name}
              </Badge>
            </Link>
          )}
          {item.advertiser && (
            <Link href={`/admin/advertisers/${item.advertiser.id}`}>
              <Badge variant="secondary" className="px-1.5 py-0 text-xs font-normal hover:bg-secondary/80">
                {item.advertiser.name}
              </Badge>
            </Link>
          )}
          {item.note && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger>
                  <Badge variant="outline" className="px-1.5 py-0 text-xs font-normal cursor-help border-dashed">
                    Not
                  </Badge>
                </TooltipTrigger>
                <TooltipContent>
                  <p className="text-xs max-w-xs">{item.note}</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
          <Badge 
            variant={isUnread ? "default" : "outline"} 
            className={cn(
              "ml-auto px-1.5 py-0 text-xs font-normal",
              isUnread ? "bg-blue-500" : "text-muted-foreground"
            )}
          >
            {isUnread ? "Yeni" : "Okundu"}
          </Badge>
        </div>
      </div>
    </Card>
  )
} 