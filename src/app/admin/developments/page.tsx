"use client"

import React, { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { CheckCircle, AlertCircle, Info, User, Users, Briefcase, Bell } from "lucide-react";
import { DashboardLayout } from "@/app/layout/dashboard-layout";
import { DashboardHeader } from "@/app/admin/components/dashboard/header";
import {TooltipProvider  } from "@/components/ui/tooltip";
import { isToday, isThisWeek, isThisMonth } from "date-fns";
import { DevelopmentsFilterPanel } from "./components/DevelopmentsFilterPanel";
import { DevelopmentsList } from "./components/DevelopmentsList";
import { fetchAdminNotificationList } from "@/shared/services/admin/admin-notification-list";

// DevelopmentNotification tipini import/oluştur
interface DevelopmentNotification {
  id: number;
  type: string;
  actionIcon: React.ReactNode;
  title: string;
  content: string;
  date: string;
  status: string;
  userType: string;
  process_type?: string;
}

function StatusBadge({ status }: { status: string }) {
  if (status === "Okundu") return <Badge variant="outline" className="border-green-500 text-green-600">Okundu</Badge>;
  return <Badge variant="default" className="bg-blue-500 text-white">Okunmadı</Badge>;
}

// Mock data - API'den gelecek
const mockNotifications = Array.from({ length: 45 }, (_, i) => ({
  id: i + 1,
  type: "campaign",
  actionIcon: null,
  title: `Bildirim ${i + 1}`,
  content: `Bu bir test bildirimidir. #${i + 1}`,
  date: new Date(Date.now() - Math.random() * 10000000000).toISOString(),
  status: Math.random() > 0.5 ? "Okunmadı" : "Okundu",
  publisher: Math.random() > 0.5 ? {
    name: "Test Yayıncı",
    avatar: "",
    id: 1
  } : null,
  advertiser: Math.random() > 0.5 ? {
    name: "Test Reklamveren",
    id: 1
  } : null,
  campaign: Math.random() > 0.5 ? {
    name: "Test Kampanya",
    id: 1
  } : null,
  userType: Math.random() > 0.3 ? "Yayıncı" : "System",
  note: Math.random() > 0.7 ? "Bu bir test notudur" : null
}));

export default function DevelopmentsPage() {
  const [currentPage, setCurrentPage] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [notifications, setNotifications] = useState<DevelopmentNotification[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const pageSize = 10;

  // Filter states
  const [dateRange, setDateRange] = useState("all");
  const [userType, setUserType] = useState("all");
  const [actionType, setActionType] = useState("all");
  const [search, setSearch] = useState("");

  const fetchNotifications = async (page: number) => {
    setIsLoading(true);
    try {
      const response = await fetchAdminNotificationList({
        skip: (page - 1) * pageSize,
        limit: pageSize,
        user_type: userType !== "all" ? userType : undefined,
        process_type: actionType !== "all" ? actionType : undefined,
        sort_by: 'created_at',
        sort_order: 'asc'
      });
      
          if (response.result?.data) {
      // Tarihe göre sırala ve sistem bildirimlerini öne al
      const sortedData = [...response.result.data].sort((a, b) => {
        // Sistem bildirimleri her zaman en üstte
        if (a.user_type === "System" && b.user_type !== "System") return -1;
        if (a.user_type !== "System" && b.user_type === "System") return 1;
        // Diğer bildirimler tarihe göre sıralanır
        return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
      });

      setNotifications(sortedData.map(n => ({
        id: parseInt(n.notification_id),
        type: n.type,
        actionIcon: getActionIcon(n.type),
        title: `${n.name} ${n.surname}`,
        content: n.process_type,
        date: new Date(n.created_at).toISOString(),
        status: n.isRead ? "Okundu" : "Okunmadı",
        userType: n.user_type,
        process_type: n.process_type
      })));
      setTotalCount(response.result.stats.total);
      }
    } catch (error) {
      console.error('Error fetching notifications:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Sayfa değişikliğinde API'yi çağır
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
    fetchNotifications(page);
  };

  // İlk yüklemede ve filtre değişikliklerinde API'yi çağır
  React.useEffect(() => {
    fetchNotifications(currentPage);
  }, [currentPage, userType, actionType]);

  return (
    <DashboardLayout>
      <DashboardHeader
        title="Gelişmeler"
        description="Sistemdeki tüm önemli olaylar, bildirimler ve notlar burada detaylı şekilde listelenir."
      />
      <TooltipProvider delayDuration={400}>
        <DevelopmentsFilterPanel
          dateRange={dateRange}
          setDateRange={setDateRange}
          userType={userType}
          setUserType={setUserType}
          actionType={actionType}
          setActionType={setActionType}
          search={search}
          setSearch={setSearch}
        />
        <DevelopmentsList
          notifications={notifications}
          onPageChange={handlePageChange}
          currentPage={currentPage}
          totalPages={Math.ceil(totalCount / pageSize)}
          pageSize={pageSize}
          isLoading={isLoading}
        />
      </TooltipProvider>
    </DashboardLayout>
  );
}

function getActionIcon(type: string) {
  switch (type) {
    case "publisher_register": return <Bell className="w-5 h-5 text-blue-500" />;
    case "payment_request": return <AlertCircle className="w-5 h-5 text-red-500" />;
    case "campaign_completed": return <CheckCircle className="w-5 h-5 text-green-500" />;
    case "info": return <Info className="w-5 h-5 text-yellow-500" />;
    case "campaign_start": return <Briefcase className="w-5 h-5 text-purple-500" />;
    default: return <Bell className="w-5 h-5 text-blue-500" />;
  }
}
