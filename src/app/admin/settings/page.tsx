"use client"

import { useState } from "react"
import { DashboardLayout } from "@/app/layout/dashboard-layout"
import { DashboardHeader } from "@/app/admin/components/dashboard/header"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { toast } from "@/components/ui/use-toast"

import { ProfileSettings } from "./components/profile-settings"
import { NotificationSettings } from "./components/notification-settings"
import { AppearanceSettings } from "./components/appearance-settings"
import { SecuritySettings } from "./components/security-settings"
import { AdminManagement } from "./components/admin-management"

export default function SettingsPage() {
  const [isLoading, setIsLoading] = useState(false)

  const onSubmit = async () => {
    setIsLoading(true)
    setTimeout(() => {
      setIsLoading(false)
      toast({
        title: "Ayarlar güncellendi",
        description: "Değişiklikleriniz başarıyla kaydedildi.",
      })
    }, 1000)
  }

  return (
    <DashboardLayout>
      <DashboardHeader
        title="Ayarlar"
        description="Hesap ayarlarınızı yönetin ve özelleştirin."
      />

      <Tabs defaultValue="general" className="space-y-4">
        <div className="relative w-full overflow-hidden">
          <TabsList className="max-w-full overflow-x-auto flex no-scrollbar pb-1">
            <TabsTrigger value="general">Genel</TabsTrigger>
            <TabsTrigger value="notifications">Bildirimler</TabsTrigger>
            <TabsTrigger value="appearance">Görünüm</TabsTrigger>
            <TabsTrigger value="security">Güvenlik</TabsTrigger>
            <TabsTrigger value="admin">Yönetici Rolleri</TabsTrigger>
          </TabsList>
        </div>

        <TabsContent value="general">
          <ProfileSettings isLoading={isLoading} onSubmit={onSubmit} />
        </TabsContent>

        <TabsContent value="notifications">
          <NotificationSettings isLoading={isLoading} onSubmit={onSubmit} />
        </TabsContent>

        <TabsContent value="appearance">
          <AppearanceSettings isLoading={isLoading} onSubmit={onSubmit} />
        </TabsContent>

        <TabsContent value="security">
          <SecuritySettings isLoading={isLoading} onSubmit={onSubmit} />
        </TabsContent>

        <TabsContent value="admin">
          <AdminManagement isLoading={isLoading} onSubmit={onSubmit} />
        </TabsContent>
      </Tabs>
    </DashboardLayout>
  )
} 