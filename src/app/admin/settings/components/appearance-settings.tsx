"use client"

import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, Card<PERSON><PERSON>le, CardDescription } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { Icons } from "@/components/ui/icons"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

interface AppearanceSettingsProps {
  isLoading: boolean;
  onSubmit: () => void;
}

export function AppearanceSettings({ isLoading, onSubmit }: AppearanceSettingsProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Görünüm Ayarları</CardTitle>
        <CardDescription>
          Arayüz tercihlerinizi özelleştirin
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <div className="space-y-2">
            <Label>Dil</Label>
            <Select defaultValue="tr">
              <SelectTrigger>
                <SelectValue placeholder="Dil seçin" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="tr">Türkçe</SelectItem>
                <SelectItem value="en">English</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <Separator />
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-base">Karanlık Mod</Label>
              <p className="text-sm text-muted-foreground">
                Karanlık tema kullanın
              </p>
            </div>
            <Switch />
          </div>
          <Separator />
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-base">Kompakt Görünüm</Label>
              <p className="text-sm text-muted-foreground">
                Daha sıkışık bir arayüz kullanın
              </p>
            </div>
            <Switch />
          </div>
        </div>
        <Button onClick={onSubmit} disabled={isLoading}>
          {isLoading && (
            <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
          )}
          Görünümü Kaydet
        </Button>
      </CardContent>
    </Card>
  )
} 