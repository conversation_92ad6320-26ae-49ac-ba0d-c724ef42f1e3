"use client"

import { <PERSON>, Card<PERSON>ontent, CardHeader, Card<PERSON><PERSON>le, CardDescription } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { Icons } from "@/components/ui/icons"

interface NotificationSettingsProps {
  isLoading: boolean;
  onSubmit: () => void;
}

export function NotificationSettings({ isLoading, onSubmit }: NotificationSettingsProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Bildirim Tercihleri</CardTitle>
        <CardDescription>
          Hangi bildirimler almak istediğinizi seçin
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-base"><PERSON>ail <PERSON>dir<PERSON>leri</Label>
              <p className="text-sm text-muted-foreground">
                Önemli güncellemeler için email bildirimleri alın
              </p>
            </div>
            <Switch defaultChecked />
          </div>
          <Separator />
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-base">Masaüstü Bildirimleri</Label>
              <p className="text-sm text-muted-foreground">
                Tarayıcı bildirimleri alın
              </p>
            </div>
            <Switch defaultChecked />
          </div>
          <Separator />
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label className="text-base">Pazarlama Bildirimleri</Label>
              <p className="text-sm text-muted-foreground">
                Yeni özellikler ve kampanyalar hakkında bilgi alın
              </p>
            </div>
            <Switch />
          </div>
        </div>
        <Button onClick={onSubmit} disabled={isLoading}>
          {isLoading && (
            <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
          )}
          Tercihleri Kaydet
        </Button>
      </CardContent>
    </Card>
  )
} 