"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON>eader, Card<PERSON>itle, CardDescription } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { Separator } from "@/components/ui/separator"
import { Icons } from "@/components/ui/icons"

interface SecuritySettingsProps {
  isLoading: boolean;
  onSubmit: () => void;
}

export function SecuritySettings({ isLoading, onSubmit }: SecuritySettingsProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Güvenlik Ayarları</CardTitle>
        <CardDescription>
          Hesap güvenlik ayarlarınızı yönetin
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="current-password">Mevcut Şifre</Label>
            <Input id="current-password" type="password" />
          </div>
          <div className="space-y-2">
            <Label htmlFor="new-password">Yeni Şifre</Label>
            <Input id="new-password" type="password" />
          </div>
          <div className="space-y-2">
            <Label htmlFor="confirm-password">Yeni Şifre (Tekrar)</Label>
            <Input id="confirm-password" type="password" />
          </div>
        </div>
        <Separator />
        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label className="text-base">İki Faktörlü Doğrulama</Label>
            <p className="text-sm text-muted-foreground">
              Hesabınızı daha güvenli hale getirin
            </p>
          </div>
          <Switch />
        </div>
        <Button onClick={onSubmit} disabled={isLoading}>
          {isLoading && (
            <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
          )}
          Güvenlik Ayarlarını Kaydet
        </Button>
      </CardContent>
    </Card>
  )
} 