"use client"

import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, Card<PERSON><PERSON>le, CardDescription } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { Icons } from "@/components/ui/icons"

interface ProfileSettingsProps {
  isLoading: boolean;
  onSubmit: () => void;
}

export function ProfileSettings({ isLoading, onSubmit }: ProfileSettingsProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Profil Bilgileri</CardTitle>
        <CardDescription>
          Kişisel bilgilerinizi güncelleyin
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex items-center gap-4">
          <div className="w-20 h-20 rounded-full bg-muted flex items-center justify-center">
            <Icons.user className="w-10 h-10 text-muted-foreground" />
          </div>
          <Button variant="outline">Fotoğraf <PERSON></Button>
        </div>
        <Separator />
        <div className="grid gap-4 md:grid-cols-2">
          <div className="space-y-2">
            <Label htmlFor="name">İsim</Label>
            <Input id="name" placeholder="İsminizi girin" defaultValue="Admin Kullanıcı" />
          </div>
          <div className="space-y-2">
            <Label htmlFor="email">Email</Label>
            <Input id="email" type="email" placeholder="Email adresinizi girin" defaultValue="<EMAIL>" />
          </div>
          <div className="space-y-2">
            <Label htmlFor="company">Şirket</Label>
            <Input id="company" placeholder="Şirket adını girin" />
          </div>
          <div className="space-y-2">
            <Label htmlFor="role">Rol</Label>
            <Input id="role" defaultValue="Yönetici" disabled />
          </div>
        </div>
        <Button onClick={onSubmit} disabled={isLoading}>
          {isLoading && (
            <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
          )}
          Değişiklikleri Kaydet
        </Button>
      </CardContent>
    </Card>
  )
} 