"use client"

import { useState } from "react"
import { Card, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Icons } from "@/components/ui/icons"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Checkbox } from "@/components/ui/checkbox"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"

// Örnek veri yapısı
const adminUsers = [
  {
    id: 1,
    name: "<PERSON><PERSON> Yılmaz",
    email: "<EMAIL>",
    role: "Super Admin",
    status: "Aktif",
    permissions: ["dashboard", "users", "campaigns", "settings", "billing", "reports"]
  },
  {
    id: 2,
    name: "<PERSON><PERSON><PERSON>mir",
    email: "<EMAIL>",
    role: "Admin",
    status: "Aktif",
    permissions: ["dashboard", "users", "campaigns"]
  },
  {
    id: 3,
    name: "Ayşe Kaya",
    email: "<EMAIL>",
    role: "Yönetici",
    status: "Pasif",
    permissions: ["dashboard", "reports"]
  }
]

const availablePermissions = [
  { id: "dashboard", label: "Dashboard Görüntüleme" },
  { id: "users", label: "Kullanıcı Yönetimi" },
  { id: "campaigns", label: "Kampanya Yönetimi" },
  { id: "settings", label: "Sistem Ayarları" },
  { id: "billing", label: "Fatura ve Ödemeler" },
  { id: "reports", label: "Raporlar" },
  { id: "analytics", label: "Analitikler" },
  { id: "content", label: "İçerik Yönetimi" }
]

interface AdminManagementProps {
  isLoading: boolean;
  onSubmit: () => void;
}

export function AdminManagement({ isLoading, onSubmit }: AdminManagementProps) {
  const [selectedPermissions, setSelectedPermissions] = useState<string[]>([])
  const [isAddUserDialogOpen, setIsAddUserDialogOpen] = useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false)
  const [selectedUser, setSelectedUser] = useState<typeof adminUsers[0] | null>(null)

  const handleAddUser = () => {
    onSubmit()
    setIsAddUserDialogOpen(false)
  }

  const handleEditUser = () => {
    onSubmit()
    setIsEditDialogOpen(false)
    setSelectedUser(null)
  }

  const openEditDialog = (user: typeof adminUsers[0]) => {
    setSelectedUser(user)
    setSelectedPermissions(user.permissions)
    setIsEditDialogOpen(true)
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>Yönetici Rolleri ve İzinler</CardTitle>
            <CardDescription>
              Sistem yöneticilerini ve erişim izinlerini yönetin
            </CardDescription>
          </div>
          <Dialog open={isAddUserDialogOpen} onOpenChange={setIsAddUserDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Icons.user className="mr-2 h-4 w-4" />
                Yeni Yönetici Ekle
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Yeni Yönetici Ekle</DialogTitle>
                <DialogDescription>
                  Yeni bir yönetici hesabı oluşturun ve izinlerini belirleyin.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4 py-4">
                <div className="space-y-2">
                  <Label>İsim Soyisim</Label>
                  <Input placeholder="İsim ve soyisim girin" />
                </div>
                <div className="space-y-2">
                  <Label>Email</Label>
                  <Input type="email" placeholder="Email adresi girin" />
                </div>
                <div className="space-y-2">
                  <Label>Rol</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Rol seçin" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="super-admin">Super Admin</SelectItem>
                      <SelectItem value="admin">Admin</SelectItem>
                      <SelectItem value="manager">Yönetici</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <Separator />
                <div className="space-y-2">
                  <Label>İzinler</Label>
                  <div className="grid grid-cols-2 gap-4">
                    {availablePermissions.map((permission) => (
                      <div key={permission.id} className="flex items-center space-x-2">
                        <Checkbox
                          id={permission.id}
                          checked={selectedPermissions.includes(permission.id)}
                          onCheckedChange={(checked) => {
                            if (checked) {
                              setSelectedPermissions([...selectedPermissions, permission.id])
                            } else {
                              setSelectedPermissions(
                                selectedPermissions.filter((id) => id !== permission.id)
                              )
                            }
                          }}
                        />
                        <label
                          htmlFor={permission.id}
                          className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                        >
                          {permission.label}
                        </label>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button variant="outline" onClick={() => setIsAddUserDialogOpen(false)}>
                  İptal
                </Button>
                <Button onClick={handleAddUser} disabled={isLoading}>
                  {isLoading && <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />}
                  Ekle
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>İsim</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>Rol</TableHead>
              <TableHead>Durum</TableHead>
              <TableHead>İzinler</TableHead>
              <TableHead className="text-right">İşlemler</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {adminUsers.map((user) => (
              <TableRow key={user.id}>
                <TableCell>{user.name}</TableCell>
                <TableCell>{user.email}</TableCell>
                <TableCell>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    user.role === 'Super Admin' 
                      ? 'bg-purple-100 text-purple-800'
                      : user.role === 'Admin'
                      ? 'bg-blue-100 text-blue-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {user.role}
                  </span>
                </TableCell>
                <TableCell>
                  <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                    user.status === 'Aktif' 
                      ? 'bg-green-100 text-green-800'
                      : 'bg-red-100 text-red-800'
                  }`}>
                    {user.status}
                  </span>
                </TableCell>
                <TableCell>
                  <div className="flex flex-wrap gap-1">
                    {user.permissions.map((permission) => (
                      <span
                        key={permission}
                        className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800"
                      >
                        {permission}
                      </span>
                    ))}
                  </div>
                </TableCell>
                <TableCell className="text-right">
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={() => openEditDialog(user)}
                  >
                    Düzenle
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Yönetici Düzenle</DialogTitle>
            <DialogDescription>
              Yönetici bilgilerini ve izinlerini düzenleyin.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label>İsim Soyisim</Label>
              <Input 
                placeholder="İsim ve soyisim girin" 
                defaultValue={selectedUser?.name}
              />
            </div>
            <div className="space-y-2">
              <Label>Email</Label>
              <Input 
                type="email" 
                placeholder="Email adresi girin" 
                defaultValue={selectedUser?.email}
              />
            </div>
            <div className="space-y-2">
              <Label>Rol</Label>
              <Select defaultValue={selectedUser?.role?.toLowerCase()}>
                <SelectTrigger>
                  <SelectValue placeholder="Rol seçin" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="super-admin">Super Admin</SelectItem>
                  <SelectItem value="admin">Admin</SelectItem>
                  <SelectItem value="manager">Yönetici</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>Durum</Label>
              <Select defaultValue={selectedUser?.status?.toLowerCase()}>
                <SelectTrigger>
                  <SelectValue placeholder="Durum seçin" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="aktif">Aktif</SelectItem>
                  <SelectItem value="pasif">Pasif</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <Separator />
            <div className="space-y-2">
              <Label>İzinler</Label>
              <div className="grid grid-cols-2 gap-4">
                {availablePermissions.map((permission) => (
                  <div key={permission.id} className="flex items-center space-x-2">
                    <Checkbox
                      id={`edit-${permission.id}`}
                      checked={selectedPermissions.includes(permission.id)}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          setSelectedPermissions([...selectedPermissions, permission.id])
                        } else {
                          setSelectedPermissions(
                            selectedPermissions.filter((id) => id !== permission.id)
                          )
                        }
                      }}
                    />
                    <label
                      htmlFor={`edit-${permission.id}`}
                      className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                    >
                      {permission.label}
                    </label>
                  </div>
                ))}
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => {
              setIsEditDialogOpen(false)
              setSelectedUser(null)
            }}>
              İptal
            </Button>
            <Button onClick={handleEditUser} disabled={isLoading}>
              {isLoading && <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />}
              Kaydet
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </Card>
  )
} 