"use client"

import { useState } from "react"
import { PaymentStats } from "./components/PaymentStats"
import { PaymentFilters } from "./components/PaymentFilters"
import { PaymentList } from "./components/PaymentList"
import { PaymentDetail } from "./components/PaymentDetail"
import { Payment as TypesPayment } from "./types"
import { Payment as ComponentPayment } from "./components/data"
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { AdvertiserPerformanceChart } from "./components/charts/LineChart"
import { AdvertiserPaymentTable } from "./components/charts/DataTable"
import { CalculatorIcon } from "lucide-react"
import { DashboardLayout } from "@/app/layout/dashboard-layout"
import { DashboardHeader } from "@/app/admin/components/dashboard/header"

export default function PaymentsPage() {
  const [detailOpen, setDetailOpen] = useState(false)
  const [selectedPayment, setSelectedPayment] = useState<TypesPayment | null>(null)

  const handleViewDetail = (payment: ComponentPayment) => {
    const convertedPayment: TypesPayment = {
      id: payment.id,
      userId: payment.userId,
      userName: payment.userName,
      userEmail: payment.userEmail,
      userImage: payment.userImage,
      amount: payment.amount,
      status: payment.status as "completed" | "pending" | "processing",
      date: payment.date,
      method: payment.method,
      invoice: payment.invoice,
      campaign: payment.campaign,
      platform: payment.platform,
      items: payment.items
    }
    setSelectedPayment(convertedPayment)
    setDetailOpen(true)
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-start">
          <DashboardHeader 
            title="Reklamveren Ödemeleri" 
            description="Tüm reklamverenlerden alınan ödemeleri yönetin ve takip edin"
          />
          <Badge className="text-blue-700 bg-blue-50 border-blue-200 flex items-center gap-1 py-1">
            <CalculatorIcon className="h-3.5 w-3.5" />
            <span>Muhasebe modülü</span>
          </Badge>
        </div>

        <div className="w-full">
          <Tabs defaultValue="overview" className="space-y-6">
            <div className="overflow-x-auto scrollbar-hide">
              <TabsList className="w-full sm:w-auto">
                <TabsTrigger value="overview">Genel Bakış</TabsTrigger>
                <TabsTrigger value="list">Ödeme Listesi</TabsTrigger>
                <TabsTrigger value="analysis">Reklamveren Analizi</TabsTrigger>
              </TabsList>
            </div>
            
            <TabsContent value="overview" className="space-y-6 min-w-0">
              <div className="w-full overflow-hidden">
                <PaymentStats />
              </div>
              <div className="w-full overflow-hidden">
                <AdvertiserPerformanceChart />
              </div>
            </TabsContent>
            
            <TabsContent value="list" className="space-y-6 min-w-0">
              <PaymentList onViewDetail={handleViewDetail} />
            </TabsContent>
            
            <TabsContent value="analysis" className="space-y-6 min-w-0">
              <div className="w-full overflow-hidden">
                <AdvertiserPaymentTable />
              </div>
            </TabsContent>
          </Tabs>

          <PaymentDetail 
            payment={selectedPayment || undefined} 
            open={detailOpen} 
            onOpenChange={setDetailOpen} 
          />
        </div>
      </div>
    </DashboardLayout>
  )
} 