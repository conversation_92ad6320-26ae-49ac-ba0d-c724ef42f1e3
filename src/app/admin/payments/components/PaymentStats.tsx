"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { getPaymentStatistics } from "@/shared/services/admin/payment-statistics"
import { ArrowUpRight, AlertTriangle } from "lucide-react"

export function PaymentStats() {
  const [stats, setStats] = useState<Awaited<ReturnType<typeof getPaymentStatistics>> | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const data = await getPaymentStatistics()
        setStats(data)
      } catch (error) {
        console.error("Ödeme istatistikleri alınamadı:", error)
      } finally {
        setLoading(false)
      }
    }

    fetchStats()
  }, [])

  if (loading) {
    return <div>Yükleniyor...</div>
  }

  if (!stats) {
    return <div>Veri bulunamadı</div>
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Toplam Ödeme</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-end gap-1">
            <div className="text-2xl font-bold">{stats.total_payments.amount.toLocaleString('tr-TR')} ₺</div>
            <span className="text-sm text-green-600 font-medium mb-1 flex items-center">
              <ArrowUpRight className="h-3 w-3 mr-1" />
              %{stats.total_payments.monthly_change_percentage} geçen aya göre
            </span>
          </div>
          <p className="text-xs text-muted-foreground">
            {stats.total_payments.count} ödeme • Son 30 günde {stats.total_payments.last_30_days_count} ödeme
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Bekleyen Ödeme</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-end gap-1">
            <div className="text-2xl font-bold text-amber-600">{stats.pending_payments.amount.toLocaleString('tr-TR')} ₺</div>
            <span className="text-sm text-amber-600 font-medium mb-1 flex items-center">
              <AlertTriangle className="h-3 w-3 mr-1" />
              {stats.pending_payments.count} işlem
            </span>
          </div>
          <p className="text-xs text-muted-foreground">
            En eski bekleyen: {stats.pending_payments.oldest_payment_days} gün
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Başarı Oranı</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-end gap-1">
            <div className="text-2xl font-bold">%{stats.success_rate.current_rate}</div>
            <span className="text-sm text-green-600 font-medium mb-1 flex items-center">
              <ArrowUpRight className="h-3 w-3 mr-1" />
              %{stats.success_rate.monthly_change_percentage} geçen aya göre
            </span>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Ortalama Ödeme</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-end gap-1">
            <div className="text-2xl font-bold">{stats.average_payment.amount.toLocaleString('tr-TR')} ₺</div>
            <span className="text-sm text-green-600 font-medium mb-1 flex items-center">
              <ArrowUpRight className="h-3 w-3 mr-1" />
              %{stats.average_payment.monthly_change_percentage} geçen aya göre
            </span>
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 