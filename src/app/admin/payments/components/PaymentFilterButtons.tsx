import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { CreditCard, Banknote, Wallet, ListFilter } from "lucide-react"

interface PaymentFilterButtonsProps {
  filterStatus: string | null
  setFilterStatus: (status: string | null) => void
}

export function PaymentFilterButtons({ filterStatus, setFilterStatus }: PaymentFilterButtonsProps) {
  const filters = [
    {
      value: null,
      label: "Tümü",
      icon: ListFilter
    },
    {
      value: "CREDIT_CARD",
      label: "<PERSON><PERSON><PERSON>",
      icon: CreditCard
    },
    {
      value: "BANK_TRANSFER",
      label: "Banka Kartı",
      icon: Banknote
    },
    {
      value: "PAYPAL",
      label: "PayPal",
      icon: Wallet
    }
  ]

  return (
    <div className="flex items-center gap-2">
      {filters.map((filter) => {
        const Icon = filter.icon
        return (
          <Button
            key={filter.value || "all"}
            variant={filterStatus === filter.value ? "default" : "outline"}
            size="sm"
            className={cn(
              "h-9 gap-1.5",
              filterStatus === filter.value && "bg-blue-600 hover:bg-blue-700"
            )}
            onClick={() => setFilterStatus(filter.value)}
          >
            <Icon className="h-3.5 w-3.5" />
            <span className="hidden sm:inline">{filter.label}</span>
          </Button>
        )
      })}
    </div>
  )
} 