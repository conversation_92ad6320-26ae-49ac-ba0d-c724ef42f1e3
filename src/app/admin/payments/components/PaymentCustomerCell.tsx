import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

interface PaymentCustomerCellProps {
  userName: string;
  userEmail: string;
  userImage?: string;
}

export function PaymentCustomerCell({ userName, userEmail, userImage }: PaymentCustomerCellProps) {
  return (
    <div className="flex items-center gap-3">
      <Avatar className="h-8 w-8">
        <AvatarImage src={userImage} alt={userName} />
        <AvatarFallback className="bg-slate-100 text-slate-600">
          {userName.slice(0, 2).toUpperCase()}
        </AvatarFallback>
      </Avatar>
      <div>
        <p className="text-sm font-medium text-gray-700">{userName}</p>
        <p className="text-xs text-gray-500">{userEmail}</p>
      </div>
    </div>
  );
} 