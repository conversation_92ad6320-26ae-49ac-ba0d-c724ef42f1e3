import { TableRow, TableCell } from "@/components/ui/table";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { FileText, Tag, BarChart, Download, Eye, MoreVertical, CreditCard, Banknote } from "lucide-react";
import { cn } from "@/lib/utils";
import { Payment } from "./data";
import { PaymentStatusBadge } from "./PaymentStatusBadge";
import { PaymentCustomerCell } from "./PaymentCustomerCell";
import { CheckCircle } from "lucide-react";
import { formatDate } from "@/lib/utils";
import { PaymentTableRowProps } from "../types";
import { formatCurrency } from "@/shared/utils/format-currency";

export function PaymentTableRow({
  payment,
  selected,
  onSelect,
  onViewDetail
}: PaymentTableRowProps) {
  const getInitials = (name: string, surname: string) => {
    return `${name.charAt(0)}${surname.charAt(0)}`;
  };

  const formattedDate = formatDate(payment.created_at);
  const [date, time] = formattedDate.split(' ');

  const getPaymentMethodIcon = (method: string) => {
    switch (method.toLowerCase()) {
      case 'kredi kartı':
        return <CreditCard className="h-3.5 w-3.5 text-blue-600" />;
      case 'banka kartı':
        return <CreditCard className="h-3.5 w-3.5 text-emerald-600" />;
      case 'paypal':
        return <Banknote className="h-3.5 w-3.5 text-purple-600" />;
      default:
        return <Banknote className="h-3.5 w-3.5 text-gray-600" />;
    }
  };

  return (
    <TableRow className={cn(
      "hover:bg-slate-50",
      selected && "bg-blue-50/40"
    )}>
      <TableCell>
        <Checkbox
          checked={selected}
          onCheckedChange={onSelect}
          aria-label={`Select payment ${payment.transaction_id}`}
        />
      </TableCell>
      <TableCell className="font-medium text-gray-700">{payment.transaction_id}</TableCell>
      <TableCell>
        <div className="flex items-center gap-2">
          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 text-blue-600">
            {getInitials(payment.name, payment.surname)}
          </div>
          <div>
            <div className="font-medium">{`${payment.name} ${payment.surname}`}</div>
            <div className="text-sm text-gray-500">{payment.email}</div>
          </div>
        </div>
      </TableCell>
      
      <TableCell>
        <div className="text-sm">
          {date}
          <div className="text-xs text-gray-500">{time}</div>
        </div>
      </TableCell>
      
      <TableCell className="text-right">
        <div className="font-medium">{formatCurrency(payment.amount)}</div>
      </TableCell>
      
      <TableCell>
        <div className="flex items-center gap-2">
          {getPaymentMethodIcon(payment.payment_method)}
          <span className="text-sm">{payment.payment_method}</span>
        </div>
      </TableCell>
      
      <TableCell>
        <PaymentStatusBadge status={payment.status} />
      </TableCell>
      
      <TableCell className="text-center">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onViewDetail(payment)}
          className="h-8 w-8 p-0"
        >
          <Eye className="h-4 w-4" />
        </Button>
      </TableCell>
    </TableRow>
  );
} 