import { Button } from "@/components/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Check, ChevronDown, CreditCard, Banknote } from "lucide-react";
import { cn } from "@/lib/utils";

interface PaymentFilterPopoverProps {
  filterStatus: string | null;
  setFilterStatus: (status: string | null) => void;
}

export function PaymentFilterPopover({ filterStatus, setFilterStatus }: PaymentFilterPopoverProps) {
  const paymentMethods = [
    { value: "CREDIT_CARD", label: "Kredi <PERSON>", icon: CreditCard },
    { value: "BANK_TRANSFER", label: "Banka Transferi", icon: Banknote },
    { value: "PAYPAL", label: "PayPal", icon: Banknote }
  ];

  const getSelectedMethod = () => {
    if (!filterStatus) return null;
    return paymentMethods.find(method => method.value === filterStatus);
  };

  const selectedMethod = getSelectedMethod();

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button variant="outline" size="sm" className="h-9 gap-1">
          {selectedMethod ? (
            <>
              <selectedMethod.icon className="h-3.5 w-3.5" />
              <span className="hidden sm:inline">{selectedMethod.label}</span>
            </>
          ) : (
            <span className="hidden sm:inline">Ödeme Yöntemi</span>
          )}
          <ChevronDown className="h-3.5 w-3.5" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[200px] p-0" align="start">
        <div className="flex flex-col">
          <Button
            variant="ghost"
            className={cn(
              "justify-start rounded-none px-4 py-2 text-sm font-normal",
              !filterStatus && "bg-accent"
            )}
            onClick={() => setFilterStatus(null)}
          >
            <Check
              className={cn(
                "mr-2 h-4 w-4",
                !filterStatus ? "opacity-100" : "opacity-0"
              )}
            />
            Tümü
          </Button>
          {paymentMethods.map((method) => (
            <Button
              key={method.value}
              variant="ghost"
              className={cn(
                "justify-start rounded-none px-4 py-2 text-sm font-normal",
                filterStatus === method.value && "bg-accent"
              )}
              onClick={() => setFilterStatus(method.value)}
            >
              <Check
                className={cn(
                  "mr-2 h-4 w-4",
                  filterStatus === method.value ? "opacity-100" : "opacity-0"
                )}
              />
              {method.label}
            </Button>
          ))}
        </div>
      </PopoverContent>
    </Popover>
  );
} 