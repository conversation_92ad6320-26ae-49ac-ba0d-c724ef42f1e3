import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";

interface PaymentStatusBadgeProps {
  status: string;
}

export function PaymentStatusBadge({ status }: PaymentStatusBadgeProps) {
  const getStatusConfig = (status: string) => {
    switch (status) {
      case "COMPLETED":
        return {
          label: "Tamamlandı",
          className: "bg-green-50 text-green-700 border-green-200"
        }
      case "PENDING":
        return {
          label: "Beklemede",
          className: "bg-yellow-50 text-yellow-700 border-yellow-200"
        }
      case "PROCESSING":
        return {
          label: "İşlemde",
          className: "bg-blue-50 text-blue-700 border-blue-200"
        }
      case "FAILED":
        return {
          label: "Başarısız",
          className: "bg-red-50 text-red-700 border-red-200"
        }
      default:
        return {
          label: status,
          className: "bg-gray-50 text-gray-700 border-gray-200"
        }
    }
  }

  const { label, className } = getStatusConfig(status)

  return (
    <Badge
      variant="outline"
      className={cn(
        "rounded-sm font-normal",
        className
      )}
    >
      {label}
    </Badge>
  )
} 