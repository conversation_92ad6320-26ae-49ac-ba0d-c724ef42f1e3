"use client"

import { useState } from "react"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Search, Plus, Download, Filter, CalendarIcon, BarChart3 } from "lucide-react"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"
import { cn } from "@/lib/utils"
import { format } from "date-fns"
import { tr } from "date-fns/locale"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

export function PaymentFilters() {
  const [date, setDate] = useState<Date>()
  const [view, setView] = useState("all")
  
  return (
    <div className="space-y-4">
      <Tabs defaultValue="all" className="w-full" onValueChange={setView}>
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div className="w-full overflow-x-auto scrollbar-hide">
            <div className="min-w-[500px] md:min-w-0">
              <TabsList className="bg-slate-100 w-full md:w-auto">
                <TabsTrigger value="all" className="data-[state=active]:bg-white">
                  Tüm Ödemeler
                </TabsTrigger>
                <TabsTrigger value="completed" className="data-[state=active]:bg-white">
                  Tamamlanan
                  <Badge className="ml-2 bg-green-100 text-green-700 hover:bg-green-100">24</Badge>
                </TabsTrigger>
                <TabsTrigger value="pending" className="data-[state=active]:bg-white">
                  Bekleyen
                  <Badge className="ml-2 bg-amber-100 text-amber-700 hover:bg-amber-100">5</Badge>
                </TabsTrigger>
                <TabsTrigger value="processing" className="data-[state=active]:bg-white">
                  İşlemde
                  <Badge className="ml-2 bg-blue-100 text-blue-700 hover:bg-blue-100">3</Badge>
                </TabsTrigger>
              </TabsList>
            </div>
          </div>
          
          <div className="flex items-center gap-2 shrink-0">
            <Button variant="outline" size="sm" className="h-8 gap-1 hidden md:flex">
              <BarChart3 className="h-3.5 w-3.5" />
              <span>Rapor Oluştur</span>
            </Button>
            <Button variant="outline" size="sm" className="h-8 gap-1">
              <Download className="h-3.5 w-3.5" />
              <span className="hidden sm:inline">Dışa Aktar</span>
            </Button>
          </div>
        </div>
      </Tabs>
      
      <div className="bg-white p-4 rounded-lg shadow-sm border">
        <div className="flex flex-col gap-4">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            <div>
              <label className="text-sm font-medium text-gray-700 mb-1 block">Arama</label>
              <div className="relative w-full">
                <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input placeholder="Müşteri adı veya ID..." className="pl-9" />
              </div>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-700 mb-1 block">Ödeme Yöntemi</label>
              <Select>
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Tüm yöntemler" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tüm yöntemler</SelectItem>
                  <SelectItem value="credit-card">Kredi Kartı</SelectItem>
                  <SelectItem value="bank-transfer">Banka Transferi</SelectItem>
                  <SelectItem value="paypal">PayPal</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div>
              <label className="text-sm font-medium text-gray-700 mb-1 block">Tarih</label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant={"outline"}
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !date && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {date ? format(date, "PPP", { locale: tr }) : "Tarih seçin"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={date}
                    onSelect={setDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>
          
          <div className="flex justify-end">
            <Button className="gap-1 w-full sm:w-auto">
              <Plus className="h-4 w-4" />
              <span>Yeni Ödeme</span>
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
} 