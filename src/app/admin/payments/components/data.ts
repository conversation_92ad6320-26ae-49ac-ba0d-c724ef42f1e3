// Payment tipi ö<PERSON>i (gerekirse güncelle)
export interface Payment {
  id: number;
  userId: string;
  userName: string;
  userEmail: string;
  userImage: string;
  amount: number;
  status: string;
  date: string;
  method: string;
  invoice: string;
  campaign: {
    id: string;
    name: string;
    type: string;
  };
  platform: string;
  items: { description: string; amount: number }[];
}

export const mockPayments: Payment[] = [
  {
    id: 1,
    userId: "ADV-001",
    userName: "Ahmet Yılmaz",
    userEmail: "<EMAIL>",
    userImage: "",
    amount: 1500,
    status: "completed",
    date: "2024-03-15",
    method: "<PERSON><PERSON><PERSON>",
    invoice: "INV-2024-001",
    campaign: {
      id: "CAM-105",
      name: "<PERSON><PERSON>",
      type: "Seasonal"
    },
    platform: "Web",
    items: [
      { description: "Banner Reklamı", amount: 900 },
      { description: "Video Reklamı", amount: 600 }
    ]
  },
  {
    id: 2,
    userId: "ADV-002",
    userName: "<PERSON><PERSON><PERSON><PERSON>",
    userEmail: "<EMAIL>",
    userImage: "",
    amount: 2800,
    status: "pending",
    date: "2024-03-14",
    method: "Banka Transferi",
    invoice: "INV-2024-002",
    campaign: {
      id: "CAM-106",
      name: "Yeni Ürün Lansmanı",
      type: "Launch"
    },
    platform: "Mobile",
    items: [
      { description: "Native Reklam", amount: 1800 },
      { description: "Sosyal Medya Desteği", amount: 1000 }
    ]
  },
  {
    id: 3,
    userId: "ADV-003",
    userName: "Mehmet Kaya",
    userEmail: "<EMAIL>",
    userImage: "",
    amount: 3200,
    status: "processing",
    date: "2024-03-13",
    method: "PayPal",
    invoice: "INV-2024-003",
    campaign: {
      id: "CAM-107",
      name: "Özel Gün Kampanyası",
      type: "Seasonal"
    },
    platform: "Multi-Platform",
    items: [
      { description: "Premium Paket", amount: 3200 }
    ]
  },
  {
    id: 4,
    userId: "ADV-004",
    userName: "Zeynep Öztürk",
    userEmail: "<EMAIL>",
    userImage: "",
    amount: 4200,
    status: "completed",
    date: "2024-03-12",
    method: "Kredi Kartı",
    invoice: "INV-2024-004",
    campaign: {
      id: "CAM-108",
      name: "Marka Bilinirliği",
      type: "Awareness"
    },
    platform: "Web + Mobile",
    items: [
      { description: "Video Kampanyası", amount: 2500 },
      { description: "Display Reklamlar", amount: 1700 }
    ]
  },
  {
    id: 5,
    userId: "ADV-005",
    userName: "Emre Can",
    userEmail: "<EMAIL>",
    userImage: "",
    amount: 1950,
    status: "pending",
    date: "2024-03-11",
    method: "Banka Transferi",
    invoice: "INV-2024-005",
    campaign: {
      id: "CAM-109",
      name: "İndirim Kampanyası",
      type: "Promotional"
    },
    platform: "Web",
    items: [
      { description: "Banner Reklamları", amount: 1200 },
      { description: "Newsletter Tanıtımı", amount: 750 }
    ]
  },
  {
    id: 6,
    userId: "ADV-006",
    userName: "Deniz Yıldız",
    userEmail: "<EMAIL>",
    userImage: "",
    amount: 5500,
    status: "completed",
    date: "2024-03-10",
    method: "Kredi Kartı",
    invoice: "INV-2024-006",
    campaign: {
      id: "CAM-110",
      name: "Büyük Tanıtım Kampanyası",
      type: "Branding"
    },
    platform: "Multi-Platform",
    items: [
      { description: "Premium Tanıtım Paketi", amount: 3500 },
      { description: "Sosyal Medya Kampanyası", amount: 2000 }
    ]
  }
]; 