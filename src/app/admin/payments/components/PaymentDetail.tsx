"use client"

import { useEffect, useState } from "react"
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON><PERSON>nt,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Payment } from "../types"
import { API_CONFIG } from "@/shared/services/api-config"
import { authService } from "@/shared/services/auth-service"
import { formatCurrency } from "@/shared/utils/format-currency"
import { formatDate } from "@/lib/utils"
import { PaymentStatusBadge } from "./PaymentStatusBadge"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Separator } from "@/components/ui/separator"
import { Card, CardContent } from "@/components/ui/card"
import { Building2, Mail, User, Briefcase, Tag, Shield } from "lucide-react"

interface UserDetail {
  user_id: string
  email: string
  status: boolean
  name: string
  surname: string
  company_name: string
  sector: string
  role: string
}

interface PaymentDetailProps {
  payment: Payment | null
  onClose: () => void
}

export function PaymentDetail({ payment, onClose }: PaymentDetailProps) {
  const [userDetail, setUserDetail] = useState<UserDetail | null>(null)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    if (payment) {
      fetchUserDetail(payment.user_id)
    }
  }, [payment])

  const fetchUserDetail = async (userId: string) => {
    try {
      setLoading(true)
      const token = authService.getAuthToken()
      if (!token) {
        console.error('No auth token found')
        return
      }

      const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.ADMIN.USER_DETAIL}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token
        },
        body: JSON.stringify({ user_id: userId })
      })

      const data = await response.json()
      if (data.status && data.result) {
        setUserDetail(data.result)
      }
    } catch (error) {
      console.error('Error fetching user detail:', error)
    } finally {
      setLoading(false)
    }
  }

  if (!payment) return null

  const getInitials = (name: string, surname: string) => {
    return `${name.charAt(0)}${surname.charAt(0)}`
  }

  return (
    <Dialog open={!!payment} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[800px]">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">Ödeme Detayı</DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Sol Taraf - Müşteri Profili */}
          <div className="md:col-span-1">
            <Card className="border-none shadow-none">
              <CardContent className="p-6">
                {loading ? (
                  <div className="text-center py-8">Yükleniyor...</div>
                ) : userDetail ? (
                  <div className="flex flex-col items-center">
                    <Avatar className="h-24 w-24 mb-4">
                      <AvatarFallback className="bg-blue-100 text-blue-600 text-2xl">
                        {getInitials(userDetail.name, userDetail.surname)}
                      </AvatarFallback>
                    </Avatar>
                    
                    <h3 className="text-lg font-semibold text-center mb-1">
                      {`${userDetail.name} ${userDetail.surname}`}
                    </h3>
                    
                    <p className="text-sm text-gray-500 text-center mb-4">
                      {userDetail.role === 'advertiser' ? 'Reklamveren' : 'Yayıncı'}
                    </p>

                    <div className="w-full space-y-4">
                      <div className="flex items-center gap-3 text-sm">
                        <Mail className="h-4 w-4 text-gray-400" />
                        <span className="text-gray-600">{userDetail.email}</span>
                      </div>
                      
                      <div className="flex items-center gap-3 text-sm">
                        <Building2 className="h-4 w-4 text-gray-400" />
                        <span className="text-gray-600">{userDetail.company_name}</span>
                      </div>
                      
                      <div className="flex items-center gap-3 text-sm">
                        <Briefcase className="h-4 w-4 text-gray-400" />
                        <span className="text-gray-600">{userDetail.sector}</span>
                      </div>
                      
                      <div className="flex items-center gap-3 text-sm">
                        <Shield className="h-4 w-4 text-gray-400" />
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          userDetail.status ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                        }`}>
                          {userDetail.status ? 'Aktif Hesap' : 'Pasif Hesap'}
                        </span>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8 text-red-500">
                    Müşteri bilgileri alınamadı
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Sağ Taraf - Ödeme Detayları */}
          <div className="md:col-span-2">
            <Card>
              <CardContent className="p-6">
                <div className="space-y-6">
                  <div>
                    <h3 className="text-lg font-semibold mb-4">Ödeme Bilgileri</h3>
                    <div className="grid grid-cols-2 gap-6">
                      <div>
                        <p className="text-sm text-gray-500 mb-1">İşlem ID</p>
                        <p className="font-medium text-gray-900">{payment.transaction_id}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500 mb-1">Tarih</p>
                        <p className="font-medium text-gray-900">{formatDate(payment.created_at)}</p>
                      </div>
                    </div>
                  </div>

                  <Separator />

                  <div>
                    <h3 className="text-lg font-semibold mb-4">Ödeme Detayları</h3>
                    <div className="grid grid-cols-2 gap-6">
                      <div>
                        <p className="text-sm text-gray-500 mb-1">Tutar</p>
                        <p className="text-2xl font-bold text-gray-900">{formatCurrency(payment.amount)}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500 mb-1">Ödeme Yöntemi</p>
                        <div className="flex items-center gap-2">
                          <Tag className="h-4 w-4 text-gray-400" />
                          <p className="font-medium text-gray-900">{payment.payment_method}</p>
                        </div>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500 mb-1">Durum</p>
                        <PaymentStatusBadge status={payment.status} />
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
} 