"use client"

import { useState, useEffect, use<PERSON>allback } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { 
  Search,
  ChevronDown,
  Calendar,
  ArrowDownUp,
  FileBarChart
} from "lucide-react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

import { Checkbox } from "@/components/ui/checkbox"
import { Input } from "@/components/ui/input"
import { PaymentTableRow } from "./PaymentTableRow"
import { PaymentStatusBadge } from "./PaymentStatusBadge"
import { PaymentFilterButtons } from "./PaymentFilterButtons"
import { API_CONFIG } from "@/shared/services/api-config"
import { PaymentListProps, Payment } from "../types"
import { authService } from "@/shared/services/auth-service"
import { PaymentDetail } from "./PaymentDetail"

interface PaymentListRequest {
  limit: number
  skip: number
  search?: string
  payment_method?: string
}

export function PaymentList({ onViewDetail }: PaymentListProps) {
  const [selectedPayments, setSelectedPayments] = useState<string[]>([])
  const [searchQuery, setSearchQuery] = useState<string>("")
  const [filterStatus, setFilterStatus] = useState<string | null>(null)
  const [payments, setPayments] = useState<Payment[]>([])
  const [selectedPayment, setSelectedPayment] = useState<Payment | null>(null)
  
  const fetchPayments = useCallback(async () => {
    try {
      const token = authService.getAuthToken()
      if (!token) {
        console.error('No auth token found')
        return
      }

      // Request body'yi oluştur
      const requestBody: PaymentListRequest = {
        limit: 0,
        skip: 0
      }

      // Sadece değer varsa ekle
      if (searchQuery.trim()) {
        requestBody.search = searchQuery.trim()
      }

      if (filterStatus) {
        requestBody.payment_method = filterStatus
      }

      const response = await fetch(`${API_CONFIG.BASE_URL}${API_CONFIG.ENDPOINTS.ADMIN.PAYMENT_LIST}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token
        },
        body: JSON.stringify(requestBody)
      })
      
      const data = await response.json()
      if (data.status && data.result?.data) {
        setPayments(data.result.data)
      }
    } catch (error) {
      console.error('Error fetching payments:', error)
    }
  }, [searchQuery, filterStatus])
  
  useEffect(() => {
    fetchPayments()
  }, [fetchPayments])
  
  const toggleSelectAll = () => {
    if (selectedPayments.length === payments.length) {
      setSelectedPayments([])
    } else {
      setSelectedPayments(payments.map(p => p.transaction_id))
    }
  }
  
  const toggleSelectPayment = (id: string) => {
    if (selectedPayments.includes(id)) {
      setSelectedPayments(selectedPayments.filter(p => p !== id))
    } else {
      setSelectedPayments([...selectedPayments, id])
    }
  }

  const handleViewDetail = (payment: Payment) => {
    setSelectedPayment(payment)
  }

  return (
    <Card className="border-none shadow-md">
      <CardHeader className="pb-0">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <CardTitle className="text-xl font-semibold text-gray-800 flex items-center gap-2">
            <FileBarChart className="h-5 w-5 text-blue-600" />
            Ödeme Listesi
          </CardTitle>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-4">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div className="relative w-full sm:w-auto">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
              <Input
                placeholder="Ara: Reklamveren, kampanya..."
                className="pl-9 h-9 w-full sm:w-[300px]"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            
            <div className="flex flex-wrap items-center gap-2 w-full sm:w-auto">
              <PaymentFilterButtons filterStatus={filterStatus} setFilterStatus={setFilterStatus} />
            </div>
          </div>
          
          <div className="relative w-full overflow-x-auto">
            <div className="min-w-[768px] lg:w-full">
              <Table>
                <TableHeader className="bg-slate-50">
                  <TableRow>
                    <TableHead className="w-[50px]">
                      <Checkbox
                        checked={selectedPayments.length === payments.length && payments.length > 0}
                        onCheckedChange={toggleSelectAll}
                        aria-label="Select all"
                      />
                    </TableHead>
                    <TableHead className="w-[100px]">ID</TableHead>
                    <TableHead>Müşteri</TableHead>
                    <TableHead>Tarih</TableHead>
                    <TableHead className="text-center">Tutar</TableHead>
                    <TableHead>Ödeme Yöntemi</TableHead>
                    <TableHead>Durum</TableHead>
                    <TableHead className="text-center">Detay</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {payments.map((payment) => (
                    <PaymentTableRow
                      key={payment.transaction_id}
                      payment={payment}
                      selected={selectedPayments.includes(payment.transaction_id)}
                      onSelect={() => toggleSelectPayment(payment.transaction_id)}
                      onViewDetail={handleViewDetail}
                    />
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
          
          <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
            <div className="text-sm text-slate-500">
              Toplam {payments.length} ödeme kaydı
            </div>
            
            <div className="flex items-center gap-2 w-full sm:w-auto">
              <Button variant="outline" size="sm" className="h-8 sm:w-auto w-1/2">
                <ChevronDown className="h-4 w-4 mr-1" />
                <span className="whitespace-nowrap">Diğer İşlemler</span>
              </Button>
              <Button size="sm" className="h-8 sm:w-auto w-1/2">
                <span className="whitespace-nowrap">Seçili ({selectedPayments.length})</span>
              </Button>
            </div>
          </div>
        </div>
      </CardContent>

      <PaymentDetail 
        payment={selectedPayment} 
        onClose={() => setSelectedPayment(null)} 
      />
    </Card>
  )
} 