"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { getMonthlyPerformance } from "@/shared/services/admin/monthly-performance"
import { formatCurrency } from "@/lib/utils"
import { LineChart, AreaChart } from "@tremor/react"

interface AdvertiserPerformanceChartProps {
  title?: string
  description?: string
  className?: string
}

const metricOptions = [
  { value: "earnings", label: "Harcama" },
  { value: "views", label: "<PERSON><PERSON><PERSON><PERSON>" },
  { value: "clicks", label: "Tıklama" }
]

export function AdvertiserPerformanceChart({
  title = "Reklamveren Performans Trendi",
  description = "Son 7 ay için reklamveren performans analizi",
  className = ""
}: AdvertiserPerformanceChartProps) {
  const [selectedMetric, setSelectedMetric] = useState("earnings")
  const [selectedView, setSelectedView] = useState("line")
  const [monthlyData, setMonthlyData] = useState<Awaited<ReturnType<typeof getMonthlyPerformance>> | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchMonthlyData = async () => {
      try {
        const data = await getMonthlyPerformance()
        setMonthlyData(data)
      } catch (error) {
        console.error("Aylık performans verileri alınamadı:", error)
      } finally {
        setLoading(false)
      }
    }
    fetchMonthlyData()
  }, [])

  if (loading) {
    return <div>Yükleniyor...</div>
  }
  if (!monthlyData) {
    return <div>Veri bulunamadı</div>
  }

  // Tüm reklamveren isimlerini topla (sıralama için)
  const allAdvertisers = new Set<string>()
  monthlyData.result.forEach(month => {
    month.advertisers.forEach(adv => {
      allAdvertisers.add(`${adv.name} ${adv.surname}`)
    })
  })
  const advertiserNames = Array.from(allAdvertisers)

  // Grafik verisini hazırla
  const chartData = monthlyData.result.map(monthData => {
    const chartRow: Record<string, string | number> = { month: monthData.month }
    monthData.advertisers.forEach(adv => {
      chartRow[`${adv.name} ${adv.surname}`] = adv[selectedMetric as keyof typeof adv] || 0
    })
    // Eksik kullanıcılar için 0 ata
    advertiserNames.forEach(name => {
      if (!(name in chartRow)) chartRow[name] = 0
    })
    return chartRow
  })

  // Değer formatlayıcı
  const formatValue = (value: number) => {
    if (selectedMetric === "earnings") {
      return formatCurrency(value)
    }
    return value.toLocaleString("tr-TR")
  }

  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <CardTitle>{title}</CardTitle>
            <p className="text-sm text-muted-foreground">{description}</p>
          </div>
          <div className="flex gap-2 items-center">
            <Select value={selectedMetric} onValueChange={setSelectedMetric}>
              <SelectTrigger className="w-[140px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {metricOptions.map(option => (
                  <SelectItem key={option.value} value={option.value}>{option.label}</SelectItem>
                ))}
              </SelectContent>
            </Select>
            <div className="flex border rounded-md overflow-hidden">
              <button
                className={`px-3 py-1 text-sm font-medium ${selectedView === "line" ? "bg-blue-50 text-blue-700" : "bg-white text-gray-700"}`}
                onClick={() => setSelectedView("line")}
                type="button"
              >
                <span className="inline-block align-middle">📈</span> Çizgi
              </button>
              <button
                className={`px-3 py-1 text-sm font-medium ${selectedView === "area" ? "bg-blue-50 text-blue-700" : "bg-white text-gray-700"}`}
                onClick={() => setSelectedView("area")}
                type="button"
              >
                <span className="inline-block align-middle">📊</span> Alan
              </button>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="mt-4">
          {selectedView === "line" ? (
            <LineChart
              className="h-72 mt-4"
              data={chartData}
              index="month"
              categories={advertiserNames}
              colors={["indigo", "rose", "cyan", "amber", "slate"]}
              yAxisWidth={60}
              valueFormatter={formatValue}
              showLegend={true}
              showGridLines={false}
              showAnimation={true}
            />
          ) : (
            <AreaChart
              className="h-72 mt-4"
              data={chartData}
              index="month"
              categories={advertiserNames}
              colors={["indigo", "rose", "cyan", "amber", "slate"]}
              yAxisWidth={60}
              valueFormatter={formatValue}
              showLegend={true}
              showGridLines={false}
              showAnimation={true}
            />
          )}
        </div>
      </CardContent>
    </Card>
  )
} 