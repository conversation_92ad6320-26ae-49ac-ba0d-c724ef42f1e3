"use client"

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from "@/components/ui/card"
import { DonutChart, Legend, List, ListItem, Text, Title, Bold, Flex } from "@tremor/react"
import { useState } from "react"
import { cn } from "@/lib/utils"
import { CreditCard, ArrowDown, ArrowUp, Info, DollarSign, ChevronsUpDown } from "lucide-react"

interface PaymentMethodData {
  yontem: string
  tutar: number
  islemSayisi: number
}

// Örnek reklamveren ödeme yöntemleri verisi
const defaultData: PaymentMethodData[] = [
  {
    yontem: "Kredi Kartı",
    tutar: 157000,
    islemSayisi: 48,
  },
  {
    yontem: "Banka Transferi",
    tutar: 98500,
    islemSayisi: 32,
  },
  {
    yontem: "PayPal",
    tutar: 41200,
    islemSayisi: 14,
  },
  {
    yontem: "<PERSON>ğ<PERSON>",
    tutar: 18600,
    islemSayisi: 6,
  },
]

interface PaymentMethodChartProps {
  title?: string
  description?: string
  data?: PaymentMethodData[]
  className?: string
}

export function PaymentMethodDistribution({
  title = "Ödeme Yöntemleri Dağılımı",
  description = "Reklamveren ödeme yöntemleri dağılımı",
  data = defaultData,
  className = "",
}: PaymentMethodChartProps) {
  const [selectedSegment, setSelectedSegment] = useState<string | null>(null)
  
  // Toplam tutar
  const totalAmount = data.reduce((sum, item) => sum + item.tutar, 0)
  
  // Toplam işlem sayısı
  const totalTransactions = data.reduce((sum, item) => sum + item.islemSayisi, 0)

  // Para birimini formatlama
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 0
    }).format(value)
  }
  
  // Yüzde hesaplama
  const calculatePercentage = (value: number) => {
    return ((value / totalAmount) * 100).toFixed(1)
  }
  
  // En çok kullanılan ödeme yöntemi
  const primaryMethod = data.reduce((prev, current) => 
    (prev.tutar > current.tutar) ? prev : current
  )
  
  // Grafik renkleri
  const chartColors = [
    "emerald-500", 
    "blue-600", 
    "purple-600", 
    "amber-500", 
    "rose-600", 
    "cyan-500"
  ]
  
  return (
    <Card className={className}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div>
          <CardTitle>{title}</CardTitle>
          <CardDescription>{description}</CardDescription>
        </div>
        <CreditCard className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        {/* DonutChart görünümü */}
        <Flex className="mt-4">
          <div className="w-full">
            <DonutChart
              data={data}
              index="yontem"
              category="tutar"
              variant="pie"
              colors={chartColors}
              valueFormatter={formatCurrency}
              showLabel={false}
              showAnimation={true}
              className="h-48"
              onValueChange={(segment) => 
                setSelectedSegment(segment ? segment.yontem : null)
              }
            />
            
            <div className="mt-4">
              <Legend
                categories={data.map((item) => item.yontem)}
                colors={chartColors}
                className="justify-center"
              />
            </div>
          </div>
        </Flex>
        
        {/* Detaylı istatistikler */}
        <div className="border-t mt-4 pt-4 space-y-3">
          <Text className="font-medium">Ödeme Yöntemleri Detayı</Text>
          {data.map((item, idx) => (
            <div
              key={item.yontem}
              className={cn(
                "flex items-center justify-between px-2 py-1 rounded-md",
                selectedSegment === item.yontem ? "bg-slate-50" : ""
              )}
            >
              <div className="flex items-center gap-2">
                <div 
                  className={`w-3 h-3 rounded-full bg-${chartColors[idx % chartColors.length].split('-')[0]}-${chartColors[idx % chartColors.length].split('-')[1]}`}
                />
                <span className="text-sm font-medium">{item.yontem}</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-500">
                  {formatCurrency(item.tutar)}
                </span>
                <span className="text-xs text-gray-400 bg-gray-100 rounded px-1">
                  %{calculatePercentage(item.tutar)}
                </span>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
      <CardFooter className="border-t pt-4">
        <div className="grid grid-cols-2 gap-4 w-full">
          <div>
            <p className="text-sm text-gray-500">Toplam İşlem</p>
            <p className="text-lg font-medium">{totalTransactions}</p>
          </div>
          <div>
            <p className="text-sm text-gray-500">En Sık Kullanılan</p>
            <p className="text-lg font-medium">{primaryMethod.yontem}</p>
          </div>
        </div>
      </CardFooter>
    </Card>
  )
} 