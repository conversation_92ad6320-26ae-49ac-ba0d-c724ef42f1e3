"use client"

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardHeader, CardTitle } from "@/components/ui/card"
import { Donut<PERSON>hart, Legend, Text, Flex, ProgressBar, Bold } from "@tremor/react"
import { useState } from "react"
import { cn } from "@/lib/utils"
import { FileCheck, FileWarning, Calendar, Clock, CheckCircle } from "lucide-react"

interface InvoiceStatusData {
  durum: string
  tutar: number
  islemSayisi: number
}

// Örnek fatura durum verisi
const defaultData: InvoiceStatusData[] = [
  {
    durum: "Kesilmiş",
    tutar: 245000,
    islemSayisi: 68,
  },
  {
    durum: "Beklemede",
    tutar: 82500,
    islemSayisi: 24,
  },
  {
    durum: "İptal Edilmiş",
    tutar: 32400,
    islemSayisi: 8,
  }
]

interface InvoiceChartProps {
  title?: string
  description?: string
  data?: InvoiceStatusData[]
  className?: string
}

export function InvoiceStatusChart({
  title = "Fatura Durumu",
  description = "<PERSON><PERSON> kesim durumlarına gö<PERSON> dağılım",
  data = defaultData,
  className = "",
}: InvoiceChartProps) {
  const [selectedSegment, setSelectedSegment] = useState<string | null>(null)
  
  // Toplam tutar
  const totalAmount = data.reduce((sum, item) => sum + item.tutar, 0)
  
  // Toplam işlem sayısı
  const totalTransactions = data.reduce((sum, item) => sum + item.islemSayisi, 0)

  // Para birimini formatlama
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 0
    }).format(value)
  }
  
  // Yüzde hesaplama
  const calculatePercentage = (value: number) => {
    return ((value / totalAmount) * 100).toFixed(1)
  }
  
  // Tamamlanma oranı
  const completionRate = ((data[0]?.islemSayisi || 0) / totalTransactions) * 100
  
  // İlerleme çubuğu değerleri
  const progressBarData = [
    { 
      name: "Kesilmiş Faturalar", 
      value: Number(calculatePercentage(data[0]?.tutar || 0)) 
    },
    { 
      name: "Bekleyen Faturalar", 
      value: Number(calculatePercentage(data[1]?.tutar || 0)) 
    }
  ]
  
  // Grafik renkleri
  const chartColors = [
    "green-500", 
    "amber-500", 
    "rose-500"
  ]
  
  // Durum ikonları
  const getStatusIcon = (status: string) => {
    switch(status) {
      case "Kesilmiş":
        return <FileCheck className="h-4 w-4 text-green-500" />
      case "Beklemede":
        return <Clock className="h-4 w-4 text-amber-500" />
      case "İptal Edilmiş":
        return <FileWarning className="h-4 w-4 text-rose-500" />
      default:
        return <FileCheck className="h-4 w-4" />
    }
  }
  
  return (
    <Card className={className}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div>
          <CardTitle>{title}</CardTitle>
          <CardDescription>{description}</CardDescription>
        </div>
        <Calendar className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        {/* DonutChart görünümü */}
        <Flex className="mt-4">
          <div className="w-full">
            <DonutChart
              data={data}
              index="durum"
              category="tutar"
              variant="donut"
              colors={chartColors}
              valueFormatter={formatCurrency}
              showLabel={false}
              showAnimation={true}
              className="h-40"
              onValueChange={(segment) => 
                setSelectedSegment(segment ? segment.durum : null)
              }
            />
            
            <div className="mt-3">
              <Legend
                categories={data.map((item) => item.durum)}
                colors={chartColors}
                className="justify-center"
              />
            </div>
          </div>
        </Flex>
        
        {/* Özet bilgiler */}
        <div className="mt-4 space-y-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center gap-2">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <Text className="font-medium">Fatura tamamlanma oranı</Text>
            </div>
            <Bold>{completionRate.toFixed(1)}%</Bold>
          </div>
          
          <ProgressBar 
            value={completionRate} 
            color="emerald" 
            className="h-2" 
          />
          
          <div className="grid grid-cols-2 gap-4 text-center mt-6">
            <div className="border rounded-md p-2">
              <Text className="text-gray-500 text-xs">Kesilmiş</Text>
              <div className="flex justify-center items-center gap-1 mt-1">
                <FileCheck className="h-4 w-4 text-green-500" />
                <Bold>{data[0]?.islemSayisi || 0}</Bold>
              </div>
            </div>
            
            <div className="border rounded-md p-2">
              <Text className="text-gray-500 text-xs">Beklemede</Text>
              <div className="flex justify-center items-center gap-1 mt-1">
                <Clock className="h-4 w-4 text-amber-500" />
                <Bold>{data[1]?.islemSayisi || 0}</Bold>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter className="border-t pt-4">
        <div className="w-full">
          <p className="text-sm text-gray-500 mb-2">Fatura Dağılımı</p>
          <div className="space-y-2 w-full">
            {data.map((item, idx) => (
              <div 
                key={item.durum}
                className="flex items-center justify-between"
              >
                <div className="flex items-center gap-2">
                  {getStatusIcon(item.durum)}
                  <span className="text-sm">{item.durum}</span>
                </div>
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium">
                    {formatCurrency(item.tutar)}
                  </span>
                  <span className="text-xs text-gray-400 bg-gray-100 px-1 rounded">
                    %{calculatePercentage(item.tutar)}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </CardFooter>
    </Card>
  )
} 