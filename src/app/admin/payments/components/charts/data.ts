export interface AdvertiserPayment {
  id: string
  advertiserId: string
  advertiserName: string
  advertiserEmail: string
  campaignId: string
  campaignName: string
  amount: number
  date: string
  status: string
  method: string
  invoice: string
  metrics: {
    impressions: number
    clicks: number
    ctr: number
  }
}

export const samplePayments: AdvertiserPayment[] = [
  {
    id: "PAY-54321",
    advertiserId: "ADV-001",
    advertiserName: "Ahmet Yılmaz",
    advertiserEmail: "<EMAIL>",
    campaignId: "CAM-105",
    campaignName: "Yaz Kampanyası",
    amount: 4800,
    date: "2024-03-15",
    status: "completed",
    method: "Kred<PERSON> Kartı",
    invoice: "INV-2024-001",
    metrics: {
      impressions: 85000,
      clicks: 2200,
      ctr: 2.58
    }
  },
  {
    id: "PAY-54322",
    advertiserId: "ADV-002",
    advertiserName: "Ayşe Demir",
    advertiserEmail: "<EMAIL>",
    campaignId: "CAM-106",
    campaignName: "<PERSON><PERSON>",
    amount: 6200,
    date: "2024-03-14",
    status: "pending",
    method: "Banka Transferi",
    invoice: "INV-2024-002",
    metrics: {
      impressions: 110000,
      clicks: 3400,
      ctr: 3.09
    }
  },
  {
    id: "PAY-54323",
    advertiserId: "ADV-003",
    advertiserName: "Mehmet Kaya",
    advertiserEmail: "<EMAIL>",
    campaignId: "CAM-107",
    campaignName: "Özel Gün Kampanyası",
    amount: 5300,
    date: "2024-03-13",
    status: "processing",
    method: "PayPal",
    invoice: "INV-2024-003",
    metrics: {
      impressions: 95000,
      clicks: 2800,
      ctr: 2.94
    }
  },
  {
    id: "PAY-54324",
    advertiserId: "ADV-004",
    advertiserName: "Zeynep Öztürk",
    advertiserEmail: "<EMAIL>",
    campaignId: "CAM-108",
    campaignName: "Marka Bilinirliği",
    amount: 7500,
    date: "2024-03-12",
    status: "completed",
    method: "Kredi Kartı",
    invoice: "INV-2024-004",
    metrics: {
      impressions: 130000,
      clicks: 4200,
      ctr: 3.23
    }
  },
  {
    id: "PAY-54325",
    advertiserId: "ADV-005",
    advertiserName: "Emre Can",
    advertiserEmail: "<EMAIL>",
    campaignId: "CAM-109",
    campaignName: "İndirim Kampanyası",
    amount: 4200,
    date: "2024-03-11",
    status: "pending",
    method: "Banka Transferi",
    invoice: "INV-2024-005",
    metrics: {
      impressions: 75000,
      clicks: 1900,
      ctr: 2.53
    }
  },
  {
    id: "PAY-54326",
    advertiserId: "ADV-001",
    advertiserName: "Ahmet Yılmaz",
    advertiserEmail: "<EMAIL>",
    campaignId: "CAM-110",
    campaignName: "Hedefli Reklam",
    amount: 3800,
    date: "2024-03-10",
    status: "completed",
    method: "Kredi Kartı",
    invoice: "INV-2024-006",
    metrics: {
      impressions: 65000,
      clicks: 1600,
      ctr: 2.46
    }
  },
  {
    id: "PAY-54327",
    advertiserId: "ADV-003",
    advertiserName: "Mehmet Kaya",
    advertiserEmail: "<EMAIL>",
    campaignId: "CAM-111",
    campaignName: "Sosyal Medya Kampanyası",
    amount: 5800,
    date: "2024-03-09",
    status: "completed",
    method: "PayPal",
    invoice: "INV-2024-007",
    metrics: {
      impressions: 98000,
      clicks: 3100,
      ctr: 3.16
    }
  },
  {
    id: "PAY-54328",
    advertiserId: "ADV-004",
    advertiserName: "Zeynep Öztürk",
    advertiserEmail: "<EMAIL>",
    campaignId: "CAM-112",
    campaignName: "Mobil Uygulama Tanıtımı",
    amount: 6800,
    date: "2024-03-08",
    status: "processing",
    method: "Kredi Kartı",
    invoice: "INV-2024-008",
    metrics: {
      impressions: 115000,
      clicks: 3800,
      ctr: 3.30
    }
  }
] 