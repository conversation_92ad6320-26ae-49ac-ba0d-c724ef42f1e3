"use client"

import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { 
  BarChart, 
  Color,
  BarList,
  Bold,
  Flex,
  Text
} from "@tremor/react"
import { subMonths, format } from "date-fns"
import { tr } from "date-fns/locale"
import { ArrowDown, ArrowUp, Info } from "lucide-react"

interface MonthlyData {
  ay: string
  tutar: number
  islemSayisi: number
}

// Son 12 ay için örnek veri
const defaultData: MonthlyData[] = Array.from({ length: 12 }).map((_, i) => {
  const date = subMonths(new Date(), 11 - i)
  // Gerçekçi aylık ödeme verisi oluştur (düzenli artış trendi ile)
  const baseAmount = 5000 + (i * 800) // Temel miktar + her ay kademeli artış
  const randomVariation = Math.random() * 3000 - 1500 // Gerçekçi dalgalanma ekle
  const amount = Math.max(2000, Math.round(baseAmount + randomVariation))
  
  // İşlem sayısı (tutarla orantılı)
  const transactions = Math.max(5, Math.round(amount / 1000) + Math.floor(Math.random() * 5))
  
  return {
    ay: format(date, 'MMM yy', { locale: tr }),
    tutar: amount,
    islemSayisi: transactions
  }
})

interface BarChartProps {
  title?: string
  description?: string
  data?: MonthlyData[]
  className?: string
}

export function MonthlyPaymentTrend({ 
  title = "Aylık Ödeme Trendi", 
  description = "Son 12 aya göre ödeme dağılımı",
  data = defaultData,
  className = ""
}: BarChartProps) {
  
  // Toplam tutar
  const totalAmount = data.reduce((sum, item) => sum + item.tutar, 0)
  
  // Toplam işlem sayısı
  const totalTransactions = data.reduce((sum, item) => sum + item.islemSayisi, 0)
  
  // Son iki ay karşılaştırması
  const lastMonthAmount = data[data.length - 1]?.tutar || 0
  const previousMonthAmount = data[data.length - 2]?.tutar || 0
  const amountDifference = lastMonthAmount - previousMonthAmount
  const amountPercentChange = previousMonthAmount 
    ? (amountDifference / previousMonthAmount) * 100 
    : 0

  // Para birimini formatlama
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 0
    }).format(value)
  }
  
  // BarList verisi - alternatif görselleştirme için
  const barListData = data.map(item => ({
    name: item.ay,
    value: item.tutar
  }));
  
  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle>{title}</CardTitle>
            <CardDescription>{description}</CardDescription>
          </div>
          <div className="flex items-center gap-1 text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded">
            <Info className="h-3.5 w-3.5" />
            <span>Son 12 Ay</span>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="h-72">
          <BarChart
            data={data}
            index="ay"
            categories={["tutar"]}
            colors={["emerald"]}
            valueFormatter={formatCurrency}
            yAxisWidth={80}
            showLegend={false}
            showTooltip={true}
            showGridLines={true}
            showAnimation={true}
            className="mt-4"
          />
        </div>
      </CardContent>
      <CardFooter className="flex justify-between border-t pt-4">
        <div>
          <p className="text-sm text-gray-500">Toplam Tutar</p>
          <p className="text-lg font-medium">{formatCurrency(totalAmount)}</p>
        </div>
        <div>
          <p className="text-sm text-gray-500">İşlem Sayısı</p>
          <p className="text-lg font-medium">{totalTransactions}</p>
        </div>
        <div>
          <p className="text-sm text-gray-500">Aylık Değişim</p>
          <div className="flex items-center gap-1">
            {amountPercentChange > 0 ? (
              <ArrowUp className="h-4 w-4 text-green-500" />
            ) : (
              <ArrowDown className="h-4 w-4 text-red-500" />
            )}
            <p className={`font-medium ${amountPercentChange > 0 ? 'text-green-600' : 'text-red-600'}`}>
              %{Math.abs(amountPercentChange).toFixed(1)}
            </p>
          </div>
        </div>
      </CardFooter>
    </Card>
  )
} 