"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import {
  DownloadIcon,
  Search,
  SlidersHorizontal,
  ArrowUpDown,
  CheckCircle,
  AlertCircle,
  Clock,
  FileText,
  TrendingUp
} from "lucide-react"
import { cn } from "@/lib/utils"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { format } from "date-fns"
import { tr } from "date-fns/locale"
import { CategoryBar } from "@tremor/react"
import { samplePayments, AdvertiserPayment } from "./data"

interface AdvertiserPaymentTableProps {
  title?: string
  description?: string
  className?: string
}

export function AdvertiserPaymentTable({
  title = "Reklamveren Ödemeleri",
  description = "Reklamveren bazlı detaylı ödeme analizi",
  className = ""
}: AdvertiserPaymentTableProps) {
  const [searchTerm, setSearchTerm] = useState("")
  const [sortField, setSortField] = useState<keyof AdvertiserPayment>("date")
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("desc")
  
  // Arama ve sıralama işlemleri
  const filteredPayments = samplePayments
    .filter(payment => 
      payment.advertiserName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      payment.campaignName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      payment.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      payment.advertiserId.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .sort((a, b) => {
      if (sortField === "amount") {
        return sortDirection === "asc" 
          ? a.amount - b.amount 
          : b.amount - a.amount
      }
      
      if (sortField === "date") {
        return sortDirection === "asc" 
          ? new Date(a.date).getTime() - new Date(b.date).getTime()
          : new Date(b.date).getTime() - new Date(a.date).getTime()
      }
      
      // String-based sorting for other fields
      const aValue = String(a[sortField]).toLowerCase()
      const bValue = String(b[sortField]).toLowerCase()
      
      return sortDirection === "asc"
        ? aValue.localeCompare(bValue)
        : bValue.localeCompare(aValue)
    })
  
  // Sıralama fonksiyonu
  const handleSort = (field: keyof AdvertiserPayment) => {
    if (field === sortField) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc")
    } else {
      setSortField(field)
      setSortDirection("desc") // Default to descending order
    }
  }
  
  // Para birimi formatı
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 0
    }).format(value)
  }
  
  // Ödeme durumu ikonu
  const getStatusBadge = (status: string) => {
    switch(status) {
      case "completed":
        return <Badge className="bg-green-100 text-green-700 hover:bg-green-200 border border-green-200 flex items-center gap-1">
          <CheckCircle className="h-3 w-3" /><span>Tamamlandı</span>
        </Badge>
      case "pending":
        return <Badge variant="outline" className="bg-amber-50 text-amber-700 hover:bg-amber-100 border border-amber-200 flex items-center gap-1">
          <AlertCircle className="h-3 w-3" /><span>Beklemede</span>
        </Badge>
      case "processing":
        return <Badge className="bg-blue-100 text-blue-700 hover:bg-blue-200 border border-blue-200 flex items-center gap-1">
          <Clock className="h-3 w-3" /><span>İşlemde</span>
        </Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }
  
  // Özet istatistikler
  const totalAmount = filteredPayments.reduce((sum, payment) => sum + payment.amount, 0)
  const totalImpressions = filteredPayments.reduce((sum, payment) => sum + (payment.metrics?.impressions || 0), 0)
  const totalClicks = filteredPayments.reduce((sum, payment) => sum + (payment.metrics?.clicks || 0), 0)
  const averageCTR = totalImpressions > 0 ? (totalClicks / totalImpressions) * 100 : 0
  
  const maxSpending = Math.max(...filteredPayments.map(p => p.amount))
  
  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
          <div>
            <CardTitle>{title}</CardTitle>
            <CardDescription>{description}</CardDescription>
          </div>
          
          <div className="flex w-full md:w-auto items-center gap-2">
            <div className="relative w-full md:w-64">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
              <Input
                placeholder="Reklamveren, kampanya ara..."
                className="pl-9 h-9"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            
            <Button variant="outline" size="sm" className="h-9 gap-1">
              <SlidersHorizontal className="h-4 w-4" />
              <span className="hidden sm:inline">Filtrele</span>
            </Button>
            
            <Button variant="outline" size="sm" className="h-9 gap-1">
              <DownloadIcon className="h-4 w-4" />
              <span className="hidden sm:inline">Dışa Aktar</span>
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="px-0">
        <div className="overflow-auto">
          <Table>
            <TableHeader className="bg-slate-50">
              <TableRow>
                <TableHead onClick={() => handleSort("advertiserName")} className="cursor-pointer pl-6">
                  <div className="flex items-center gap-1">
                    <span>Reklamveren</span>
                    <ArrowUpDown className="h-3.5 w-3.5" />
                  </div>
                </TableHead>
                <TableHead onClick={() => handleSort("campaignName")} className="cursor-pointer">
                  <div className="flex items-center gap-1">
                    <span>Kampanya</span>
                    <ArrowUpDown className="h-3.5 w-3.5" />
                  </div>
                </TableHead>
                <TableHead onClick={() => handleSort("amount")} className="cursor-pointer text-right">
                  <div className="flex items-center gap-1 justify-end">
                    <span>Tutar</span>
                    <ArrowUpDown className="h-3.5 w-3.5" />
                  </div>
                </TableHead>
                <TableHead onClick={() => handleSort("date")} className="cursor-pointer">
                  <div className="flex items-center gap-1">
                    <span>Tarih</span>
                    <ArrowUpDown className="h-3.5 w-3.5" />
                  </div>
                </TableHead>
                <TableHead>Metrikler</TableHead>
                <TableHead>Fatura</TableHead>
                <TableHead onClick={() => handleSort("status")} className="cursor-pointer">
                  <div className="flex items-center gap-1">
                    <span>Durum</span>
                    <ArrowUpDown className="h-3.5 w-3.5" />
                  </div>
                </TableHead>
                <TableHead className="text-right pr-6">Ödeme Yöntemi</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredPayments.map((payment) => (
                <TableRow key={payment.id} className="hover:bg-slate-50">
                  <TableCell className="font-medium pl-6">
                    <div className="flex items-center gap-3">
                      <Avatar className="h-8 w-8">
                        <AvatarFallback className="bg-slate-100 text-slate-800">
                          {payment.advertiserName.substring(0, 2).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium">{payment.advertiserName}</div>
                        <div className="text-xs text-slate-500">{payment.advertiserEmail}</div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">{payment.campaignName}</div>
                      <div className="text-xs text-slate-500">{payment.campaignId}</div>
                    </div>
                  </TableCell>
                  <TableCell className="text-right font-medium">
                    {formatCurrency(payment.amount)}
                  </TableCell>
                  <TableCell>
                    {format(new Date(payment.date), "d MMM yyyy", { locale: tr })}
                  </TableCell>
                  <TableCell>
                    <div className="space-y-1">
                      <div className="flex justify-between text-xs">
                        <span>Gösterim:</span>
                        <span className="font-medium">{payment.metrics?.impressions.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between text-xs">
                        <span>Tıklama:</span>
                        <span className="font-medium">{payment.metrics?.clicks.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between text-xs">
                        <span>CTR:</span>
                        <span className="font-medium text-green-600">%{payment.metrics?.ctr.toFixed(2)}</span>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-1 text-blue-600 text-sm">
                      <FileText className="h-3.5 w-3.5" />
                      <span>{payment.invoice}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    {getStatusBadge(payment.status)}
                  </TableCell>
                  <TableCell className="text-right pr-6">
                    {payment.method}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
      <CardFooter className="flex justify-between border-t py-4">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 w-full">
          <div>
            <p className="text-sm text-gray-500">Toplam Ödeme</p>
            <p className="text-lg font-medium">{formatCurrency(totalAmount)}</p>
          </div>
          <div>
            <p className="text-sm text-gray-500">Toplam Gösterim</p>
            <p className="text-lg font-medium">{totalImpressions.toLocaleString()}</p>
          </div>
          <div>
            <p className="text-sm text-gray-500">Toplam Tıklama</p>
            <p className="text-lg font-medium">{totalClicks.toLocaleString()}</p>
          </div>
          <div>
            <p className="text-sm text-gray-500">Ortalama CTR</p>
            <p className="text-lg font-medium">%{averageCTR.toFixed(2)}</p>
          </div>
        </div>
      </CardFooter>
    </Card>
  )
} 