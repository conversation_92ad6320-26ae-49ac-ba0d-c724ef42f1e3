export interface Payment {
  transaction_id: string
  created_at: number
  amount: number
  payment_method: string
  user_id: string
  status: string
  email: string
  name: string
  surname: string
}

export interface PaymentListProps {
  onViewDetail?: (payment: Payment) => void
}

export interface PaymentTableRowProps {
  payment: Payment
  selected: boolean
  onSelect: () => void
  onViewDetail: (payment: Payment) => void
}

export interface PaymentItem {
  id: number
  description: string
  amount: number
  quantity: number
}

export interface PaymentStats {
  total: number
  pending: number
  processing: number
  completed: number
  pendingCount: number
  processingCount: number
  totalCount: number
  monthlyGrowth: number
} 