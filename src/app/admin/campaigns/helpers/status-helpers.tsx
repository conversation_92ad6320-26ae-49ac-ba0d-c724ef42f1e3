import { Play, Pause, CheckCircle, Edit, CalendarCheck } from "lucide-react";
import { CampaignStatus } from "../types";

export const getStatusBadgeStyles = (status: CampaignStatus | string) => {
  switch (status?.toLowerCase()) {
    case "active":
      return "bg-emerald-50 text-emerald-700 border-emerald-200 hover:bg-emerald-100";
    case "paused":
      return "bg-amber-50 text-amber-700 border-amber-200 hover:bg-amber-100";
    case "completed":
      return "bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100";
    case "draft":
      return "bg-slate-50 text-slate-700 border-slate-200 hover:bg-slate-100";
    default:
      return "bg-gray-50 text-gray-700 border-gray-200 hover:bg-gray-100";
  }
};

export const getStatusIcon = (status: CampaignStatus | string) => {
  switch (status?.toLowerCase()) {
    case "active":
      return <Play className="h-3.5 w-3.5" />;
    case "paused":
      return <Pause className="h-3.5 w-3.5" />;
    case "completed":
      return <CheckCircle className="h-3.5 w-3.5" />;
    case "draft":
      return <Edit className="h-3.5 w-3.5" />;
    case "planned":
      return <CalendarCheck className="h-3.5 w-3.5" />;
    default:
      return null;
  }
};

export const getStatusText = (status: CampaignStatus | string) => {
  switch (status?.toLowerCase()) {
    case "active":
      return "Aktif";
    case "paused":
      return "Duraklatıldı";
    case "completed":
      return "Tamamlandı";
    case "draft":
      return "Taslak";
    case "planned":
      return "Planlandı";
    default:
      return status;
  }
};

export const getStatusColor = (status: CampaignStatus | string) => {
  switch (status?.toLowerCase()) {
    case "active":
      return "text-emerald-600";
    case "paused":
      return "text-amber-600";
    case "completed":
      return "text-blue-600";
    case "draft":
      return "text-slate-600";
    case "planned":
      return "text-purple-600";
    default:
      return "text-gray-600";
  }
}; 