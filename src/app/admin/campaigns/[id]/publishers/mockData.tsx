import { CampaignPublisherDetail, PublisherInfo, PublisherStatus } from '../../types'; 

// Ana mock publisher listesi (page.tsx içindekinin aynısı veya oradan import edilebilir)
const mockPublishers: PublisherInfo[] = [
  { id: 101, name: "<PERSON><PERSON><PERSON><PERSON> Yıldız", avatar: "AY", status: "active" },
  { id: 102, name: "<PERSON><PERSON>", avatar: "BK", status: "pending" },
  { id: 103, name: "Can Demir", avatar: "CD", status: "active" },
  { id: 104, name: "<PERSON><PERSON> Çelik", avatar: "DÇ", status: "active" },
  { id: 105, name: "<PERSON><PERSON>", avatar: "EA", status: "rejected" },
  { id: 106, name: "<PERSON><PERSON>ahi<PERSON>", avatar: "FŞ", status: "pending" },
];


export const mockCampaignPublisherDetails: CampaignPublisherDetail[] = [
  // Kampanya 1 için
  { ...mockPublishers.find(p => p.id === 101)!, campaignId: 1, earnings: 150.75, clicks: 120, impressions: 15000, communityName: 'Telegram Kanalı A', communityUrl: 'https://t.me/kanalA', publicationStatus: 'active', publicationUrl: 'https://t.me/kanalA/123' },
  { ...mockPublishers.find(p => p.id === 102)!, campaignId: 1, earnings: 0, clicks: 0, impressions: 0, communityName: 'Telegram Grubu B', communityUrl: 'https://t.me/grupB', publicationStatus: 'inactive' }, // Pending olduğu için inactive
  { ...mockPublishers.find(p => p.id === 103)!, campaignId: 1, earnings: 95.20, clicks: 80, impressions: 10000, communityName: 'Facebook Sayfası C', communityUrl: 'https://facebook.com/sayfaC', publicationStatus: 'active', publicationUrl: 'https://facebook.com/sayfaC/posts/456' },

  // Kampanya 2 için
  { ...mockPublishers.find(p => p.id === 104)!, campaignId: 2, earnings: 210.00, clicks: 150, impressions: 18000, communityName: 'Instagram Profili D', communityUrl: 'https://instagram.com/profilD', publicationStatus: 'active', publicationUrl: 'https://instagram.com/p/CrxYz123' },

  // Kampanya 3 için
  { ...mockPublishers.find(p => p.id === 101)!, campaignId: 3, earnings: 300.50, clicks: 250, impressions: 30000, communityName: 'Twitter Hesabı E', communityUrl: 'https://twitter.com/hesapE', publicationStatus: 'active', publicationUrl: 'https://twitter.com/hesapE/status/123' },
  { ...mockPublishers.find(p => p.id === 105)!, campaignId: 3, earnings: 0, clicks: 0, impressions: 0, communityName: 'Twitter Hesabı F', communityUrl: 'https://twitter.com/hesapF', publicationStatus: 'inactive' }, // Rejected olduğu için inactive
  { ...mockPublishers.find(p => p.id === 106)!, campaignId: 3, earnings: 0, clicks: 0, impressions: 0, communityName: 'LinkedIn Profili G', communityUrl: 'https://linkedin.com/in/profilG', publicationStatus: 'inactive' }, // Pending olduğu için inactive
  { ...mockPublishers.find(p => p.id === 102)!, campaignId: 3, earnings: 180.00, clicks: 190, impressions: 22000, communityName: 'LinkedIn Şirket H', communityUrl: 'https://linkedin.com/company/sirketH', publicationStatus: 'active', publicationUrl: 'https://linkedin.com/feed/update/urn:li:activity:456' },

  // Kampanya 4 için
  { ...mockPublishers.find(p => p.id === 103)!, campaignId: 4, earnings: 0, clicks: 0, impressions: 0, communityName: 'Youtube Kanalı I', communityUrl: 'https://youtube.com/kanalI', publicationStatus: 'inactive' }, // Pending olduğu için inactive
  { ...mockPublishers.find(p => p.id === 104)!, campaignId: 4, earnings: 0, clicks: 0, impressions: 0, communityName: 'Youtube Kanalı J', communityUrl: 'https://youtube.com/kanalJ', publicationStatus: 'inactive' }, // Pending olduğu için inactive

  // Kampanya 5 için (Hiç yayıncı yoktu, ekleyelim)
   { ...mockPublishers.find(p => p.id === 102)!, campaignId: 5, earnings: 0, clicks: 0, impressions: 0, communityName: 'Facebook Grubu K', communityUrl: 'https://facebook.com/groups/grupK', publicationStatus: 'inactive' }, 
   { ...mockPublishers.find(p => p.id === 106)!, campaignId: 5, earnings: 0, clicks: 0, impressions: 0, communityName: 'Instagram Hesabı L', communityUrl: 'https://instagram.com/hesapL', publicationStatus: 'inactive' }, 
];

// Kampanya bilgilerini de alalım (page.tsx içindeki gibi)
// Not: Bu veriyi normalde API'den alırsınız.
import { Campaign } from '../../types';
import { Send, Facebook, Instagram, Twitter, Linkedin, Youtube } from "lucide-react";

const campaignsMock: Campaign[] = [
    {
    id: 1, name: "Yaz Kampanyası 2024", status: "active", budget: 5000, spent: 2500, remaining: 2500, impressions: 50000, clicks: 2500, ctr: 5, startDate: "2024-05-01", endDate: "2024-08-31", publishHours: { start: "08:00", end: "22:00" }, target: "18-35 yaş", flag: "🇹🇷", countryName: "Türkiye", communities: [{ name: "Telegram", icon: <Send className="h-4 w-4 text-blue-500"/> },{ name: "Facebook", icon: <Facebook className="h-4 w-4 text-blue-800"/> }], advertiser: { id: "ADV-001", name: "Migros Marketler", image: "" }, relatedPages: [{ id: "P1", name: "Anasayfa", url: "/", views: 12500 },{ id: "P2", name: "Ürünler", url: "/products", views: 8200 },{ id: "P3", name: "Kampanyalar", url: "/campaigns", views: 15800 }], lastUpdated: "2024-03-18", publishers: [] // Bu kısım artık CampaignPublisherDetail ile yönetilecek gibi düşünebiliriz.
  },
  {
    id: 2, name: "Bahar İndirimi Kampanyası", status: "paused", budget: 3000, spent: 1200, remaining: 1800, impressions: 25000, clicks: 1000, ctr: 4, startDate: "2024-03-01", endDate: "2024-04-30", publishHours: { start: "09:00", end: "21:00" }, target: "Tüm yaş grupları", flag: "🇩🇪", countryName: "Almanya", communities: [{ name: "Instagram", icon: <Instagram className="h-4 w-4 text-pink-600"/> },], advertiser: { id: "ADV-002", name: "LC Waikiki", image: "" }, relatedPages: [{ id: "P1", name: "Anasayfa", url: "/", views: 7500 },{ id: "P4", name: "Kadın Giyim", url: "/women", views: 10200 },{ id: "P5", name: "Erkek Giyim", url: "/men", views: 7300 }], lastUpdated: "2024-03-15", publishers: []
  },
    {
    id: 3, name: "Kış Sezonu Koleksiyonu", status: "completed", budget: 4000, spent: 4000, remaining: 0, impressions: 80000, clicks: 4800, ctr: 6, startDate: "2023-11-01", endDate: "2024-02-28", publishHours: { start: "00:00", end: "23:59" }, target: "25-45 yaş", flag: "🇫🇷", countryName: "Fransa", communities: [{ name: "Twitter", icon: <Twitter className="h-4 w-4 text-sky-500"/> },{ name: "LinkedIn", icon: <Linkedin className="h-4 w-4 text-blue-700"/> }], advertiser: { id: "ADV-003", name: "Beymen", image: "" }, relatedPages: [{ id: "P6", name: "Kış Koleksiyonu", url: "/winter-collection", views: 25000 },{ id: "P7", name: "Lüks Giyim", url: "/luxury", views: 18000 },{ id: "P8", name: "Aksesuar", url: "/accessories", views: 12000 }], lastUpdated: "2024-02-28", publishers: []
  },
  {
    id: 4, name: "Anneler Günü Özel", status: "draft", budget: 2500, spent: 0, remaining: 2500, impressions: 0, clicks: 0, ctr: 0, startDate: "2024-05-01", endDate: "2024-05-15", publishHours: { start: "07:00", end: "23:00" }, target: "Kadınlar, 28-55 yaş", flag: "🇬🇧", countryName: "Birleşik Krallık", communities: [{ name: "Youtube", icon: <Youtube className="h-4 w-4 text-red-600"/> }], advertiser: { id: "ADV-004", name: "Flormar", image: "" }, relatedPages: [{ id: "P9", name: "Anneler Günü", url: "/mothers-day", views: 0 },{ id: "P10", name: "Makyaj Ürünleri", url: "/makeup", views: 0 }], lastUpdated: "2024-03-10", publishers: []
  },
  {
    id: 5, name: "Teknoloji Haftası", status: "active", budget: 8000, spent: 3200, remaining: 4800, impressions: 62000, clicks: 3100, ctr: 5, startDate: "2024-03-15", endDate: "2024-04-15", publishHours: { start: "10:00", end: "22:00" }, target: "Teknoloji meraklıları", flag: "🇺🇸", countryName: "Amerika Birleşik Devletleri", communities: [{ name: "Facebook", icon: <Facebook className="h-4 w-4 text-blue-800"/> },{ name: "Instagram", icon: <Instagram className="h-4 w-4 text-pink-600"/> }], advertiser: { id: "ADV-005", name: "Media Markt", image: "" }, relatedPages: [{ id: "P11", name: "Elektronik", url: "/electronics", views: 28000 },{ id: "P12", name: "Bilgisayarlar", url: "/computers", views: 15000 },{ id: "P13", name: "Akıllı Telefonlar", url: "/smartphones", views: 19000 }], lastUpdated: "2024-03-17", publishers: []
  }
];

export const getCampaignById = (id: number): Campaign | undefined => {
    return campaignsMock.find(c => c.id === id);
} 