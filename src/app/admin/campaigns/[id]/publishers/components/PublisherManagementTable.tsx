import React, { useEffect, useState } from 'react';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { CampaignPublisherDetail } from '../../../types';
import { But<PERSON> } from "@/components/ui/button";
import { ArrowUpDown, ArrowUp, ArrowDown } from "lucide-react";
import { PublisherTableRow } from './PublisherTableRow';
import { Skeleton } from "@/components/ui/skeleton"

type SortDirection = 'ascending' | 'descending';
type SortConfig = {
  key: keyof CampaignPublisherDetail | null;
  direction: SortDirection;
} | null;

interface PublisherManagementTableProps {
  publishers: CampaignPublisherDetail[];
  sortConfig: SortConfig;
  onSort: (key: keyof CampaignPublisherDetail) => void;
  onApprovePublisher: (publisherId: number) => void;
  onRejectPublisher: (publisherId: number) => void;
}

export const PublisherManagementTable: React.FC<PublisherManagementTableProps> = ({
  publishers,
  sortConfig,
  onSort,
  onApprovePublisher,
  onRejectPublisher,
}) => {
  const [loading, setLoading] = useState(true);
  useEffect(() => {
    const timer = setTimeout(() => setLoading(false), 1000);
    return () => clearTimeout(timer);
  }, []);

  const renderSortIcon = (columnKey: keyof CampaignPublisherDetail) => {
    if (!sortConfig || sortConfig.key !== columnKey) {
      return <ArrowUpDown className="ml-2 h-3 w-3 text-muted-foreground/70" />;
    }
    if (sortConfig.direction === 'ascending') {
      return <ArrowUp className="ml-2 h-3 w-3" />;
    }
    return <ArrowDown className="ml-2 h-3 w-3" />;
  };

  const SortableHeader = ({ columnKey, children }: { columnKey: keyof CampaignPublisherDetail, children: React.ReactNode }) => (
    <Button variant="ghost" onClick={() => onSort(columnKey)} className="px-1 py-0 h-auto -ml-1">
      {children}
      {renderSortIcon(columnKey)}
    </Button>
  );

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead><SortableHeader columnKey="name">Yayıncı</SortableHeader></TableHead>
            <TableHead><SortableHeader columnKey="status">Durum</SortableHeader></TableHead>
            <TableHead><SortableHeader columnKey="communityName">Topluluk</SortableHeader></TableHead>
            <TableHead><SortableHeader columnKey="publicationStatus">Yayın Durumu</SortableHeader></TableHead>
            <TableHead className="text-right"><SortableHeader columnKey="earnings">Kazanç</SortableHeader></TableHead>
            <TableHead className="text-right"><SortableHeader columnKey="clicks">Tıklama</SortableHeader></TableHead>
            <TableHead className="text-right"><SortableHeader columnKey="impressions">Gösterim</SortableHeader></TableHead>
            <TableHead className="text-right">İşlemler</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {loading ? (
            Array.from({ length: 6 }).map((_, idx) => (
              <TableRow key={`skeleton-${idx}`}>
                <TableCell><div className="flex items-center gap-3"><Skeleton className="h-9 w-9 rounded-full" /><Skeleton className="h-4 w-24" /></div></TableCell>
                <TableCell><Skeleton className="h-5 w-16 rounded-md" /></TableCell>
                <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                <TableCell><Skeleton className="h-4 w-16" /></TableCell>
                <TableCell className="text-right"><Skeleton className="h-4 w-16 ml-auto" /></TableCell>
                <TableCell className="text-right"><Skeleton className="h-4 w-12 ml-auto" /></TableCell>
                <TableCell className="text-right"><Skeleton className="h-4 w-12 ml-auto" /></TableCell>
                <TableCell className="text-right"><Skeleton className="h-7 w-7 ml-auto rounded-md" /></TableCell>
              </TableRow>
            ))
          ) : (
            publishers.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} className="h-24 text-center text-muted-foreground">
                  Bu kampanya için yayıncı bulunamadı.
                </TableCell>
              </TableRow>
            ) : (
              publishers.map((detail) => (
                <PublisherTableRow 
                  key={detail.id} 
                  publisherDetail={detail} 
                  onApprove={onApprovePublisher}
                  onReject={onRejectPublisher}
                />
              ))
            )
          )}
        </TableBody>
      </Table>
    </div>
  );
}; 