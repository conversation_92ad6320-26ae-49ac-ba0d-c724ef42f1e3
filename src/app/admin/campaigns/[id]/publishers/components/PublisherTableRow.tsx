import React, { useState } from 'react';
import { TableRow, TableCell } from '@/components/ui/table';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Check, X, CircleHelp, Link as LinkIcon, RefreshCw } from 'lucide-react';
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from '@/components/ui/dropdown-menu';
import { CampaignPublisherDetail, PublisherStatus } from '../../../types';

interface PublisherTableRowProps {
  publisherDetail: CampaignPublisherDetail;
  onApprove: (publisherId: number) => void;
  onReject: (publisherId: number) => void;
}

const getStatusBadgeStyles = (status: PublisherStatus) => {
  switch (status) {
    case "active": return "border-green-300 bg-green-50 text-green-700";
    case "pending": return "border-amber-300 bg-amber-50 text-amber-700";
    case "rejected": return "border-red-300 bg-red-50 text-red-700";
    default: return "border-gray-300 bg-gray-50 text-gray-700";
  }
};

const getStatusText = (status: PublisherStatus) => {
  switch (status) {
    case "active": return "Onaylandı";
    case "pending": return "Beklemede";
    case "rejected": return "Reddedildi";
    default: return "Bilinmiyor";
  }
};

export const PublisherTableRow: React.FC<PublisherTableRowProps> = ({ 
  publisherDetail,
  onApprove,
  onReject
}) => {
  const { id, name, avatar, status, communityName, earnings, clicks, impressions } = publisherDetail;
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  const handleDropdownItemClick = (action: () => void) => {
    action();
    setIsDropdownOpen(false);
  };

  return (
    <TableRow className="hover:bg-slate-50 transition-colors">
      <TableCell>
        <div className="flex items-center gap-3">
          <Avatar className="h-9 w-9">
            {/* <AvatarImage src={avatar} /> */}
            <AvatarFallback>{avatar || name.slice(0, 2).toUpperCase()}</AvatarFallback>
          </Avatar>
          <div className="font-medium">{name}</div>
        </div>
      </TableCell>
      <TableCell>
        <Badge variant="outline" className={`text-xs ${getStatusBadgeStyles(status)}`}>
          {getStatusText(status)}
        </Badge>
      </TableCell>
      <TableCell>
        {publisherDetail.communityUrl ? (
          <a 
            href={publisherDetail.communityUrl} 
            target="_blank" 
            rel="noopener noreferrer" 
            className="text-blue-600 hover:underline hover:text-blue-800 transition-colors text-sm"
          >
            {communityName}
          </a>
        ) : (
          <span className="text-sm">{communityName}</span>
        )}
      </TableCell>
      <TableCell>
        <div className="flex items-center gap-1.5 text-sm">
          <span className={publisherDetail.publicationStatus === 'active' ? 'text-green-700' : 'text-slate-600'}>
            {publisherDetail.publicationStatus === 'active' ? 'Aktif' : 'Pasif'}
          </span>
          {publisherDetail.publicationUrl && (
            <TooltipProvider delayDuration={100}>
                <Tooltip>
                    <TooltipTrigger asChild>
                        <a href={publisherDetail.publicationUrl} target="_blank" rel="noopener noreferrer">
                            <LinkIcon className="h-3.5 w-3.5 text-blue-600 hover:text-blue-800" />
                        </a>
                    </TooltipTrigger>
                    <TooltipContent>Paylaşımı Gör</TooltipContent>
                </Tooltip>
            </TooltipProvider>
          )}
        </div>
      </TableCell>
      <TableCell className="text-right">{earnings.toLocaleString('tr-TR')} ₺</TableCell>
      <TableCell className="text-right">{clicks.toLocaleString('tr-TR')}</TableCell>
      <TableCell className="text-right">{impressions.toLocaleString('tr-TR')}</TableCell>
      <TableCell className="text-right">
        {/* Durum Değiştirme Dropdown Menüsü */}
        <DropdownMenu open={isDropdownOpen} onOpenChange={setIsDropdownOpen}>
          <TooltipProvider delayDuration={100}>
            <Tooltip>
              <TooltipTrigger asChild>
                <DropdownMenuTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-7 w-7">
                    <RefreshCw className="h-4 w-4 text-muted-foreground" />
                  </Button>
                </DropdownMenuTrigger>
              </TooltipTrigger>
              <TooltipContent>Durumu Değiştir</TooltipContent>
            </Tooltip>
          </TooltipProvider>
          <DropdownMenuContent align="end">
            <DropdownMenuItem 
              onClick={() => handleDropdownItemClick(() => onApprove(id))} 
              disabled={status === 'active'}
              className="flex items-center gap-2 cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <Check className="h-4 w-4 text-green-600" />
              <span>Onayla</span>
            </DropdownMenuItem>
            <DropdownMenuItem 
              onClick={() => handleDropdownItemClick(() => onReject(id))} 
              disabled={status === 'rejected'}
              className="flex items-center gap-2 cursor-pointer text-red-600 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <X className="h-4 w-4" />
              <span>Reddet</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </TableCell>
    </TableRow>
  );
}; 