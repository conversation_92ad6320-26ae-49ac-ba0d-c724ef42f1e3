"use client";

import React, { useState, useEffect, useMemo } from 'react';
import { useParams } from 'next/navigation';
import { DashboardLayout } from '@/app/layout/dashboard-layout';
import { DashboardHeader } from '@/app/admin/components/dashboard/header';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { Campaign, CampaignPublisherDetail, PublisherStatus } from '../../types'; // Ana types
import { mockCampaignPublisherDetails, getCampaignById } from './mockData'; // Mock veriler
import { PublisherManagementTable } from './components/PublisherManagementTable';
// CampaignTableRow'dan durum stillerini ve ikonlarını import et
import { getStatusBadgeStyles, getStatusIcon } from '../../helpers/status-helpers';
import { useToast } from "@/components/ui/use-toast"


type SortDirection = 'ascending' | 'descending';
type SortConfig = {
  key: keyof CampaignPublisherDetail | null;
  direction: SortDirection;
} | null;

export default function CampaignPublishersPage() {
  const params = useParams();
  const campaignId = parseInt(params.id as string, 10);
  const [campaign, setCampaign] = useState<Campaign | null>(null);
  const [publishers, setPublishers] = useState<CampaignPublisherDetail[]>([]);
  const { toast } = useToast()

  // Sayfalama State
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 10; // Sayfa başına öğe sayısı (örneğin 10)

  // Sıralama State
  const [sortConfig, setSortConfig] = useState<SortConfig>(null);

  useEffect(() => {
    // Normalde burada API çağrısı yapılır
    const foundCampaign = getCampaignById(campaignId);
    if (foundCampaign) {
      setCampaign(foundCampaign);
      // İlgili kampanyanın yayıncı detaylarını filtrele
      const filteredPublishers = mockCampaignPublisherDetails.filter(
        (p) => p.campaignId === campaignId
      );
      setPublishers(filteredPublishers);
    } else {
      // Kampanya bulunamadı durumu
      console.error("Kampanya bulunamadı:", campaignId);
      // İsteğe bağlı olarak kullanıcıyı bilgilendir veya yönlendir
    }
  }, [campaignId]);

  const handleUpdatePublisherStatus = (publisherId: number, newStatus: PublisherStatus) => {
    // Mock veriyi güncelle (normalde API çağrısı)
    setPublishers(prevPublishers => 
      prevPublishers.map(p => 
        p.id === publisherId ? { ...p, status: newStatus } : p
      )
    );
    // Mock ana veriyi de güncelle (sayfa yenilendiğinde kaybolmaması için, normalde gerekmez)
    const detailIndex = mockCampaignPublisherDetails.findIndex(p => p.id === publisherId && p.campaignId === campaignId);
    if (detailIndex > -1) {
        mockCampaignPublisherDetails[detailIndex].status = newStatus;
    }
    
    // Kullanıcıyı bilgilendir
    toast({ 
        title: "Yayıncı Durumu Güncellendi",
        description: `Yayıncı #${publisherId} durumu "${newStatus}" olarak ayarlandı.`,
        variant: newStatus === 'rejected' ? 'destructive' : 'default'
    })
    console.log(`Yayıncı ${publisherId} durumu ${newStatus} olarak güncellendi (mock).`);
  };

  // Sıralama Fonksiyonu
  const handleSort = (key: keyof CampaignPublisherDetail | null) => {
    let direction: SortDirection = 'ascending';
    if (sortConfig && sortConfig.key === key && sortConfig.direction === 'ascending') {
      direction = 'descending';
    }
    setSortConfig({ key, direction });
    setCurrentPage(1); // Sıralama değiştiğinde ilk sayfaya dön
  };

  // Sıralanmış ve Sayfalanmış Veri (useMemo ile)
  const paginatedPublishers = useMemo(() => {
    const sortedPublishers = [...publishers];

    if (sortConfig && sortConfig.key) {
      sortedPublishers.sort((a, b) => {
        const aValue = a[sortConfig.key as keyof CampaignPublisherDetail];
        const bValue = b[sortConfig.key as keyof CampaignPublisherDetail];

        // Undefined değer kontrolü: undefined değerleri sona at
        if (aValue === undefined && bValue === undefined) return 0;
        if (aValue === undefined) return 1; // a tanımsızsa sona gider
        if (bValue === undefined) return -1; // b tanımsızsa sona gider

        // Basit string/number karşılaştırması, daha karmaşık tipler için geliştirilebilir
        if (aValue < bValue) {
          return sortConfig.direction === 'ascending' ? -1 : 1;
        }
        if (aValue > bValue) {
          return sortConfig.direction === 'ascending' ? 1 : -1;
        }
        return 0;
      });
    }

    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return sortedPublishers.slice(startIndex, endIndex);
  }, [publishers, sortConfig, currentPage, itemsPerPage]);

  // Toplam sayfa sayısı
  const totalPages = Math.ceil(publishers.length / itemsPerPage);

  // Sayfa Değiştirme Fonksiyonları
  const handleNextPage = () => {
    setCurrentPage((prev) => Math.min(prev + 1, totalPages));
  };

  const handlePreviousPage = () => {
    setCurrentPage((prev) => Math.max(prev - 1, 1));
  };

  const handleApprovePublisher = (publisherId: number) => {
    handleUpdatePublisherStatus(publisherId, 'active');
  };

  const handleRejectPublisher = (publisherId: number) => {
    handleUpdatePublisherStatus(publisherId, 'rejected');
  };

  if (!campaign) {
    // Yükleniyor veya bulunamadı durumu
    // Daha iyi bir yükleme göstergesi eklenebilir
    return (
        <DashboardLayout>
             <div className="flex items-center justify-center h-64 text-muted-foreground">
                 Kampanya bilgileri yükleniyor veya bulunamadı...
            </div>
        </DashboardLayout>
    )
  }

  return (
    <DashboardLayout>
      <DashboardHeader
        title={`Kampanya Yayıncıları: ${campaign.name}`}
        description={`"${campaign.name}" kampanyasına katılan yayıncıları yönetin.`}
      />
       <div className="mb-4">
         <Link href="/admin/campaigns">
            <Button variant="outline" size="sm" className="gap-1">
              <ArrowLeft className="h-4 w-4" />
              Geri Dön
            </Button>
         </Link>
        </div>

      {/* Kampanya Özet Kartı */}
      <Card className="mb-6">
        <CardHeader>
            <div className="flex items-center justify-between">
                <CardTitle className="text-lg">Kampanya Özeti</CardTitle>
                <Badge className={`gap-1 ${getStatusBadgeStyles(campaign.status)}`}>
                    {getStatusIcon(campaign.status)}
                    <span>
                    {campaign.status === "active" && "Aktif"}
                    {campaign.status === "paused" && "Duraklatıldı"}
                    {campaign.status === "completed" && "Tamamlandı"}
                    {campaign.status === "draft" && "Taslak"}
                    </span>
                </Badge>
            </div>
          {/* <CardDescription>Kampanyanın temel bilgileri.</CardDescription> */}
        </CardHeader>
        <CardContent className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
           <div>
                <div className="text-muted-foreground text-xs mb-1">Bütçe</div>
                <div className="font-medium">{campaign.budget.toLocaleString('tr-TR')} ₺</div>
            </div>
           <div>
                <div className="text-muted-foreground text-xs mb-1">Harcanan</div>
                <div className="font-medium">{campaign.spent.toLocaleString('tr-TR')} ₺</div>
            </div>
            <div>
                <div className="text-muted-foreground text-xs mb-1">Tarihler</div>
                <div className="font-medium">
                    {new Date(campaign.startDate).toLocaleDateString('tr-TR')} - {new Date(campaign.endDate).toLocaleDateString('tr-TR')}
                </div>
            </div>
            <div>
                <div className="text-muted-foreground text-xs mb-1">Hedef Kitle</div>
                <div className="font-medium">{campaign.target}</div>
            </div>
        </CardContent>
      </Card>

      {/* Yayıncı Yönetim Tablosu */}
      <PublisherManagementTable 
        publishers={paginatedPublishers}
        sortConfig={sortConfig}
        onSort={handleSort}
        onApprovePublisher={handleApprovePublisher}
        onRejectPublisher={handleRejectPublisher}
      />

      {/* Sayfalama Kontrolleri */} 
      {totalPages > 1 && (
        <div className="flex items-center justify-end space-x-2 py-4">
          <span className="text-sm text-muted-foreground">
            Sayfa {currentPage} / {totalPages}
          </span>
          <Button
            variant="outline"
            size="sm"
            onClick={handlePreviousPage}
            disabled={currentPage === 1}
          >
            Önceki
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleNextPage}
            disabled={currentPage === totalPages}
          >
            Sonraki
          </Button>
        </div>
      )}
    </DashboardLayout>
  );
} 