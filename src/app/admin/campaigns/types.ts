import { Send, Facebook, Instagram, Twitter, Linkedin, Youtube } from "lucide-react";
import React from "react";

export interface PublisherInfo {
  id: number;
  name: string;
  avatar: string; // URL or identifier
  status: 'active' | 'pending' | 'rejected'; // Yayınc<PERSON> durumu eklendi
}

export type CampaignStatus = "ACTIVE" | "STOP" | "PLANNED" | "FINISHED";
export type CampaignStatusDisplay = "active" | "paused" | "planned" | "completed"; // UI gösterimi için

export interface Campaign {
  id: number
  campaign_id: string // Düzenleme için gerçek kampanya ID'si
  name: string
  status: CampaignStatusDisplay
  budget: number
  spent: number
  remaining: number
  impressions: number
  clicks: number
  ctr: number
  startDate: string
  endDate: string
  publishHours: {
    start: string
    end: string
  }
  target: string
  flag?: string
  countryName?: string; // Ülke adı eklendi
  communities: {
    active: number;
    pending: number;
  }
  advertising_platform: string
  advertiser: AdvertiserInfo
  relatedPages: RelatedPage[];
  lastUpdated: string
  publishers?: PublisherInfo[];
}

// Backend statüsünü UI statüsüne çevirme yardımcısı
export const mapStatusToDisplay = (status: string): CampaignStatusDisplay => {
  switch (status.toUpperCase()) {
    case "ACTIVE":
      return "active";
    case "STOP":
      return "paused";
    case "PLANNED":
      return "planned";
    case "FINISHED":
      return "completed";
    default:
      return "planned";
  }
};

// UI statüsünü backend statüsüne çevirme yardımcısı
export const mapDisplayToStatus = (display: CampaignStatusDisplay): CampaignStatus => {
  switch (display) {
    case "active":
      return "ACTIVE";
    case "paused":
      return "STOP";
    case "planned":
      return "PLANNED";
    case "completed":
      return "FINISHED";
    default:
      return "PLANNED";
  }
};

export type RelatedPage = {
  id: string;
  name: string;
  url: string;
  views: number;
};

export interface AdvertiserInfo {
  id: string;
  name: string;
  image?: string;
}

// Yeni: Kampanya özelindeki yayıncı detayı (PublisherInfo'yu genişletir)
export interface CampaignPublisherDetail extends PublisherInfo { 
  campaignId: number; // Hangi kampanya için olduğu
  earnings: number;
  clicks: number;
  impressions: number;
  communityName: string; // Hangi toplulukta?
  communityUrl?: string; // Topluluk linki (opsiyonel)
  publicationStatus: 'active' | 'inactive'; // Yayın durumu
  publicationUrl?: string; // Paylaşım linki (opsiyonel)
  // publisherStatus: PublisherStatus; // Gerekirse kampanya özel durumu için ayrı alan, şimdilik PublisherInfo'daki status'ü kullanıyoruz.
}

// PublisherInfo içindeki status tipini tanımla (eğer yoksa)
export type PublisherStatus = 'active' | 'pending' | 'rejected';

// PublisherInfo arayüzünü kontrol et ve status alanı yoksa ekle
// (Önceki adımlarda eklenmişti, bu sadece bir kontrol)
export interface PublisherInfo {
  id: number;
  name: string;
  avatar: string; 
  status: PublisherStatus; // Yayıncı durumu
} 