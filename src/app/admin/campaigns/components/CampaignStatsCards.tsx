import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Play, Pause, CreditCard, BarChart3, TrendingUp, Clock } from "lucide-react";

interface CampaignStats {
  active: number;
  paused: number;
  total: number;
  totalBudget: number;
  totalSpent: number;
}

interface CampaignStatsCardsProps {
  stats: CampaignStats;
}

export const CampaignStatsCards: React.FC<CampaignStatsCardsProps> = ({ stats }) => (
  <div className="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-4">
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-sm font-medium">Aktif Kampanyalar</CardTitle>
        <div className="p-1.5 bg-emerald-50 text-emerald-600 rounded-full">
          <Play className="h-4 w-4" />
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{stats.active}</div>
        <div className="flex items-center gap-1 mt-1">
          <TrendingUp className="h-3.5 w-3.5 text-emerald-600" />
          <p className="text-xs text-muted-foreground">
            Toplam {stats.total} kampanyadan {((stats.active / stats.total) * 100).toFixed(1)}%
          </p>
        </div>
      </CardContent>
    </Card>

    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-sm font-medium">Duraklatılmış</CardTitle>
        <div className="p-1.5 bg-amber-50 text-amber-600 rounded-full">
          <Pause className="h-4 w-4" />
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{stats.paused}</div>
        <div className="flex items-center gap-1 mt-1">
          <Clock className="h-3.5 w-3.5 text-amber-600" />
          <p className="text-xs text-muted-foreground">
            Toplam {stats.total} kampanyadan {((stats.paused / stats.total) * 100).toFixed(1)}%
          </p>
        </div>
      </CardContent>
    </Card>

    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-sm font-medium">Toplam Bütçe</CardTitle>
        <div className="p-1.5 bg-blue-50 text-blue-600 rounded-full">
          <CreditCard className="h-4 w-4" />
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{stats.totalBudget.toLocaleString('tr-TR')} ₺</div>
        <div className="flex items-center gap-1 mt-1">
          <TrendingUp className="h-3.5 w-3.5 text-blue-600" />
          <p className="text-xs text-muted-foreground">
            Kampanya başına {(stats.totalBudget / stats.total).toLocaleString('tr-TR')} ₺
          </p>
        </div>
      </CardContent>
    </Card>

    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-sm font-medium">Toplam Harcama</CardTitle>
        <div className="p-1.5 bg-purple-50 text-purple-600 rounded-full">
          <BarChart3 className="h-4 w-4" />
        </div>
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">{stats.totalSpent.toLocaleString('tr-TR')} ₺</div>
        <Progress 
          value={(stats.totalSpent / stats.totalBudget) * 100} 
          className="h-2 mt-2"
        />
        <div className="flex items-center gap-1 mt-1.5">
          <TrendingUp className="h-3.5 w-3.5 text-purple-600" />
          <p className="text-xs text-muted-foreground">
            Bütçenin {Math.round((stats.totalSpent / stats.totalBudget) * 100)}%'i kullanıldı
          </p>
        </div>
      </CardContent>
    </Card>
  </div>
); 