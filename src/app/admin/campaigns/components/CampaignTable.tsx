import React, { useEffect, useState } from "react";
import { Table, TableBody, TableHead, TableHeader, TableRow, TableCell } from "@/components/ui/table";
import { Checkbox } from "@/components/ui/checkbox";
import { Campaign } from "../types";
import { CampaignTableRow } from "./CampaignTableRow";
import { Skeleton } from "@/components/ui/skeleton"

interface CampaignTableProps {
  campaigns: Campaign[];
  selectedCampaigns: number[];
  onSelectAll: () => void;
  onSelectCampaign: (id: number) => void;
  onViewDetails: (id: number) => void;
  onConfirmStatusChange: (id: number, action: string) => void;
  onEditCampaign: (id: number) => void;
}

export const CampaignTable: React.FC<CampaignTableProps> = ({
  campaigns,
  selectedCampaigns,
  onSelectAll,
  onSelectCampaign,
  onViewDetails,
  onConfirmStatusChange,
  onEditCampaign,
}) => {
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => setLoading(false), 1000);
    return () => clearTimeout(timer);
  }, []);

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[30px]">
              <Checkbox 
                checked={selectedCampaigns.length === campaigns.length && campaigns.length > 0}
                onCheckedChange={onSelectAll}
              />
            </TableHead>
            <TableHead>Kampanya</TableHead>
            <TableHead>Durum</TableHead>
            <TableHead>Bütçe</TableHead>
            <TableHead>Kalan</TableHead>
            <TableHead>Topluluklar</TableHead>
            <TableHead>ÜLKE</TableHead>
            <TableHead>YAYINCILAR</TableHead>
            <TableHead className="text-right">İşlemler</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {loading ? (
            Array.from({ length: 5 }).map((_, idx) => (
              <TableRow key={`skeleton-${idx}`}>
                <TableCell><Skeleton className="h-4 w-4 rounded" /></TableCell>
                <TableCell><Skeleton className="h-4 w-32" /></TableCell>
                <TableCell><Skeleton className="h-5 w-20 rounded-md" /></TableCell>
                <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                <TableCell><Skeleton className="h-4 w-20" /></TableCell>
                <TableCell><Skeleton className="h-4 w-10" /></TableCell>
                <TableCell><Skeleton className="h-4 w-10" /></TableCell>
                <TableCell className="text-right"><Skeleton className="h-4 w-8 ml-auto" /></TableCell>
              </TableRow>
            ))
          ) : (
            campaigns.map((campaign) => (
              <CampaignTableRow
                key={campaign.id}
                campaign={campaign}
                isSelected={selectedCampaigns.includes(campaign.id)}
                onSelectCampaign={onSelectCampaign}
                onViewDetails={onViewDetails}
                onConfirmStatusChange={onConfirmStatusChange}
                onEditCampaign={onEditCampaign}
              />
            ))
          )}
        </TableBody>
      </Table>
    </div>
  );
}; 