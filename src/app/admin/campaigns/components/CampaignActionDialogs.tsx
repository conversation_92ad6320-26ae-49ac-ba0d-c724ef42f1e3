import React from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON>, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Separator } from "@/components/ui/separator";
import { Progress } from "@/components/ui/progress";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Play, Pause, Trash2, Edit, Eye, Calendar, Clock, Users } from "lucide-react";
import { Campaign, CampaignStatus } from "../types";
import { getStatusBadgeStyles, getStatusIcon } from "../helpers/status-helpers";

interface CampaignActionDialogsProps {
  showConfirmDialog: boolean;
  setShowConfirmDialog: (open: boolean) => void;
  confirmAction: string;
  selectedCampaign: Campaign | null;
  handleConfirmAction: (campaignId: number, action: string) => void;
  showDetailDialog: boolean;
  setShowDetailDialog: (open: boolean) => void;
}

export const CampaignActionDialogs: React.FC<CampaignActionDialogsProps> = ({
  showConfirmDialog,
  setShowConfirmDialog,
  confirmAction,
  selectedCampaign,
  handleConfirmAction,
  showDetailDialog,
  setShowDetailDialog,
}) => {
  const router = useRouter();
  
  if (!selectedCampaign) return null;

  const finalAction = 
    confirmAction === "delete" ? "delete" : 
    confirmAction === "activate" ? "resume" : 
    confirmAction === "pause" ? "pause" : "delete";

  const getActionIcon = () => {
    switch (confirmAction) {
      case "activate":
        return <Play className="h-4 w-4 text-emerald-600" />;
      case "pause":
        return <Pause className="h-4 w-4 text-amber-600" />;
      case "delete":
        return <Trash2 className="h-4 w-4 text-red-600" />;
      default:
        return null;
    }
  };

  const getActionTitle = () => {
    switch (confirmAction) {
      case "activate":
        return "Kampanyayı Aktifleştir";
      case "pause":
        return "Kampanyayı Duraklat";
      case "delete":
        return "Kampanyayı Sil";
      default:
        return "";
    }
  };

  const getActionDescription = () => {
    const campaignName = <span className="font-medium">{selectedCampaign.name}</span>;
    
    switch (confirmAction) {
      case "activate":
        return (
          <span>
            {campaignName} isimli kampanyayı aktifleştirmek istediğinize emin misiniz?
            <br />
            <span className="text-muted-foreground">
              Bu işlem kampanyayı yayına alacak ve bütçe kullanımı başlayacaktır.
            </span>
          </span>
        );
      case "pause":
        return (
          <span>
            {campaignName} isimli kampanyayı duraklatmak istediğinize emin misiniz?
            <br />
            <span className="text-muted-foreground">
              Bu işlem kampanyayı duraklatacak ve bütçe kullanımı geçici olarak durdurulacaktır.
            </span>
          </span>
        );
      case "delete":
        return (
          <span>
            {campaignName} isimli kampanyayı silmek istediğinize emin misiniz?
            <br />
            <span className="text-muted-foreground">
              Bu işlem geri alınamaz ve tüm kampanya verileri silinecektir.
            </span>
          </span>
        );
      default:
        return "";
    }
  };

  const getActionButtonText = () => {
    switch (confirmAction) {
      case "activate":
        return "Aktifleştir";
      case "pause":
        return "Duraklat";
      case "delete":
        return "Sil";
      default:
        return "";
    }
  };

  const getActionButtonVariant = () => {
    switch (confirmAction) {
      case "activate":
        return "default";
      case "pause":
        return "outline";
      case "delete":
        return "destructive";
      default:
        return "default";
    }
  };

  return (
    <>
      {/* Onay Dialogu */}
      <Dialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              {getActionIcon()}
              {getActionTitle()}
            </DialogTitle>
            <DialogDescription className="pt-2">
              {getActionDescription()}
            </DialogDescription>
          </DialogHeader>
          
          <div className="flex flex-col gap-4 py-4">
            <div className="flex items-center justify-between p-3 rounded-lg border bg-slate-50">
              <div className="space-y-1">
                <p className="text-sm font-medium">{selectedCampaign.name}</p>
                <div className="flex items-center gap-3">
                  <Badge className={`gap-1 ${getStatusBadgeStyles(selectedCampaign.status)}`}>
                    {getStatusIcon(selectedCampaign.status)}
                    <span>{selectedCampaign.status}</span>
                  </Badge>
                  <span className="flex items-center gap-1 text-xs text-muted-foreground">
                    <Calendar className="h-3.5 w-3.5" />
                    {new Date(selectedCampaign.startDate).toLocaleDateString('tr-TR')} - 
                    {new Date(selectedCampaign.endDate).toLocaleDateString('tr-TR')}
                  </span>
                </div>
              </div>
              <div className="flex flex-col items-end gap-1">
                <span className="text-sm font-medium">
                  {selectedCampaign.budget.toLocaleString('tr-TR')} ₺
                </span>
                <span className="flex items-center gap-1 text-xs text-muted-foreground">
                  <Users className="h-3.5 w-3.5" />
                  {selectedCampaign.publishers?.length || 0} Yayıncı
                </span>
              </div>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowConfirmDialog(false)}>
              Vazgeç
            </Button>
            <Button 
              variant={getActionButtonVariant()} 
              onClick={() => handleConfirmAction(selectedCampaign.id || 0, finalAction)}
            >
              {getActionButtonText()}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Kampanya Detay Dialogu */}
      <Dialog open={showDetailDialog} onOpenChange={setShowDetailDialog}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          {selectedCampaign && (
            <>
              <DialogHeader>
                <div className="flex items-center justify-between">
                  <DialogTitle className="text-xl">{selectedCampaign.name}</DialogTitle>
                  <Badge className={`gap-1 ${getStatusBadgeStyles(selectedCampaign.status)}`}>
                    {getStatusIcon(selectedCampaign.status)}
                    <span>
                      {selectedCampaign.status === "active" && "Aktif"}
                      {selectedCampaign.status === "paused" && "Duraklatıldı"}
                      {selectedCampaign.status === "completed" && "Tamamlandı"}
                      {selectedCampaign.status === "draft" && "Taslak"}
                    </span>
                  </Badge>
                </div>
                <DialogDescription className="text-sm text-muted-foreground">
                  Kampanya ayrıntıları ve performans metrikleri
                </DialogDescription>
              </DialogHeader>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 py-4">
                {/* Temel Bilgiler */}
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">Temel Bilgiler</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-1">
                      <div className="text-sm text-muted-foreground">Reklamveren</div>
                      <div className="flex items-center gap-2">
                        <Avatar className="h-8 w-8">
                          <AvatarImage src={selectedCampaign.advertiser.image} />
                          <AvatarFallback className="text-xs">
                            {selectedCampaign.advertiser.name.slice(0, 2).toUpperCase()}
                          </AvatarFallback>
                        </Avatar>
                        <span>{selectedCampaign.advertiser.name}</span>
                      </div>
                    </div>
                    
                    <div className="space-y-1">
                      <div className="text-sm text-muted-foreground">Hedef Kitle</div>
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4 text-slate-400" />
                        <span>{selectedCampaign.target}</span>
                      </div>
                    </div>
                    
                    <div className="space-y-1">
                      <div className="text-sm text-muted-foreground">Yayın Tarihleri</div>
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-slate-400" />
                        <span>
                          {new Date(selectedCampaign.startDate).toLocaleDateString('tr-TR')} - 
                          {new Date(selectedCampaign.endDate).toLocaleDateString('tr-TR')}
                        </span>
                      </div>
                    </div>
                    
                    <div className="space-y-1">
                      <div className="text-sm text-muted-foreground">Yayın Saatleri</div>
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-slate-400" />
                        <span>{selectedCampaign.publishHours.start} - {selectedCampaign.publishHours.end}</span>
                      </div>
                    </div>
                    
                    <div className="space-y-1">
                      <div className="text-sm text-muted-foreground">Son Güncelleme</div>
                      <div>{new Date(selectedCampaign.lastUpdated).toLocaleDateString('tr-TR')}</div>
                    </div>
                  </CardContent>
                </Card>

                {/* Bütçe ve Performans Bilgileri */}
                <Card>
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">Bütçe ve Performans</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-1">
                      <div className="text-sm text-muted-foreground">Toplam Bütçe</div>
                      <div className="text-lg font-medium">{selectedCampaign.budget.toLocaleString('tr-TR')} ₺</div>
                    </div>
                    
                    <div className="space-y-1">
                      <div className="text-sm text-muted-foreground">Harcanan Bütçe</div>
                      <div>{selectedCampaign.spent.toLocaleString('tr-TR')} ₺</div>
                    </div>
                    
                    <div className="space-y-1">
                      <div className="text-sm text-muted-foreground">Kalan Bütçe</div>
                      <div>{selectedCampaign.remaining.toLocaleString('tr-TR')} ₺</div>
                      <Progress
                        value={(1 - (selectedCampaign.spent / selectedCampaign.budget)) * 100}
                        className="h-2 mt-1"
                      />
                    </div>
                    
                    <Separator />
                    
                    <div className="space-y-1">
                      <div className="text-sm text-muted-foreground">Gösterimler</div>
                      <div>{selectedCampaign.impressions.toLocaleString('tr-TR')}</div>
                    </div>
                    
                    <div className="space-y-1">
                      <div className="text-sm text-muted-foreground">Tıklamalar</div>
                      <div>{selectedCampaign.clicks.toLocaleString('tr-TR')}</div>
                    </div>
                    
                    <div className="space-y-1">
                      <div className="text-sm text-muted-foreground">Tıklama Oranı (CTR)</div>
                      <div>%{selectedCampaign.ctr}</div>
                    </div>
                  </CardContent>
                </Card>

                {/* İlişkili Sayfalar */}
                <Card className="md:col-span-2">
                  <CardHeader className="pb-2">
                    <CardTitle className="text-base">İlişkili Sayfalar</CardTitle>
                    <CardDescription>
                      Bu kampanyanın gösterildiği sayfalar ve performans istatistikleri
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="rounded-md border">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead>Sayfa</TableHead>
                            <TableHead>URL</TableHead>
                            <TableHead className="text-right">Görüntülenme</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {selectedCampaign.relatedPages.map((page) => (
                            <TableRow key={page.id}>
                              <TableCell className="font-medium">{page.name}</TableCell>
                              <TableCell className="text-blue-600 hover:underline">{page.url}</TableCell>
                              <TableCell className="text-right">{page.views.toLocaleString('tr-TR')}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>

                    <div className="mt-4 text-center text-sm text-muted-foreground">
                      <div className="flex justify-center gap-6 mt-8">
                        <div className="flex flex-col items-center">
                          <div className="text-xl font-bold">
                            {selectedCampaign.relatedPages.reduce((sum, page) => sum + page.views, 0).toLocaleString('tr-TR')}
                          </div>
                          <div className="text-xs text-muted-foreground">Toplam Görüntülenme</div>
                        </div>
                        <div className="flex flex-col items-center">
                          <div className="text-xl font-bold">{selectedCampaign.relatedPages.length}</div>
                          <div className="text-xs text-muted-foreground">Toplam Sayfa</div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
              
              <DialogFooter>
                <div className="flex justify-between w-full">
                  <Button variant="outline" onClick={() => setShowDetailDialog(false)}>Kapat</Button>
                  <div className="space-x-2">
                    <Button variant="outline" onClick={() => router.push(`/admin/campaigns/edit/${selectedCampaign.campaign_id}`)}>
                      <Edit className="h-4 w-4 mr-2" />
                      Düzenle
                    </Button>
                  </div>
                </div>
              </DialogFooter>
            </>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
}; 