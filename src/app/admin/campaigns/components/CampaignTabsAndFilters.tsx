import React from "react";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Popover, PopoverTrigger, PopoverContent } from "@/components/ui/popover";
import { Checkbox } from "@/components/ui/checkbox";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Search, Filter, ArrowDownUp, ChevronDown, Play, Pause, Clock, Calendar, Check } from "lucide-react";
import { Campaign } from "../types"; // Varsayılan tip dosyasını import et
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { AdminUser } from "@/shared/services/admin/admin-list";

interface CampaignStats {
  active: number;
  paused: number;
  completed: number;
  planned: number;
  total: number;
}

interface CampaignTabsAndFiltersProps {
  selectedTab: string;
  setSelectedTab: (tab: string) => void;
  campaignStats: CampaignStats;
  searchQuery: string;
  setSearchQuery: (query: string) => void;
  selectedAdvertisers: string[];
  toggleAdvertiserFilter: (id: string) => void;
  dateRange: string;
  setDateRange: (range: string) => void;
  resetFilters: () => void;
  advertisers: AdminUser[];
  sortOption: string;
  setSortOption: (sort: string) => void;
}

const TabIcon = ({ tab }: { tab: string }) => {
  switch (tab) {
    case "active":
      return <Play className="h-3.5 w-3.5 text-emerald-600" />;
    case "paused":
      return <Pause className="h-3.5 w-3.5 text-amber-600" />;
    case "planned":
      return <Calendar className="h-3.5 w-3.5 text-indigo-600" />;
    case "completed":
      return <Check className="h-3.5 w-3.5 text-red-600" />;
    default:
      return null;
  }
};

export const CampaignTabsAndFilters: React.FC<CampaignTabsAndFiltersProps> = ({
  selectedTab,
  setSelectedTab,
  campaignStats,
  searchQuery,
  setSearchQuery,
  selectedAdvertisers,
  toggleAdvertiserFilter,
  dateRange,
  setDateRange,
  resetFilters,
  advertisers,
  sortOption,
  setSortOption,
}) => (
  <div className="bg-white rounded-lg border shadow-sm">
    <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between border-b p-4">
      <Tabs value={selectedTab} onValueChange={setSelectedTab} className="w-full lg:w-auto">
        <div className="overflow-x-auto lg:overflow-x-visible">
          <TabsList className="flex w-max lg:w-auto gap-2 min-w-full lg:min-w-0">
            <TabsTrigger value="all" className="data-[state=active]:bg-slate-100 whitespace-nowrap flex-shrink-0">
              <div className="flex items-center gap-1.5">
                <span className="text-sm">Tümü</span>
                <Badge variant="outline" className="bg-slate-50 text-xs px-1.5 py-0.5">
                {campaignStats.total}
              </Badge>
            </div>
          </TabsTrigger>
            <TabsTrigger value="active" className="data-[state=active]:bg-emerald-50 whitespace-nowrap flex-shrink-0">
              <div className="flex items-center gap-1.5">
              <TabIcon tab="active" />
                <span className="text-sm">Aktif</span>
                <Badge variant="outline" className="bg-emerald-50 text-emerald-700 border-emerald-200 text-xs px-1.5 py-0.5">
                {campaignStats.active}
              </Badge>
            </div>
          </TabsTrigger>
            <TabsTrigger value="paused" className="data-[state=active]:bg-amber-50 whitespace-nowrap flex-shrink-0">
              <div className="flex items-center gap-1.5">
              <TabIcon tab="paused" />
                <span className="text-sm">Durduruldu</span>
                <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200 text-xs px-1.5 py-0.5">
                {campaignStats.paused}
              </Badge>
            </div>
          </TabsTrigger>
            <TabsTrigger value="planned" className="data-[state=active]:bg-indigo-50 whitespace-nowrap flex-shrink-0">
              <div className="flex items-center gap-1.5">
              <TabIcon tab="planned" />
                <span className="text-sm">Planlı</span>
                <Badge variant="outline" className="bg-indigo-50 text-indigo-700 border-indigo-200 text-xs px-1.5 py-0.5">
                {campaignStats.planned}
              </Badge>
            </div>
          </TabsTrigger>
            <TabsTrigger value="completed" className="data-[state=active]:bg-blue-50 whitespace-nowrap flex-shrink-0">
              <div className="flex items-center gap-1.5">
                <TabIcon tab="completed" />
                <span className="text-sm">Bitti</span>
                <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200 text-xs px-1.5 py-0.5">
                  {campaignStats.completed}
                </Badge>
              </div>
            </TabsTrigger>
        </TabsList>
        </div>
      </Tabs>
      
      <div className="flex items-center gap-2 mt-4 lg:mt-0 flex-wrap lg:flex-nowrap">
        <div className="relative flex-1 lg:w-[320px]">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Kampanya ara..."
            className="pl-9 h-9 min-w-[200px] bg-slate-50 border-slate-200 focus-visible:ring-slate-200"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        
        <Popover>
          <PopoverTrigger asChild>
            <Button variant="outline" size="sm" className="h-9 gap-1 bg-slate-50 border-slate-200 hover:bg-slate-100">
              <Filter className="h-3.5 w-3.5 text-slate-600" />
              <span className="hidden sm:inline">Filtrele</span>
              <ChevronDown className="h-3.5 w-3.5 text-slate-400" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-[280px] p-4">
            <div className="space-y-4">
              <h4 className="font-medium text-slate-900">Filtrele</h4>
              
              <div className="space-y-2">
                <h5 className="text-sm font-medium text-slate-700">Reklamveren</h5>
                <div className="space-y-2 max-h-32 overflow-y-auto pr-2">
                  {advertisers.map(advertiser => (
                    <div key={advertiser.user_id} className="flex items-center space-x-2">
                      <Checkbox 
                        id={`advertiser-${advertiser.user_id}`} 
                        checked={selectedAdvertisers.includes(advertiser.user_id)}
                        onCheckedChange={() => toggleAdvertiserFilter(advertiser.user_id)}
                      />
                      <label 
                        htmlFor={`advertiser-${advertiser.user_id}`} 
                        className="text-sm text-slate-600 hover:text-slate-900 cursor-pointer"
                      >
                        {advertiser.name} {advertiser.surname} ({advertiser.company_name})
                      </label>
                    </div>
                  ))}
                </div>
              </div>
              
              <Separator />
              
              <div className="space-y-2">
                <h5 className="text-sm font-medium text-slate-700">Tarih</h5>
                <Select value={dateRange} onValueChange={setDateRange}>
                  <SelectTrigger className="bg-slate-50 border-slate-200">
                    <SelectValue placeholder="Tüm tarihler" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tüm tarihler</SelectItem>
                    <SelectItem value="last-7">Son 7 gün</SelectItem>
                    <SelectItem value="last-30">Son 30 gün</SelectItem>
                    <SelectItem value="last-90">Son 90 gün</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="pt-2 flex justify-end gap-2">
                <Button 
                  variant="outline" 
                  size="sm" 
                  onClick={resetFilters}
                  className="text-slate-600 hover:text-slate-900"
                >
                  Sıfırla
                </Button>
              </div>
            </div>
          </PopoverContent>
        </Popover>
        
        <Popover>
          <PopoverTrigger asChild>
            <Button variant="outline" size="sm" className="h-9 gap-1 bg-slate-50 border-slate-200 hover:bg-slate-100">
              <ArrowDownUp className="h-3.5 w-3.5 text-slate-600" />
              <span className="hidden sm:inline">Sırala</span>
              <ChevronDown className="h-3.5 w-3.5 text-slate-400" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-[200px] p-4">
            <div className="space-y-4">
              <h4 className="font-medium text-slate-900">Sıralama</h4>
              <div className="space-y-1">
                <Button 
                  variant={sortOption === "asc" ? "default" : "ghost"} 
                  size="sm" 
                  className="w-full justify-start text-slate-600 hover:text-slate-900"
                  onClick={() => setSortOption("asc")}
                >
                  Bütçe (Düşük-Yüksek)
                </Button>
                <Button 
                  variant={sortOption === "desc" ? "default" : "ghost"} 
                  size="sm" 
                  className="w-full justify-start text-slate-600 hover:text-slate-900"
                  onClick={() => setSortOption("desc")}
                >
                  Bütçe (Yüksek-Düşük)
                </Button>
              </div>
            </div>
          </PopoverContent>
        </Popover>
      </div>
    </div>
  </div>
); 