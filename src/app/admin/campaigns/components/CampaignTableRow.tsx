import React, { useState } from "react";
import { TableRow, TableCell } from "@/components/ui/table";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuLabel, DropdownMenuItem, DropdownMenuSeparator } from "@/components/ui/dropdown-menu";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Play, Pause, Trash2, Edit, Eye, MoreVertical, UserCheck, Clock, CalendarDays, DollarSign, BarChart3, Send, Instagram, Facebook, Twitter, Linkedin, Youtube } from "lucide-react";
import { Campaign } from "../types";
import Link from 'next/link';
import { getStatusBadgeStyles, getStatusIcon, getStatusText } from "../helpers/status-helpers";
import { mapDisplayToStatus } from "../types";

interface CampaignTableRowProps {
  campaign: Campaign;
  isSelected: boolean;
  onSelectCampaign: (id: number) => void;
  onViewDetails: (id: number) => void;
  onConfirmStatusChange: (id: number, action: string) => void;
  onEditCampaign: (id: number) => void;
}

export const CampaignTableRow: React.FC<CampaignTableRowProps> = ({
  campaign,
  isSelected,
  onSelectCampaign,
  onViewDetails,
  onConfirmStatusChange,
  onEditCampaign,
}) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  const handleDropdownItemClick = (action: () => void) => {
    action();
    setIsDropdownOpen(false);
  };

  return (
    <TableRow key={campaign.id} className="group">
      <TableCell>
        <Checkbox 
          checked={isSelected} 
          onCheckedChange={() => onSelectCampaign(campaign.id)}
        />
      </TableCell>
      <TableCell>
        <div className="space-y-1">
          <div className="font-medium">{campaign.name}</div>
          <TooltipProvider delayDuration={100}>
            <Tooltip>
              <TooltipTrigger>
                <div className="flex items-center gap-1 text-xs text-muted-foreground cursor-default">
                  <CalendarDays className="h-3 w-3" />
                  <span>
                    {new Date(campaign.startDate).toLocaleDateString('tr-TR')} - 
                    {new Date(campaign.endDate).toLocaleDateString('tr-TR')}
                  </span>
                </div>
              </TooltipTrigger>
              <TooltipContent className="text-xs py-1 px-2 rounded-md">Başlangıç - Bitiş Tarihleri</TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </TableCell>
      <TableCell>
        <Badge className={`gap-1 ${getStatusBadgeStyles(campaign.status)}`}>
          {getStatusIcon(campaign.status)}
          <span>{getStatusText(campaign.status)}</span>
        </Badge>
      </TableCell>
      <TableCell>
        <div className="flex flex-col">
          <div className="flex items-center gap-1">
            <DollarSign className="h-3.5 w-3.5 text-emerald-600" />
            <span>{campaign.budget.toLocaleString('tr-TR')} ₺</span>
          </div>
          <div className="flex items-center gap-1 text-xs text-muted-foreground mt-0.5">
            <BarChart3 className="h-3 w-3" />
            <span>Harcanan: {campaign.spent.toLocaleString('tr-TR')} ₺</span>
          </div>
        </div>
      </TableCell>
      <TableCell>
        <div className="flex flex-col">
          <span>{campaign.remaining.toLocaleString('tr-TR')} ₺</span>
          <TooltipProvider delayDuration={100}>
            <Tooltip>
              <TooltipTrigger asChild>
                <Progress
                  value={campaign.budget > 0 ? (campaign.spent / campaign.budget) * 100 : 0}
                  className="h-1.5 mt-1.5 cursor-pointer"
                />
              </TooltipTrigger>
              <TooltipContent className="text-xs py-1 px-2 rounded-md">
                Bütçe Kullanımı: %{campaign.budget > 0 ? ((campaign.spent / campaign.budget) * 100).toFixed(1) : 0}
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </div>
      </TableCell>
      <TableCell>
        <div className="flex items-center gap-2">
          <div className="flex -space-x-1">
            <TooltipProvider delayDuration={100}>
              <Tooltip>
                <TooltipTrigger className="hover:z-10">
                  <div className="rounded-full p-1 bg-white border-2 border-background shadow-sm transition-transform hover:scale-110">
                    {campaign.advertising_platform === 'Telegram' ? (
                      <Send className="h-4 w-4 text-blue-500" />
                    ) : campaign.advertising_platform === 'Instagram' ? (
                      <Instagram className="h-4 w-4 text-pink-600" />
                    ) : campaign.advertising_platform === 'Facebook' ? (
                      <Facebook className="h-4 w-4 text-blue-800" />
                    ) : campaign.advertising_platform === 'Twitter' ? (
                      <Twitter className="h-4 w-4 text-blue-400" />
                    ) : campaign.advertising_platform === 'Linkedin' ? (
                      <Linkedin className="h-4 w-4 text-blue-700" />
                    ) : campaign.advertising_platform === 'Youtube' ? (
                      <Youtube className="h-4 w-4 text-red-600" />
                    ) : null}
                  </div>
                </TooltipTrigger>
                <TooltipContent className="text-xs py-1 px-2 rounded-md">{campaign.advertising_platform}</TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
      </TableCell>
      <TableCell>
        <TooltipProvider delayDuration={100}>
          <Tooltip>
            <TooltipTrigger>
              <span className="cursor-pointer text-lg">{campaign.flag || "🌐"}</span>
            </TooltipTrigger>
            <TooltipContent className="text-xs py-1 px-2 rounded-md">
              {campaign.countryName || "Bilinmeyen Ülke"}
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </TableCell>
      <TableCell>
        {(() => {
          const activeCount = campaign.communities.active;
          const pendingCount = campaign.communities.pending;

          return (
            <Link href={`/admin/campaigns/${campaign.id}/publishers`} className="cursor-pointer">
              <div className="flex items-center gap-1.5">
                {activeCount > 0 && (
                  <TooltipProvider delayDuration={100}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Badge variant="outline" className="py-0.5 px-1.5 border-emerald-300 bg-emerald-50 text-emerald-700 font-semibold">
                          <UserCheck className="h-3 w-3 mr-1" />
                          <span className="text-[11px]">{activeCount}</span>
                        </Badge>
                      </TooltipTrigger>
                      <TooltipContent className="text-xs py-1 px-2 rounded-md">Aktif Yayıncılar</TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}
                {pendingCount > 0 && (
                  <TooltipProvider delayDuration={100}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Badge variant="outline" className="py-0.5 px-1.5 border-amber-300 bg-amber-50 text-amber-700 font-semibold">
                          <Clock className="h-3 w-3 mr-1" />
                          <span className="text-[11px]">{pendingCount}</span>
                        </Badge>
                      </TooltipTrigger>
                      <TooltipContent className="text-xs py-1 px-2 rounded-md">Bekleyen Yayıncılar</TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                )}
                {activeCount === 0 && pendingCount === 0 && (
                  <span className="text-xs text-muted-foreground">-</span>
                )}
              </div>
            </Link>
          );
        })()}
      </TableCell>
      <TableCell className="text-right">
        <DropdownMenu open={isDropdownOpen} onOpenChange={setIsDropdownOpen}>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" size="sm" className="h-8 w-8 p-0 opacity-0 group-hover:opacity-100 transition-opacity">
              <MoreVertical className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <DropdownMenuLabel>İşlemler</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem 
              className="flex gap-2 cursor-pointer"
              onClick={() => handleDropdownItemClick(() => onViewDetails(campaign.id))}
            >
              <Eye className="h-4 w-4" />
              <span>Görüntüle</span>
            </DropdownMenuItem>
            <DropdownMenuItem 
              className="flex gap-2 cursor-pointer"
              onClick={() => handleDropdownItemClick(() => onEditCampaign(campaign.id))}
            >
              <Edit className="h-4 w-4" />
              <span>Düzenle</span>
            </DropdownMenuItem>
            
            {campaign.status === "active" && (
              <DropdownMenuItem 
                className="flex gap-2 cursor-pointer"
                onClick={() => handleDropdownItemClick(() => onConfirmStatusChange(campaign.id, "pause"))}
              >
                <Pause className="h-4 w-4" />
                <span>Duraklat</span>
              </DropdownMenuItem>
            )}
            
            {campaign.status === "paused" && (
              <DropdownMenuItem 
                className="flex gap-2 cursor-pointer"
                onClick={() => handleDropdownItemClick(() => onConfirmStatusChange(campaign.id, "resume"))}
              >
                <Play className="h-4 w-4" />
                <span>Aktifleştir</span>
              </DropdownMenuItem>
            )}
            
            <DropdownMenuSeparator />
            <DropdownMenuItem 
              className="flex gap-2 cursor-pointer text-red-600 focus:text-red-600"
              onClick={() => handleDropdownItemClick(() => onConfirmStatusChange(campaign.id, "delete"))}
            >
              <Trash2 className="h-4 w-4" />
              <span>Sil</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </TableCell>
    </TableRow>
  );
}; 