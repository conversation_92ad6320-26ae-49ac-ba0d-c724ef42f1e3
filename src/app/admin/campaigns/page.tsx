"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { DashboardHeader } from "@/app/admin/components/dashboard/header"
import { DashboardLayout } from "@/app/layout/dashboard-layout"
import {
  Send,
  Facebook
} from "lucide-react"
import { Campaign, PublisherInfo, CampaignStatus, CampaignStatusDisplay, mapStatusToDisplay, mapDisplayToStatus } from "./types"
import { CampaignStatsCards } from "./components/CampaignStatsCards"
import { CampaignTabsAndFilters } from "./components/CampaignTabsAndFilters"
import { CampaignTable } from "./components/CampaignTable"
import { CampaignActionDialogs } from "./components/CampaignActionDialogs"
import { fetchAdminCampaignList, AdminCampaign, AdminCampaignListParams, fetchAdvertiserList, AdminUser } from "@/shared/services/admin/admin-list"
import { fetchCampaignPause } from "@/shared/services/advertiser/advertiser-campaign-pause"
import { fetchCampaignResume } from "@/shared/services/advertiser/advertiser-campaign-resume"
import { toast } from "@/components/ui/use-toast"

// Gü<PERSON>li tarih dönü<PERSON>ümü
function safeDate(ts?: number | string) {
  if (!ts) return "";
  
  let timestamp: number;
  
  if (typeof ts === 'string') {
    // String ise parse et
    timestamp = parseFloat(ts);
  } else {
    timestamp = ts;
  }
  
  if (isNaN(timestamp)) return "";
  
  try {
    // Eğer timestamp saniye cinsindense (10 haneli), milisaniyeye çevir
    if (timestamp < 10000000000) {
      timestamp = timestamp * 1000;
    }
    return new Date(timestamp).toISOString().slice(0,10);
  } catch {
    return "";
  }
}

export default function CampaignsPage() {
  const router = useRouter()
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCampaign, setSelectedCampaign] = useState<Campaign | null>(null)
  const [showConfirmDialog, setShowConfirmDialog] = useState(false)
  const [confirmAction, setConfirmAction] = useState<string>("")
  const [selectedTab, setSelectedTab] = useState("all")
  const [selectedCampaigns, setSelectedCampaigns] = useState<number[]>([])
  const [selectedAdvertisers, setSelectedAdvertisers] = useState<string[]>([])
  const [dateRange, setDateRange] = useState("all")
  const [showDetailDialog, setShowDetailDialog] = useState(false)
  const [campaigns, setCampaigns] = useState<Campaign[]>([])
  const [loading, setLoading] = useState(true)
  const [totalCampaigns, setTotalCampaigns] = useState(0)
  const [rowsPerPage, setRowsPerPage] = useState(10)
  const [currentPage, setCurrentPage] = useState(1)
  const [tabCounts, setTabCounts] = useState({
    total: 0,
    active: 0,
    paused: 0,
    planned: 0,
    completed: 0
  })
  const [advertisers, setAdvertisers] = useState<AdminUser[]>([])
  const [sortOption, setSortOption] = useState<string>("desc")

  // Tab değerlerini backend formatına dönüştür
  const getBackendStatus = (tab: string): string | undefined => {
    if (tab === "all") return undefined;
    
    const statusMap: Record<string, string> = {
      "active": "ACTIVE",
      "paused": "STOP",
      "planned": "PLANNED",
      "completed": "FINISHED"
    };
    
    return statusMap[tab];
  }

  // İlk yüklemede tüm kampanyaları çekerek tab sayılarını hesapla
  useEffect(() => {
    const fetchAllCampaignsForCounts = async () => {
      try {
        const allCampaignsParams: AdminCampaignListParams = {
          limit: 1000, // Tüm kampanyaları çekmek için yüksek limit
          skip: 0
        }
        const response = await fetchAdminCampaignList(allCampaignsParams)
        
        if (response.status && response.result?.data) {
          // Tüm kampanyalar için status sayılarını hesapla
          const statusCounts = response.result.data.reduce((counts, campaign) => {
            const status = mapStatusToDisplay(campaign.status || "PLANNED");
            switch (status) {
              case "active":
                counts.active++;
                break;
              case "paused":
                counts.paused++;
                break;
              case "planned":
                counts.planned++;
                break;
              case "completed":
                counts.completed++;
                break;
            }
            counts.total++;
            return counts;
          }, { active: 0, paused: 0, planned: 0, completed: 0, total: 0 });

          setTabCounts(statusCounts);
        }
      } catch (err) {
        console.error("Tab sayıları yüklenirken hata:", err);
      }
    }
    
    fetchAllCampaignsForCounts()
  }, []) // Sadece component mount olduğunda çalışır

  // Reklamverenleri yükle
  useEffect(() => {
    const loadAdvertisers = async () => {
      try {
        const response = await fetchAdvertiserList();
        if (response.status && response.result?.data) {
          setAdvertisers(response.result.data);
        }
      } catch (err) {
        console.error("Reklamverenler yüklenirken hata:", err);
      }
    }
    
    loadAdvertisers();
  }, [])

  // Sayfa değiştiğinde veya tab seçildiğinde veri çekme
  useEffect(() => {
    setLoading(true)
    
    const fetchCampaignData = async () => {
      const params: AdminCampaignListParams = {
        limit: rowsPerPage,
        skip: (currentPage - 1) * rowsPerPage,
        status: getBackendStatus(selectedTab),
        search: searchQuery
      }
      
      // Sıralama parametresi ekle
      if (sortOption && sortOption !== "") {
        params.sort = sortOption as 'asc' | 'desc';
      }
      
      // Tarih filtresi ekle
      if (dateRange !== "all") {
        switch (dateRange) {
          case "last-7":
            params.created_at = "last7days";
            break;
          case "last-30":
            params.created_at = "last30days";
            break;
          case "last-90":
            params.created_at = "last90days";
            break;
        }
      }
      
      // Reklamveren filtresi ekle (sadece ilk seçilen reklamveren)
      if (selectedAdvertisers.length > 0) {
        params.created_from = selectedAdvertisers[0];
      }
      
      try {
        // Aktif tab için kampanya listesini çek
        const response = await fetchAdminCampaignList(params)
        
        if (response.status && response.result?.data) {
          // API'den gelen veriyi tabloya uygun şekilde map'le
          const mapped = response.result.data.map((c, idx) => ({
            id: idx + 1 + (currentPage - 1) * rowsPerPage, // Global sıralama için
            campaign_id: c.campaign_id, // Düzenleme için gerçek kampanya ID'si
            name: c.title || c.campaign_title,
            status: mapStatusToDisplay(c.status || "PLANNED"),
            budget: c.campaign_budget,
            spent: c.spending || 0, // spending field'ını kullan (earnings değil)
            remaining: c.campaign_budget - (c.spending || 0), // spending'e göre kalan bütçe hesapla
            impressions: c.views || 0, // API'den gelen views değeri
            clicks: c.clicks || 0, // API'den gelen clicks değeri
            ctr: c.ctr || 0, // API'den gelen ctr değeri
            startDate: safeDate(c.starts_at),
            endDate: safeDate(c.ends_at),
            publishHours: { start: "", end: "" },
            target: c.campaign_aim || "-",
            flag: c.country === "TR" ? "🇹🇷" : c.country === "DE" ? "🇩🇪" : c.country === "FR" ? "🇫🇷" : c.country === "GB" ? "🇬🇧" : c.country === "US" ? "🇺🇸" : "🌐",
            countryName: c.country === "TR" ? "Türkiye" : c.country === "US" ? "Amerika" : c.country === "DE" ? "Almanya" : c.country === "FR" ? "Fransa" : c.country === "GB" ? "İngiltere" : c.country || "Bilinmeyen Ülke",
            communities: c.communities || { active: 0, pending: 0 },
            advertising_platform: c.advertising_platform || "Telegram", // Eksik olan alan eklendi
            communityIcons: c.category && c.category.length > 0
              ? c.category.map(cat => ({ name: cat, icon: <Send className="h-4 w-4 text-blue-500"/> }))
              : [
                  { name: "Telegram", icon: <Send className="h-4 w-4 text-blue-500"/> },
                  { name: "Facebook", icon: <Facebook className="h-4 w-4 text-blue-800"/> }
                ],
            advertiser: { id: c.created_from || "-", name: "Fake Reklamveren", image: "" },
            relatedPages: [],
            lastUpdated: c.created_at ? new Date(c.created_at).toISOString().slice(0,10) : "",
            publishers: [
              { id: 101, name: "Ayşe Yıldız", avatar: "AY", status: "active" },
              { id: 102, name: "Bora Kaya", avatar: "BK", status: "pending" },
              { id: 103, name: "Can Demir", avatar: "CD", status: "active" }
            ] as PublisherInfo[]
          }))
          setCampaigns(mapped)
          
          // Toplam kampanya sayısını güncelle
          setTotalCampaigns(response.result.stats?.total || mapped.length);
        } else {
          setCampaigns([])
          setTotalCampaigns(0)
        }
      } catch (err) {
        setCampaigns([])
        setTotalCampaigns(0)
      } finally {
        setLoading(false)
      }
    }
    
    fetchCampaignData()
  }, [selectedTab, searchQuery, rowsPerPage, currentPage, sortOption, dateRange, selectedAdvertisers])

  const filteredCampaigns = campaigns // API'den filtreli geldiği için tekrar filtrelemeye gerek yok

  const campaignStats = {
    active: tabCounts.active,
    paused: tabCounts.paused,
    completed: tabCounts.completed,
    planned: tabCounts.planned,
    total: tabCounts.total,
    totalBudget: campaigns.reduce((sum, c) => sum + (c.budget || 0), 0),
    totalSpent: campaigns.reduce((sum, c) => sum + (c.spent || 0), 0)
  }
  
  const handleStatusChange = async (campaignId: number, action: string) => {
    try {
      const campaign = campaigns.find(c => c.id === campaignId);
      if (!campaign) {
        throw new Error("Kampanya bulunamadı");
      }

      let response;
      if (action === "pause" || action === "STOP") {
        response = await fetchCampaignPause({ campaign_id: campaign.campaign_id });
      } else if (action === "resume" || action === "ACTIVE") {
        response = await fetchCampaignResume({ campaign_id: campaign.campaign_id });
      } else {
        throw new Error("Geçersiz işlem");
      }

      if (response.status) {
        // Kampanya listesini güncelle
        setCampaigns(prevCampaigns => 
          prevCampaigns.map(c => 
            c.id === campaignId
              ? { ...c, status: action === "resume" || action === "ACTIVE" ? "active" : "paused" }
              : c
          )
        );

        toast({
          title: "Başarılı",
          description: action === "resume" || action === "ACTIVE"
            ? "Kampanya başarıyla aktifleştirildi."
            : "Kampanya başarıyla duraklatıldı.",
          variant: "default"
        });
      } else {
        throw new Error(response.desc || "İşlem başarısız");
      }
    } catch (error) {
      console.error("Kampanya durum değiştirme hatası:", error);
      toast({
        title: "Hata",
        description: error instanceof Error ? error.message : "Kampanya durumu değiştirilirken bir hata oluştu",
        variant: "destructive"
      });
    } finally {
      setShowConfirmDialog(false);
    }
  };
  
  const confirmStatusChange = (campaignId: number, newStatus: string) => {
    setSelectedCampaign(campaigns.find(c => c.id === campaignId) || null)
    setConfirmAction(newStatus)
    setShowConfirmDialog(true)
  }
  
  const toggleSelectAll = () => {
    if (selectedCampaigns.length === filteredCampaigns.length) {
      setSelectedCampaigns([])
    } else {
      setSelectedCampaigns(filteredCampaigns.map(c => c.id))
    }
  }
  
  const toggleSelectCampaign = (id: number) => {
    if (selectedCampaigns.includes(id)) {
      setSelectedCampaigns(selectedCampaigns.filter(cId => cId !== id))
    } else {
      setSelectedCampaigns([...selectedCampaigns, id])
    }
  }
  
  const toggleAdvertiserFilter = (advertiserId: string) => {
    if (selectedAdvertisers.includes(advertiserId)) {
      setSelectedAdvertisers([]) // Seçili olanı tıklarsa kaldır
    } else {
      setSelectedAdvertisers([advertiserId]) // Sadece bu advertiser'ı seç
    }
  }
  
  const resetFilters = () => {
    setSelectedAdvertisers([])
    setDateRange("all")
    setSortOption("desc")
  }
  
  const viewCampaignDetails = (campaignId: number) => {
    setSelectedCampaign(campaigns.find(c => c.id === campaignId) || null)
    setShowDetailDialog(true)
  }

  const editCampaign = (campaignId: number) => {
    const campaign = campaigns.find(c => c.id === campaignId)
    if (campaign?.campaign_id) {
      router.push(`/admin/campaigns/edit/${campaign.campaign_id}`)
    }
  }

  // Pagination fonksiyonları
  const totalPages = Math.ceil(totalCampaigns / rowsPerPage)

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  const handleRowsPerPageChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setRowsPerPage(Number(e.target.value))
    setCurrentPage(1)
  }

  return (
    <DashboardLayout>
      <DashboardHeader
        title="Kampanyalar"
        description="Kampanyalarınızı yönetin ve performanslarını takip edin"
        />
      <div className="space-y-6">

        {/* İstatistik Kartları */}
        <CampaignStatsCards stats={campaignStats} />

        {/* Tablar ve Filtreler */}
        <CampaignTabsAndFilters
          selectedTab={selectedTab}
          setSelectedTab={setSelectedTab}
          campaignStats={tabCounts}
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
          selectedAdvertisers={selectedAdvertisers}
          toggleAdvertiserFilter={toggleAdvertiserFilter}
          dateRange={dateRange}
          setDateRange={setDateRange}
          resetFilters={resetFilters}
          advertisers={advertisers}
          sortOption={sortOption}
          setSortOption={setSortOption}
        />

        {/* Kampanya Tablosu */}
        <CampaignTable
          campaigns={filteredCampaigns}
          selectedCampaigns={selectedCampaigns}
          onSelectAll={toggleSelectAll}
          onSelectCampaign={toggleSelectCampaign}
          onViewDetails={viewCampaignDetails}
          onConfirmStatusChange={confirmStatusChange}
          onEditCampaign={editCampaign}
        />

        {/* Pagination */}
        <div className="flex items-center justify-between mt-4">
          <div>
            <span className="text-sm text-muted-foreground">
              Toplam {totalCampaigns} kampanyadan {(currentPage - 1) * rowsPerPage + 1}-{Math.min(currentPage * rowsPerPage, totalCampaigns)} arası gösteriliyor
            </span>
          </div>
          <div className="flex items-center gap-2">
            <button
              className="px-2 py-1 border rounded disabled:opacity-50"
              onClick={() => handlePageChange(currentPage - 1)}
              disabled={currentPage === 1}
            >Önceki</button>
            <span className="text-sm">{currentPage} / {totalPages}</span>
            <button
              className="px-2 py-1 border rounded disabled:opacity-50"
              onClick={() => handlePageChange(currentPage + 1)}
              disabled={currentPage === totalPages}
            >Sonraki</button>
            <select
              className="ml-4 border rounded px-2 py-1"
              value={rowsPerPage}
              onChange={handleRowsPerPageChange}
            >
              <option value={10}>10</option>
              <option value={20}>20</option>
              <option value={50}>50</option>
            </select>
          </div>
        </div>

        {/* Dialoglar */}
        <CampaignActionDialogs
          showConfirmDialog={showConfirmDialog}
          setShowConfirmDialog={setShowConfirmDialog}
          confirmAction={confirmAction}
          selectedCampaign={selectedCampaign}
          handleConfirmAction={handleStatusChange}
          showDetailDialog={showDetailDialog}
          setShowDetailDialog={setShowDetailDialog}
        />
      </div>
    </DashboardLayout>
  )
} 