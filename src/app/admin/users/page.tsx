"use client"

import { useState, useEffect } from "react"
import { DashboardLayout } from "@/app/layout/dashboard-layout"
import { DashboardHeader } from "@/app/admin/components/dashboard/header"
import { UserStats } from "./components/UserStats"
import { UserFilters } from "./components/UserFilters"
import { UserTable } from "./components/UserTable"
import { UserPagination } from "./components/UserPagination"
import { UserModal } from "./user-modal"
import { User } from "./types"
import { fetchAdminUserList, fetchAdminUserStatistics, AdminUserStatisticsResponse } from "@/shared/services/admin/admin-list"

export default function EnhancedUsersPage() {
  const [selectedUsers, setSelectedUsers] = useState<(string | number)[]>([])
  const [searchQuery, setSearchQuery] = useState("")
  const [roleFilter, setRoleFilter] = useState("all")
  const [statusFilter, setStatusFilter] = useState("all")
  const [rowsPerPage, setRowsPerPage] = useState(10)
  const [currentPage, setCurrentPage] = useState(1)
  const [users, setUsers] = useState<User[]>([])
  const [totalUsers, setTotalUsers] = useState(0)
  const [selectedUser, setSelectedUser] = useState<User | null>(null)
  const [modalMode, setModalMode] = useState<"view" | "edit" | "delete">("view")
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [loading, setLoading] = useState(true)
  const [userStats, setUserStats] = useState<AdminUserStatisticsResponse["result"] | null>(null)

  // Kullanıcı verisini API'den çek
  useEffect(() => {
    setLoading(true);
    const fetchUsers = async () => {
      try {
        let roleParam: 'advertiser' | 'admin' | 'publisher' | undefined = undefined;
        if (roleFilter !== "all") {
          roleParam = roleFilter as 'advertiser' | 'admin' | 'publisher';
        }
        const params = {
          limit: rowsPerPage,
          skip: (currentPage - 1) * rowsPerPage,
          status: statusFilter === "all" ? undefined : statusFilter === "Aktif" ? true : false,
          role: roleParam,
          search: searchQuery || undefined
        };
        const response = await fetchAdminUserList(params);
        if (response.status && response.result?.data) {
          // API Kullanıcı tipini tanımla
          interface ApiUser {
            user_id: string | number;
            name: string;
            surname?: string;
            email: string;
            role: string;
            status: boolean;
            company_name?: string;
            sector?: string;
            created_at?: number;
          }
          
          // API'den gelen kullanıcıları User tipine dönüştür
          const mappedUsers: User[] = response.result.data.map((u: ApiUser) => ({
            id: u.user_id,
            name: u.name,
            surname: u.surname,
            email: u.email,
            role: u.role === "advertiser" ? "Reklamveren" : u.role === "publisher" ? "Yayıncı" : "Admin",
            status: u.status === true ? "active" : "inactive",
            company_name: u.company_name,
            sector: u.sector,
            created_at: u.created_at
          }));
          setUsers(mappedUsers);
          setTotalUsers(response.result.stats?.total || 0);
        } else {
          setUsers([]);
          setTotalUsers(0);
        }
      } catch (e) {
        setUsers([]);
        setTotalUsers(0);
      } finally {
        setLoading(false);
      }
    };
    fetchUsers();
  }, [rowsPerPage, currentPage, statusFilter, roleFilter, searchQuery]);

  // Kullanıcı istatistiklerini çek
  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await fetchAdminUserStatistics();
        if (response.status && response.result) {
          setUserStats(response.result);
        } else {
          setUserStats(null);
        }
      } catch {
        setUserStats(null);
      }
    };
    fetchStats();
  }, []);

  // Toplu seçim işlevi
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedUsers(users.map(user => user.id))
    } else {
      setSelectedUsers([])
    }
  }

  // Tekil seçim işlevi
  const handleSelectUser = (userId: string | number) => {
    setSelectedUsers(prev =>
      prev.includes(userId)
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    )
  }

  // Kullanıcı görüntüleme işlemi
  const handleViewUser = (userId: string | number) => {
    const user = users.find(u => u.id === userId)
    if (user) {
      setSelectedUser(user)
      setModalMode("view")
      setIsModalOpen(true)
    }
  }

  // Kullanıcı düzenleme işlemi
  const handleEditUser = (userId: string | number) => {
    const user = users.find(u => u.id === userId)
    if (user) {
      setSelectedUser(user)
      setModalMode("edit")
      setIsModalOpen(true)
    }
  }

  // Kullanıcı silme işlemi
  const handleDeleteUser = (userId: string | number) => {
    const user = users.find(u => u.id === userId)
    if (user) {
      setSelectedUser(user)
      setModalMode("delete")
      setIsModalOpen(true)
    }
  }

  // Kullanıcı silme işlemi
  const handleDeleteConfirm = (userId: string | number) => {
    setUsers(prevUsers => prevUsers.filter(u => u.id !== userId))
    setSelectedUsers(prev => prev.filter(id => id !== userId))
  }

  const handleAddUser = () => {
    setSelectedUser(null)
    setIsModalOpen(true)
  }

  const totalPages = Math.ceil(totalUsers / rowsPerPage);

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <DashboardHeader 
          title="Kullanıcılar" 
          description="Tüm kullanıcıları görüntüleyin ve yönetin"
        />

        <UserStats userStats={userStats} />

        <UserFilters
          selectedUsers={selectedUsers}
          searchQuery={searchQuery}
          roleFilter={roleFilter}
          statusFilter={statusFilter}
          rowsPerPage={rowsPerPage}
          onSearchChange={setSearchQuery}
          onRoleFilterChange={setRoleFilter}
          onStatusFilterChange={setStatusFilter}
          onRowsPerPageChange={(value) => {
            setRowsPerPage(value)
            setCurrentPage(1)
          }}
          onAddUser={handleAddUser}
        />

        <UserTable
          users={users}
          selectedUsers={selectedUsers}
          loading={loading}
          rowsPerPage={rowsPerPage}
          onSelectAll={handleSelectAll}
          onSelectUser={handleSelectUser}
          onViewUser={handleViewUser}
          onEditUser={handleEditUser}
          onDeleteUser={handleDeleteUser}
        />

        <UserPagination
          selectedUsers={selectedUsers}
          currentPage={currentPage}
          totalPages={totalPages}
          rowsPerPage={rowsPerPage}
          loading={loading}
          onPageChange={setCurrentPage}
          onRowsPerPageChange={(value) => {
            setRowsPerPage(value)
            setCurrentPage(1)
          }}
        />
      </div>

      <UserModal
        user={selectedUser}
        open={isModalOpen}
        mode={modalMode}
        onClose={() => {
          setIsModalOpen(false)
          setSelectedUser(null)
        }}
        onDelete={handleDeleteConfirm}
      />
    </DashboardLayout>
  )
} 