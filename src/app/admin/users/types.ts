import { Status } from "./helpers/status-badge"

interface Community {
  name: string;
  country: string;
  link: string;
  icon: React.ReactNode; // Keep ReactNode for flexibility or use string/enum for icon names
}

export interface User {
  id: string | number; // API'den gelen user_id
  name: string;
  surname?: string;
  email: string;
  role: "<PERSON><PERSON><PERSON><PERSON><PERSON>" | "<PERSON><PERSON><PERSON><PERSON><PERSON>" | "Admin";
  status: "active" | "pending" | "inactive";
  company_name?: string;
  sector?: string;
  created_at?: number;
  avatar?: string;
  phone?: string;
  address?: {
    street?: string;
    city?: string;
    state?: string;
    zipCode?: string;
    country?: string;
    flag?: string;
  };
  registrationDate?: string;
  lastLogin?: string;
  lastActive?: string;
  birthDate?: string;
  bio?: string;
  socialMedia?: {
    twitter?: string;
    linkedin?: string;
    instagram?: string;
  };
  campaigns?: number;
  earnings?: number;
  spends?: number;
  twoFactorEnabled?: boolean;
  permissions?: string[];
  communities?: Community[];
} 