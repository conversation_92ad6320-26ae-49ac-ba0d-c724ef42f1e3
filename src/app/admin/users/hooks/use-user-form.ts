import { useState, useEffect, useCallback } from "react"
import { User } from "../types"

export function useUserForm(user: User | null, onClose: () => void) {
  const [formData, setFormData] = useState<Partial<User>>(user || {})
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [hasChanges, setHasChanges] = useState(false)
  
  // Modal açıldığında formu sıfırla ve kullanıcı verilerini yükle
  useEffect(() => {
    if (user) {
      setFormData(user)
      setErrors({})
      setHasChanges(false)
    }
  }, [user])
  
  // Form gönderiminde işlenecek veriyi doğrula ve işle
  const validateForm = useCallback(() => {
    const newErrors: Record<string, string> = {}
    
    if (!formData.name) newErrors.name = "Ad alanı zorunludur"
    if (!formData.email) newErrors.email = "E-posta alanı zorunludur"
    if (formData.email && !formData.email.includes("@")) newErrors.email = "Geçerli bir e-posta adresi giriniz"
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }, [formData])
  
  // Form değerlerini değiştirme fonksiyonu
  const handleChange = (field: keyof User | "_reset", value: unknown) => {
    if (field === "_reset") {
      // Değişiklikleri geri al
      setFormData(user || {})
      setHasChanges(false)
      return
    }
    
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }))
    setHasChanges(true)
  }

  // Alt alanları değiştirme işlevi (örn. address.city)
  const handleNestedChange = (parent: keyof User, field: string, value: unknown) => {
    setFormData((prev) => {
      const parentValue = prev?.[parent]
      return {
        ...prev,
        [parent]: {
          ...(typeof parentValue === 'object' && parentValue !== null ? parentValue : {}),
          [field]: value,
        },
      }
    })
    setHasChanges(true)
  }

  // Kullanıcıyı güncelleme işlevi
  const handleSubmit = () => {
    setIsSubmitting(true)
    
    if (!validateForm()) {
      setIsSubmitting(false)
      return
    }
    
    // Formdan güncellenmiş kullanıcı bilgilerini al
    const updatedUser = {
      ...user,
      ...formData,
    }
    
    // TODO: Gerçek API çağrısı
    console.log("Kullanıcı güncelleniyor:", updatedUser)
    
    // Başarılı güncelleme simülasyonu
    setTimeout(() => {
      setIsSubmitting(false)
      onClose()
      // Başarılı güncelleme bildirimi için toast eklenebilir
    }, 1000)
  }

  // Formu iptal et ve değişiklikleri geri al
  const handleCancel = () => {
    if (hasChanges) {
      if (window.confirm("Değişiklikleriniz kaydedilmeyecek. Devam etmek istiyor musunuz?")) {
        onClose()
      }
    } else {
      onClose()
    }
  }

  return {
    formData,
    errors,
    isSubmitting,
    hasChanges,
    setFormData,
    handleChange,
    handleNestedChange,
    handleSubmit,
    handleCancel,
    validateForm
  }
} 