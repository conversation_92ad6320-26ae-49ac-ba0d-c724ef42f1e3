import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON>alogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { AlertCircle, Save, X, ArrowLeft, Loader2 } from "lucide-react"
import { User as UserType } from "./types"
import { getStatusColor } from "./helpers/status-badge"
import { useUserForm } from "./hooks/use-user-form"

// Tab bileşenleri
import { UserProfileTab } from "./tabs/user-profile-tab"
import { UserContactTab } from "./tabs/user-contact-tab"
import { UserCommunitiesTab } from "./tabs/user-communities-tab"
import { UserActivityTab } from "./tabs/user-activity-tab"
import { UserFinanceTab } from "./tabs/user-finance-tab"

interface UserModalProps {
  user: UserType | null
  open: boolean
  mode: "view" | "edit" | "delete"
  onClose: () => void
  onDelete?: (userId: string | number) => void
}

export function UserModal({ user, open, mode, onClose, onDelete }: UserModalProps) {
  const [activeTab, setActiveTab] = useState("profile")
  
  const {
    formData,
    errors,
    isSubmitting,
    hasChanges,
    handleChange,
    handleNestedChange,
    handleSubmit,
    handleCancel
  } = useUserForm(user, onClose)

  if (!user && mode !== "view") {
    return null
  }
  
  // Stil tanımları
  const tabContentStyle = {
    position: "absolute" as const,
    width: "100%",
    visibility: "hidden" as const,
    height: 0,
    overflow: "hidden"
  }
  
  const activeTabContentStyle = {
    position: "relative" as const,
    visibility: "visible" as const, 
    height: "auto"
  }

  return (
    <Dialog open={open} onOpenChange={handleCancel}>
      <DialogContent className={mode !== "delete" ? "sm:max-w-[700px] h-[650px] overflow-hidden flex flex-col" : "sm:max-w-[425px]"}>
        <DialogHeader>
          <div className="flex items-center justify-between">
            <div>
              <DialogTitle>
                {mode === "view" ? "Kullanıcı Detayları" :
                 mode === "edit" ? "Kullanıcı Düzenle" :
                 "Kullanıcı Sil"}
              </DialogTitle>
              <DialogDescription>
                {mode === "view" ? "Kullanıcı bilgilerini görüntüleyin" :
                 mode === "edit" ? "Kullanıcı bilgilerini düzenleyin" :
                 "Bu kullanıcıyı silmek istediğinizden emin misiniz?"}
              </DialogDescription>
            </div>
            {hasChanges && mode === "edit" && (
              <Badge className="bg-amber-100 text-amber-700 ml-2">Değişiklikler kaydedilmedi</Badge>
            )}
          </div>
        </DialogHeader>

        {mode === "delete" ? (
          <div className="py-4">
            <div className="flex items-center gap-4 mb-4">
              <div className="h-16 w-16 rounded-full bg-gray-100 flex items-center justify-center text-2xl font-bold">
                {user?.name[0]}
              </div>
              <div>
                <h3 className="font-medium">{user?.name}</h3>
                <p className="text-sm text-gray-500">{user?.email}</p>
                <Badge className={getStatusColor(user ? String(user.status) : undefined)}>{user?.status}</Badge>
              </div>
            </div>
            <div className="bg-red-50 p-4 rounded-md border border-red-200 mb-4">
              <div className="flex gap-2">
                <AlertCircle className="h-5 w-5 text-red-500" />
                <div>
                  <h4 className="font-medium text-red-700">Dikkat!</h4>
                  <p className="text-sm text-red-600">Bu işlem geri alınamaz. Kullanıcı silindikten sonra tüm verilerine erişiminiz kaybolacaktır.</p>
                </div>
              </div>
            </div>
          </div>
        ) : (
          <Tabs defaultValue="profile" className="w-full flex flex-col flex-1" value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid grid-cols-5 mb-6 sticky top-0 bg-white z-10">
              <TabsTrigger value="profile">Profil</TabsTrigger>
              <TabsTrigger value="contact">İletişim</TabsTrigger>
              <TabsTrigger value="communities">Topluluklar</TabsTrigger>
              <TabsTrigger value="activity">Aktivite</TabsTrigger>
              <TabsTrigger value="finance">Finans</TabsTrigger>
            </TabsList>
            
            <div className="flex-1 overflow-y-auto" style={{ height: "450px" }}>
              <TabsContent value="profile" className="space-y-4" style={activeTab === "profile" ? activeTabContentStyle : tabContentStyle}>
                <UserProfileTab 
                  user={user} 
                  mode={mode} 
                  formData={formData} 
                  errors={errors} 
                  handleChange={handleChange} 
                />
              </TabsContent>

              <TabsContent value="contact" className="space-y-4" style={activeTab === "contact" ? activeTabContentStyle : tabContentStyle}>
                <UserContactTab 
                  user={user} 
                  mode={mode} 
                  formData={formData} 
                  errors={errors}
                  handleChange={handleChange}
                  handleNestedChange={handleNestedChange}
                />
              </TabsContent>

              <TabsContent value="communities" className="space-y-4" style={activeTab === "communities" ? activeTabContentStyle : tabContentStyle}>
                <UserCommunitiesTab user={user} />
              </TabsContent>

              <TabsContent value="activity" className="space-y-4" style={activeTab === "activity" ? activeTabContentStyle : tabContentStyle}>
                <UserActivityTab user={user} />
              </TabsContent>

              <TabsContent value="finance" className="space-y-4" style={activeTab === "finance" ? activeTabContentStyle : tabContentStyle}>
                <UserFinanceTab user={user} />
              </TabsContent>
            </div>
          </Tabs>
        )}

        <DialogFooter className="flex items-center justify-between">
          <div>
            {mode === "edit" && (
              <Button variant="outline" size="sm" className="mr-2" onClick={() => handleChange("_reset", user || {})}>
                <ArrowLeft className="w-4 h-4 mr-2" />
                Değişiklikleri Geri Al
              </Button>
            )}
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={handleCancel}>
              {mode === "delete" ? "İptal" : "Kapat"}
            </Button>
            {mode === "delete" && onDelete && (
              <Button
                variant="destructive"
                onClick={() => {
                  onDelete(user!.id)
                  onClose()
                }}
              >
                <X className="w-4 h-4 mr-2" />
                Sil
              </Button>
            )}
            {mode === "edit" && (
              <Button 
                onClick={handleSubmit} 
                disabled={isSubmitting || !hasChanges}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Kaydediliyor...
                  </>
                ) : (
                  <>
                    <Save className="w-4 h-4 mr-2" />
                    Kaydet
                  </>
                )}
              </Button>
            )}
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
} 