import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Users, UsersIcon } from "lucide-react"
import { AdminUserStatisticsResponse } from "@/shared/services/admin/admin-list"

interface UserStatsProps {
  userStats: AdminUserStatisticsResponse["result"] | null
}

export function UserStats({ userStats }: UserStatsProps) {
  return (
    <div className="grid grid-cols-1 justify-center md:grid-cols-5 gap-4 mb-6">
      <Card>
        <CardContent className="p-4">
          <div className="mb-1">
            <div className="flex items-center justify-center gap-1">
              <UsersIcon className="h-4 w-4 text-indigo-500" />
              <span className="text-lg font-bold text-muted-foreground">Toplam Kullanıcı</span>
            </div>
          </div>
          <div className="mt-3 text-center">
            <span className="text-3xl font-bold">{userStats ? userStats.total_user : '-'}</span>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-4">
          <div className="mb-1">
            <div className="flex items-center justify-center gap-1">
              <Users className="h-4 w-4 text-green-500" />
              <span className="text-lg font-bold text-muted-foreground">Aktif Kullanıcı</span>
            </div>
          </div>
          <div className="flex flex-col text-center">
            <span className="text-3xl font-bold">{userStats ? userStats.active_user : '-'}</span>
            <span className="text-xs text-muted-foreground">{userStats && userStats.total_user ? ((userStats.active_user / userStats.total_user) * 100).toFixed(1) : '-'}% aktif kullanıcı</span>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardContent className="p-4">
          <div className="mb-1">
            <div className="flex items-center justify-center gap-1">
              <Users className="h-4 w-4 text-red-500" />
              <span className="text-lg font-bold text-muted-foreground">Pasif Kullanıcı</span>
            </div>
          </div>
          <div className="flex flex-col text-center">
            <span className="text-3xl font-bold">{userStats ? userStats.passive_user : '-'}</span>
            <span className="text-xs text-muted-foreground">{userStats && userStats.total_user ? ((userStats.passive_user / userStats.total_user) * 100).toFixed(1) : '-'}% engelli kullanıcı</span>
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 