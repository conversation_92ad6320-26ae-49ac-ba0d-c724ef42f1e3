import { Button } from "@/components/ui/button"
import { ChevronLeft, ChevronRight } from "lucide-react"

interface UserPaginationProps {
  selectedUsers: (string | number)[]
  currentPage: number
  totalPages: number
  rowsPerPage: number
  loading: boolean
  onPageChange: (page: number) => void
  onRowsPerPageChange: (value: number) => void
}

export function UserPagination({
  selectedUsers,
  currentPage,
  totalPages,
  rowsPerPage,
  loading,
  onPageChange,
  onRowsPerPageChange
}: UserPaginationProps) {
  return (
    <div className="flex items-center justify-between mt-4">
      <div className="flex-1 text-sm text-muted-foreground">
        {selectedUsers.length > 0 && (
          <p>{selectedUsers.length} kullanıcı seçildi</p>
        )}
      </div>
      <div className="flex items-center space-x-6 lg:space-x-8">
        <div className="flex items-center space-x-2">
          <p className="text-sm font-medium"><PERSON><PERSON> başına</p>
          <select
            className="h-8 w-[70px] rounded-md border border-input bg-transparent"
            value={rowsPerPage}
            onChange={(e) => onRowsPerPageChange(Number(e.target.value))}
          >
            <option value="10">10</option>
            <option value="25">25</option>
            <option value="50">50</option>
          </select>
        </div>
        <div className="flex w-[100px] items-center justify-center text-sm font-medium">
          Sayfa {currentPage} / {totalPages}
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            className="h-8 w-8 p-0"
            onClick={() => onPageChange(currentPage - 1)}
            disabled={currentPage === 1 || loading}
          >
            <span className="sr-only">Önceki sayfa</span>
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button
            variant="outline"
            className="h-8 w-8 p-0"
            onClick={() => onPageChange(currentPage + 1)}
            disabled={currentPage === totalPages || loading}
          >
            <span className="sr-only">Sonraki sayfa</span>
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  )
} 