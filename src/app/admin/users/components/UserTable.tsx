import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from "@/components/ui/table"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Eye, Edit, Trash, Users, X as XIcon } from "lucide-react"
import { Icons } from "@/components/icons"
import { cn } from "@/lib/utils"
import { User } from "@/app/admin/users/types"

interface UserTableProps {
  users: User[]
  selectedUsers: (string | number)[]
  loading: boolean
  rowsPerPage: number
  onSelectAll: (checked: boolean) => void
  onSelectUser: (userId: string | number) => void
  onViewUser: (userId: string | number) => void
  onEditUser: (userId: string | number) => void
  onDeleteUser: (userId: string | number) => void
}

export function UserTable({
  users,
  selectedUsers,
  loading,
  rowsPerPage,
  onSelectAll,
  onSelectUser,
  onViewUser,
  onEditUser,
  onDeleteUser
}: UserTableProps) {
  return (
    <div className="rounded-md border shadow-sm">
      <Table>
        <TableHeader>
          <TableRow className="bg-muted/50 hover:bg-muted/70">
            <TableHead className="w-12">
              <Checkbox 
                checked={selectedUsers.length === users.length && users.length > 0}
                onCheckedChange={(checked) => onSelectAll(!!checked)}
                aria-label="Tüm kullanıcıları seç"
                className="translate-y-[2px]"
              />
            </TableHead>
            <TableHead className="font-semibold">KULLANICI</TableHead>
            <TableHead className="font-semibold">ROL</TableHead>
            <TableHead className="font-semibold">DURUM</TableHead>
            <TableHead className="font-semibold">ÜLKE</TableHead>
            <TableHead className="font-semibold">TOPLULUK</TableHead>
            <TableHead className="text-right font-semibold">İŞLEMLER</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {loading ? (
            Array.from({ length: rowsPerPage }).map((_, idx) => (
              <TableRow key={`skeleton-${idx}`} className="animate-pulse">
                <TableCell><div className="h-4 w-4 rounded bg-muted" /></TableCell>
                <TableCell>
                  <div className="flex items-center gap-3">
                    <div className="h-10 w-10 rounded-full bg-muted" />
                    <div className="space-y-2">
                      <div className="h-4 w-24 rounded bg-muted" />
                      <div className="h-3 w-32 rounded bg-muted" />
                    </div>
                  </div>
                </TableCell>
                <TableCell><div className="h-5 w-20 rounded bg-muted" /></TableCell>
                <TableCell><div className="h-5 w-16 rounded bg-muted" /></TableCell>
                <TableCell><div className="h-4 w-24 rounded bg-muted" /></TableCell>
                <TableCell><div className="h-6 w-16 rounded bg-muted" /></TableCell>
                <TableCell><div className="h-8 w-24 ml-auto rounded bg-muted" /></TableCell>
              </TableRow>
            ))
          ) : users.length === 0 ? (
            <TableRow>
              <TableCell colSpan={7} className="h-24 text-center">
                <div className="flex flex-col items-center justify-center space-y-1">
                  <div className="relative mb-2 text-muted-foreground">
                    <Users className="h-8 w-8" />
                    <XIcon className="h-3 w-3 absolute -bottom-1 -right-1" />
                  </div>
                  <p className="text-lg font-medium text-muted-foreground">Kullanıcı Bulunamadı</p>
                  <p className="text-sm text-muted-foreground">
                    Arama kriterlerinize uygun kullanıcı bulunmamaktadır.
                  </p>
                </div>
              </TableCell>
            </TableRow>
          ) : (
            users.map((user) => (
              <TableRow key={user.id} className="group hover:bg-muted/50">
                <TableCell>
                  <Checkbox 
                    checked={selectedUsers.includes(user.id)}
                    onCheckedChange={(checked) => checked && onSelectUser(user.id)}
                    aria-label={`${user.name} kullanıcısını seç`}
                    className="translate-y-[2px]"
                  />
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-3">
                    <Avatar className="h-10 w-10 bg-primary/10">
                      <AvatarFallback className="bg-primary/10 text-primary">
                        {user.name?.split(" ").map(n => n[0]).join("").toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="font-medium leading-none">{user.name}</p>
                      <p className="text-sm text-muted-foreground mt-1">{user.email}</p>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge
                    className={cn(
                      "font-medium text-white capitalize",
                      user.role === "Reklamveren" && "bg-blue-600 hover:bg-blue-700",
                      user.role === "Yayıncı" && "bg-purple-600 hover:bg-purple-700",
                      user.role === "Admin" && "bg-rose-600 hover:bg-rose-700"
                    )}
                  >
                    {user.role.toLowerCase()}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Badge
                    className={cn(
                      "font-medium",
                      user.status === "active" && "bg-green-50 text-green-700 hover:bg-green-100",
                      user.status === "pending" && "bg-amber-50 text-amber-700 hover:bg-amber-100",
                      user.status === "inactive" && "bg-red-50 text-red-700 hover:bg-red-100"
                    )}
                  >
                    {user.status === "active" ? "Aktif" : 
                     user.status === "pending" ? "Beklemede" : "Pasif"}
                  </Badge>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <span className="text-lg">{user.address?.flag || "🌐"}</span>
                    <span className="text-sm">{user.address?.country || "-"}</span>
                  </div>
                </TableCell>
                <TableCell>
                  {user.role === "Yayıncı" ? (
                    <div className="flex items-center gap-2">
                      <div className="rounded-md bg-blue-50 p-1" title="Telegram">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                          <circle cx="12" cy="12" r="12" fill="#229ED9"/>
                          <path d="M17.5 7.5L15.5 17.5C15.3 18.3 14.8 18.5 14.1 18.1L11.1 15.9L9.7 17.2C9.5 17.4 9.3 17.6 9 17.6L9.2 14.5L15.2 9.2C15.5 8.9 15.1 8.8 14.7 9.1L7.8 13.2L4.8 12.3C4.1 12.1 4.1 11.6 5 11.3L16.5 7.2C17.2 7 17.7 7.4 17.5 7.5Z" fill="#fff"/>
                        </svg>
                      </div>
                      <div className="rounded-md bg-orange-50 p-1" title="Reddit">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                          <circle cx="12" cy="12" r="12" fill="#FF4500"/>
                          <path d="M12 8.5C13.5 8.5 15 9.5 15 11M12 8.5C10.5 8.5 9 9.5 9 11M12 8.5V6M12 8.5L12 6" stroke="#FF4500" strokeWidth="1.5" strokeLinecap="round"/>
                          <circle cx="8.5" cy="13.5" r="1.5" fill="#fff"/>
                          <circle cx="15.5" cy="13.5" r="1.5" fill="#fff"/>
                          <ellipse cx="12" cy="17" rx="3" ry="1.5" fill="#fff"/>
                        </svg>
                      </div>
                    </div>
                  ) : user.role === "Reklamveren" ? (
                    <div className="rounded-md bg-green-50 p-1 w-fit" title="Reklamveren">
                      <Icons.wallet className="h-5 w-5 text-green-600" />
                    </div>
                  ) : (
                    <div className="rounded-md bg-gray-50 p-1 w-fit" title="Admin">
                      <Icons.shield className="h-5 w-5 text-gray-600" />
                    </div>
                  )}
                </TableCell>
                <TableCell>
                  <div className="flex items-center justify-end gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
                    <Button 
                      variant="ghost" 
                      size="icon"
                      className="h-8 w-8 hover:bg-blue-50 hover:text-blue-600"
                      onClick={() => onViewUser(user.id)}
                    >
                      <Eye className="h-4 w-4" />
                      <span className="sr-only">Görüntüle</span>
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="icon"
                      className="h-8 w-8 hover:bg-amber-50 hover:text-amber-600"
                      onClick={() => onEditUser(user.id)}
                    >
                      <Edit className="h-4 w-4" />
                      <span className="sr-only">Düzenle</span>
                    </Button>
                    <Button 
                      variant="ghost" 
                      size="icon"
                      className="h-8 w-8 hover:bg-red-50 hover:text-red-600"
                      onClick={() => onDeleteUser(user.id)}
                    >
                      <Trash className="h-4 w-4" />
                      <span className="sr-only">Sil</span>
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  )
} 