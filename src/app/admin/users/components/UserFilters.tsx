import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Search, ChevronDown, Plus as PlusIcon } from "lucide-react"

interface UserFiltersProps {
  selectedUsers: (string | number)[]
  searchQuery: string
  roleFilter: string
  statusFilter: string
  rowsPerPage: number
  onSearchChange: (value: string) => void
  onRoleFilterChange: (value: string) => void
  onStatusFilterChange: (value: string) => void
  onRowsPerPageChange: (value: number) => void
  onAddUser: () => void
}

export function UserFilters({
  selectedUsers,
  searchQuery,
  roleFilter,
  statusFilter,
  rowsPerPage,
  onSearchChange,
  onRoleFilterChange,
  onStatusFilterChange,
  onRowsPerPageChange,
  onAddUser
}: UserFiltersProps) {
  return (
    <>
      <div className="flex items-center justify-start mb-6">
        <div className="flex items-center gap-2">
          <Button variant="outline" className="flex items-center gap-1">
            <span className="text-muted-foreground">{selectedUsers.length} Seçildi</span>
          </Button>
        
          <Button className="flex items-center gap-1" onClick={onAddUser}>
            <PlusIcon className="h-4 w-4" />
            Yeni Kullanıcı
          </Button>
        </div>
      </div>

      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
        <div className="relative w-full md:w-auto">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="İsim, email veya takım ara..."
            className="pl-9 w-full md:w-[300px]"
            value={searchQuery}
            onChange={(e) => onSearchChange(e.target.value)}
          />
        </div>

        <div className="flex items-center gap-2 w-full md:w-auto">
          <select 
            className="border rounded-md px-4 py-2 bg-white w-full md:w-auto"
            value={roleFilter}
            onChange={(e) => onRoleFilterChange(e.target.value)}
          >
            <option value="all">Tüm Roller</option>
            <option value="advertiser">Reklamveren</option>
            <option value="publisher">Yayıncı</option>
            <option value="admin">Admin</option>
          </select>

          <select 
            className="border rounded-md px-4 py-2 bg-white w-full md:w-auto"
            value={statusFilter}
            onChange={(e) => onStatusFilterChange(e.target.value)}
          >
            <option value="all">Tüm Durumlar</option>
            <option value="Aktif">Aktif</option>
            <option value="Pasif">Pasif</option>
          </select>

          <select 
            className="border rounded-md px-4 py-2 bg-white w-full md:w-auto"
            value={rowsPerPage}
            onChange={(e) => onRowsPerPageChange(Number(e.target.value))}
          >
            <option value={10}>10 Satır</option>
            <option value={25}>25 Satır</option>
            <option value={50}>50 Satır</option>
          </select>
        </div>
      </div>
    </>
  )
} 