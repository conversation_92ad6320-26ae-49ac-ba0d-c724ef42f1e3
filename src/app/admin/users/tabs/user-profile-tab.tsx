import { User, Briefcase } from "lucide-react"
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { cn } from "@/lib/utils"
import { User as UserType } from "../types"
import { ModalSectionTitle } from "../helpers/modal-section-title"
import { StatusBadge } from "../helpers/status-badge"
import dynamic from "next/dynamic"

const ClientDate = dynamic(() => import("@/components/ClientDate"), { ssr: false });

interface UserProfileTabProps {
  user: UserType | null
  mode: "view" | "edit"
  formData: Partial<UserType>
  errors: Record<string, string>
  handleChange: (field: keyof UserType | '_reset', value: unknown) => void
}

export function UserProfileTab({ 
  user, 
  mode, 
  formData, 
  errors, 
  handleChange 
}: UserProfileTabProps) {
  return (
    <>
      {mode === "edit" ? (
        // Düzenleme formu
        <>
          <div className="flex items-center gap-4 mb-4">
            <div className="h-20 w-20 rounded-full bg-gray-100 flex items-center justify-center text-3xl font-bold">
              {formData.name?.[0] || user?.name?.[0]}
            </div>
            <div className="flex-1">
              <div className="space-y-2">
                <Label htmlFor="name">Ad Soyad</Label>
                <Input 
                  id="name" 
                  value={formData.name || ""} 
                  onChange={(e) => handleChange("name", e.target.value)}
                  className={cn(errors.name && "border-red-500")}
                />
                {errors.name && <p className="text-red-500 text-xs">{errors.name}</p>}
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>
                  <ModalSectionTitle icon={Briefcase}>İş Bilgileri</ModalSectionTitle>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="role">Rol</Label>
                  <Select 
                    value={formData.role || ""} 
                    onValueChange={(value) => handleChange("role", value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Rol seçin" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Yönetici">Yönetici</SelectItem>
                      <SelectItem value="Editör">Editör</SelectItem>
                      <SelectItem value="Reklamveren">Reklamveren</SelectItem>
                      <SelectItem value="Yayıncı">Yayıncı</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="status">Durum</Label>
                  <Select 
                    value={formData.status || ""} 
                    onValueChange={(value) => handleChange("status", value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Durum seçin" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Aktif">Aktif</SelectItem>
                      <SelectItem value="Beklemede">Beklemede</SelectItem>
                      <SelectItem value="Engelli">Engelli</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle>
                  <ModalSectionTitle icon={User}>Kişisel Bilgiler</ModalSectionTitle>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="birthDate">Doğum Tarihi</Label>
                  <Input 
                    id="birthDate" 
                    type="date" 
                    value={formData.birthDate ? formData.birthDate.split('T')[0] : ""} 
                    onChange={(e) => handleChange("birthDate", e.target.value)}
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="bio">Hakkında</Label>
                  <Textarea 
                    id="bio" 
                    value={formData.bio || ""} 
                    onChange={(e) => handleChange("bio", e.target.value)}
                    rows={3}
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        </>
      ) : (
        // Görüntüleme modu
        <>
          <div className="flex items-center gap-4 mb-4">
            <div className="h-20 w-20 rounded-full bg-gray-100 flex items-center justify-center text-3xl font-bold">
              {user?.name[0]}
            </div>
            <div>
              <h3 className="text-xl font-medium">{user?.name}</h3>
              <div className="flex items-center gap-2 mt-1">
                <Badge variant="outline">{user?.role}</Badge>
                <StatusBadge status={user?.status || "inactive"} />
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>
                  <ModalSectionTitle icon={Briefcase}>İş Bilgileri</ModalSectionTitle>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div>
                  <p className="text-sm font-medium text-gray-500">Rol</p>
                  <p>{user?.role || "Bilgi yok"}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Kampanya Sayısı</p>
                  <p>{user?.campaigns !== undefined ? user.campaigns : "Bilgi yok"}</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle>
                  <ModalSectionTitle icon={User}>Kişisel Bilgiler</ModalSectionTitle>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                <div>
                  <p className="text-sm font-medium text-gray-500">Doğum Tarihi</p>
                  <p><ClientDate dateString={user?.birthDate} /></p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-500">Hakkında</p>
                  <p className="text-sm">{user?.bio || "Kullanıcı hakkında bilgi bulunmuyor."}</p>
                </div>
              </CardContent>
            </Card>
          </div>
        </>
      )}
    </>
  )
} 