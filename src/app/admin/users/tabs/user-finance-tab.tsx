import { Wallet, Bar<PERSON>hart2 } from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { User as UserType } from "../types"

interface UserFinanceTabProps {
  user: UserType | null
}

export function UserFinanceTab({
  user
}: UserFinanceTabProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-base flex items-center gap-2">
            <Wallet className="h-4 w-4" />
            Finansal Özet
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          {user?.role === "Reklamveren" ? (
            <>
              <div>
                <p className="text-sm font-medium text-gray-500">Toplam Harcama</p>
                <p className="text-xl font-bold">{user?.spends !== undefined ? `${user.spends.toLocaleString('tr-TR')} ₺` : "Bilgi yok"}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Bu Ay</p>
                <p className="text-lg">2.850 ₺</p>
              </div>
            </>
          ) : user?.role === "Yayıncı" ? (
            <>
              <div>
                <p className="text-sm font-medium text-gray-500">Toplam Kazanç</p>
                <p className="text-xl font-bold">{user?.earnings !== undefined ? `${user.earnings.toLocaleString('tr-TR')} ₺` : "Bilgi yok"}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Bu Ay</p>
                <p className="text-lg">1.235 ₺</p>
              </div>
            </>
          ) : (
            <p className="text-sm text-gray-500">Finansal bilgiler bu kullanıcı rolü için uygulanabilir değil</p>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-base flex items-center gap-2">
            <BarChart2 className="h-4 w-4" />
            Performans
          </CardTitle>
        </CardHeader>
        <CardContent>
          {user?.role === "Reklamveren" ? (
            <div className="space-y-3">
              <div>
                <p className="text-sm font-medium text-gray-500">Kampanya Sayısı</p>
                <p>{user?.campaigns || 0}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Toplam İzlenme</p>
                <p>135.842</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Ortalama CTR</p>
                <p>2.8%</p>
              </div>
            </div>
          ) : user?.role === "Yayıncı" ? (
            <div className="space-y-3">
              <div>
                <p className="text-sm font-medium text-gray-500">Kampanya Sayısı</p>
                <p>{user?.campaigns || 0}</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Toplam Gösterim</p>
                <p>284.521</p>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-500">Ortalama RPM</p>
                <p>4.32 ₺</p>
              </div>
            </div>
          ) : (
            <p className="text-sm text-gray-500">Performans bilgileri bu kullanıcı rolü için uygulanabilir değil</p>
          )}
        </CardContent>
      </Card>
    </div>
  )
} 