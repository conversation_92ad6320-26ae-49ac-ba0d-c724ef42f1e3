import { Mail, Phone, MapPin } from "lucide-react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { cn } from "@/lib/utils"
import { User as UserType } from "../types"

interface UserContactTabProps {
  user: UserType | null
  mode: "view" | "edit"
  formData: Partial<UserType>
  errors: Record<string, string>
  handleChange: (field: keyof UserType | '_reset', value: unknown) => void
  handleNestedChange: (parent: keyof UserType, field: string, value: unknown) => void
}

export function UserContactTab({
  user,
  mode,
  formData,
  errors,
  handleChange,
  handleNestedChange
}: UserContactTabProps) {
  return (
    <>
      {mode === "edit" ? (
        // Düzenleme modu için iletişim formu
        <>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base flex items-center gap-2">
                <Mail className="h-4 w-4" />
                İletişim Bilgileri
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">E-posta Adresi</Label>
                <Input 
                  id="email" 
                  type="email"
                  value={formData.email || ""} 
                  onChange={(e) => handleChange("email", e.target.value)}
                  className={cn(errors.email && "border-red-500")}
                />
                {errors.email && <p className="text-red-500 text-xs">{errors.email}</p>}
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="phone">Telefon</Label>
                <Input 
                  id="phone" 
                  value={formData.phone || ""} 
                  onChange={(e) => handleChange("phone", e.target.value)}
                />
              </div>
              
              <div className="space-y-4">
                <Label>Adres</Label>
                
                <div className="space-y-2">
                  <Label htmlFor="street" className="text-xs">Sokak</Label>
                  <Input 
                    id="street" 
                    value={formData.address?.street || ""} 
                    onChange={(e) => handleNestedChange("address", "street", e.target.value)}
                  />
                </div>
                
                <div className="grid grid-cols-2 gap-2">
                  <div className="space-y-2">
                    <Label htmlFor="city" className="text-xs">Şehir</Label>
                    <Input 
                      id="city" 
                      value={formData.address?.city || ""} 
                      onChange={(e) => handleNestedChange("address", "city", e.target.value)}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="state" className="text-xs">İlçe</Label>
                    <Input 
                      id="state" 
                      value={formData.address?.state || ""} 
                      onChange={(e) => handleNestedChange("address", "state", e.target.value)}
                    />
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-2">
                  <div className="space-y-2">
                    <Label htmlFor="zipCode" className="text-xs">Posta Kodu</Label>
                    <Input 
                      id="zipCode" 
                      value={formData.address?.zipCode || ""} 
                      onChange={(e) => handleNestedChange("address", "zipCode", e.target.value)}
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="country" className="text-xs">Ülke</Label>
                    <Input 
                      id="country" 
                      value={formData.address?.country || ""} 
                      onChange={(e) => handleNestedChange("address", "country", e.target.value)}
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base">Sosyal Medya</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="twitter" className="text-xs">Twitter</Label>
                <div className="flex items-center">
                  <span className="mr-2 text-gray-500">@</span>
                  <Input 
                    id="twitter" 
                    value={formData.socialMedia?.twitter || ""} 
                    onChange={(e) => handleNestedChange("socialMedia", "twitter", e.target.value)}
                  />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="linkedin" className="text-xs">LinkedIn</Label>
                <Input 
                  id="linkedin" 
                  value={formData.socialMedia?.linkedin || ""} 
                  onChange={(e) => handleNestedChange("socialMedia", "linkedin", e.target.value)}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="instagram" className="text-xs">Instagram</Label>
                <div className="flex items-center">
                  <span className="mr-2 text-gray-500">@</span>
                  <Input 
                    id="instagram" 
                    value={formData.socialMedia?.instagram || ""} 
                    onChange={(e) => handleNestedChange("socialMedia", "instagram", e.target.value)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        </>
      ) : (
        // Görüntüleme modu
        <>
          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base flex items-center gap-2">
                <Mail className="h-4 w-4" />
                İletişim Bilgileri
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-start gap-3">
                <Mail className="h-5 w-5 text-gray-500 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-gray-500">E-posta Adresi</p>
                  <p>{user?.email || "Bilgi yok"}</p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <Phone className="h-5 w-5 text-gray-500 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-gray-500">Telefon</p>
                  <p>{user?.phone || "Bilgi yok"}</p>
                </div>
              </div>
              <div className="flex items-start gap-3">
                <MapPin className="h-5 w-5 text-gray-500 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-gray-500">Adres</p>
                  {user?.address ? (
                    <>
                      <p>{user.address.street}</p>
                      <p>{`${user.address.city}, ${user.address.state} ${user.address.zipCode}`}</p>
                      <p>{user.address.country}</p>
                    </>
                  ) : (
                    <p>Bilgi yok</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="pb-2">
              <CardTitle className="text-base">Sosyal Medya</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              {user?.socialMedia?.twitter && (
                <div>
                  <p className="text-sm font-medium text-gray-500">Twitter</p>
                  <p className="text-blue-600">@{user.socialMedia.twitter}</p>
                </div>
              )}
              {user?.socialMedia?.linkedin && (
                <div>
                  <p className="text-sm font-medium text-gray-500">LinkedIn</p>
                  <p className="text-blue-600">{user.socialMedia.linkedin}</p>
                </div>
              )}
              {user?.socialMedia?.instagram && (
                <div>
                  <p className="text-sm font-medium text-gray-500">Instagram</p>
                  <p className="text-blue-600">@{user.socialMedia.instagram}</p>
                </div>
              )}
              {(!user?.socialMedia?.twitter && !user?.socialMedia?.linkedin && !user?.socialMedia?.instagram) && (
                <p className="text-sm text-gray-500">Sosyal medya bilgisi bulunmuyor</p>
              )}
            </CardContent>
          </Card>
        </>
      )}
    </>
  )
} 