import {  Send, <PERSON><PERSON>ointer<PERSON>lick, SquareArrowOutUpRight } from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { User as UserType } from "../types"

interface Community {
  name: string;
  country: string;
  link: string;
  icon: React.ReactNode;
}

interface UserCommunitiesTabProps {
  user: UserType | null
}

export function UserCommunitiesTab({ user }: UserCommunitiesTabProps) {
  // Örnek topluluklar
  const communities: Community[] = user?.communities || [
    {
      name: "Telegram",
      country: "Türkiye",
      link: "https://t.me/ornek",
      icon: <Send className="h-5 w-5 text-blue-500" />
    }
  ]

  return (
    <>
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-base flex items-center gap-2">
            <MousePointerClick className="h-4 w-4" />
            Topluluklar
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {communities.length === 0 ? (
            <p className="text-sm text-gray-500">Kullanıcıya ait topluluk bulunmuyor.</p>
          ) : (
            <div className="space-y-3">
              {communities.map((community: Community, idx: number) => (
                <div key={idx} className="flex items-center gap-4 p-3 border rounded-md bg-gray-50">
                  <div>{community.icon}</div>
                  <div className="flex-1">
                    <div className="font-medium">{community.name}</div>
                    <div className="text-xs text-muted-foreground">{community.country}</div>
                  </div>
                  <a href={community.link} target="_blank" rel="noopener noreferrer" className="text-blue-600 underline flex items-center gap-1">
                    <SquareArrowOutUpRight className="h-6 w-6 inline" />
                  </a>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </>
  )
} 