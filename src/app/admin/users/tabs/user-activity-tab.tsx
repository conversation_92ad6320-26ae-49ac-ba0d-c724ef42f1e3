import { Clock, FileText } from "lucide-react"
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { User as UserType } from "../types"
import dynamic from "next/dynamic"

const ClientDate = dynamic(() => import("@/components/ClientDate"), { ssr: false });

interface UserActivityTabProps {
  user: UserType | null
}

export function UserActivityTab({
  user
}: UserActivityTabProps) {
  return (
    <>
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-base flex items-center gap-2">
            <Clock className="h-4 w-4" />
            Hesap Aktivitesi
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div>
            <p className="text-sm font-medium text-gray-500">Kayıt Tarihi</p>
            <p><ClientDate dateString={user?.registrationDate} /></p>
          </div>
          <div>
            <p className="text-sm font-medium text-gray-500"><PERSON></p>
            <p><ClientDate dateString={user?.lastLogin} /></p>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-base flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Son Hareketler
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="border-l-2 border-green-500 pl-4 relative">
              <div className="absolute w-2 h-2 bg-green-500 rounded-full -left-[5px] top-1.5"></div>
              <p className="font-medium">Profil güncellendi</p>
              <p className="text-sm text-gray-500">10 Haziran 2023, 14:23111</p>
            </div>
            <div className="border-l-2 border-blue-500 pl-4 relative">
              <div className="absolute w-2 h-2 bg-blue-500 rounded-full -left-[5px] top-1.5"></div>
              <p className="font-medium">Yeni kampanya oluşturuldu</p>
              <p className="text-sm text-gray-500">5 Haziran 2023, 09:15</p>
            </div>
            <div className="border-l-2 border-gray-300 pl-4 relative">
              <div className="absolute w-2 h-2 bg-gray-300 rounded-full -left-[5px] top-1.5"></div>
              <p className="font-medium">Oturum açıldı</p>
              <p className="text-sm text-gray-500">1 Haziran 2023, 10:03</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </>
  )
} 