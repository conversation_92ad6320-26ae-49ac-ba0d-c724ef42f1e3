import { Badge, type BadgeProps } from "@/components/ui/badge"

export type Status = "active" | "pending" | "inactive" | "banned"

// Badge varyant tipi (badge.tsx'den alınabilir veya burada tanımlanabilir)
type BadgeVariant = BadgeProps["variant"];

interface StatusBadgeProps {
  status: Status
}

export function StatusBadge({ status }: StatusBadgeProps) {
  const getVariant = (status: Status): BadgeVariant => {
    switch (status) {
      case "active":
        return "success"
      case "pending":
        return "warning"
      case "inactive":
        return "secondary"
      case "banned":
        return "destructive"
      default:
        return "default"
    }
  }

  const getLabel = (status: Status) => {
    switch (status) {
      case "active":
        return "Aktif"
      case "pending":
        return "Beklemede"
      case "inactive":
        return "Pasif"
      case "banned":
        return "Yasaklı"
      default:
        return status
    }
  }

  return (
    <Badge variant={getVariant(status)}>
      {getLabel(status)}
    </Badge>
  )
}

// Kullanıcı durum badge'i için stil sınıfını döndürür
export function getStatusColor(status?: string): string {
  return status === "Aktif" ? "bg-green-100 text-green-700" :
    status === "Beklemede" ? "bg-amber-100 text-amber-700" :
    status === "Engelli" ? "bg-red-100 text-red-700" :
    "bg-gray-100 text-gray-700"
} 