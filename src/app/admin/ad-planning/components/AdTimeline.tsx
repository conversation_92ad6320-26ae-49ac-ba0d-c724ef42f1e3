import React, { useMemo } from "react";
import AdCard from "./AdCard";

interface Publisher {
  id: number;
  name: string;
  type?: string;
  subscribers?: number;
  engagementRate?: number;
  status: string;
  url?: string;
  category?: string;
}

interface Ad {
  id: number;
  name: string;
  time: string;
  brand?: string;
  category?: string;
  budget?: number;
  spent?: number;
  reach?: number;
  publishers: Publisher[];
  isoDate: string;
}

interface Campaign {
  date: string;
  isoDate: string;
  value: string;
  ads: Ad[];
}

interface AdTimelineProps {
  campaign: Campaign;
  timeSlots: string[];
  search: string;
  searchType: string;
  statusFilter: string;
  showEmptySlots: boolean;
  expandedAds: number[];
  setExpandedAds: (ids: number[]) => void;
  handleApprove: (adId: number, publisherId: number) => void;
  handleReject: (adId: number, publisherId: number) => void;
  handleBulkApprove: (adId: number, publisherIds: number[]) => void;
  handleBulkReject: (adId: number, publisherIds: number[]) => void;
  onOpenEditModal: (ad: Ad) => void;
  isLoading?: boolean; // Yeni prop
}

const AdTimeline: React.FC<AdTimelineProps> = ({
  campaign,
  timeSlots,
  search,
  searchType,
  statusFilter,
  showEmptySlots,
  expandedAds,
  setExpandedAds,
  handleApprove,
  handleReject,
  handleBulkApprove,
  handleBulkReject,
  onOpenEditModal,
  isLoading = false,
}) => {
  // Filtreleme işlemlerini memoize edelim
  const filteredSlots = useMemo(() => {
    return (showEmptySlots ? timeSlots : Array.from(new Set(campaign.ads.map(ad => ad.time))).sort());
  }, [showEmptySlots, timeSlots, campaign.ads]);

  // Slot başına düşen reklamları memoize edelim
  const getFilteredAdsAtSlot = useMemo(() => {
    return (slot: string) => {
      let adsAtSlot = campaign.ads.filter((ad) => ad.time === slot);
      
      if (search) {
        const searchTerm = search.toLowerCase();
        adsAtSlot = adsAtSlot.filter(ad => {
          const nameMatch = ad.name.toLowerCase().includes(searchTerm);
          const brandMatch = ad.brand?.toLowerCase().includes(searchTerm);
          const categoryMatch = ad.category?.toLowerCase().includes(searchTerm);
          const publisherMatch = ad.publishers.some(p => p.name.toLowerCase().includes(searchTerm));
          const communityMatch = (ad as any).communityName && (ad as any).communityName.toLowerCase().includes(searchTerm);

          if (searchType === "all") return nameMatch || brandMatch || categoryMatch || publisherMatch || communityMatch;
          if (searchType === "telegram") return ad.publishers.some(p => p.type === "Telegram" && p.name.toLowerCase().includes(searchTerm));
          if (searchType === "brand") return brandMatch;
          if (searchType === "community") return categoryMatch || communityMatch;
          return false;
        });
      }

      if (statusFilter !== "all") {
        adsAtSlot = adsAtSlot.filter(ad => ad.publishers.some(p => p.status === statusFilter));
      }

      return adsAtSlot;
    };
  }, [campaign.ads, search, searchType, statusFilter]);

  if (isLoading) {
    return (
      <div className="mt-6 space-y-8 animate-pulse">
        {[1, 2, 3].map((i) => (
          <div key={i} className="flex items-stretch">
            <div className="flex flex-col items-center mr-6 w-20 flex-shrink-0">
              <div className="w-16 h-16 rounded-full bg-slate-200"></div>
              <div className="w-0.5 flex-grow mt-2 bg-slate-200" style={{ minHeight: '60px' }}></div>
            </div>
            <div className="flex-1 grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="h-[200px] bg-slate-100 rounded-xl"></div>
              <div className="h-[200px] bg-slate-100 rounded-xl"></div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="mt-6">
      {filteredSlots.map((slot, idx, arr) => {
        const adsAtSlot = getFilteredAdsAtSlot(slot);
        
        if (adsAtSlot.length === 0 && !showEmptySlots) {
          return null;
        }

        return (
          <div key={slot} className="flex items-stretch mb-12 last:mb-0 group">
            <div className="flex flex-col items-center mr-8 w-24 flex-shrink-0">
              <div 
                className={`relative flex items-center justify-center w-20 h-20 rounded-2xl font-bold text-xl shadow-lg will-change-transform
                            ${adsAtSlot.length > 0 
                              ? "bg-gradient-to-br from-blue-500 to-blue-600 text-white border-2 border-blue-400" 
                              : "bg-gradient-to-br from-slate-50 to-slate-100 text-slate-500 border-2 border-slate-200"}
                            hover:scale-105 hover:rotate-2 transition-transform duration-200`}
                role="status"
                aria-label={`${slot} saatindeki reklamlar`}
              >
                {slot}
                {adsAtSlot.length > 0 && (
                  <span 
                    className="absolute -top-3 -right-3 flex h-8 w-8 items-center justify-center rounded-xl bg-gradient-to-br from-red-500 to-red-600 
                             text-sm font-bold text-white shadow-lg border-2 border-white will-change-transform
                             hover:scale-105 transition-transform duration-200"
                    aria-label={`${adsAtSlot.length} reklam`}
                  >
                    {adsAtSlot.length}
                  </span>
                )}
              </div>
              {idx < arr.length -1 && (
                 <div 
                   className={`w-1 flex-grow mt-4 rounded-full transition-transform duration-200
                              ${adsAtSlot.length > 0 
                                ? "bg-gradient-to-b from-blue-400 to-blue-200 group-hover:scale-x-125" 
                                : "bg-gradient-to-b from-slate-200 to-slate-100"}`} 
                   style={{ minHeight: '80px' }}
                 />
              )}
            </div>

            <div className="flex-1 transition-transform duration-200 group-hover:translate-x-1">
              {adsAtSlot.length === 0 ? (
                <div className="border-2 rounded-2xl bg-gradient-to-br from-slate-50 to-white shadow-md p-8 min-h-[120px] 
                              flex flex-col items-center justify-center group/slot hover:border-blue-300 transition-all duration-200
                              hover:shadow-lg will-change-transform hover:-translate-y-0.5">
                  <div className="w-16 h-16 rounded-xl bg-slate-100 flex items-center justify-center mb-4 
                                transition-all duration-200 group-hover/slot:bg-blue-50 group-hover/slot:rotate-[10deg]">
                    <span className="text-3xl text-slate-400 group-hover/slot:text-blue-400">+</span>
                  </div>
                  <span className="text-slate-400 group-hover/slot:text-blue-500 transition-colors text-sm text-center font-medium">
                    Bu zaman diliminde aktif reklam bulunmuyor
                    <br />
                    <span className="text-xs opacity-75 mt-1 inline-block">Yeni reklam eklemek için tıklayın</span>
                  </span>
                </div>
              ) : (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  {adsAtSlot.map((ad) => (
                    <AdCard
                      key={ad.id}
                      ad={ad}
                      expandedAds={expandedAds}
                      setExpandedAds={setExpandedAds}
                      handleApprove={handleApprove}
                      handleReject={handleReject}
                      handleBulkApprove={handleBulkApprove}
                      handleBulkReject={handleBulkReject}
                      onOpenEditModal={onOpenEditModal}
                    />
                  ))}
                </div>
              )}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default AdTimeline;