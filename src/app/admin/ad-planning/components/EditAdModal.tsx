import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  Di<PERSON>Header,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Ad, Publisher } from "./AdCard";
import { format, parse, parseISO, isValid } from "date-fns";
import { CalendarIcon, ClockIcon } from 'lucide-react';
import { toast } from "@/components/ui/use-toast";
import { AlertCircle } from 'lucide-react';

interface EditAdModalProps {
  isOpen: boolean;
  onClose: () => void;
  ad: Ad | null;
  onSave: (adId: number, newDate: string, newTime: string) => Promise<void>; // Promise olarak değiştirildi
  timeSlots: string[];
}

const EditAdModal: React.FC<EditAdModalProps> = ({ isOpen, onClose, ad, onSave, timeSlots }) => {
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined);
  const [selectedTime, setSelectedTime] = useState<string>("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string>("");

  useEffect(() => {
    if (ad) {
      const dateObj = ad.isoDate ? parseISO(ad.isoDate) : undefined;
      setSelectedDate(isValid(dateObj) ? dateObj : undefined);
      setSelectedTime(ad.time || "");
      setError("");
    } else {
      setSelectedDate(undefined);
      setSelectedTime("");
      setError("");
    }
  }, [ad]);

  const handleSave = async () => {
    if (!ad || !selectedDate || !selectedTime) {
      setError("Lütfen tarih ve saat seçin");
      return;
    }

    try {
      setIsSubmitting(true);
      setError("");
      const formattedDate = format(selectedDate, "yyyy-MM-dd");
      await onSave(ad.id, formattedDate, selectedTime);
      toast({
        title: "Başarılı!",
        description: "Reklam zamanı başarıyla güncellendi.",
        duration: 3000,
      });
      onClose();
    } catch (err) {
      setError("Bir hata oluştu. Lütfen tekrar deneyin.");
      console.error(err);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!ad) return null;

  const today = new Date();
  const isDateValid = selectedDate && selectedDate >= today;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[480px] bg-white">
        <DialogHeader>
          <DialogTitle>Reklam Zamanını Düzenle</DialogTitle>
          <DialogDescription>
            '{ad.name}' adlı reklamın yayınlanacağı tarih ve saati güncelleyin.
          </DialogDescription>
        </DialogHeader>
        
        {error && (
          <div className="flex items-center gap-2 p-3 rounded-md bg-red-50 text-red-700 text-sm">
            <AlertCircle className="h-4 w-4" />
            <span>{error}</span>
          </div>
        )}

        <div className="grid gap-6 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="date-picker" className="text-right">
              Tarih
            </Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  id="date-picker"
                  variant={"outline"}
                  className={`col-span-3 justify-start text-left font-normal 
                    ${!selectedDate && "text-muted-foreground"}
                    ${!isDateValid && selectedDate ? "border-red-500 text-red-500" : ""}`}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {selectedDate ? format(selectedDate, "PPP") : <span>Bir tarih seçin</span>}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={selectedDate}
                  onSelect={(date) => {
                    setSelectedDate(date);
                    setError("");
                  }}
                  disabled={(date) => date < today}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
            {selectedDate && !isDateValid && (
              <p className="col-span-3 col-start-2 text-xs text-red-500">
                Geçmiş bir tarih seçemezsiniz
              </p>
            )}
          </div>

          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="time-select" className="text-right">
              Saat
            </Label>
            <Select 
              value={selectedTime} 
              onValueChange={(value) => {
                setSelectedTime(value);
                setError("");
              }}
            >
              <SelectTrigger id="time-select" className="col-span-3">
                <ClockIcon className="mr-2 h-4 w-4 text-gray-500" />
                <SelectValue placeholder="Bir saat seçin" />
              </SelectTrigger>
              <SelectContent>
                {timeSlots.map(slot => (
                  <SelectItem key={slot} value={slot}>{slot}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        <DialogFooter>
          <Button 
            variant="outline" 
            onClick={onClose}
            disabled={isSubmitting}
          >
            İptal
          </Button>
          <Button 
            onClick={handleSave} 
            disabled={!selectedDate || !selectedTime || !isDateValid || isSubmitting}
            className="min-w-[100px]"
          >
            {isSubmitting ? (
              <span className="inline-flex items-center gap-2">
                <svg className="animate-spin h-4 w-4" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" fill="none" />
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                </svg>
                Kaydediliyor
              </span>
            ) : (
              "Kaydet"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default EditAdModal;