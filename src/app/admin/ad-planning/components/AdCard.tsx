import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Info, ExternalLink, Check, X, Users, Zap, TrendingUp, ChevronDown, ChevronUp, AlertTriangle, PlusCircle, Pencil } from "lucide-react";
import { Progress } from "@/components/ui/progress";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Checkbox } from "@/components/ui/checkbox";

export interface Publisher {
  id: number;
  name: string;
  type?: string;
  subscribers?: number;
  engagementRate?: number;
  status: string;
  url?: string;
  category?: string;
}

export interface Ad {
  id: number;
  name: string;
  time: string;
  brand?: string;
  category?: string;
  budget?: number;
  spent?: number;
  reach?: number;
  publishers: Publisher[];
  isoDate: string;
  totalPosts?: number;
  campaignId?: string;
  communityViews?: number;
  communityStatus?: string;
}

interface AdCardProps {
  ad: Ad;
  expandedAds: number[];
  setExpandedAds: (ids: number[]) => void;
  handleApprove: (adId: number, publisherId: number) => void;
  handleReject: (adId: number, publisherId: number) => void;
  handleBulkApprove?: (adId: number, publisherIds: number[]) => void;
  handleBulkReject?: (adId: number, publisherIds: number[]) => void;
  onOpenEditModal: (ad: Ad) => void;
}

const AdCard: React.FC<AdCardProps> = ({
  ad,
  expandedAds,
  setExpandedAds,
  handleApprove,
  handleReject,
  handleBulkApprove,
  handleBulkReject,
  onOpenEditModal,
}) => {
  const { publishers } = ad;

  const approvedCount = publishers.filter(p => p.status === "approved").length;
  const pendingCount = publishers.filter(p => p.status === "pending").length;
  const rejectedCount = publishers.filter(p => p.status === "rejected").length;
  const progress = ad.budget && ad.spent ? Math.round((ad.spent / ad.budget) * 100) : 0;

  const [selectedPublisherIds, setSelectedPublisherIds] = React.useState<number[]>([]);
  const allSelectablePendingIds = publishers.filter(p => p.status === "pending").map(p => p.id);
  const isAllSelected = allSelectablePendingIds.length > 0 && allSelectablePendingIds.every(id => selectedPublisherIds.includes(id));

  const handleSelectAll = () => {
    if (isAllSelected) {
      setSelectedPublisherIds([]);
    } else {
      setSelectedPublisherIds(allSelectablePendingIds);
    }
  };

  const handleSelectOne = (id: number) => {
    setSelectedPublisherIds(prev => prev.includes(id) ? prev.filter(i => i !== id) : [...prev, id]);
  };

  const onBulkApprove = () => {
    if (handleBulkApprove && selectedPublisherIds.length > 0) {
      handleBulkApprove(ad.id, selectedPublisherIds);
      setSelectedPublisherIds([]);
    }
  };
  const onBulkReject = () => {
    if (handleBulkReject && selectedPublisherIds.length > 0) {
      handleBulkReject(ad.id, selectedPublisherIds);
      setSelectedPublisherIds([]);
    }
  };

  const isExpanded = expandedAds.includes(ad.id);

  return (
    <Card className="bg-white/80 backdrop-filter backdrop-blur-sm rounded-xl shadow-lg 
      hover:shadow-2xl hover:shadow-blue-100/20 transition-all duration-300 ease-in-out 
      overflow-hidden border border-slate-200/60 hover:border-blue-200/60 group
      hover:bg-gradient-to-br hover:from-white hover:to-blue-50/30">
      <CardHeader className="p-5 bg-gradient-to-br from-slate-50/90 to-white border-b border-slate-200/70">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <h3 className="text-lg font-semibold text-slate-800 truncate group-hover:text-blue-700 
              transition-colors relative after:absolute after:bottom-0 after:left-0 after:w-full 
              after:h-0.5 after:bg-blue-500/0 group-hover:after:bg-blue-500/10 
              after:transition-all after:duration-300" 
              title={ad.name}>{ad.name}
            </h3>
            <span className="text-xs text-slate-500 bg-slate-100/80 px-2 py-0.5 rounded-full">
              {ad.time}
            </span>
            {ad.campaignId && (
              <span className="text-xs text-blue-600 bg-blue-50/80 px-2 py-0.5 rounded-full font-mono">
                {ad.campaignId}
              </span>
            )}
          </div>
          <div className="flex items-center gap-2 flex-shrink-0">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    className="h-8 w-8 text-slate-500 hover:text-blue-600 hover:bg-blue-50 
                      hover:scale-105 transform transition-all relative overflow-hidden
                      after:absolute after:inset-0 after:bg-blue-400/0 hover:after:bg-blue-400/5
                      after:transition-all after:duration-300" 
                    onClick={() => onOpenEditModal(ad)}
                  >
                    <Pencil className="h-4 w-4 relative z-10" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent 
                  side="top" 
                  className="text-xs bg-slate-800/95 text-white backdrop-blur-sm px-2 py-1 rounded-md"
                >
                  <p>Reklamı Düzenle (Tarih/Saat)</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="h-8 w-8 rounded-full flex items-center justify-center 
                    hover:bg-blue-50 cursor-pointer group/info transition-all
                    hover:scale-105 relative overflow-hidden
                    after:absolute after:inset-0 after:bg-blue-400/0 
                    hover:after:bg-blue-400/5 after:transition-all after:duration-300">
                    <Info className="h-4 w-4 text-slate-400 group-hover/info:text-blue-600 
                      transition-colors relative z-10" />
                  </div>
                </TooltipTrigger>
                <TooltipContent 
                  side="top" 
                  className="text-xs bg-slate-800/95 text-white backdrop-blur-sm 
                    border border-slate-700/50 p-3 rounded-lg space-y-1 max-w-xs"
                >
                  <p className="font-medium flex items-center gap-2">
                    <span className="w-20 text-slate-400">Marka:</span>
                    <span className="text-blue-300">{ad.brand || "-"}</span>
                  </p>
                  <p className="font-medium flex items-center gap-2">
                    <span className="w-20 text-slate-400">Kategori:</span>
                    <span className="text-blue-300">{ad.category || "-"}</span>
                  </p>
                  {ad.communityViews && (
                    <p className="font-medium flex items-center gap-2">
                      <span className="w-20 text-slate-400">Topluluk:</span>
                      <span className="text-blue-300">{ad.communityViews.toLocaleString()} görüntülenme</span>
                    </p>
                  )}
                  {ad.communityStatus && (
                    <p className="font-medium flex items-center gap-2">
                      <span className="w-20 text-slate-400">Durum:</span>
                      <span className={`${ad.communityStatus === 'ACTIVE' ? 'text-green-300' : 'text-yellow-300'}`}>
                        {ad.communityStatus}
                      </span>
                    </p>
                  )}
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
        <div className="flex flex-wrap items-center gap-x-4 gap-y-2 text-xs text-slate-600 mt-3">
          <span className="flex items-center px-3 py-1.5 rounded-full bg-blue-50/80 
            border border-blue-100/50 hover:bg-blue-100/50 transition-colors
            hover:border-blue-200/50 cursor-default group/stat">
            <TrendingUp className="h-3.5 w-3.5 mr-2 text-blue-500 
              group-hover/stat:scale-110 transition-transform" /> 
            {ad.reach !== undefined ? ad.reach.toLocaleString() : "-"} Görüntülenme
          </span>
          <span className="flex items-center px-3 py-1.5 rounded-full bg-green-50/80 
            border border-green-100/50 hover:bg-green-100/50 transition-colors
            hover:border-green-200/50 cursor-default group/stat">
            <Zap className="h-3.5 w-3.5 mr-2 text-green-500 
              group-hover/stat:scale-110 transition-transform" /> 
            {ad.budget !== undefined ? ad.budget.toLocaleString() : "-"}₺ Bütçe
          </span>
          <span className="flex items-center px-3 py-1.5 rounded-full bg-amber-50/80 
            border border-amber-100/50 hover:bg-amber-100/50 transition-colors
            hover:border-amber-200/50 cursor-default group/stat">
            <Users className="h-3.5 w-3.5 mr-2 text-amber-500 
              group-hover/stat:scale-110 transition-transform" /> 
            {pendingCount} Bekleyen
          </span>
          {ad.totalPosts && (
            <span className="flex items-center px-3 py-1.5 rounded-full bg-purple-50/80 
              border border-purple-100/50 hover:bg-purple-100/50 transition-colors
              hover:border-purple-200/50 cursor-default group/stat">
              <PlusCircle className="h-3.5 w-3.5 mr-2 text-purple-500 
                group-hover/stat:scale-110 transition-transform" /> 
              {ad.totalPosts} Post
            </span>
          )}
        </div>
      </CardHeader>

      <CardContent className="p-5 space-y-4">
        <div>
          <div className="flex justify-between items-center text-xs mb-2">
            <span className="font-medium text-slate-600">
              Harcama: <span className="text-blue-600 font-semibold">{ad.spent !== undefined ? ad.spent.toLocaleString() : "-"}₺</span> 
              <span className="text-slate-400 ml-1">({progress}%)</span>
            </span>
            <span className="font-medium text-slate-600">
              {publishers.length} Kanaldan <span className="text-green-600 font-semibold">{approvedCount} Onaylı</span>
            </span>
          </div>
          <div className="relative h-2.5 overflow-hidden rounded-full bg-slate-100/70 border border-slate-200/50">
            <Progress 
              value={progress} 
              className="h-full rounded-full transition-all duration-500 ease-out
                [&>*]:bg-gradient-to-r [&>*]:from-blue-500 [&>*]:via-blue-400 [&>*]:to-cyan-400
                [&>*]:animate-gradient [&>*]:bg-[length:200%_200%]
                [&>*]:shadow-sm" 
            />
          </div>
          <div className="flex gap-1.5 mt-2">
            {rejectedCount > 0 && (
              <Badge 
                variant="destructive" 
                className="py-0.5 px-2 text-xs font-normal bg-red-50/80 text-red-700 
                  border border-red-200/50 shadow-sm hover:bg-red-100/50 transition-colors"
              >
                <AlertTriangle className="h-3 w-3 mr-1.5"/> {rejectedCount} Ret
              </Badge>
            )}
          </div> 
        </div>

        <Button
          variant="outline"
          size="sm"
          className="w-full flex items-center justify-center text-slate-600 hover:text-blue-700 
            hover:border-blue-200 hover:bg-blue-50/50 transition-all group/btn
            border-slate-200/70 shadow-sm hover:shadow-md"
          onClick={() => {
            setExpandedAds(
              isExpanded
                ? expandedAds.filter((id) => id !== ad.id)
                : [...expandedAds, ad.id]
            );
          }}
        >
          {isExpanded ? "Kanalları Gizle" : "Kanalları Göster"}
          {isExpanded ? 
            <ChevronUp className="h-4 w-4 ml-2 group-hover/btn:text-blue-700 transition-all group-hover/btn:-translate-y-0.5" /> : 
            <ChevronDown className="h-4 w-4 ml-2 group-hover/btn:text-blue-700 transition-all group-hover/btn:translate-y-0.5" />
          }
        </Button>

        {isExpanded && (
          <div className="mt-4 pt-4 border-t border-slate-200/70">
            {publishers.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-8 text-slate-500">
                <Users className="h-12 w-12 text-slate-300 mb-3 animate-pulse" />
                <p className="text-sm">Bu reklam için tanımlı yayıncı bulunmuyor.</p>
              </div>
            ) : (
              <>
                {pendingCount > 0 && (
                  <div className="flex items-center gap-3 mb-4 p-3.5 
                    bg-gradient-to-br from-slate-50/80 to-white rounded-lg 
                    border border-slate-200/70 shadow-sm hover:shadow-md 
                    transition-all duration-300">
                    <Checkbox 
                      id={`select-all-${ad.id}`} 
                      checked={isAllSelected} 
                      onCheckedChange={handleSelectAll} 
                      className="border-slate-300 data-[state=checked]:bg-blue-600 
                        data-[state=checked]:border-blue-600 transition-colors"
                    />
                    <label 
                      htmlFor={`select-all-${ad.id}`} 
                      className="text-sm font-medium text-slate-700 cursor-pointer 
                        hover:text-blue-700 transition-colors"
                    >
                      Tüm Bekleyenleri Seç ({pendingCount})
                    </label>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="ml-auto text-green-600 hover:bg-green-50 hover:text-green-700 
                        h-8 px-3 disabled:opacity-50 disabled:cursor-not-allowed transition-all
                        hover:shadow-sm"
                      disabled={selectedPublisherIds.length === 0 || !handleBulkApprove}
                      onClick={onBulkApprove}
                    >
                      <Check className="h-4 w-4 mr-2" /> Seçilileri Onayla
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-red-600 hover:bg-red-50 hover:text-red-700 h-8 px-3
                        disabled:opacity-50 disabled:cursor-not-allowed transition-all
                        hover:shadow-sm"
                      disabled={selectedPublisherIds.length === 0 || !handleBulkReject}
                      onClick={onBulkReject}
                    >
                      <X className="h-4 w-4 mr-2" /> Seçilileri Reddet
                    </Button>
                  </div>
                )}

                <div className="grid grid-cols-1 gap-4">
                  {publishers.map((pub) => (
                    <div 
                      key={pub.id} 
                      className={`p-4 rounded-lg border transition-all duration-200 ease-in-out overflow-hidden
                        ${pub.status === "approved" ? 
                          "bg-gradient-to-br from-green-50/90 to-green-50/30 border-green-200/70 hover:shadow-lg hover:shadow-green-100/30" : 
                          pub.status === "rejected" ? 
                          "bg-gradient-to-br from-red-50/90 to-red-50/30 border-red-200/70 hover:shadow-lg hover:shadow-red-100/30" : 
                          "bg-white border-slate-200/70 hover:border-blue-300 hover:shadow-lg hover:shadow-blue-100/30"}
                        ${selectedPublisherIds.includes(pub.id) && pub.status === "pending" ? 
                          "ring-2 ring-blue-400/50 border-blue-400/50 shadow-md shadow-blue-100/30" : ""}
                        group/pub hover:scale-[1.01]`}
                    >
                      <div className="flex items-start gap-3">
                        {pub.status === "pending" && (
                          <Checkbox 
                            id={`pub-checkbox-${ad.id}-${pub.id}`}
                            checked={selectedPublisherIds.includes(pub.id)}
                            onCheckedChange={() => handleSelectOne(pub.id)}
                            className="mt-1 flex-shrink-0 border-slate-300 data-[state=checked]:bg-blue-600 
                              data-[state=checked]:border-blue-600 transition-colors"
                          />
                        )}
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1.5">
                            <span className="font-semibold text-sm text-slate-800 truncate 
                              group-hover/pub:text-blue-700 transition-colors" 
                              title={pub.name}>
                              {pub.name}
                            </span>
                            {pub.url && (
                              <TooltipProvider>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <a 
                                      href={pub.url} 
                                      target="_blank" 
                                      rel="noopener noreferrer" 
                                      className="text-blue-500 hover:text-blue-700 transition-colors
                                        hover:scale-110 transform flex-shrink-0"
                                    >
                                      <ExternalLink className="h-3.5 w-3.5" />
                                    </a>
                                  </TooltipTrigger>
                                  <TooltipContent 
                                    side="top" 
                                    className="text-xs bg-slate-800/95 text-white backdrop-blur-sm px-2 py-1"
                                  >
                                    <p>{pub.url}</p>
                                  </TooltipContent>
                                </Tooltip>
                              </TooltipProvider>
                            )}
                          </div>
                          <div className="space-y-1">
                            <p className="text-xs text-slate-500 flex items-center gap-2">
                              <span className="font-medium min-w-[80px]">{pub.type || "Bilinmiyor"}</span>
                              <span className="text-blue-600 font-medium">
                                {pub.subscribers !== undefined ? pub.subscribers.toLocaleString() : "?"} takipçi
                              </span>
                            </p>
                            <p className="text-xs text-slate-500 flex items-center gap-2">
                              <span className="font-medium min-w-[80px]">Kategori:</span>
                              <span className="text-slate-700">{pub.category || "-"}</span>
                            </p>
                            <p className="text-xs text-slate-500 flex items-center gap-2">
                              <span className="font-medium min-w-[80px]">Etkileşim:</span>
                              <span className="text-blue-600 font-medium">
                                {pub.engagementRate !== undefined ? pub.engagementRate : "-"}%
                              </span>
                            </p>
                          </div>
                          <div className="mt-2.5">
                            {pub.status === "approved" && (
                              <Badge className="w-fit bg-green-50 text-green-700 border-green-200/70 
                                text-xs font-medium shadow-sm hover:bg-green-100/50 transition-colors">
                                <Check className="h-3.5 w-3.5 mr-1.5"/>Onaylandı
                              </Badge>
                            )}
                            {pub.status === "rejected" && (
                              <Badge className="w-fit bg-red-50 text-red-700 border-red-200/70 
                                text-xs font-medium shadow-sm hover:bg-red-100/50 transition-colors">
                                <X className="h-3.5 w-3.5 mr-1.5"/>Reddedildi
                              </Badge>
                            )}
                          </div>
                        </div>
                        {pub.status === "pending" && !selectedPublisherIds.includes(pub.id) && (
                          <div className="flex flex-col gap-1.5 flex-shrink-0">
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button 
                                    variant="outline" 
                                    size="icon" 
                                    className="h-7 w-7 bg-white border-green-300 text-green-600 
                                      hover:bg-green-50 hover:text-green-700 hover:border-green-400 
                                      transition-all hover:scale-105 shadow-sm"
                                    onClick={() => handleApprove(ad.id, pub.id)}
                                  >
                                    <Check className="h-4 w-4" />
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent side="top" className="text-xs bg-slate-800/95 text-white backdrop-blur-sm px-2 py-1">
                                  <p>Onayla</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                            <TooltipProvider>
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button 
                                    variant="outline" 
                                    size="icon" 
                                    className="h-7 w-7 bg-white border-red-300 text-red-600 
                                      hover:bg-red-50 hover:text-red-700 hover:border-red-400 
                                      transition-all hover:scale-105 shadow-sm"
                                    onClick={() => handleReject(ad.id, pub.id)}
                                  >
                                    <X className="h-4 w-4" />
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent side="top" className="text-xs bg-slate-800/95 text-white backdrop-blur-sm px-2 py-1">
                                  <p>Reddet</p>
                                </TooltipContent>
                              </Tooltip>
                            </TooltipProvider>
                          </div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default AdCard;