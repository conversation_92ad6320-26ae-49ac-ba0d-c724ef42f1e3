import React from "react";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { But<PERSON> as ShadButton } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { format } from "date-fns";
import { CalendarIcon, SearchIcon, FilterIcon, ListFilter, Settings2 } from "lucide-react";

interface FilterBarProps {
  dateRange: { from: Date | undefined; to: Date | undefined };
  setDateRange: (range: { from: Date | undefined; to: Date | undefined }) => void;
  search: string;
  setSearch: (v: string) => void;
  searchType: string;
  setSearchType: (v: string) => void;
  statusFilter: string;
  setStatusFilter: (v: string) => void;
  showEmptySlots: boolean;
  setShowEmptySlots: (v: boolean) => void;
}

const FilterBar: React.FC<FilterBarProps> = ({ dateRange, setDateRange, search, setSearch, searchType, setSearchType, statusFilter, setStatusFilter, showEmptySlots, setShowEmptySlots }) => {
  return (
    <div className="flex flex-col md:flex-row md:items-center gap-3 mb-6 bg-slate-50 p-4 rounded-lg shadow-md border border-slate-200">
      {/* shadcn Date Range Picker */}
      <Popover>
        <PopoverTrigger asChild>
          <ShadButton
            variant="outline"
            className="w-full md:w-[260px] justify-start text-left font-normal bg-white hover:bg-slate-50 border-slate-300 text-slate-700 group"
          >
            <CalendarIcon className="mr-2 h-4 w-4 text-slate-500 group-hover:text-blue-600 transition-colors" />
            {dateRange.from && dateRange.to ? (
              <span className="text-blue-700 font-medium">
                {format(dateRange.from, "dd MMM yyyy")} - {format(dateRange.to, "dd MMM yyyy")}
              </span>
            ) : (
              <span className="text-slate-500">Tarih Aralığı Seçin</span>
            )}
          </ShadButton>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            mode="range"
            selected={dateRange}
            onSelect={range => setDateRange({from: range?.from, to: range?.to})}
            numberOfMonths={2}
            initialFocus
            defaultMonth={dateRange.from}
          />
        </PopoverContent>
      </Popover>

      {/* Arama ve arama tipi */}
      <div className="flex-1 flex items-center gap-2 bg-white border border-slate-300 rounded-md shadow-sm focus-within:ring-2 focus-within:ring-blue-500 focus-within:border-blue-500 transition-all">
        <SearchIcon className="h-4 w-4 text-slate-400 ml-3 flex-shrink-0" />
        <Input
          type="search"
          placeholder="Arama yapın..."
          className="flex-grow border-0 focus:ring-0 focus:outline-none shadow-none pl-1 py-2 h-auto text-sm"
          value={search}
          onChange={e => setSearch(e.target.value)}
        />
        <Select value={searchType} onValueChange={setSearchType}>
          <SelectTrigger className="w-auto md:w-[150px] border-0 focus:ring-0 focus:outline-none shadow-none bg-transparent text-slate-600 hover:text-blue-600 text-xs pr-2">
            <SelectValue placeholder="Arama Tipi" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Tümü</SelectItem>
            <SelectItem value="telegram">Telegram Kanalı</SelectItem>
            <SelectItem value="brand">Reklamveren</SelectItem>
            <SelectItem value="community">Topluluk</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <Select value={statusFilter} onValueChange={setStatusFilter}>
        <SelectTrigger className="w-full md:w-auto min-w-[160px] bg-white hover:bg-slate-50 border-slate-300 text-slate-700 group">
           <ListFilter className="mr-2 h-4 w-4 text-slate-500 group-hover:text-blue-600 transition-colors" />
          <SelectValue placeholder="Durum Filtresi" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">Tüm Durumlar</SelectItem>
          <SelectItem value="pending">Bekleyen</SelectItem>
          <SelectItem value="approved">Onaylanan</SelectItem>
          <SelectItem value="rejected">Reddedilen</SelectItem>
        </SelectContent>
      </Select>

      <label className="flex items-center gap-2 text-sm font-medium text-slate-700 cursor-pointer select-none p-2 rounded-md hover:bg-slate-100 transition-colors">
        <Checkbox id="showEmptySlotsCheckbox" checked={showEmptySlots} onCheckedChange={v => setShowEmptySlots(!!v)} className="border-slate-400 data-[state=checked]:bg-blue-600 data-[state=checked]:border-blue-600"/>
        Boş Slotları Göster
      </label>
    </div>
  );
};

export default FilterBar; 