import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON>Title } from "@/components/ui/card";
import { Check, Clock, BarChart3, Users, ListChecks, TrendingUp, TrendingDown } from "lucide-react";
import React from "react";
import { Progress } from "@/components/ui/progress";
import { cn } from "@/lib/utils";

interface StatisticsCardsProps {
  totalAds: number;
  totalChannels: number;
  approved: number;
  pending: number;
  selectedDayDate?: string;
  previousTotalAds?: number;
  previousTotalChannels?: number;
  isLoading?: boolean;
}

const StatisticsCards: React.FC<StatisticsCardsProps> = ({
  totalAds,
  totalChannels,
  approved,
  pending,
  selectedDayDate,
  previousTotalAds,
  previousTotalChannels,
  isLoading = false,
}) => {
  const iconClasses = "h-4 w-4";

  const calculateTrend = (current: number, previous?: number) => {
    if (!previous) return null;
    const difference = ((current - previous) / previous) * 100;
    return {
      value: Math.abs(difference).toFixed(1),
      isPositive: difference > 0,
    };
  };

  const adsTrend = calculateTrend(totalAds, previousTotalAds);
  const channelsTrend = calculateTrend(totalChannels, previousTotalChannels);

  const renderTrend = (trend: { value: string; isPositive: boolean } | null) => {
    if (!trend) return null;
    const Icon = trend.isPositive ? TrendingUp : TrendingDown;
    return (
      <div className={cn(
        "flex items-center text-xs gap-1 mt-1",
        trend.isPositive ? "text-green-600" : "text-red-600"
      )}>
        <Icon className="h-3 w-3" />
        <span>{trend.value}% {trend.isPositive ? "artış" : "azalış"}</span>
      </div>
    );
  };

  const cards = [
    {
      title: "Toplam Reklam",
      value: totalAds,
      icon: ListChecks,
      iconColor: "text-blue-600",
      trend: adsTrend,
      progress: totalAds > 0 ? (approved + pending) / totalAds * 100 : 0,
      progressColor: "bg-blue-500"
    },
    {
      title: "Toplam Kanal",
      value: totalChannels,
      icon: Users,
      iconColor: "text-purple-600",
      trend: channelsTrend,
      progress: totalChannels > 0 ? approved / totalChannels * 100 : 0,
      progressColor: "bg-purple-500"
    },
    {
      title: "Onaylanan",
      value: approved,
      icon: Check,
      iconColor: "text-green-600",
      progress: totalChannels > 0 ? approved / totalChannels * 100 : 0,
      progressColor: "bg-green-500"
    },
    {
      title: "Bekleyen",
      value: pending,
      icon: Clock,
      iconColor: "text-amber-600",
      progress: totalChannels > 0 ? pending / totalChannels * 100 : 0,
      progressColor: "bg-amber-500"
    }
  ];

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        {[1, 2, 3, 4].map((i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader className="pb-2">
              <div className="h-4 w-24 bg-slate-200 rounded" />
            </CardHeader>
            <CardContent>
              <div className="h-8 w-16 bg-slate-200 rounded mb-2" />
              <div className="h-2 w-full bg-slate-100 rounded" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
      {cards.map((card) => {
        const Icon = card.icon;
        return (
          <Card key={card.title} className="transition-all duration-200 hover:shadow-lg hover:border-slate-300">
            <CardHeader className="pb-2 flex flex-row items-center justify-between">
              <CardTitle className="text-sm font-medium">{card.title}</CardTitle>
              <Icon className={cn(iconClasses, card.iconColor)} />
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <div className="text-2xl font-bold">
                  {new Intl.NumberFormat('tr-TR').format(card.value)}
                </div>
                {card.trend && renderTrend(card.trend)}
                {selectedDayDate && (
                  <p className="text-xs text-muted-foreground mt-1">{selectedDayDate} için</p>
                )}
                <Progress 
                  value={card.progress} 
                  className="h-1.5" 
                  indicatorClassName={cn("transition-all duration-500", card.progressColor)}
                />
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};

export default StatisticsCards;