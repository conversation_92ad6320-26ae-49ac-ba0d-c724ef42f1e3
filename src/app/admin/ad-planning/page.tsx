"use client"

import * as React from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, <PERSON><PERSON><PERSON>ontent } from "@/components/ui/tabs"
import StatisticsCards from "./components/StatisticsCards";
import FilterBar from "./components/FilterBar";
import AdTimeline from "./components/AdTimeline";
import EditAdModal from "./components/EditAdModal";
import { DashboardLayout } from "@/app/layout/dashboard-layout";
import { DashboardHeader } from "@/app/admin/components/dashboard/header";
import { format, parse, isValid, parseISO } from "date-fns";
import { tr } from 'date-fns/locale';
import { adPlanningService, type AdStatistics, type HourlyData, type Campaign as ApiCampaign } from "@/shared/services/ad-planning-service";

// Ad ve Publisher tipleri (AdCard'dan alınabilir veya ortak bir dosyada olabilir)
interface Publisher {
  id: number;
  name: string;
  type?: string;
  subscribers?: number;
  engagementRate?: number;
  status: string;
  url?: string;
  category?: string;
}

interface Ad {
  id: number;
  name: string;
  time: string; // HH:mm formatında
  brand?: string;
  category?: string;
  budget?: number;
  spent?: number;
  reach?: number;
  publishers: Publisher[];
  isoDate: string; // YYYY-MM-DD formatında, zorunlu alan
  totalPosts?: number; // API'dan gelen total_posts_count
  campaignId?: string; // API'dan gelen campaign_id
  communityViews?: number; // API'dan gelen community_details.views
  communityStatus?: string; // API'dan gelen community_details.status
}

interface Campaign {
  date: string; // Kullanıcıya gösterilecek format (örn: "22 Haziran 2025")
  isoDate: string; // YYYY-MM-DD formatında, filtreleme ve işlemler için
  value: string; // Tab değeri için (örn: "22-haziran")
  ads: Ad[];
}

// Timeline için saat slotlarını oluştur
function generateTimeSlots() {
  const slots = [];
  for (let hour = 0; hour < 24; hour++) {
    for (let min = 0; min < 60; min += 30) {
      slots.push(`${hour.toString().padStart(2, "0")}:${min.toString().padStart(2, "0")}`);
    }
  }
  return slots;
}

// API kampanya verilerini UI format'ına dönüştür
function convertApiCampaignToAd(apiCampaign: ApiCampaign, hour: string, adId: number): Ad {
  const publishers: Publisher[] = [];
  
  // Her kampanya için approval durumlarına göre publisher'lar oluştur
  for (let i = 0; i < apiCampaign.pending_status_count; i++) {
    publishers.push({
      id: adId * 1000 + i, // Sabit ID kullan
      name: apiCampaign.community_username,
      type: "Telegram",
      subscribers: apiCampaign.community_details.member_count,
      engagementRate: 0,
      status: "pending",
      category: apiCampaign.community_details.category
    });
  }
  
  for (let i = 0; i < apiCampaign.approved_status_count; i++) {
    publishers.push({
      id: adId * 1000 + i + 100, // Sabit ID kullan
      name: apiCampaign.community_username,
      type: "Telegram", 
      subscribers: apiCampaign.community_details.member_count,
      engagementRate: 0,
      status: "approved",
      category: apiCampaign.community_details.category
    });
  }

  return {
    id: adId,
    name: `Kampanya ${apiCampaign.campaign_id}`,
    time: hour,
    brand: apiCampaign.community_username,
    category: apiCampaign.community_details.category,
    budget: apiCampaign.campaign_budget,
    spent: apiCampaign.spending,
    reach: apiCampaign.views,
    publishers: publishers,
    isoDate: "", // Will be set by parent
    totalPosts: apiCampaign.total_posts_count,
    campaignId: apiCampaign.campaign_id,
    communityViews: apiCampaign.community_details.views,
    communityStatus: apiCampaign.community_details.status
  };
}

export default function AdPlanningPage() {
  const [campaigns, setCampaigns] = React.useState<Campaign[]>([])
  const [availableDates, setAvailableDates] = React.useState<string[]>([])
  const [activeTab, setActiveTab] = React.useState("")
  const [statistics, setStatistics] = React.useState<AdStatistics>({
    approved_ads: 0,
    pending_ads: 0,
    total_ads: 0,
    total_channels: 0
  })
  const [statusFilter, setStatusFilter] = React.useState<string>("all")
  const [search, setSearch] = React.useState("")
  const [searchType, setSearchType] = React.useState<string>("all")
  const [expandedAds, setExpandedAds] = React.useState<number[]>([])
  const [showEmptySlots, setShowEmptySlots] = React.useState<boolean>(true)
  const [dateRange, setDateRange] = React.useState<{ from: Date | undefined; to: Date | undefined }>({
    from: undefined,
    to: undefined,
  })
  const [loading, setLoading] = React.useState(true)

  // Modal state'leri
  const [isEditModalOpen, setIsEditModalOpen] = React.useState(false);
  const [selectedAdForEdit, setSelectedAdForEdit] = React.useState<Ad | null>(null);

  // Timeline slotları
  const timeSlots = generateTimeSlots();

  // Gün listesini API'dan al
  React.useEffect(() => {
    const fetchDayList = async () => {
      try {
        const response = await adPlanningService.getDayList();
        if (response.status && response.result && response.result.length > 0) {
          setAvailableDates(response.result);
          setActiveTab(response.result[0]); // İlk tarihi aktif yap
        }
      } catch (error) {
        console.error("Gün listesi yüklenirken hata:", error);
      }
    };

    fetchDayList();
  }, []);

  // Aktif tarih değiştiğinde kampanya verilerini ve istatistikleri yükle
  React.useEffect(() => {
    if (!activeTab) return;

    const fetchData = async () => {
      setLoading(true);
      try {
        // İstatistikleri al
        const statsResponse = await adPlanningService.getAdvertStatistics(activeTab);
        if (statsResponse.status) {
          setStatistics(statsResponse.result);
        }

        // Saatlik kampanya verilerini al
        const campaignResponse = await adPlanningService.getPlanDayListByHour(activeTab);
        
        if (campaignResponse.status && campaignResponse.result) {
          const hourlyData = campaignResponse.result;
          
          // API verilerini UI format'ına dönüştür
          const ads: Ad[] = [];
          let adIdCounter = 1;

          hourlyData.forEach((hourData: HourlyData) => {
            hourData.campaigns.forEach((apiCampaign: ApiCampaign) => {
              const ad = convertApiCampaignToAd(apiCampaign, hourData.hour, adIdCounter++);
              ad.isoDate = activeTab;
              ads.push(ad);
            });
          });

          // Kampanya objesi oluştur
          const dateObj = parseISO(activeTab);
          const formattedDate = format(dateObj, "dd MMMM yyyy", { locale: tr });

          const campaign: Campaign = {
            date: formattedDate,
            isoDate: activeTab,
            value: activeTab, // ISO date kullan, formatted date değil
            ads: ads.sort((a, b) => a.time.localeCompare(b.time))
          };

          setCampaigns([campaign]);
        }
      } catch (error) {
        console.error("Veri yüklenirken hata:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [activeTab]);

  // Tab değiştirme handler'ı - date string'i kullanarak
  const handleTabChange = (dateValue: string) => {
    setActiveTab(dateValue);
  };

  // Kampanyaları seçili tarih aralığına göre filtrele
  const filteredCampaigns = campaigns.filter((c) => {
    if (!dateRange.from || !dateRange.to) return true;
    if (!c.isoDate) return true;
    const d = parseISO(c.isoDate);
    return isValid(d) && d >= dateRange.from && d <= dateRange.to;
  });

  // Available dates'i campaign format'ına dönüştür (tab listesi için)
  const availableCampaigns = availableDates.map(date => {
    const dateObj = parseISO(date);
    const formattedDate = format(dateObj, "dd MMMM yyyy", { locale: tr });
    return {
      date: formattedDate,
      isoDate: date,
      value: date, // Tab value olarak ISO date kullan
      ads: []
    };
  });

  const findAdAndCampaignIndices = (adId: number): { campaignIndex: number; adIndex: number } | null => {
    for (let cIdx = 0; cIdx < campaigns.length; cIdx++) {
      const aIdx = campaigns[cIdx].ads.findIndex(ad => ad.id === adId);
      if (aIdx !== -1) {
        return { campaignIndex: cIdx, adIndex: aIdx };
      }
    }
    return null;
  };

  // Onayla
  const handleApprove = (adId: number, publisherId: number) => {
    setCampaigns((prev) => {
      const newCampaigns = JSON.parse(JSON.stringify(prev));
      const indices = findAdAndCampaignIndices(adId);
      if (indices) {
        const { campaignIndex, adIndex } = indices;
        const publisherIndex = newCampaigns[campaignIndex].ads[adIndex].publishers.findIndex((p: Publisher) => p.id === publisherId);
        if (publisherIndex !== -1) {
          newCampaigns[campaignIndex].ads[adIndex].publishers[publisherIndex].status = "approved";
        }
      }
      return newCampaigns;
    });
  };

  // Reddet
  const handleReject = (adId: number, publisherId: number) => {
    setCampaigns((prev) => {
      const newCampaigns = JSON.parse(JSON.stringify(prev));
      const indices = findAdAndCampaignIndices(adId);
      if (indices) {
        const { campaignIndex, adIndex } = indices;
        const publisherIndex = newCampaigns[campaignIndex].ads[adIndex].publishers.findIndex((p: Publisher) => p.id === publisherId);
        if (publisherIndex !== -1) {
          newCampaigns[campaignIndex].ads[adIndex].publishers[publisherIndex].status = "rejected";
        }
      }
      return newCampaigns;
    });
  };

  // Toplu onay
  const handleBulkApprove = (adId: number, publisherIds: number[]) => {
    setCampaigns((prev) => {
      const newCampaigns = JSON.parse(JSON.stringify(prev));
      const indices = findAdAndCampaignIndices(adId);
      if (indices) {
        const { campaignIndex, adIndex } = indices;
        publisherIds.forEach((publisherId) => {
          const publisherIndex = newCampaigns[campaignIndex].ads[adIndex].publishers.findIndex((p: Publisher) => p.id === publisherId);
          if (publisherIndex !== -1) {
            newCampaigns[campaignIndex].ads[adIndex].publishers[publisherIndex].status = "approved";
          }
        });
      }
      return newCampaigns;
    });
  };

  // Toplu red
  const handleBulkReject = (adId: number, publisherIds: number[]) => {
    setCampaigns((prev) => {
      const newCampaigns = JSON.parse(JSON.stringify(prev));
      const indices = findAdAndCampaignIndices(adId);
      if (indices) {
        const { campaignIndex, adIndex } = indices;
        publisherIds.forEach((publisherId) => {
          const publisherIndex = newCampaigns[campaignIndex].ads[adIndex].publishers.findIndex((p: Publisher) => p.id === publisherId);
          if (publisherIndex !== -1) {
            newCampaigns[campaignIndex].ads[adIndex].publishers[publisherIndex].status = "rejected";
          }
        });
      }
      return newCampaigns;
    });
  };

  // Modal açma fonksiyonu
  const handleOpenEditModal = (ad: Ad) => {
    setSelectedAdForEdit(ad);
    setIsEditModalOpen(true);
  };

  // Modal kapatma fonksiyonu
  const handleCloseEditModal = () => {
    setIsEditModalOpen(false);
    setSelectedAdForEdit(null);
  };

  // Reklam tarih/saat güncelleme fonksiyonu
  const handleUpdateAdDateTime = async (adId: number, newIsoDate: string, newTime: string) => {
    setCampaigns(prevCampaigns => {
      const updatedCampaigns = JSON.parse(JSON.stringify(prevCampaigns));
      let adToMove: Ad | null = null;
      let sourceCampaignIndex = -1;
      let sourceAdIndex = -1;

      // Önce reklamı bul ve eski yerinden çıkar
      for (let i = 0; i < updatedCampaigns.length; i++) {
        const adIndex = updatedCampaigns[i].ads.findIndex((ad: Ad) => ad.id === adId);
        if (adIndex !== -1) {
          adToMove = updatedCampaigns[i].ads[adIndex];
          sourceCampaignIndex = i;
          sourceAdIndex = adIndex;
          updatedCampaigns[i].ads.splice(adIndex, 1);
          break;
        }
      }

      if (!adToMove) return updatedCampaigns; // Reklam bulunamazsa bir şey yapma

      // Reklamın yeni bilgilerini güncelle
      adToMove.isoDate = newIsoDate;
      adToMove.time = newTime;

      // Yeni tarih için doğru kampanyayı bul veya oluştur
      const targetCampaignIndex = updatedCampaigns.findIndex((c: Campaign) => c.isoDate === newIsoDate);

      if (targetCampaignIndex === -1) {
        // Yeni kampanya oluştur
        const newCampaignDate = parseISO(newIsoDate);
        const newCampaign: Campaign = {
          date: format(newCampaignDate, "dd MMMM yyyy", { locale: tr }),
          isoDate: newIsoDate,
          value: newIsoDate,
          ads: [adToMove]
        };
        updatedCampaigns.push(newCampaign);
        // Kampanyaları tarihe göre sırala (isteğe bağlı ama iyi bir pratik)
        updatedCampaigns.sort((a: Campaign, b: Campaign) => new Date(a.isoDate).getTime() - new Date(b.isoDate).getTime());
        // Yeni eklenen kampanyanın tab'ını aktif yap
        setActiveTab(newCampaign.value);

      } else {
        // Mevcut kampanyaya ekle ve reklamları saate göre sırala
        updatedCampaigns[targetCampaignIndex].ads.push(adToMove);
        updatedCampaigns[targetCampaignIndex].ads.sort((a: Ad, b: Ad) => a.time.localeCompare(b.time));
         // Eğer aktif tab değişmediyse ve reklam aynı gün içindeyse, tab'ı değiştirmeye gerek yok
        // Fakat farklı bir güne taşındıysa ve o günün tabı aktif değilse, aktif tabı güncelle
        const targetCampaignValue = updatedCampaigns[targetCampaignIndex].value;
        if (activeTab !== targetCampaignValue) {
            // Eğer reklam farklı bir güne taşındıysa, yeni günün tabını aktif et
            // Ancak, eğer dateRange filtresi varsa ve yeni gün bu aralıkta değilse, tab değişmeyebilir.
            // Bu durumu kontrol etmek için, filteredCampaigns içinde yeni günün olup olmadığına bakılabilir.
            // Şimdilik basitçe yeni tab'ı set ediyoruz.
             setActiveTab(targetCampaignValue);
        }
      }
      
      // Eğer kaynak kampanya boş kaldıysa ve "Boş Slotları Göster" aktif değilse
      // ve kaynak kampanya, dateRange filtresi dışındaysa silinebilir.
      // Bu kısım daha karmaşık bir mantık gerektirebilir, şimdilik eklenmedi.

      return updatedCampaigns;
    });
  };

  // İstatistikler - API'dan gelen veriler kullanılıyor
  const selectedDayCampaign = campaigns.find((c) => c.value === activeTab)

  if (loading) {
    return (
      <DashboardLayout>
        <DashboardHeader
          title="Reklam Akışı & Planlama"
          description="Planlanmış kampanyalarınızı, topluluk ve kanallar bazında zaman çizelgesiyle yönetin."
        />
        <div className="flex items-center justify-center h-64">
          <div className="text-lg">Yükleniyor...</div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <DashboardHeader
        title="Reklam Akışı & Planlama"
        description="Planlanmış kampanyalarınızı, topluluk ve kanallar bazında zaman çizelgesiyle yönetin."
      />
      <StatisticsCards
        totalAds={statistics.total_ads}
        totalChannels={statistics.total_channels}
        approved={statistics.approved_ads}
        pending={statistics.pending_ads}
        selectedDayDate={selectedDayCampaign?.date}
      />
      <div className="mt-8">
        <FilterBar
          dateRange={dateRange}
          setDateRange={setDateRange}
          search={search}
          setSearch={setSearch}
          searchType={searchType}
          setSearchType={setSearchType}
          statusFilter={statusFilter}
          setStatusFilter={setStatusFilter}
          showEmptySlots={showEmptySlots}
          setShowEmptySlots={setShowEmptySlots}
        />
        <Tabs value={activeTab} onValueChange={handleTabChange}>
          <TabsList className="mb-4">
            {availableCampaigns.map((c) => (
              <TabsTrigger key={c.value} value={c.value}>{c.date}</TabsTrigger>
            ))}
          </TabsList>
          {filteredCampaigns.map((campaign, dateIndex) => (
            <TabsContent key={campaign.value} value={campaign.value}>
              <h2 className="mb-4 text-lg font-medium">{campaign.date} Tarihli Kampanyalar</h2>
              <AdTimeline
                campaign={campaign}
                timeSlots={timeSlots}
                search={search}
                searchType={searchType}
                statusFilter={statusFilter}
                showEmptySlots={showEmptySlots}
                expandedAds={expandedAds}
                setExpandedAds={setExpandedAds}
                handleApprove={handleApprove}
                handleReject={handleReject}
                handleBulkApprove={handleBulkApprove}
                handleBulkReject={handleBulkReject}
                onOpenEditModal={handleOpenEditModal}
              />
            </TabsContent>
          ))}
        </Tabs>
        <EditAdModal 
          isOpen={isEditModalOpen}
          onClose={handleCloseEditModal}
          ad={selectedAdForEdit}
          onSave={handleUpdateAdDateTime}
          timeSlots={timeSlots}
        />
      </div>
    </DashboardLayout>
  )
} 