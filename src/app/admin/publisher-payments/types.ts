export interface PublisherPayment {
  id: number
  publisherId: string
  publisherName: string
  publisherImage?: string
  publisherEmail: string
  amount: number
  status: "completed" | "pending" | "processing"
  date: string
  method: string
  reference?: string
  details?: {
    accountNumber?: string
    accountName?: string
    bankName?: string
    items?: PaymentItem[]
  }
}

export interface PaymentItem {
  id: number
  campaignId: string
  campaignName: string
  amount: number
  date: string
}

export interface PublisherPaymentStats {
  total: number
  pending: number
  processing: number
  completed: number
  pendingCount: number
  processingCount: number
  totalCount: number
  monthlyGrowth: number
}

export interface Publisher {
  id: string
  name: string
  email: string
  category?: string
  image?: string
  registrationDate?: string
  status: "active" | "inactive" | "suspended"
  accountDetails?: {
    bank?: string
    accountNumber?: string
    accountName?: string
    swift?: string
    iban?: string
  }
}

export interface Payment {
  id: string
  publisher: {
    id: string
    name: string
    email: string
  }
  amount: number
  date: string
  status: string
  method: "bank" | "paypal" | "crypto" | "other"
  reference?: string
  invoiced: boolean
  invoiceNumber?: string
  campaign?: string
  description?: string
  items?: Array<{
    id: string
    description: string
    amount: number
    campaign?: string
  }>
}

export interface PaymentStats {
  totalPayments: number
  pendingPayments: number
  processingPayments: number
  totalAmount: number
  pendingAmount: number
  monthlyComparison: {
    changePercentage: number
    trend: "up" | "down" | "stable"
  }
  invoiceStatus: {
    withInvoice: number
    withoutInvoice: number
    percentage: number
  }
  paymentMethods: {
    bank: number
    paypal: number
    crypto: number
    other: number
  }
}

export interface FilterOptions {
  status?: string[]
  dateRange?: {
    start: Date
    end: Date
  }
  amountRange?: {
    min: number
    max: number
  }
  invoiceStatus?: "all" | "with" | "without"
  paymentMethod?: string[]
  publisher?: string
  campaign?: string
  search?: string
}

export const mockPayments: Payment[] = [
  {
    id: "PAY-001",
    publisher: {
      id: "PUB-001",
      name: "Teknoloji Blog",
      email: "<EMAIL>"
    },
    amount: 2500,
    date: "2024-03-15T14:30:00",
    status: "tamamlandı",
    method: "bank",
    reference: "REF-9876543",
    invoiced: true,
    invoiceNumber: "INV-2024-001",
    campaign: "Yaz Teknoloji Kampanyası"
  },
  {
    id: "PAY-002",
    publisher: {
      id: "PUB-002",
      name: "Spor Haberleri",
      email: "<EMAIL>"
    },
    amount: 1800,
    date: "2024-03-14T10:15:00",
    status: "beklemede",
    method: "paypal",
    reference: "REF-5432198",
    invoiced: false,
    campaign: "Spor Ekipmanları Tanıtımı"
  },
  {
    id: "PAY-003",
    publisher: {
      id: "PUB-003",
      name: "Yaşam Stili",
      email: "<EMAIL>"
    },
    amount: 3200,
    date: "2024-03-13T09:45:00",
    status: "işlemde",
    method: "bank",
    reference: "REF-1234567",
    invoiced: true,
    invoiceNumber: "INV-2024-002"
  },
  {
    id: "PAY-004",
    publisher: {
      id: "PUB-004",
      name: "Moda ve Güzellik",
      email: "<EMAIL>"
    },
    amount: 1500,
    date: "2024-03-10T16:20:00",
    status: "tamamlandı",
    method: "paypal",
    reference: "REF-7654321",
    invoiced: true,
    invoiceNumber: "INV-2024-003",
    campaign: "Bahar Modası"
  },
  {
    id: "PAY-005",
    publisher: {
      id: "PUB-005",
      name: "Ekonomi Platformu",
      email: "<EMAIL>"
    },
    amount: 2800,
    date: "2024-03-08T11:30:00",
    status: "beklemede",
    method: "bank",
    reference: "REF-2345678",
    invoiced: false
  }
]

export const mockStats: PaymentStats = {
  totalPayments: 124,
  pendingPayments: 18,
  processingPayments: 6,
  totalAmount: 285000,
  pendingAmount: 42000,
  monthlyComparison: {
    changePercentage: 12.5,
    trend: "up"
  },
  invoiceStatus: {
    withInvoice: 92,
    withoutInvoice: 32,
    percentage: 74.2
  },
  paymentMethods: {
    bank: 85,
    paypal: 28,
    crypto: 7,
    other: 4
  }
} 