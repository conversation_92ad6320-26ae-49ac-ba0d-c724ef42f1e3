"use client"

import { useState, useEffect } from "react"
import { DashboardLayout } from "@/app/layout/dashboard-layout"
import { DashboardHeader } from "@/app/admin/components/dashboard/header"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { PublisherPaymentStats } from "./components/PublisherPaymentStats"
import { PublisherPaymentList } from "./components/PublisherPaymentList"
import { PublisherPaymentDetail } from "./components/PublisherPaymentDetail"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { mockPayments, Payment, mockStats } from "./types"
import { PublisherPayment } from "./components/types"
import { Badge } from "@/components/ui/badge"
import { CircleHelp, FileBarChart, LineChart, PanelLeft, BarChart4, Calendar, BookOpen, PieChart as PieChartIcon, LayoutDashboard, BarChartBig } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import {
  Sheet,
  She<PERSON><PERSON><PERSON>nt,
  SheetDes<PERSON>,
  She<PERSON><PERSON>eader,
  She<PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON>rigger,
} from "@/components/ui/sheet"
import { MonthlyBarChart } from "./components/charts/BarChart"
import { PaymentMethodChart } from "./components/charts/PieChart"
import { PublisherPerformanceChart } from "./components/charts/LineChart"
import { StatusBadge } from "./components/StatusBadge"
import { getTopPublisherPayments, TopPublisherPayment } from "@/shared/services/admin/publisher-payment-statistics"

export default function PublisherPaymentsPage() {
  const [detailOpen, setDetailOpen] = useState(false)
  const [selectedPayment, setSelectedPayment] = useState<PublisherPayment | null>(null)
  const [activeTab, setActiveTab] = useState<string>("overview")
  const [statView, setStatView] = useState<string>("overview")
  const [showHelp, setShowHelp] = useState<boolean>(false)
  const [showDetailedAnalysis, setShowDetailedAnalysis] = useState<boolean>(false)
  const [topPublishers, setTopPublishers] = useState<TopPublisherPayment[]>([])
  const [loading, setLoading] = useState(true)
  
  const handleViewDetail = (payment: Payment) => {
    const publisherPayment: PublisherPayment = {
      id: Number(payment.id),
      publisher: {
        id: Number(payment.publisher.id),
        name: payment.publisher.name,
        email: payment.publisher.email
      },
      amount: payment.amount,
      status: payment.status,
      date: payment.date,
      created_at: payment.date,
      paymentMethod: payment.method,
    }
    
    setSelectedPayment(publisherPayment)
    setDetailOpen(true)
  }
  
  const handlePaymentStatusChange = (payment: PublisherPayment, newStatus: string) => {
    console.log(`Payment ${payment.id} status changed to ${newStatus}`)
    
    // Burada state güncelleme yerine, gerçek uygulamada backend'e istek atılacak
    // ve sonrasında veri yeniden çekilecek
  }

  useEffect(() => {
    const fetchTopPublishers = async () => {
      try {
        setLoading(true)
        const data = await getTopPublisherPayments()
        setTopPublishers(data)
      } catch (error) {
        console.error('En çok ödeme yapılan yayıncılar alınamadı:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchTopPublishers()
  }, [])
  
  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex justify-between items-start">
          <DashboardHeader 
            title="Yayıncı Ödemeleri" 
            description="Yayıncılarınıza yapılan ödemeleri görüntüleyin ve yönetin"
          />
          <Badge className="text-blue-700 bg-blue-50 border-blue-200 flex items-center gap-1 py-1">
            <FileBarChart className="h-3.5 w-3.5" />
            <span>Muhasebe modülü</span>
          </Badge>
        </div>
        
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <div className="flex justify-between items-end border-b pb-2">
            <TabsList>
              <TabsTrigger value="overview" className="flex items-center gap-1">
                <LayoutDashboard className="h-4 w-4" />
                <span>Genel Bakış</span>
              </TabsTrigger>
              <TabsTrigger value="monthly" className="flex items-center gap-1">
                <BarChartBig className="h-4 w-4" />
                <span>Aylık Rapor</span>
              </TabsTrigger>
              <TabsTrigger value="publishers" className="flex items-center gap-1">
                <BookOpen className="h-4 w-4" />
                <span>Yayıncı Performansı</span>
              </TabsTrigger>
            </TabsList>
            
            
          </div>
          
          <TabsContent value="overview" className="mt-6">
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
              <div className="lg:col-span-4">
                <PublisherPaymentStats />
              </div>
              <div className="lg:col-span-2">
                <PaymentMethodChart 
                  title="Ödeme Yöntemleri" 
                  description="Yöntemlere göre ödeme dağılımı"
                />
              </div>
              <div className="lg:col-span-4 space-y-4">
                <PublisherPaymentList 
                  payments={mockPayments}
                />
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="monthly" className="mt-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="col-span-4">
                <MonthlyBarChart 
                  title="Aylık Ödeme Analizi" 
                  description="Son 12 aya ait aylık ödeme dağılımı"
                  monthsToShow={12}
                />
              </div>
              
              <div className="md:col-span-2">
                <PaymentMethodChart 
                  title="Ödeme Yöntemleri Analizi"
                  description="Yöntemlere göre ödeme dağılımı"
                />
              </div>
              
              <div className="md:col-span-2">
                <Card>
                  <CardHeader>
                    <CardTitle>Fatura Durum Analizi</CardTitle>
                    <CardDescription>Aylık bazda fatura durumları</CardDescription>
                  </CardHeader>
                  <CardContent className="h-60 flex items-center justify-center">
                    <div className="p-4 rounded-md bg-slate-50 border w-full text-center">
                      <div className="flex items-center justify-center mb-2">
                        <div className="w-4 h-4 bg-green-500 rounded-full mr-2"></div>
                        <span className="text-sm">Faturalı: %{mockStats.invoiceStatus.percentage} ({mockStats.invoiceStatus.withInvoice})</span>
                      </div>
                      <div className="flex items-center justify-center">
                        <div className="w-4 h-4 bg-amber-500 rounded-full mr-2"></div>
                        <span className="text-sm">Faturasız: %{(100 - mockStats.invoiceStatus.percentage).toFixed(1)} ({mockStats.invoiceStatus.withoutInvoice})</span>
                      </div>
                      <div className="mt-4 h-2 bg-slate-200 rounded-full overflow-hidden">
                        <div className="h-full bg-green-500 rounded-full" style={{ width: `${mockStats.invoiceStatus.percentage}%` }}></div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
              
              <div className="col-span-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Yayıncı Bazlı Ödeme Dağılımı</CardTitle>
                    <CardDescription>En çok ödeme yapılan yayıncılar</CardDescription>
                  </CardHeader>
                  <CardContent className="h-80">
                    <div className="overflow-auto h-full">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50 sticky top-0">
                          <tr>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Yayıncı</th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Toplam Ödeme</th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">İşlem Sayısı</th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Son Ödeme</th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {loading ? (
                            <tr>
                              <td colSpan={4} className="px-6 py-4 text-center">
                                <div className="flex items-center justify-center">
                                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
                                </div>
                              </td>
                            </tr>
                          ) : topPublishers.length === 0 ? (
                            <tr>
                              <td colSpan={4} className="px-6 py-4 text-center text-gray-500">
                                Henüz ödeme kaydı bulunmuyor
                              </td>
                            </tr>
                          ) : (
                            topPublishers.map((publisher, index) => (
                              <tr key={index}>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <div className="flex items-center">
                                    <div className="ml-4">
                                      <div className="text-sm font-medium text-gray-900">{publisher.publisher_name}</div>
                                      <div className="text-xs text-gray-500">{publisher.publisher_email}</div>
                                    </div>
                                  </div>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <div className="text-sm font-medium text-gray-900">{publisher.total_payment} ₺</div>
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                  {publisher.transaction_count}
                                </td>
                                <td className="px-6 py-4 whitespace-nowrap">
                                  <div className="text-sm text-gray-900">{publisher.last_payment_date}</div>
                                </td>
                              </tr>
                            ))
                          )}
                        </tbody>
                      </table>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>
          
          <TabsContent value="publishers" className="mt-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="col-span-3">
                <PublisherPerformanceChart 
                  title="Yayıncı Performans Trendi"
                  description="Son 7 aya ait yayıncı performans metrikleri"
                  onShowDetailedAnalysis={() => setShowDetailedAnalysis(true)}
                />
              </div>
              
              <div className="md:col-span-3">
                <Card>
                  <CardHeader>
                    <CardTitle>Yayıncı Performans Detayları</CardTitle>
                    <CardDescription>Yayıncıların detaylı performans metrikleri</CardDescription>
                  </CardHeader>
                  <CardContent className="h-96">
                    <div className="overflow-auto h-full">
                      <table className="min-w-full divide-y divide-gray-200">
                        <thead className="bg-gray-50 sticky top-0">
                          <tr>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Yayıncı</th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Toplam Gösterim</th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Gösterim Değişimi</th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tıklama</th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tıklama Değişimi</th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">CTR</th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">CTR Değişimi</th>
                            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Aylık Kazanç</th>
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {mockPayments.map((payment, index) => (
                            <tr key={payment.id}>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="flex items-center">
                                  <div className="ml-4">
                                    <div className="text-sm font-medium text-gray-900">{payment.publisher.name}</div>
                                    <div className="text-xs text-gray-500">{payment.publisher.email}</div>
                                  </div>
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm text-gray-900">7,300</div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className={`text-sm ${-30.48 < 0 ? 'text-red-600' : 'text-green-600'}`}>
                                  %-30.48
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm text-gray-900">820</div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className={`text-sm ${-29.31 < 0 ? 'text-red-600' : 'text-green-600'}`}>
                                  %-29.31
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm text-gray-900">%11.23</div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className={`text-sm ${1.68 > 0 ? 'text-green-600' : 'text-red-600'}`}>
                                  %+1.68
                                </div>
                              </td>
                              <td className="px-6 py-4 whitespace-nowrap">
                                <div className="text-sm text-gray-900">410 ₺</div>
                              </td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>
        </Tabs>
        
        {selectedPayment && (
          <PublisherPaymentDetail 
            payment={selectedPayment}
            isOpen={detailOpen}
            onClose={() => setDetailOpen(false)}
            onStatusChange={handlePaymentStatusChange}
          />
        )}
      </div>
      
      <Sheet open={showHelp} onOpenChange={setShowHelp}>
        <SheetContent>
          <SheetHeader>
            <SheetTitle>Yardım & Destek</SheetTitle>
            <SheetDescription>
              Yayıncı ödemeleri modülü hakkında yardım
            </SheetDescription>
          </SheetHeader>
          
          <div className="mt-6 space-y-6">
            <div>
              <h3 className="text-lg font-medium mb-2">Ödemeler Nasıl Yönetilir?</h3>
              <p className="text-sm text-slate-600">
                Bu sayfada, yayıncılarınıza yapılan tüm ödemeleri görüntüleyebilir ve yönetebilirsiniz. 
                Ödeme durumlarını güncelleyebilir, ödeme detaylarını görüntüleyebilir ve yeni ödemeler oluşturabilirsiniz.
              </p>
            </div>
            
            <div>
              <h3 className="text-lg font-medium mb-2">Ödemeler Nasıl Yapılır?</h3>
              <ol className="text-sm text-slate-600 space-y-2 list-decimal pl-4">
                <li>Listeden bir yayıncı seçin veya "Yeni Ödeme" butonuna tıklayın</li>
                <li>Ödeme tutarını, yöntemini ve açıklamasını girin</li>
                <li>Ödemeyi kaydedin ve durumunu güncelleyin</li>
                <li>Fatura kesilmesi gerekiyorsa, fatura talebinde bulunun</li>
              </ol>
            </div>
            
            <div>
              <h3 className="text-lg font-medium mb-2">Raporlar Hakkında</h3>
              <p className="text-sm text-slate-600">
                Aylık raporlar sekmesinde, ödeme trendlerini ve performans metriklerini görüntüleyebilirsiniz.
                Yayıncı performansı sekmesinde ise yayıncıların kazanç ve performans karşılaştırmalarını görebilirsiniz.
              </p>
            </div>
            
            <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
              <h3 className="text-base font-medium text-blue-800 mb-2">Destek İletişim</h3>
              <p className="text-sm text-blue-700">
                Herhangi bir sorunla karşılaşırsanız, muhasebe departmanı ile iletişime geçebilirsiniz:
                <br />
                <a href="mailto:<EMAIL>" className="font-medium underline"><EMAIL></a>
                <br />
                <a href="tel:+901234567890" className="font-medium">+90 ************</a>
              </p>
            </div>
          </div>
        </SheetContent>
      </Sheet>
      
      <Sheet open={showDetailedAnalysis} onOpenChange={setShowDetailedAnalysis}>
        <SheetContent side="right" className="w-full sm:w-3/4 md:w-[800px] overflow-y-auto">
          <SheetHeader>
            <SheetTitle>Detaylı Performans Analizi</SheetTitle>
            <SheetDescription>
              Tüm yayıncı performans metriklerinin detaylı analizi
            </SheetDescription>
          </SheetHeader>
          
          <div className="mt-6 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Card>
                <CardHeader className="p-4 pb-2">
                  <CardTitle className="text-sm">İçerik Sayısı</CardTitle>
                  <CardDescription className="text-xs">Son 30 gün</CardDescription>
                </CardHeader>
                <CardContent className="p-4 pt-0">
                  <div className="text-2xl font-bold">246</div>
                  <div className="text-xs text-green-600">%12 artış</div>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="p-4 pb-2">
                  <CardTitle className="text-sm">Toplam Gösterim</CardTitle>
                  <CardDescription className="text-xs">Son 30 gün</CardDescription>
                </CardHeader>
                <CardContent className="p-4 pt-0">
                  <div className="text-2xl font-bold">3.2M</div>
                  <div className="text-xs text-green-600">%18 artış</div>
                </CardContent>
              </Card>
              
              <Card>
                <CardHeader className="p-4 pb-2">
                  <CardTitle className="text-sm">Ortalama Ödeme</CardTitle>
                  <CardDescription className="text-xs">Yayıncı başına</CardDescription>
                </CardHeader>
                <CardContent className="p-4 pt-0">
                  <div className="text-2xl font-bold">2,450 ₺</div>
                  <div className="text-xs text-green-600">%5 artış</div>
                </CardContent>
              </Card>
            </div>
            
            <div className="border rounded-lg p-4">
              <h3 className="text-base font-medium mb-4">Kategori Bazlı Yayıncı Performansı</h3>
              
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-sm font-medium">Teknoloji</span>
                    <span className="text-sm">%38</span>
                  </div>
                  <div className="h-2 bg-slate-100 rounded-full">
                    <div className="h-full bg-purple-500 rounded-full" style={{ width: '38%' }}></div>
                  </div>
                </div>
                
                <div>
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-sm font-medium">Yaşam Stili</span>
                    <span className="text-sm">%24</span>
                  </div>
                  <div className="h-2 bg-slate-100 rounded-full">
                    <div className="h-full bg-blue-500 rounded-full" style={{ width: '24%' }}></div>
                  </div>
                </div>
                
                <div>
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-sm font-medium">Spor</span>
                    <span className="text-sm">%18</span>
                  </div>
                  <div className="h-2 bg-slate-100 rounded-full">
                    <div className="h-full bg-green-500 rounded-full" style={{ width: '18%' }}></div>
                  </div>
                </div>
                
                <div>
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-sm font-medium">Moda</span>
                    <span className="text-sm">%12</span>
                  </div>
                  <div className="h-2 bg-slate-100 rounded-full">
                    <div className="h-full bg-amber-500 rounded-full" style={{ width: '12%' }}></div>
                  </div>
                </div>
                
                <div>
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-sm font-medium">Diğer</span>
                    <span className="text-sm">%8</span>
                  </div>
                  <div className="h-2 bg-slate-100 rounded-full">
                    <div className="h-full bg-slate-500 rounded-full" style={{ width: '8%' }}></div>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="border rounded-lg p-4">
              <h3 className="text-base font-medium mb-4">Yayıncı Ödeme & Fatura Durumu</h3>
              
              <div className="overflow-hidden rounded-md border">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead>
                    <tr className="bg-gray-50">
                      <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Yayıncı</th>
                      <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Ödeme</th>
                      <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">İşlem Sayısı</th>
                      <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Fatura</th>
                      <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase">Durum</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {mockPayments.map((payment, index) => (
                      <tr key={payment.id}>
                        <td className="px-4 py-3 whitespace-nowrap text-sm">{payment.publisher.name}</td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm font-medium">{payment.amount} ₺</td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm">{index + 3}</td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm">
                          {payment.invoiced ? (
                            <Badge className="bg-green-100 text-green-800">Kesildi</Badge>
                          ) : (
                            <Badge variant="outline" className="text-amber-600">Bekliyor</Badge>
                          )}
                        </td>
                        <td className="px-4 py-3 whitespace-nowrap text-sm">
                          <Badge className={
                            payment.status === "tamamlandı" ? "bg-green-100 text-green-800" :
                            payment.status === "beklemede" ? "bg-amber-100 text-amber-800" :
                            "bg-blue-100 text-blue-800"
                          }>
                            {payment.status}
                          </Badge>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    </DashboardLayout>
  )
} 