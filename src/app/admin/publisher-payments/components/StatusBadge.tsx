import { Badge } from "@/components/ui/badge"
import { CheckCircle, Clock, CreditCard, AlertTriangle } from "lucide-react"
import React from "react"

interface StatusBadgeProps {
  status: string
  type?: "payment" | "invoice" | "custom"
}

export const StatusBadge: React.FC<StatusBadgeProps> = ({ status, type = "payment" }) => {
  if (type === "invoice") {
    if (status === "faturalı" || status === "invoiced" || status === "true") {
      return (
        <Badge className="bg-green-100 text-green-800 hover:bg-green-200 flex items-center gap-1 border-green-200">
          <CheckCircle className="h-3 w-3" /> Fatura Kesildi
        </Badge>
      )
    } else {
      return (
        <Badge variant="outline" className="text-amber-700 bg-amber-50 hover:bg-amber-100 border-amber-200 flex items-center gap-1">
          <AlertTriangle className="h-3 w-3" /> Fatura Bekleniyor
        </Badge>
      )
    }
  }
  // Payment status
  switch (status) {
    case "tamamlandı":
    case "completed":
      return (
        <Badge className="bg-green-100 text-green-800 hover:bg-green-200 flex items-center gap-1 border-green-200">
          <CheckCircle className="h-3 w-3" /> Tamamlandı
        </Badge>
      )
    case "beklemede":
    case "pending":
      return (
        <Badge variant="outline" className="text-amber-700 bg-amber-50 hover:bg-amber-100 border-amber-200 flex items-center gap-1">
          <Clock className="h-3 w-3" /> Beklemede
        </Badge>
      )
    case "işlemde":
    case "processing":
      return (
        <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-200 flex items-center gap-1 border-blue-200">
          <CreditCard className="h-3 w-3" /> İşlemde
        </Badge>
      )
    case "başarısız":
    case "failed":
      return (
        <Badge variant="destructive" className="bg-red-100 text-red-800 hover:bg-red-200 border-red-200 flex items-center gap-1">
          <AlertTriangle className="h-3 w-3" /> Başarısız
        </Badge>
      )
    default:
      return <Badge variant="outline">{status}</Badge>
  }
} 