import { FileText, Download as DownloadIcon, Plus } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { format, subMonths } from "date-fns"
import { tr } from "date-fns/locale"
import React from "react"
import { PublisherPayment } from "./types"

interface PaymentDocumentsTabProps {
  payment: PublisherPayment
}

export const PaymentDocumentsTab: React.FC<PaymentDocumentsTabProps> = ({ payment }) => {
  // Generate a reference number if it doesn't exist
  const reference = payment.id.toString().padStart(6, '0')

  return (
    <div className="space-y-4">
      <h3 className="text-base font-medium">Belgeler</h3>
      <div className="space-y-3">
        <div className="flex items-center justify-between p-3 border rounded-lg hover:bg-slate-50">
          <div className="flex items-center gap-3">
            <FileText className="h-8 w-8 text-blue-500" />
            <div>
              <div className="font-medium">Fatura-{reference}.pdf</div>
              <div className="text-xs text-slate-500">
                480 KB • {format(new Date(payment.date), "PPP", { locale: tr })}
              </div>
            </div>
          </div>
          <Button variant="ghost" size="icon" className="h-8 w-8">
            <DownloadIcon className="h-4 w-4" />
          </Button>
        </div>
        <div className="flex items-center justify-between p-3 border rounded-lg hover:bg-slate-50">
          <div className="flex items-center gap-3">
            <FileText className="h-8 w-8 text-green-500" />
            <div>
              <div className="font-medium">Ödeme-Makbuzu-{reference}.pdf</div>
              <div className="text-xs text-slate-500">
                320 KB • {format(new Date(payment.date), "PPP", { locale: tr })}
              </div>
            </div>
          </div>
          <Button variant="ghost" size="icon" className="h-8 w-8">
            <DownloadIcon className="h-4 w-4" />
          </Button>
        </div>
        <div className="flex items-center justify-between p-3 border rounded-lg hover:bg-slate-50">
          <div className="flex items-center gap-3">
            <FileText className="h-8 w-8 text-amber-500" />
            <div>
              <div className="font-medium">Sözleşme-{payment.publisher.name}.pdf</div>
              <div className="text-xs text-slate-500">
                1.2 MB • {format(subMonths(new Date(), 6), "PPP", { locale: tr })}
              </div>
            </div>
          </div>
          <Button variant="ghost" size="icon" className="h-8 w-8">
            <DownloadIcon className="h-4 w-4" />
          </Button>
        </div>
      </div>
      <div className="mt-6">
        <Button className="gap-1">
          <Plus className="h-4 w-4" />
          <span>Belge Yükle</span>
        </Button>
      </div>
    </div>
  )
} 