import { Button } from "@/components/ui/button"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { StatusBadge } from "./StatusBadge"
import { UserCell } from "./UserCell"
import { Clock, CreditCard, CheckCircle } from "lucide-react"
import React, { useState } from "react"

interface PaymentDetailHeaderProps {
  publisher: { name: string; email: string }
  amount: number
  status: string
  onStatusChange: (newStatus: string) => void
  onPaymentClick: () => void
  paymentCompleted?: boolean
}

export const PaymentDetailHeader: React.FC<PaymentDetailHeaderProps> = ({
  publisher,
  amount,
  status,
  onStatusChange,
  onPaymentClick,
  paymentCompleted = false,
}) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  const handleDropdownItemClick = (action: () => void) => {
    action();
    setIsDropdownOpen(false);
  };

  return (
    <div className="flex flex-col gap-1 space-y-0 mb-4">
      <div className="flex items-center gap-4 pt-4 mt-2">
        <UserCell name={publisher.name} email={publisher.email} size="lg" />
      </div>
      <div className="flex justify-between items-center mt-4">
        <div>
          <div className="text-sm text-slate-500">Tutar</div>
          <div className="text-2xl font-bold">{amount} ₺</div>
        </div>
        <div className="flex items-center gap-2">
          <DropdownMenu open={isDropdownOpen} onOpenChange={setIsDropdownOpen}>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="gap-1">
                <StatusBadge status={status} type="payment" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Durumu Değiştir</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem 
                className="flex items-center gap-2 cursor-pointer" 
                onClick={() => handleDropdownItemClick(() => onStatusChange("beklemede"))}
              > 
                <Clock className="h-4 w-4" /> 
                Beklemede 
              </DropdownMenuItem>
              <DropdownMenuItem 
                className="flex items-center gap-2 cursor-pointer" 
                onClick={() => handleDropdownItemClick(() => onStatusChange("işlemde"))}
              > 
                <CreditCard className="h-4 w-4" /> 
                İşlemde 
              </DropdownMenuItem>
              <DropdownMenuItem 
                className="flex items-center gap-2 cursor-pointer" 
                onClick={() => handleDropdownItemClick(() => onStatusChange("tamamlandı"))}
              > 
                <CheckCircle className="h-4 w-4" /> 
                Tamamlandı 
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
          <Button onClick={onPaymentClick} disabled={paymentCompleted}>
            <CreditCard className="h-4 w-4 mr-2" />
            Ödeme Yap
          </Button>
        </div>
      </div>
    </div>
  )
} 