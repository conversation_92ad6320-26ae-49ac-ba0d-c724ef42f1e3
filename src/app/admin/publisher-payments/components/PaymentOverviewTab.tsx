import { format, subDays } from "date-fns"
import { tr } from "date-fns/locale"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"
import { StatusBadge } from "./StatusBadge"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { FileText, AlertTriangle, CheckCircle, ArrowUpRight, Copy, BarChart3, Calendar } from "lucide-react"
import { Button } from "@/components/ui/button"
import React from "react"
import { PublisherPayment } from "./types"

interface PaymentOverviewTabProps {
  payment: PublisherPayment
}

export const PaymentOverviewTab: React.FC<PaymentOverviewTabProps> = ({ payment }) => {
  // Generate a reference number if it doesn't exist
  const reference = `REF-${payment.id.toString().substring(0, 8)}`

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div className="space-y-1 p-4 bg-slate-50 rounded-lg border">
          <div className="text-sm text-slate-500 flex items-center gap-1">
            <Calendar className="h-3.5 w-3.5" />
            <span>Tarih</span>
          </div>
          <div className="font-medium">
            {format(new Date(payment.date), "PPP", { locale: tr })}
          </div>
        </div>
        <div className="space-y-1 p-4 bg-slate-50 rounded-lg border">
          <div className="text-sm text-slate-500 flex items-center gap-1">
            <BarChart3 className="h-3.5 w-3.5" />
            <span>Dönem</span>
          </div>
          <div className="font-medium">
            {format(new Date(payment.date), "MMMM yyyy", { locale: tr })}
          </div>
        </div>
        <div className="space-y-1 p-4 bg-slate-50 rounded-lg border">
          <div className="text-sm text-slate-500 flex items-center gap-1">
            <FileText className="h-3.5 w-3.5" />
            <span>Fatura</span>
          </div>
          <div className="font-medium flex items-center gap-2">
            <StatusBadge status={"faturasız"} type="invoice" />
          </div>
        </div>
      </div>
      <div className="border rounded-lg overflow-hidden">
        <div className="bg-slate-50 p-4 border-b">
          <h3 className="font-medium">Ödeme Detayları</h3>
        </div>
        <div className="p-4 space-y-3 text-sm">
          <div className="flex justify-between items-start">
            <div className="text-slate-500">Ödeme Referansı</div>
            <div className="font-medium flex items-center gap-1">
              <span className="font-mono bg-slate-100 px-2 py-1 rounded">
                {reference}
              </span>
              <Button variant="ghost" size="icon" className="h-6 w-6">
                <Copy className="h-3.5 w-3.5" />
              </Button>
            </div>
          </div>
          <div className="flex justify-between items-start">
            <div className="text-slate-500">Kampanya</div>
            <div className="font-medium flex items-center gap-1">
              <span>{payment.campaign?.name || "Genel Ödeme"}</span>
              {payment.campaign && (
                <ArrowUpRight className="h-3.5 w-3.5 text-slate-400" />
              )}
            </div>
          </div>
          <div className="flex justify-between items-start">
            <div className="text-slate-500">Ödeme Yöntemi</div>
            <div className="font-medium flex items-center">
              {payment.paymentMethod === "bank" && "Banka Transferi"}
              {payment.paymentMethod === "paypal" && "PayPal"}
              {payment.paymentMethod === "crypto" && "Kripto Para"}
              {payment.paymentMethod === "other" && "Diğer"}
              {!payment.paymentMethod && "Banka Transferi"}
            </div>
          </div>
          <div className="flex justify-between items-start">
            <div className="text-slate-500">Açıklama</div>
            <div className="font-medium">
              {payment.notes || format(new Date(payment.date), "MMMM yyyy", { locale: tr }) + " dönemi ödemesi"}
            </div>
          </div>
          <Separator />
          <div className="flex justify-between items-start pt-2">
            <div className="text-slate-500">Oluşturulma Tarihi</div>
            <div className="font-medium">
              {format(subDays(new Date(payment.date), 5), "PPP", { locale: tr })}
            </div>
          </div>
        </div>
      </div>
      <div className="border rounded-lg overflow-hidden">
        <div className="bg-slate-50 p-4 border-b">
          <h3 className="font-medium">Banka Hesabı Bilgileri</h3>
        </div>
        <div className="p-4 space-y-3 text-sm">
          <div className="flex justify-between items-start">
            <div className="text-slate-500">Banka Adı</div>
            <div className="font-medium">{payment.bankAccount?.bank || "Garanti Bankası"}</div>
          </div>
          <div className="flex justify-between items-start">
            <div className="text-slate-500">Hesap Sahibi</div>
            <div className="font-medium">{payment.bankAccount?.accountHolder || payment.publisher.name}</div>
          </div>
          <div className="flex justify-between items-start">
            <div className="text-slate-500">IBAN</div>
            <div className="font-medium flex items-center gap-1">
              <span className="font-mono bg-slate-100 px-2 py-1 rounded">
                {payment.bankAccount?.iban || "TR55 0006 2000 1234 0006 6989 56"}
              </span>
              <Button variant="ghost" size="icon" className="h-6 w-6">
                <Copy className="h-3.5 w-3.5" />
              </Button>
            </div>
          </div>
          <div className="flex justify-between items-start">
            <div className="text-slate-500">Şube Kodu</div>
            <div className="font-medium">{payment.bankAccount?.accountNumber || "123"}</div>
          </div>
        </div>
      </div>
      <div className="border rounded-lg overflow-hidden">
        <div className="bg-slate-50 p-4 border-b">
          <h3 className="font-medium">İşlem Notları</h3>
        </div>
        <div className="p-4 space-y-3 text-sm">
          <div className="flex items-start gap-2 p-2 border-b">
            <Avatar className="h-8 w-8">
              <AvatarFallback className="bg-slate-100 text-slate-600 text-xs">AY</AvatarFallback>
            </Avatar>
            <div>
              <div className="flex items-center gap-2">
                <span className="font-medium">Ahmet Yılmaz</span>
                <span className="text-xs text-slate-500">Bugün, 14:30</span>
              </div>
              <p className="text-sm mt-1">Ödeme beklemede, fatura kesildikten sonra işleme alınacak.</p>
            </div>
          </div>
          <div className="pt-2 flex gap-2">
            <Avatar className="h-8 w-8">
              <AvatarFallback className="bg-blue-100 text-blue-600 text-xs">SİZ</AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <input type="text" placeholder="Not ekleyin..." className="w-full rounded-md border border-slate-300 p-2 text-sm" />
              </div>
              <div className="flex justify-end">
                <Button size="sm">
                  <FileText className="h-3.5 w-3.5 mr-1" />
                  Not Ekle
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
} 