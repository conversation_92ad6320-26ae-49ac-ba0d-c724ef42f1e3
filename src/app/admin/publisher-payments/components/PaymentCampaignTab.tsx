import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Button } from "@/components/ui/button"
import { ExternalLink } from "lucide-react"
import React from "react"
import { PublisherPayment } from "./types"

interface PaymentCampaignTabProps {
  payment: PublisherPayment
}

export const PaymentCampaignTab: React.FC<PaymentCampaignTabProps> = ({ payment }) => {
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-base font-medium">Kampanya Detayları</h3>
        <Button variant="link" size="sm" className="h-8 flex items-center gap-1 text-blue-600 p-0">
          <ExternalLink className="h-3.5 w-3.5" />
          <span>Kampanyayı görüntüle</span>
        </Button>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="p-4 pb-2">
            <CardTitle className="text-sm font-medium">İçerik Sayısı</CardTitle>
            <CardDescription className="text-xs">Yayınlanan içerikler</CardDescription>
          </CardHeader>
          <CardContent className="p-4 pt-0">
            <div className="text-2xl font-bold">{payment.campaign?.contentCount || 12}</div>
            <span className="text-xs text-green-600">+3 önceki dönemden</span>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="p-4 pb-2">
            <CardTitle className="text-sm font-medium">Toplam Gösterim</CardTitle>
            <CardDescription className="text-xs">Tüm içerikler</CardDescription>
          </CardHeader>
          <CardContent className="p-4 pt-0">
            <div className="text-2xl font-bold">{payment.campaign?.impressions || 358291}</div>
            <span className="text-xs text-green-600">%18 artış</span>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="p-4 pb-2">
            <CardTitle className="text-sm font-medium">Dönüşüm Oranı</CardTitle>
            <CardDescription className="text-xs">Kampanya performansı</CardDescription>
          </CardHeader>
          <CardContent className="p-4 pt-0">
            <div className="text-2xl font-bold">%{payment.campaign?.conversionRate || 3.2}</div>
            <span className="text-xs text-amber-600">%0.5 azalış</span>
          </CardContent>
        </Card>
      </div>
      <div className="bg-slate-50 p-4 rounded-lg border text-sm">
        <h4 className="font-medium mb-2">Kazanç Dağılımı</h4>
        <div className="space-y-3">
          <div className="flex justify-between items-center">
            <span>Temel ödeme</span>
            <span className="font-medium">{payment.amount * 0.7} ₺</span>
          </div>
          <div className="flex justify-between items-center">
            <span>Performans bonusu</span>
            <span className="font-medium">{payment.amount * 0.2} ₺</span>
          </div>
          <div className="flex justify-between items-center">
            <span>Erken teslim bonusu</span>
            <span className="font-medium">{payment.amount * 0.1} ₺</span>
          </div>
          <Separator />
          <div className="flex justify-between items-center font-medium">
            <span>Toplam kazanç</span>
            <span>{payment.amount} ₺</span>
          </div>
        </div>
      </div>
    </div>
  )
} 