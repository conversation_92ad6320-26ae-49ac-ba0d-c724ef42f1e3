"use client"

import { Payment } from "../types"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import { 
  Eye, 
  ArrowUpRight, 
  Mail, 
  FileText, 
  CheckCircle, 
  AlertTriangle, 
  CreditCard, 
  Clock, 
  MoreHorizontal,
  Download,
  ChevronLeft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
  Search,
  AlertCircle,
  CheckCircle2,
  ListIcon,
} from "lucide-react"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { format } from "date-fns"
import { tr } from "date-fns/locale"
import { useState, useEffect } from "react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Toolt<PERSON>, Toolt<PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>Provider, TooltipTrigger } from "@/components/ui/tooltip"
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination"
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card"
import { StatusBadge } from "./StatusBadge"
import { UserCell } from "./UserCell"
import { getPublisherPaymentList, PublisherPayment } from "@/shared/services/admin/publisher-payment-statistics"
import { PublisherPaymentFilters } from "./PublisherPaymentFilters"

interface PublisherPaymentListProps {
  onViewDetail: (payment: Payment) => void
}

export function PublisherPaymentList({ onViewDetail }: PublisherPaymentListProps) {
  const [selectedRow, setSelectedRow] = useState<string | null>(null)
  const [currentPage, setCurrentPage] = useState<number>(1)
  const [itemsPerPage, setItemsPerPage] = useState<number>(10)
  const [payments, setPayments] = useState<PublisherPayment[]>([])
  const [totalItems, setTotalItems] = useState<number>(0)
  const [loading, setLoading] = useState<boolean>(true)
  const [searchTerm, setSearchTerm] = useState<string>("")
  
  const totalPages = Math.ceil(totalItems / itemsPerPage)
  
  useEffect(() => {
    const fetchPayments = async () => {
      try {
        setLoading(true)
        const result = await getPublisherPaymentList({
          limit: itemsPerPage,
          skip: (currentPage - 1) * itemsPerPage,
          search: searchTerm
        })
        setPayments(result.data)
        setTotalItems(result.stats.total)
      } catch (error) {
        console.error('Ödeme listesi alınamadı:', error)
      } finally {
        setLoading(false)
      }
    }
    
    fetchPayments()
  }, [currentPage, itemsPerPage, searchTerm])
  
  const handleRowClick = (id: string) => {
    setSelectedRow(id)
  }
  
  const handleViewDetails = (payment: PublisherPayment) => {
    // Convert PublisherPayment to Payment type
    const convertedPayment: Payment = {
      id: payment.withdraw_request_id,
      publisher: {
        id: payment.user_id,
        name: `${payment.name || ''} ${payment.surname || ''}`.trim() || 'İsimsiz Yayıncı',
        email: payment.email || ''
      },
      amount: payment.amount,
      status: payment.status.toLowerCase(),
      date: new Date(payment.created_at).toISOString(),
      method: payment.payment_method === "PAYPAL" ? "paypal" :
              payment.payment_method === "CRYPTO" ? "crypto" :
              payment.payment_method === "BANK_TRANSFER" ? "bank" : "other",
      reference: payment.withdraw_request_id,
      invoiced: false // This information is not available in the API response
    }
    onViewDetail(convertedPayment)
  }
  
  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }
  
  const handleSearch = (term: string) => {
    setSearchTerm(term)
    setCurrentPage(1) // Reset to first page when searching
  }
  
  const renderPageNumbers = () => {
    const pageNumbers = []
    const maxVisiblePages = 5
    
    if (totalPages <= maxVisiblePages) {
      for (let i = 1; i <= totalPages; i++) {
        pageNumbers.push(
          <PaginationItem key={i}>
            <PaginationLink 
              onClick={() => handlePageChange(i)}
              isActive={currentPage === i}
            >
              {i}
            </PaginationLink>
          </PaginationItem>
        )
      }
    } else {
      pageNumbers.push(
        <PaginationItem key={1}>
          <PaginationLink 
            onClick={() => handlePageChange(1)}
            isActive={currentPage === 1}
          >
            1
          </PaginationLink>
        </PaginationItem>
      )
      
      if (currentPage > 3) {
        pageNumbers.push(
          <PaginationItem key="start-ellipsis">
            <span className="px-4 py-2">...</span>
          </PaginationItem>
        )
      }
      
      const startPage = Math.max(2, currentPage - 1)
      const endPage = Math.min(totalPages - 1, currentPage + 1)
      
      for (let i = startPage; i <= endPage; i++) {
        pageNumbers.push(
          <PaginationItem key={i}>
            <PaginationLink 
              onClick={() => handlePageChange(i)}
              isActive={currentPage === i}
            >
              {i}
            </PaginationLink>
          </PaginationItem>
        )
      }
      
      if (currentPage < totalPages - 2) {
        pageNumbers.push(
          <PaginationItem key="end-ellipsis">
            <span className="px-4 py-2">...</span>
          </PaginationItem>
        )
      }
      
      pageNumbers.push(
        <PaginationItem key={totalPages}>
          <PaginationLink 
            onClick={() => handlePageChange(totalPages)}
            isActive={currentPage === totalPages}
          >
            {totalPages}
          </PaginationLink>
        </PaginationItem>
      )
    }
    
    return pageNumbers
  }
  
  const renderPagination = () => {
    if (totalItems <= itemsPerPage) {
      return (
        <div className="flex justify-end items-center w-full">
          <div className="flex items-center gap-4">
            <div className="text-sm text-slate-500">
              Toplam {totalItems} ödeme kaydı
            </div>
            
            <div className="flex items-center gap-2">
              <span className="text-sm text-slate-500">Sayfa başına:</span>
              <select 
                className="px-2 py-1 border rounded text-sm"
                value={itemsPerPage}
                onChange={(e) => {
                  setItemsPerPage(Number(e.target.value))
                  setCurrentPage(1)
                }}
              >
                <option value={5}>5</option>
                <option value={10}>10</option>
                <option value={25}>25</option>
                <option value={50}>50</option>
              </select>
            </div>
          </div>
        </div>
      )
    }

    return (
      <div className="flex justify-end items-center w-full">
        <div className="flex items-center gap-4">
          <div className="text-sm text-slate-500">
            Toplam {totalItems} ödeme kaydı
          </div>
          
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious 
                  onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
                  className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                />
              </PaginationItem>
              
              {renderPageNumbers()}
              
              <PaginationItem>
                <PaginationNext 
                  onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
                  className={currentPage === totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
          
          <div className="flex items-center gap-2">
            <span className="text-sm text-slate-500">Sayfa başına:</span>
            <select 
              className="px-2 py-1 border rounded text-sm"
              value={itemsPerPage}
              onChange={(e) => {
                setItemsPerPage(Number(e.target.value))
                setCurrentPage(1)
              }}
            >
              <option value={5}>5</option>
              <option value={10}>10</option>
              <option value={25}>25</option>
              <option value={50}>50</option>
            </select>
          </div>
        </div>
      </div>
    )
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex justify-between items-center">
          <div>
            <CardTitle>Yayıncı Ödemeleri</CardTitle>
            <CardDescription>Tüm yayıncılara yapılan ödemelerin listesi</CardDescription>
          </div>
          <div className="flex gap-2">
            <PublisherPaymentFilters onSearch={handleSearch} />
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border overflow-hidden">
          <Table>
            <TableHeader>
              <TableRow className="bg-slate-50">
                <TableHead className="w-[40px]">
                  <input 
                    type="checkbox" 
                    className="rounded border-gray-300" 
                    checked={selectedRow !== null}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedRow(payments[0]?.withdraw_request_id || null)
                      } else {
                        setSelectedRow(null)
                      }
                    }}
                  />
                </TableHead>
                <TableHead className="w-[200px]">Yayıncı</TableHead>
                <TableHead>Referans</TableHead>
                <TableHead>Tutar</TableHead>
                <TableHead>Tarih</TableHead>
                <TableHead>Durum</TableHead>
                <TableHead>Ödeme Yöntemi</TableHead>
                <TableHead>Banka Bilgileri</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={8} className="text-center py-8">
                    <div className="flex items-center justify-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
                    </div>
                  </TableCell>
                </TableRow>
              ) : payments.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={8} className="text-center py-8">
                    <div className="text-slate-500">Henüz ödeme kaydı bulunmuyor</div>
                  </TableCell>
                </TableRow>
              ) : (
                payments.map(payment => (
                <TableRow 
                    key={payment.withdraw_request_id} 
                    className={`cursor-pointer hover:bg-slate-50 transition-colors ${selectedRow === payment.withdraw_request_id ? 'bg-slate-100' : ''}`}
                    onClick={() => {
                      handleRowClick(payment.withdraw_request_id)
                      handleViewDetails(payment)
                    }}
                >
                    <TableCell onClick={(e) => e.stopPropagation()}>
                    <input 
                      type="checkbox" 
                      className="rounded border-gray-300" 
                        checked={selectedRow === payment.withdraw_request_id}
                      onChange={(e) => {
                        if (e.target.checked) {
                            setSelectedRow(payment.withdraw_request_id)
                            handleViewDetails(payment)
                        } else {
                          setSelectedRow(null)
                        }
                      }}
                    />
                  </TableCell>
                  <TableCell>
                      <UserCell 
                        name={`${payment.name || ''} ${payment.surname || ''}`.trim() || 'İsimsiz Yayıncı'} 
                        email={payment.email || ''} 
                        size="md" 
                      />
                  </TableCell>
                  <TableCell>
                    <span className="text-xs font-mono bg-slate-100 px-2 py-1 rounded">
                        {payment.withdraw_request_id}
                    </span>
                  </TableCell>
                  <TableCell>
                    <div className="font-medium">{payment.amount} ₺</div>
                  </TableCell>
                  <TableCell>
                      <div className="text-sm">{format(new Date(payment.created_at), "PPP", { locale: tr })}</div>
                      <div className="text-xs text-slate-500">{format(new Date(payment.created_at), "HH:mm")}</div>
                  </TableCell>
                  <TableCell>
                      <StatusBadge status={payment.status.toLowerCase()} type="payment" />
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                        {payment.payment_method === "BANK_TRANSFER" && "Banka Transferi"}
                        {payment.payment_method === "PAYPAL" && "PayPal"}
                        {payment.payment_method === "CRYPTO" && "Kripto Para"}
                        {!payment.payment_method && "Banka Transferi"}
                    </div>
                  </TableCell>
                    <TableCell>
                      {payment.payment_method === "BANK_TRANSFER" && (
                        <div className="space-y-1">
                          <div className="text-sm font-medium">{payment.bank_name}</div>
                          <div className="text-xs text-slate-500">{payment.account_holder_name}</div>
                          <div className="text-xs font-mono bg-slate-100 px-2 py-1 rounded">
                            {payment.iban}
                          </div>
                    </div>
                      )}
                      {payment.payment_method === "PAYPAL" && (
                        <div className="text-sm text-slate-500">PayPal hesabı</div>
                      )}
                      {payment.payment_method === "CRYPTO" && (
                        <div className="text-sm text-slate-500">Kripto cüzdan adresi</div>
                      )}
                      {!payment.payment_method && (
                        <div className="text-sm text-slate-500">Banka bilgileri girilmemiş</div>
                      )}
                  </TableCell>
                </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
      <CardFooter className="flex justify-end gap-2 border-t pt-6">
            {renderPagination()}
      </CardFooter>
    </Card>
  )
} 