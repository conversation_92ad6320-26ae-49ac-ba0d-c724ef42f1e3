import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import React from "react"

interface UserCellProps {
  name: string
  email?: string
  size?: "sm" | "md" | "lg"
}

export const UserCell: React.FC<UserCellProps> = ({ name, email, size = "md" }) => {
  const avatarSize = size === "sm" ? "h-6 w-6" : size === "lg" ? "h-12 w-12" : "h-8 w-8"
  const textSize = size === "sm" ? "text-xs" : size === "lg" ? "text-lg" : "text-sm"
  return (
    <div className="flex items-center gap-2">
      <Avatar className={avatarSize}>
        <AvatarFallback className="bg-blue-100 text-blue-600">
          {name.substring(0, 2).toUpperCase()}
        </AvatarFallback>
      </Avatar>
      <div>
        <div className={`font-medium ${textSize}`}>{name}</div>
        {email && <div className="text-xs text-slate-500">{email}</div>}
      </div>
    </div>
  )
} 