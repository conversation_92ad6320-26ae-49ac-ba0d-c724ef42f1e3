// PublisherPayment ve ilgili tipler
export interface Publisher {
  id: number;
  name: string;
  avatar?: string;
  email: string;
  phone?: string;
  company?: string;
}

export interface Document {
  id: number;
  name: string;
  date: string;
  type: string;
  size: string;
  url: string;
}

export interface BankAccount {
  bank: string;
  accountHolder: string;
  iban: string;
  accountNumber?: string;
}

export interface PublisherPayment {
  id: number;
  publisher: Publisher;
  amount: number;
  status: string; // "beklemede" | "onaylandı" | "reddedildi" | "tamamlandı"
  date: string;
  created_at: string | number;
  paymentDate?: string;
  paymentMethod?: string;
  bankAccount?: BankAccount;
  documents?: Document[];
  campaign?: {
    id: number;
    name: string;
    contentCount?: number;
    impressions?: number;
    conversionRate?: number;
  };
  notes?: string;
  history?: {
    date: string;
    action: string;
    user: string;
    note?: string;
  }[];
} 