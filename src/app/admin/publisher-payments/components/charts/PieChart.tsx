"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Donut<PERSON><PERSON>, Legend, Text } from "@tremor/react"
import { useState, useEffect } from "react"
import { cn } from "@/lib/utils"
import { CreditCard } from "lucide-react"
import { getPublisherPaymentMethodStatistics } from "@/shared/services/admin/publisher-payment-statistics"

interface PaymentMethodData {
  yontem: string
  tutar: number
}

interface PaymentMethodChartProps {
  title?: string
  description?: string
  className?: string
}

export function PaymentMethodChart({
  title = "Ödeme Yöntemleri",
  description = "Yöntemlere göre ödeme dağılımı",
  className = "",
}: PaymentMethodChartProps) {
  const [selectedSegment, setSelectedSegment] = useState<string | null>(null)
  const [data, setData] = useState<PaymentMethodData[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchData = async () => {
      try {
        const result = await getPublisherPaymentMethodStatistics()
        const formattedData = Object.entries(result).map(([method, amount]) => ({
          yontem: method === 'BANK_TRANSFER' ? 'Banka Transferi' :
                 method === 'CREDIT_CARD' ? 'Kredi Kartı' :
                 method === 'PAYPAL' ? 'PayPal' :
                 method === 'CRYPTO' ? 'Kripto Para' : method,
          tutar: amount
        }))
        setData(formattedData)
      } catch (error) {
        console.error('Ödeme yöntemi istatistikleri alınamadı:', error)
        setData([])
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [])

  // Para birimini formatlama
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 0
    }).format(value)
  }
  
  // Grafik renkleri
  const chartColors = [
    "blue-600", 
    "emerald-500", 
    "amber-500", 
    "rose-600"
  ]
  
  if (loading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>{title}</CardTitle>
          <CardDescription>{description}</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="h-48 flex items-center justify-center">
            <p>Yükleniyor...</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className={className}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <div>
          <CardTitle>{title}</CardTitle>
          <CardDescription>{description}</CardDescription>
        </div>
        <CreditCard className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="mt-4">
          <DonutChart
            data={data}
            index="yontem"
            category="tutar"
            variant="pie"
            colors={chartColors}
            valueFormatter={formatCurrency}
            showLabel={false}
            showAnimation={true}
            className="h-48"
            onValueChange={(segment) => 
              setSelectedSegment(segment ? segment.yontem : null)
            }
          />
          
          <div className="mt-4">
            <Legend
              categories={data.map((item) => item.yontem)}
              colors={chartColors}
              className="justify-center"
            />
          </div>
        </div>
        
        <div className="border-t mt-4 pt-4 space-y-3">
          <Text className="font-medium">Ödeme Yöntemleri</Text>
          {data.map((item, idx) => (
            <div
              key={item.yontem}
              className={cn(
                "flex items-center justify-between px-2 py-1 rounded-md",
                selectedSegment === item.yontem ? "bg-slate-50" : ""
              )}
            >
              <div className="flex items-center gap-2">
                <div 
                  className={`w-3 h-3 rounded-full bg-${chartColors[idx].split('-')[0]}-${chartColors[idx].split('-')[1]}`}
                />
                <span className="text-sm font-medium">{item.yontem}</span>
              </div>
              <span className="text-sm text-gray-500">
                {formatCurrency(item.tutar)}
              </span>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
} 