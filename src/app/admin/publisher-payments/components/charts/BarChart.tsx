"use client"

import { useEffect, useState } from "react"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { <PERSON><PERSON>hart, BarList, Flex, Text } from "@tremor/react"
import { getLast12MonthsPaymentStatistics, Last12MonthsPaymentStat } from "@/shared/services/admin/last12months-payment-statistics"
import { ArrowDown, ArrowUp, Info } from "lucide-react"

export function MonthlyBarChart({
  title = "Dönemsel Ödeme Dağılımı",
  description = "Son 7 aya ait ödeme dağılımı",
  className = "",
  monthsToShow = 7
}) {
  const [data, setData] = useState<Last12MonthsPaymentStat[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchData = async () => {
      try {
        const result = await getLast12MonthsPaymentStatistics()
        // Only take the last N months of data
        const filteredData = result.slice(-monthsToShow)
        setData(filteredData)
      } catch {
        setData(Array.from({ length: monthsToShow }).map((_, i) => ({
          month: '',
          month_full: '',
          year: 0,
          month_start: '',
          month_end: '',
          amount: 0,
          count: 0,
        })))
      } finally {
        setLoading(false)
      }
    }
    fetchData()
  }, [monthsToShow])

  // Grafik için veri hazırlama
  const chartData = data.map(item => ({
    ay: item.month,
    tutar: item.amount
  }))

  // BarList için veri (büyükten küçüğe sıralı)
  const barListData = [...data]
    .sort((a, b) => b.amount - a.amount)
    .map(item => ({
      name: item.month,
      value: item.amount
    }))

  // Para birimini formatlama
  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat('tr-TR', {
      style: 'currency',
      currency: 'TRY',
      minimumFractionDigits: 0
    }).format(value)
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
        <div className="absolute top-4 right-4 flex items-center gap-2 text-xs bg-gray-100 px-2 py-1 rounded">
          <Info className="h-3.5 w-3.5" /> Son {monthsToShow} Ay
        </div>
      </CardHeader>
      <CardContent>
        <div className="h-80">
          <BarChart
            data={chartData}
            index="ay"
            categories={["tutar"]}
            colors={["blue"]}
            valueFormatter={formatCurrency}
            yAxisWidth={80}
            showLegend={false}
            showGridLines={true}
            showAnimation={true}
          />
        </div>
      </CardContent>
      <CardFooter className="flex justify-between border-t pt-4">
        <div>
          <p className="text-sm text-gray-500">Toplam Tutar</p>
          <p className="text-lg font-medium">{formatCurrency(data.reduce((sum, item) => sum + item.amount, 0))}</p>
        </div>
      </CardFooter>
    </Card>
  )
} 