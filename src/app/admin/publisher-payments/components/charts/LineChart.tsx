"use client"

import {
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON><PERSON>er,
  CardTitle,
} from "@/components/ui/card"
import { <PERSON><PERSON>hart, AreaChart, Card as TremorCard, Flex, Metric, Text, Title, List, ListItem } from "@tremor/react"
import { subMonths, format } from "date-fns"
import { tr } from "date-fns/locale"
import { ArrowRight, Info } from "lucide-react"
import { Button } from "@/components/ui/button"

interface PublisherPerformanceData {
  ay: string
  izlenimler: number
  tiklamalar: number
  donusumler: number
}

// Son 7 ay için örnek veri
const defaultData: PublisherPerformanceData[] = Array.from({ length: 7 }).map((_, i) => {
  const date = subMonths(new Date(), 6 - i)
  const impressions = Math.floor(Math.random() * 1000000) + 100000
  const clicks = Math.floor(Math.random() * 50000) + 5000
  const conversions = Math.floor(Math.random() * 1000) + 100
  
  return {
    ay: format(date, 'MMM yy', { locale: tr }),
    izlenimler: impressions,
    tiklamalar: clicks,
    donusumler: conversions
  }
})

interface PublisherPerformanceChartProps {
  title?: string
  description?: string
  data?: PublisherPerformanceData[]
  className?: string
  onShowDetailedAnalysis?: () => void
}

export function PublisherPerformanceChart({
  title = "Yayıncı Performans Trendi",
  description = "Son 7 aya ait performans metrikleri",
  data = defaultData,
  className = "",
  onShowDetailedAnalysis
}: PublisherPerformanceChartProps) {
  
  // Sayı formatı
  const formatNumber = (value: number) => {
    return new Intl.NumberFormat('tr-TR').format(value)
  }
  
  // Toplam görüntülenme
  const totalImpressions = data.reduce((sum, item) => sum + item.izlenimler, 0)
  
  // Toplam tıklama
  const totalClicks = data.reduce((sum, item) => sum + item.tiklamalar, 0)
  
  // Görüntülenme-tıklama oranı (CTR)
  const viewRate = totalImpressions > 0 
    ? (totalClicks / totalImpressions) * 100 
    : 0
  
  // Son ay tıklama oranı
  const lastMonthData = data[data.length - 1]
  const lastMonthViewRate = lastMonthData 
    ? (lastMonthData.tiklamalar / lastMonthData.izlenimler) * 100 
    : 0
  
  // Daha belirgin renkler
  const chartColors = ["blue-600", "amber-500", "emerald-600"]
  
  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex justify-between items-start">
          <div>
            <CardTitle>{title}</CardTitle>
            <CardDescription>{description}</CardDescription>
          </div>
          <div className="flex items-center gap-1 text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded">
            <Info className="h-3.5 w-3.5" />
            <span>Son 7 Ay</span>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="h-80">
          <LineChart
            data={data}
            index="ay"
            categories={["izlenimler", "tiklamalar", "donusumler"]}
            colors={chartColors}
            valueFormatter={formatNumber}
            yAxisWidth={80}
            showLegend={true}
            showGridLines={true}
            showAnimation={true}
            showXAxis={true}
            showYAxis={true}
            showTooltip={true}
            curveType="monotone"
            className="mt-4"
          />
        </div>
        
        {/* Alternatif görünüm - LineChart çalışmıyorsa */}
        <div className="hidden mt-8 border-t pt-4">
          <Text className="font-medium mb-2">Aylık Performans Değerleri</Text>
          <div className="space-y-3">
            {data.map((item, index) => (
              <div key={index} className="border p-2 rounded">
                <div className="font-medium">{item.ay}</div>
                <div className="grid grid-cols-3 gap-2 mt-2">
                  <div>
                    <div className="text-xs text-gray-500">Görüntüleme</div>
                    <div className="font-medium text-blue-600">{formatNumber(item.izlenimler)}</div>
                  </div>
                  <div>
                    <div className="text-xs text-gray-500">Tıklama</div>
                    <div className="font-medium text-amber-500">{formatNumber(item.tiklamalar)}</div>
                  </div>
                  <div>
                    <div className="text-xs text-gray-500">Dönüşüm</div>
                    <div className="font-medium text-emerald-600">{formatNumber(item.donusumler)}</div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </CardContent>
      <CardFooter className="border-t pt-4 flex justify-between">
        <div>
          <p className="text-sm text-gray-500">Görüntülenme Oranı</p>
          <p className="text-lg font-medium">%{viewRate.toFixed(2)}</p>
        </div>
        
        <Button 
          variant="outline" 
          className="gap-1"
          onClick={onShowDetailedAnalysis}
        >
          <span>Detaylı Analiz</span>
          <ArrowRight className="h-4 w-4" />
        </Button>
      </CardFooter>
    </Card>
  )
} 