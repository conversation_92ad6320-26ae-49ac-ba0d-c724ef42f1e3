"use client"

import React, { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>eader } from "@/components/ui/sheet"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { ArrowLeft, X } from "lucide-react"
import { PaymentDetailHeader } from "./PaymentDetailHeader"
import { PaymentOverviewTab } from "./PaymentOverviewTab"
import { PaymentCampaignTab } from "./PaymentCampaignTab"
import { PaymentFinancialTab } from "./PaymentFinancialTab"
import { PaymentDocumentsTab } from "./PaymentDocumentsTab"
import { PublisherPayment } from "./types"

interface PublisherPaymentDetailProps {
  payment: PublisherPayment
  isOpen: boolean
  onClose: () => void
  onStatusChange?: (payment: PublisherPayment, newStatus: string) => void
}

export function PublisherPaymentDetail({ payment, isOpen, onClose, onStatusChange }: PublisherPaymentDetailProps) {
  const [activeTab, setActiveTab] = useState("overview")
  const [activeStatus, setActiveStatus] = useState(payment.status)
  const [isPaymentDialogOpen, setIsPaymentDialogOpen] = useState(false)

  const handleStatusChange = (newStatus: string) => {
    setActiveStatus(newStatus)
    if (onStatusChange) {
      onStatusChange({ ...payment, status: newStatus }, newStatus)
    }
  }

  return (
    <Sheet open={isOpen} onOpenChange={onClose}>
      <SheetContent className="sm:max-w-[600px] p-0 overflow-auto">
        <div className="grid grid-rows-[auto_1fr]">
          <div className="sticky top-0 z-10 bg-white border-b p-6 pb-4">
            <SheetHeader className="flex flex-col gap-1 space-y-0 mb-4">
              <div className="flex justify-between">
                <Button variant="ghost" size="icon" className="h-8 w-8 absolute left-4 top-4" onClick={onClose}>
                  <ArrowLeft className="h-4 w-4" />
                </Button>
                <Button variant="ghost" size="icon" className="h-8 w-8 absolute right-4 top-4" onClick={onClose}>
                  <X className="h-4 w-4" />
                </Button>
              </div>
              <PaymentDetailHeader
                publisher={payment.publisher}
                amount={payment.amount}
                status={activeStatus}
                onStatusChange={handleStatusChange}
                onPaymentClick={() => setIsPaymentDialogOpen(true)}
                paymentCompleted={activeStatus === "tamamlandı"}
              />
            </SheetHeader>
          </div>
          <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab} className="mt-0">
            <div className="sticky top-[249px] bg-white z-10 border-b">
              <div className="px-6 pt-4">
                <TabsList className="grid grid-cols-4">
                  <TabsTrigger value="overview">Genel</TabsTrigger>
                  <TabsTrigger value="campaign">Kampanya</TabsTrigger>
                  <TabsTrigger value="financial">Finansal</TabsTrigger>
                  <TabsTrigger value="documents">Belgeler</TabsTrigger>
                </TabsList>
              </div>
            </div>
            <TabsContent value="overview" className="m-0 p-6 space-y-4">
              <PaymentOverviewTab payment={payment} />
            </TabsContent>
            <TabsContent value="campaign" className="m-0 p-6">
              <PaymentCampaignTab payment={payment} />
            </TabsContent>
            <TabsContent value="financial" className="m-0 p-6">
              <PaymentFinancialTab payment={payment} />
            </TabsContent>
            <TabsContent value="documents" className="m-0 p-6 space-y-4">
              <PaymentDocumentsTab payment={payment} />
            </TabsContent>
          </Tabs>
        </div>
      </SheetContent>
    </Sheet>
  )
} 