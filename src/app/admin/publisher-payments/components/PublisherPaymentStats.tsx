"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card"
import { 
  BadgeDollarSign, 
  Clock, 
  FileCheck, 
  TrendingUp, 
  Banknote, 
  BarChart, 
  PieChart, 
  Calendar, 
  ArrowRight, 
  Users,
  FileText,
  RefreshCw
} from "lucide-react"
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Button } from "@/components/ui/button"
import { PaymentStats } from "../types"
import { MonthlyBarChart } from "./charts/BarChart"
import { PaymentMethodChart } from "./charts/PieChart"
import { getPublisherPaymentStatistics } from "@/shared/services/admin/publisher-payment-statistics"

export function PublisherPaymentStats() {
  const [stats, setStats] = useState<null | Awaited<ReturnType<typeof getPublisherPaymentStatistics>>>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const data = await getPublisherPaymentStatistics()
        setStats(data)
      } catch (e) {
        setStats({
          total_payments: { count: 0, amount: 0 },
          pending_payments: { count: 0, amount: 0 },
          processing_payments: { count: 0, amount: 0 },
          completed_payments: { count: 0, amount: 0 },
        })
      } finally {
        setLoading(false)
      }
    }
    fetchStats()
  }, [])

  if (loading) {
    return <div>Yükleniyor...</div>
  }

  return (
    <Card className="border-none shadow-sm">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="text-xl">İstatistikler</CardTitle>
            <CardDescription>Yayıncı ödemeleri istatistikleri</CardDescription>
          </div>
          
        </div>
      </CardHeader>
      <CardContent>
        <TabsContent value="overview" className="mt-0">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="flex justify-between">
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">Toplam Ödemeler</p>
                    <p className="text-2xl font-bold">{stats!.total_payments.count}</p>
                  </div>
                  <div className="h-12 w-12 rounded-full bg-primary/10 flex items-center justify-center">
                    <BadgeDollarSign className="h-6 w-6 text-primary" />
                  </div>
                </div>
                <div className="flex justify-between mt-4 text-xs">
                  <p className="text-muted-foreground">
                    Toplam: {stats!.total_payments.amount.toLocaleString('tr-TR')} ₺
                  </p>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4">
                <div className="flex justify-between">
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">Bekleyen Ödemeler</p>
                    <p className="text-2xl font-bold">{stats!.pending_payments.count}</p>
                  </div>
                  <div className="h-12 w-12 rounded-full bg-amber-50 flex items-center justify-center">
                    <Clock className="h-6 w-6 text-amber-500" />
                  </div>
                </div>
                <div className="flex justify-between mt-4 text-xs">
                  <p className="text-muted-foreground">
                    Toplam: {stats!.pending_payments.amount.toLocaleString('tr-TR')} ₺
                  </p>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4">
                <div className="flex justify-between">
                  <div className="space-y-1">
                    <p className="text-sm text-muted-foreground">İşlemdeki Ödemeler</p>
                    <p className="text-2xl font-bold">{stats!.processing_payments.count}</p>
                  </div>
                  <div className="h-12 w-12 rounded-full bg-blue-50 flex items-center justify-center">
                    <RefreshCw className="h-6 w-6 text-blue-500" />
                  </div>
                </div>
                <div className="flex justify-between mt-4 text-xs">
                  <p className="text-muted-foreground">
                    Toplam: {stats!.processing_payments.amount.toLocaleString('tr-TR')} ₺
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
          
          <div className="mt-6">
            <MonthlyBarChart
              title="Dönemsel Ödeme Dağılımı"
              description="Son 7 aya ait ödeme dağılımı"
            />
          </div>
        </TabsContent>
        
        <TabsContent value="monthly" className="mt-0">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">Toplam Ödeme (Bu Ay)</p>
                  <p className="text-2xl font-bold">42.500 ₺</p>
                </div>
                <div className="flex justify-between mt-4 text-xs items-center">
                  <div className="bg-slate-100 rounded-full flex items-center p-1 px-2">
                    <Calendar className="h-3 w-3 mr-1 text-slate-500" />
                    <span>Nisan 2024</span>
                  </div>
                  <p className="flex items-center text-green-600">
                    <TrendingUp className="h-3 w-3 mr-1" />
                    %12.5
                  </p>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4">
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">Ortalama Ödeme</p>
                  <p className="text-2xl font-bold">2.125 ₺</p>
                </div>
                <div className="flex justify-between mt-4 text-xs items-center">
                  <div className="bg-slate-100 rounded-full flex items-center p-1 px-2">
                    <Users className="h-3 w-3 mr-1 text-slate-500" />
                    <span>20 Yayıncı</span>
                  </div>
                  <p className="flex items-center text-amber-600">
                    <TrendingUp className="h-3 w-3 mr-1" />
                    %3.2
                  </p>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-4">
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">En Büyük Ödeme</p>
                  <p className="text-2xl font-bold">8.750 ₺</p>
                </div>
                <div className="flex justify-between mt-4 text-xs items-center">
                  <div className="bg-slate-100 rounded-full flex items-center p-1 px-2">
                    <FileText className="h-3 w-3 mr-1 text-slate-500" />
                    <span>Teknoloji Blog</span>
                  </div>
                  <Button variant="ghost" size="sm" className="h-6 p-0 text-blue-500">
                    Detaylar
                    <ArrowRight className="h-3 w-3 ml-1" />
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
          
          <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Kategori Dağılımı</CardTitle>
                <CardDescription className="text-xs">Ödemelerin kategori bazlı dağılımı</CardDescription>
              </CardHeader>
              <CardContent className="p-4">
                <div className="h-[200px] flex items-center justify-center text-slate-400">
                  <PieChart className="h-8 w-8 mr-3" />
                  <span>Grafik verisi yükleniyor...</span>
                </div>
              </CardContent>
              <CardFooter className="p-4 pt-0 flex justify-between text-xs text-slate-500">
                <span>Toplam 5 kategori</span>
                <Button variant="ghost" size="sm" className="h-6 p-0">
                  <span>Detaylı Rapor</span>
                  <ArrowRight className="h-3 w-3 ml-1" />
                </Button>
              </CardFooter>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Aylık Trend</CardTitle>
                <CardDescription className="text-xs">Son 6 aylık ödeme trendi</CardDescription>
              </CardHeader>
              <CardContent className="p-4">
                <div className="h-[200px] flex items-center justify-center text-slate-400">
                  <BarChart className="h-8 w-8 mr-3" />
                  <span>Grafik verisi yükleniyor...</span>
                </div>
              </CardContent>
              <CardFooter className="p-4 pt-0 flex justify-between text-xs text-slate-500">
                <span>Son Güncellenme: Bugün</span>
                <Button variant="ghost" size="sm" className="h-6 p-0">
                  <span>Tam Ekran</span>
                  <ArrowRight className="h-3 w-3 ml-1" />
                </Button>
              </CardFooter>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="payment-channels" className="mt-0">
          {/* Ödeme yöntemi ve işlem süresi kartları kaldırıldı, sadece backend'den gelen alanlar kullanılacak */}
        </TabsContent>
      </CardContent>
    </Card>
  )
} 