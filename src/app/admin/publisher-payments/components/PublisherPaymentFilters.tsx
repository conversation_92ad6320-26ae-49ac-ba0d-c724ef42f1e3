"use client"

import { useState } from "react"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Search, Plus, Filter, CalendarIcon, Download, FileText, CheckCircle, ChevronDown, SlidersHorizontal } from "lucide-react"
import { format, subMonths } from "date-fns"

interface PublisherPaymentFiltersProps {
  onSearch: (searchTerm: string) => void
}

export function PublisherPaymentFilters({ onSearch }: PublisherPaymentFiltersProps) {
  const [startDate, setStartDate] = useState<Date>(subMonths(new Date(), 1))
  const [endDate, setEndDate] = useState<Date>(new Date())
  const [dateRange, setDateRange] = useState<"tüm" | "aylik" | "özel">("aylik")
  const [priceRange, setPriceRange] = useState([0, 5000])
  const [showFilters, setShowFilters] = useState(false)
  const [searchTerm, setSearchTerm] = useState("")
  
  const handleSearch = (value: string) => {
    setSearchTerm(value)
    onSearch(value)
  }
  
  return (
    <div className="space-y-4">
      <div className="flex flex-col md:flex-row gap-4 justify-between">
        <div className="flex flex-col md:flex-row gap-4 items-start md:items-center">
          <div className="relative w-full md:w-[300px]">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input 
              placeholder="Yayıncı adı, ID veya referans ara..." 
              className="pl-9"
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
            />
          </div>
        </div>
        
      </div>
    </div>
  )
} 