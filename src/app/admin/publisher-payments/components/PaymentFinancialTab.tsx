import { format } from "date-fns"
import { tr } from "date-fns/locale"
import { StatusBadge } from "./StatusBadge"
import { Separator } from "@/components/ui/separator"
import React from "react"
import { PublisherPayment } from "./types"

interface PaymentFinancialTabProps {
  payment: PublisherPayment
}

export const PaymentFinancialTab: React.FC<PaymentFinancialTabProps> = ({ payment }) => {
  const previousPayments = [
    { id: "P001", date: "2023-11-15", amount: payment.amount * 0.8, status: "tamamlandı" },
    { id: "P002", date: "2023-12-15", amount: payment.amount * 0.9, status: "tamamlandı" },
    { id: "P003", date: "2024-01-15", amount: payment.amount * 1.1, status: "tamamlandı" },
  ]

  // Generate a reference number if it doesn't exist
  const reference = `REF-${payment.id.toString().substring(0, 8)}`

  return (
    <div className="space-y-4">
      <h3 className="text-base font-medium">Ödeme Geçmişi</h3>
      <div className="overflow-hidden rounded-md border">
        <table className="w-full text-sm">
          <thead>
            <tr className="border-b bg-slate-50">
              <th className="py-2 px-4 text-left font-medium">Referans</th>
              <th className="py-2 px-4 text-left font-medium">Tarih</th>
              <th className="py-2 px-4 text-left font-medium">Tutar</th>
              <th className="py-2 px-4 text-center font-medium">Durum</th>
            </tr>
          </thead>
          <tbody>
            {previousPayments.map(item => (
              <tr key={item.id} className="border-b last:border-b-0 hover:bg-slate-50">
                <td className="py-2 px-4">
                  <span className="font-mono bg-slate-100 px-2 py-1 rounded text-xs">
                    {item.id}
                  </span>
                </td>
                <td className="py-2 px-4">{format(new Date(item.date), "PPP", { locale: tr })}</td>
                <td className="py-2 px-4 font-medium">{item.amount} ₺</td>
                <td className="py-2 px-4 text-center">
                  <StatusBadge status={item.status} type="payment" />
                </td>
              </tr>
            ))}
            <tr className="bg-blue-50">
              <td className="py-2 px-4">
                <span className="font-mono bg-blue-100 px-2 py-1 rounded text-xs">
                  {reference}
                </span>
              </td>
              <td className="py-2 px-4">{format(new Date(payment.date), "PPP", { locale: tr })}</td>
              <td className="py-2 px-4 font-medium">{payment.amount} ₺</td>
              <td className="py-2 px-4 text-center">
                <StatusBadge status={payment.status} type="payment" />
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div className="mt-4 p-4 bg-slate-50 rounded-lg border">
        <h4 className="font-medium mb-2 text-sm">Özet Bilgiler</h4>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <div className="text-slate-500 text-xs">Toplam Ödeme</div>
            <div className="font-medium">{payment.amount * 3.8} ₺</div>
          </div>
          <div>
            <div className="text-slate-500 text-xs">Ortalama Ödeme</div>
            <div className="font-medium">{payment.amount * 0.95} ₺</div>
          </div>
          <div>
            <div className="text-slate-500 text-xs">En Yüksek Ödeme</div>
            <div className="font-medium">{payment.amount * 1.1} ₺</div>
          </div>
          <div>
            <div className="text-slate-500 text-xs">Bu Yıl Toplam</div>
            <div className="font-medium">{payment.amount * 2.8} ₺</div>
          </div>
        </div>
      </div>
    </div>
  )
} 