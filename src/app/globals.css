@import "react-day-picker/dist/style.css";

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom enhanced UI styles */
@layer components {
  /* Enhanced card styling */
  .card {
    @apply bg-white rounded-lg border border-slate-200 transition-all duration-200 hover:shadow-md;
  }

  /* Enhanced button states */
  .button-primary {
    @apply bg-blue-600 text-white rounded-md px-4 py-2 transition-all duration-200 hover:bg-blue-700 active:bg-blue-800 focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50;
  }

  /* Enhanced table styling */
  .enhanced-table {
    @apply w-full border-collapse;
  }

  .enhanced-table thead tr {
    @apply bg-slate-50 border-b border-slate-200;
  }

  .enhanced-table th {
    @apply px-4 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider;
  }

  .enhanced-table td {
    @apply px-4 py-3 text-sm text-slate-700 border-b border-slate-200;
  }

  .enhanced-table tbody tr {
    @apply hover:bg-slate-50 transition-colors duration-150;
  }

  /* Enhanced stats card */
  .stats-card {
    @apply flex flex-col p-5 rounded-lg border border-slate-200 bg-white hover:shadow-md transition-shadow duration-200;
  }

  /* Enhanced badge states */
  .badge-success {
    @apply bg-green-100 text-green-800 px-2 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-warning {
    @apply bg-amber-100 text-amber-800 px-2 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-error {
    @apply bg-red-100 text-red-800 px-2 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-info {
    @apply bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full text-xs font-medium;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-slate-100 rounded-full;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-slate-300 rounded-full hover:bg-slate-400 transition-colors;
  }

  /* Soft shadows for cards */
  .shadow-soft {
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.04);
  }

  /* Hover animations */
  .hover-lift {
    @apply transition-all duration-200;
  }

  .hover-lift:hover {
    transform: translateY(-2px);
    @apply shadow-md;
  }

  /* Better form controls */
  .form-input {
    @apply w-full rounded-md border border-slate-300 px-3 py-2 text-sm placeholder:text-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50;
  }

  .form-select {
    @apply w-full rounded-md border border-slate-300 px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50;
  }

  /* Sidebar improvements */
  .sidebar-item {
    @apply flex items-center w-full px-3 py-2 rounded-md text-slate-700 hover:bg-blue-50 hover:text-blue-600 transition-all duration-200 cursor-pointer;
  }

  .sidebar-item.active {
    @apply bg-blue-500 text-white font-medium shadow-md border-l-4 border-blue-700;
  }

  .sidebar-item:hover {
    @apply transform-gpu scale-[1.02] shadow-sm;
  }

  /* Improved dashboard header */
  .dashboard-header {
    @apply mb-6 pb-4 border-b border-slate-200;
  }

  .dashboard-title {
    @apply text-2xl font-bold text-slate-900;
  }

  .dashboard-subtitle {
    @apply mt-1 text-sm text-slate-500;
  }

  /* Touch-friendly mobile enhancements */
  .touch-target {
    @apply min-h-[44px] min-w-[44px]; /* Apple's recommended minimum touch target size */
  }

  .mobile-button {
    @apply py-4 px-6 text-base font-medium rounded-xl transition-all duration-300 touch-target;
  }

  .mobile-input {
    @apply py-4 px-4 text-base rounded-xl border-2 transition-all duration-300 touch-target;
  }

  /* Enhanced mobile interactions */
  @media (max-width: 768px) {
    .mobile-enhanced {
      @apply py-5 px-6 text-lg;
    }

    .mobile-form-spacing {
      @apply space-y-6;
    }

    .mobile-card-padding {
      @apply p-6;
    }
  }

  /* Improved tap targets for mobile */
  .tap-highlight-none {
    -webkit-tap-highlight-color: transparent;
  }

  .touch-manipulation {
    touch-action: manipulation;
  }
}

/* Animasyonlu arka plan için blob animasyonları */
@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

/* 3D dönüşüm efektleri */
.perspective {
  perspective: 1000px;
}

.rotate-y-45:hover {
  transform: rotateY(10deg);
}

/* Izgara arka planı */
.bg-grid-slate-900\/\[0\.03\] {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 32 32' width='32' height='32' fill='none' stroke='rgb(15 23 42 / 0.03)'%3e%3cpath d='M0 .5H31.5V32'/%3e%3c/svg%3e");
}

/* Hover efektleri */
.group:hover .group-hover\:scale-110 {
  transform: scale(1.1);
}

.hover\:scale-\[1\.02\]:hover {
  transform: scale(1.02);
}

/* Diğer animasyonlar ve özel stiller */
.campaign-description-editor {
  direction: ltr !important;
  unicode-bidi: plaintext !important;
}

/* Hide scrollbar but keep functionality */
.hide-scrollbar {
  -ms-overflow-style: none; /* IE and Edge */
  scrollbar-width: none; /* Firefox */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome, Safari and Opera */
}

@layer utilities {
  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
}
