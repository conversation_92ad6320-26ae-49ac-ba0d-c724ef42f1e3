import { LoginForm } from "./components/LoginForm"

export default function LoginPage() {
  return (
    <main className="h-screen flex flex-col lg:flex-row items-stretch">
      {/* Branding & Info Section */}
      <div className="relative bg-gradient-to-br from-indigo-50 via-purple-50 to-slate-50 w-full lg:w-1/2 p-8 lg:p-12 xl:p-16 flex flex-col justify-center overflow-hidden hidden lg:flex">
        {/* Background patterns */}
        <div className="absolute top-0 left-0 right-0 h-16 bg-gradient-to-r from-indigo-500/10 to-purple-600/10"></div>
        <div className="absolute -top-24 -right-24 w-96 h-96 rounded-full bg-indigo-500/5"></div>
        <div className="absolute top-1/3 -left-24 w-80 h-80 rounded-full bg-purple-500/5 backdrop-blur-3xl"></div>
        <div className="absolute bottom-0 right-0 w-full h-16 bg-gradient-to-r from-indigo-500/10 to-purple-600/10"></div>
        
        {/* Branding Content */}
        <div className="relative z-10">
          <div className="inline-block mb-6">
            <div className="flex items-center gap-2 text-xl font-semibold text-indigo-700">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                <polyline points="7.5 4.21 12 6.81 16.5 4.21"></polyline>
                <polyline points="7.5 19.79 7.5 14.6 3 12"></polyline>
                <polyline points="21 12 16.5 14.6 16.5 19.79"></polyline>
                <polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline>
                <line x1="12" y1="22.08" x2="12" y2="12"></line>
              </svg>
              <span>Adnomio</span>
            </div>
          </div>
          
          <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 text-slate-900">
            İçerik pazarlamanın <br/> 
            <span className="text-indigo-600">geleceğine hoş geldiniz</span>
          </h1>
          
          <p className="text-base text-slate-600 mb-8 max-w-lg">
            Adnomio ile içerik yaratıcıları ve markalar arasında güçlü bağlantılar kurun, kampanyalarınızı yönetin ve etkileşimlerinizi artırın.
          </p>
          
          <div className="space-y-6">
            <div className="flex items-start gap-3">
              <div className="w-8 h-8 rounded-full bg-indigo-100 flex items-center justify-center flex-shrink-0 mt-0.5">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
              </div>
              <div>
                <h3 className="font-medium text-slate-900 mb-1">Markanızı güçlendirin</h3>
                <p className="text-sm text-slate-600">Yüksek etkileşimli içerik yaratıcılarıyla işbirliği yaparak marka bilinirliğinizi artırın.</p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <div className="w-8 h-8 rounded-full bg-indigo-100 flex items-center justify-center flex-shrink-0 mt-0.5">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div>
                <h3 className="font-medium text-slate-900 mb-1">İçeriklerinizi değerlendirin</h3>
                <p className="text-sm text-slate-600">İçerik üreticileri olarak içeriklerinizi doğrudan markalarla buluşturun ve gelir elde edin.</p>
              </div>
            </div>
            
            <div className="flex items-start gap-3">
              <div className="w-8 h-8 rounded-full bg-indigo-100 flex items-center justify-center flex-shrink-0 mt-0.5">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              <div>
                <h3 className="font-medium text-slate-900 mb-1">Analitik ve raporlama</h3>
                <p className="text-sm text-slate-600">Detaylı analitik araçlarla kampanya performansınızı takip edin ve ROI'nizi optimize edin.</p>
              </div>
            </div>
          </div>
        </div>
        
        {/* Brand patterns */}
        <div className="hidden md:block absolute bottom-0 right-0 opacity-90 z-0">
          <svg width="320" height="320" viewBox="0 0 320 320" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="160" cy="160" r="160" fill="url(#paint0_radial)" fillOpacity="0.1" />
            <circle cx="160" cy="160" r="110" fill="url(#paint1_radial)" fillOpacity="0.05" />
            <circle cx="160" cy="160" r="60" fill="url(#paint2_radial)" fillOpacity="0.1" />
            <defs>
              <radialGradient id="paint0_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(160 160) rotate(90) scale(160)">
                <stop stopColor="#6366F1" />
                <stop offset="1" stopColor="#6366F1" stopOpacity="0" />
              </radialGradient>
              <radialGradient id="paint1_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(160 160) rotate(90) scale(110)">
                <stop stopColor="#6366F1" />
                <stop offset="1" stopColor="#6366F1" stopOpacity="0" />
              </radialGradient>
              <radialGradient id="paint2_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(160 160) rotate(90) scale(60)">
                <stop stopColor="#6366F1" />
                <stop offset="1" stopColor="#6366F1" stopOpacity="0" />
              </radialGradient>
            </defs>
          </svg>
        </div>
      </div>
      
      {/* Login Form Section */}
      <div className="relative w-full lg:w-1/2 p-6 lg:p-12 xl:p-16 flex flex-col justify-center min-h-screen lg:min-h-0">
        {/* Mobil Arka Plan Desenleri */}
        <div className="absolute inset-0 lg:hidden overflow-hidden">
          <div className="absolute -top-24 -right-24 w-96 h-96 rounded-full bg-indigo-500/5"></div>
          <div className="absolute top-1/2 -left-24 w-80 h-80 rounded-full bg-purple-500/5"></div>
          <div className="absolute bottom-0 right-0 left-0 h-16 bg-gradient-to-r from-indigo-500/10 to-purple-600/10"></div>
          <div className="absolute top-0 right-0 left-0 h-16 bg-gradient-to-r from-purple-600/10 to-indigo-500/10"></div>
        </div>
        
        {/* Form Container */}
        <div className="relative z-10 max-w-md mx-auto w-full">
          {/* Mobile-only Header */}
          <div className="lg:hidden mb-8 text-center">
            <div className="inline-block mb-4">
              <div className="flex items-center justify-center gap-2 text-2xl font-semibold text-indigo-700">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                  <polyline points="7.5 4.21 12 6.81 16.5 4.21"></polyline>
                  <polyline points="7.5 19.79 7.5 14.6 3 12"></polyline>
                  <polyline points="21 12 16.5 14.6 16.5 19.79"></polyline>
                  <polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline>
                  <line x1="12" y1="22.08" x2="12" y2="12"></line>
                </svg>
                <span>Adnomio</span>
              </div>
            </div>
            <h2 className="text-2xl font-bold text-slate-900">Hesabınıza Giriş Yapın</h2>
          </div>

          {/* Form Card - 3D Effect */}
          <div className="lg:hidden relative bg-white p-6 rounded-2xl shadow-lg border border-indigo-100 before:absolute before:inset-0 before:-z-10 before:translate-x-2 before:translate-y-2 before:bg-indigo-100/50 before:rounded-2xl before:blur-sm">
            <div className="mb-6">
              <p className="text-slate-600">Adnomio'ya giriş yaparak içerik pazarlama deneyiminizi yönetin.</p>
            </div>
            
            <LoginForm />
          </div>
          
          {/* Desktop Form - Original */}
          <div className="hidden lg:block">
            <div className="mb-8 lg:mt-8">
              <h2 className="text-2xl font-bold text-slate-900 mb-2">Hesabınıza Giriş Yapın</h2>
              <p className="text-slate-600">Adnomio'ya giriş yaparak içerik pazarlama deneyiminizi yönetin.</p>
            </div>
            
            <LoginForm />
          </div>
        </div>
      </div>
    </main>
  )
}