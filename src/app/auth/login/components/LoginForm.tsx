"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { toast } from "@/components/ui/use-toast"
import { Eye, EyeOff, Mail, Lock, CheckCircle2, ChevronRight, UserPlus, Info } from "lucide-react"
import Link from "next/link"
import { authService } from "@/shared/services"
import { useFormValidation } from "@/shared/hooks/useFormValidation"
import { useFormValidationContext } from "@/shared/hooks/FormValidationProvider"
import { FormField } from "@/components/ui/form/FormField"
import { FormErrorBoundary } from "@/components/ui/form/FormErrorBoundary"
import { useLanguage } from "@/shared/hooks/useLanguage"

export function LoginForm() {
  const router = useRouter()
  const { t } = useLanguage("auth")
  const [showPassword, setShowPassword] = useState(false)
  const [formData, setFormData] = useState({
    email: "",
    password: ""
  })
  const [formFocus, setFormFocus] = useState({
    email: false,
    password: false
  })

  const { errors, isSubmitting, handleChange, handleSubmit } = useFormValidation(
    useFormValidationContext().getFieldRules('login')
  )
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    handleChange(name, value)
  }

  const handleFocus = (field: 'email' | 'password') => {
    setFormFocus(prev => ({
      ...prev,
      [field]: true
    }))
  }

  const handleBlur = (field: 'email' | 'password') => {
    setFormFocus(prev => ({
      ...prev,
      [field]: false
    }))
  }

  const onSubmitForm = async () => {
    const response = await authService.login(formData.email, formData.password)

    if (response.status && response.result && response.result.user && response.result.token) {
      const userToStore = {
        ...response.result.user,
        token: response.result.token
      }
      localStorage.setItem("user", JSON.stringify(userToStore))

      toast({
        title: t("success.login.title"),
        description: t("success.login.description"),
        className: "bg-green-50 border-green-200 text-green-800"
      })
      
      const redirectMap = {
        admin: "/admin",
        advertiser: "/advertiser",
        publisher: "/publisher"
      }
      
      router.push(redirectMap[response.result.user.role] || "/")
    } else {
      throw new Error(response.desc || t("errors.invalidCredentials"))
    }
  }

  const handleFormSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    await handleSubmit(formData, onSubmitForm, 'login')
  }

  return (
    <FormErrorBoundary formName="Giriş">
      <div className="space-y-6">
        <form onSubmit={handleFormSubmit} className="space-y-5">
          <FormField
            id="email"
            name="email"
            type="email"
            label={t("login.form.email")}
            value={formData.email}
            error={errors.email}
            icon={<Mail className="h-4 w-4" strokeWidth={2} />}
            placeholder={t("login.form.emailPlaceholder")}
            required
            autoComplete="email"
            focused={formFocus.email}
            onChange={handleInputChange}
            onFocus={() => handleFocus('email')}
            onBlur={() => handleBlur('email')}
          />
          
          <div className="space-y-1.5 relative">
            <FormField
              id="password"
              name="password"
              type={showPassword ? "text" : "password"}
              label={t("login.form.password")}
              value={formData.password}
              error={errors.password}
              icon={<Lock className="h-4 w-4" strokeWidth={2} />}
              placeholder={t("login.form.passwordPlaceholder")}
              required
              autoComplete="current-password"
              focused={formFocus.password}
              onChange={handleInputChange}
              onFocus={() => handleFocus('password')}
              onBlur={() => handleBlur('password')}
            />
            <button
              type="button"
              className={`absolute right-3 top-[38px] flex items-center transition-colors duration-200 z-20 ${formFocus.password ? 'text-blue-600' : 'text-slate-600'} hover:text-slate-800`}
              onClick={() => setShowPassword(!showPassword)}
            >
              {showPassword ? <EyeOff className="h-4 w-4" strokeWidth={2} /> : <Eye className="h-4 w-4" strokeWidth={2} />}
            </button>
            <div className="flex justify-end">
              <Link 
                href="/auth/forgot-password" 
                className="text-sm text-blue-600 hover:text-blue-800 hover:underline"
              >
                {t("login.form.forgotPassword")}
              </Link>
            </div>
          </div>
          
          {errors.submit && (
            <div className="p-3 rounded-md bg-red-50 border border-red-200 text-red-800 text-sm mb-2">
              <div className="flex items-start">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-red-600 mr-2 flex-shrink-0 mt-0.5" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
                <div>{t("errors.invalidCredentials")}</div>
              </div>
            </div>
          )}
          
          <Button 
            type="submit" 
            className={`w-full py-6 mt-3 text-base transition-all duration-300 ${
              isSubmitting 
                ? 'bg-blue-600'
                : 'bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 hover:scale-[1.02]'
            } text-white font-medium shadow-lg shadow-blue-500/20 rounded-xl`}
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <div className="flex items-center justify-center gap-2">
                <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span>{t("login.form.submitting")}</span>
              </div>
            ) : (
              <span>{t("login.form.submit")}</span>
            )}
          </Button>
        </form>
        
        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <span className="w-full border-t border-slate-200"></span>
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-white px-2 text-slate-500">{t("login.form.noAccount")}</span>
          </div>
        </div>
        
        <div className="space-y-3">
          <div className="grid grid-cols-1 gap-4">
            <Link 
              href="/auth/signup/advertiser" 
              className="group relative overflow-hidden w-full bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white py-4 rounded-xl shadow-md shadow-blue-500/20 transition-all duration-300 hover:scale-[1.02]"
            >
              <div className="absolute top-0 -right-1 w-16 h-16 -rotate-45 translate-x-1 bg-white/10 blur-xl transform-gpu"></div>
              <div className="flex items-center justify-between px-4">
                <div className="flex items-center space-x-2">
                  <UserPlus className="h-5 w-5" />
                  <span className="font-medium text-sm">Reklamveren olarak kaydol</span>
                </div>
                <ChevronRight className="h-4 w-4 transition-transform group-hover:translate-x-1" />
              </div>
              <div className="absolute -bottom-24 -left-24 w-64 h-64 rounded-full bg-white/5 border border-white/10"></div>
            </Link>
            
            <Link 
              href="/auth/signup/publisher" 
              className="group relative overflow-hidden w-full bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700 text-white py-4 rounded-xl shadow-md shadow-emerald-500/20 transition-all duration-300 hover:scale-[1.02]"
            >
              <div className="absolute top-0 -right-1 w-16 h-16 -rotate-45 translate-x-1 bg-white/10 blur-xl transform-gpu"></div>
              <div className="flex items-center justify-between px-4">
                <div className="flex items-center space-x-2">
                  <UserPlus className="h-5 w-5" />
                  <span className="font-medium text-sm">İçerik üreticisi olarak kaydol</span>
                </div>
                <ChevronRight className="h-4 w-4 transition-transform group-hover:translate-x-1" />
              </div>
              <div className="absolute -bottom-24 -left-24 w-64 h-64 rounded-full bg-white/5 border border-white/10"></div>
            </Link>
          </div>
          
          <div className="flex items-center gap-2 mt-3 mx-auto max-w-xs">
            <Info className="h-4 w-4 text-slate-500 flex-shrink-0" />
            <p className="text-xs text-slate-500 text-center">
              Kaydolarak ve giriş yaparak <Link href="/terms" className="text-blue-600 hover:underline">Kullanım Koşulları</Link>'nı ve <Link href="/privacy" className="text-blue-600 hover:underline">Gizlilik Politikası</Link>'nı kabul etmiş olursunuz.
            </p>
          </div>
        </div>
      </div>
    </FormErrorBoundary>
  )
}   