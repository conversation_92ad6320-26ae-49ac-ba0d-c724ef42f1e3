"use client"

import Link from "next/link"
import { ForgotPasswordForm } from "./components/ForgotPasswordForm"
import { motion } from "framer-motion"

export default function ForgotPasswordPage() {
  return (
    <main className="h-screen flex flex-col lg:flex-row items-stretch overflow-hidden">
      {/* Branding & Info Section (desktop only) */}
      <motion.div
        initial={{ opacity: 0, x: -50 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
        className="relative bg-gradient-to-br from-indigo-50 via-purple-50 to-slate-50 w-full lg:w-1/2 p-8 lg:p-12 xl:p-16 flex-col justify-center overflow-hidden hidden lg:flex"
      >
        {/* Enhanced Background patterns with animations */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1.5, delay: 0.3 }}
          className="absolute top-0 left-0 right-0 h-20 bg-gradient-to-r from-indigo-500/15 via-purple-500/10 to-pink-500/15"
        ></motion.div>

        <motion.div
          initial={{ scale: 0, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 1.2, delay: 0.5, ease: "easeOut" }}
          className="absolute -top-32 -right-32 w-[400px] h-[400px] rounded-full bg-gradient-to-br from-indigo-400/10 to-purple-600/15 backdrop-blur-3xl"
        ></motion.div>

        <motion.div
          initial={{ scale: 0, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 1.4, delay: 0.7, ease: "easeOut" }}
          className="absolute top-1/3 -left-32 w-[350px] h-[350px] rounded-full bg-gradient-to-br from-purple-400/10 to-pink-500/15 backdrop-blur-3xl"
        ></motion.div>

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 1.5, delay: 0.9 }}
          className="absolute bottom-0 right-0 w-full h-20 bg-gradient-to-r from-purple-500/15 via-indigo-500/10 to-blue-500/15"
        ></motion.div>

        {/* Floating particles */}
        <motion.div
          animate={{
            y: [0, -20, 0],
            opacity: [0.3, 0.6, 0.3]
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute top-1/4 right-1/4 w-2 h-2 rounded-full bg-indigo-400/40"
        ></motion.div>

        <motion.div
          animate={{
            y: [0, -15, 0],
            opacity: [0.2, 0.5, 0.2]
          }}
          transition={{
            duration: 3.5,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 1
          }}
          className="absolute top-3/4 left-1/3 w-1.5 h-1.5 rounded-full bg-purple-400/40"
        ></motion.div>
        {/* Branding Content (desktop only) */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="relative z-10"
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="inline-block mb-6"
          >
            <motion.div
              whileHover={{ scale: 1.05 }}
              transition={{ type: "spring", stiffness: 300 }}
              className="flex items-center gap-2 text-xl font-semibold text-indigo-700 cursor-pointer"
            >
              {/* ...existing SVG... */}
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                <polyline points="7.5 4.21 12 6.81 16.5 4.21"></polyline>
                <polyline points="7.5 19.79 7.5 14.6 3 12"></polyline>
                <polyline points="21 12 16.5 14.6 16.5 19.79"></polyline>
                <polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline>
                <line x1="12" y1="22.08" x2="12" y2="12"></line>
              </svg>
              <span>Adnomio</span>
            </motion.div>
          </motion.div>
          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 text-slate-900"
          >
            Şifrenizi mi <br/>
            <span className="text-indigo-600">unuttunuz?</span>
          </motion.h1>
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.0 }}
            className="text-base text-slate-600 mb-8 max-w-lg"
          >
            Endişelenmeyin, e-posta adresinizi girerek şifrenizi sıfırlayabilirsiniz. Size şifre sıfırlama bağlantısı göndereceğiz.
          </motion.p>
          {/* Steps (desktop only) */}
          <div className="space-y-6">
            {/* ...existing steps... */}
            <div className="flex items-start gap-3">
              <div className="w-8 h-8 rounded-full bg-indigo-100 flex items-center justify-center flex-shrink-0 mt-0.5">
                {/* ...existing SVG... */}
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                </svg>
              </div>
              <div>
                <h3 className="font-medium text-slate-900 mb-1">E-posta adresinizi girin</h3>
                <p className="text-sm text-slate-600">Hesabınızla ilişkilendirilmiş e-posta adresinizi giriniz.</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-8 h-8 rounded-full bg-indigo-100 flex items-center justify-center flex-shrink-0 mt-0.5">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
              <div>
                <h3 className="font-medium text-slate-900 mb-1">E-postanızı kontrol edin</h3>
                <p className="text-sm text-slate-600">Size şifre sıfırlama bağlantısı içeren bir e-posta göndereceğiz.</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-8 h-8 rounded-full bg-indigo-100 flex items-center justify-center flex-shrink-0 mt-0.5">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>
              <div>
                <h3 className="font-medium text-slate-900 mb-1">Yeni şifre oluşturun</h3>
                <p className="text-sm text-slate-600">Bağlantıya tıklayarak güvenli bir yeni şifre oluşturun.</p>
              </div>
            </div>
          </div>
        </motion.div>
        {/* Brand patterns */}
        <div className="hidden md:block absolute bottom-0 right-0 opacity-90 z-0">
          {/* ...existing SVG... */}
          <svg width="320" height="320" viewBox="0 0 320 320" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="160" cy="160" r="160" fill="url(#paint0_radial)" fillOpacity="0.1" />
            <circle cx="160" cy="160" r="110" fill="url(#paint1_radial)" fillOpacity="0.05" />
            <circle cx="160" cy="160" r="60" fill="url(#paint2_radial)" fillOpacity="0.1" />
            <defs>
              <radialGradient id="paint0_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(160 160) rotate(90) scale(160)">
                <stop stopColor="#6366F1" />
                <stop offset="1" stopColor="#6366F1" stopOpacity="0" />
              </radialGradient>
              <radialGradient id="paint1_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(160 160) rotate(90) scale(110)">
                <stop stopColor="#6366F1" />
                <stop offset="1" stopColor="#6366F1" stopOpacity="0" />
              </radialGradient>
              <radialGradient id="paint2_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(160 160) rotate(90) scale(60)">
                <stop stopColor="#6366F1" />
                <stop offset="1" stopColor="#6366F1" stopOpacity="0" />
              </radialGradient>
            </defs>
          </svg>
        </div>
      </motion.div>
      {/* Forgot Password Form Section (mobile & desktop) */}
      <motion.div
        initial={{ opacity: 0, x: 50 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.8, ease: "easeOut", delay: 0.2 }}
        className="relative w-full lg:w-1/2 p-6 lg:p-12 xl:p-16 flex flex-col justify-center min-h-screen lg:min-h-0"
      >
        {/* Enhanced Mobile Background Patterns */}
        <div className="absolute inset-0 lg:hidden overflow-hidden">
          <motion.div
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 1.2, delay: 0.3 }}
            className="absolute -top-32 -right-32 w-[400px] h-[400px] rounded-full bg-gradient-to-br from-indigo-400/8 to-purple-600/12 backdrop-blur-2xl"
          ></motion.div>
          <motion.div
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 1.4, delay: 0.5 }}
            className="absolute top-1/2 -left-32 w-[350px] h-[350px] rounded-full bg-gradient-to-br from-purple-400/8 to-pink-500/12 backdrop-blur-2xl"
          ></motion.div>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.7 }}
            className="absolute bottom-0 right-0 left-0 h-20 bg-gradient-to-r from-indigo-500/12 via-purple-500/8 to-pink-500/12"
          ></motion.div>
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.9 }}
            className="absolute top-0 right-0 left-0 h-20 bg-gradient-to-r from-purple-600/12 via-indigo-500/8 to-blue-500/12"
          ></motion.div>
        </div>
        {/* Mobile-only Header */}
        <motion.div
          initial={{ opacity: 0, y: -30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="lg:hidden mb-8"
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="flex flex-col items-center gap-2"
          >
            <motion.div
              whileHover={{ scale: 1.05 }}
              transition={{ type: "spring", stiffness: 300 }}
              className="flex items-center gap-2 text-2xl font-semibold text-indigo-700"
            >
              {/* ...existing SVG... */}
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                <polyline points="7.5 4.21 12 6.81 16.5 4.21"></polyline>
                <polyline points="7.5 19.79 7.5 14.6 3 12"></polyline>
                <polyline points="21 12 16.5 14.6 16.5 19.79"></polyline>
                <polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline>
                <line x1="12" y1="22.08" x2="12" y2="12"></line>
              </svg>
              <span>Adnomio</span>
            </motion.div>
            <motion.h2
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.8 }}
              className="text-2xl font-bold text-slate-900"
            >
              Şifrenizi Sıfırlayın
            </motion.h2>
          </motion.div>
        </motion.div>
        {/* Enhanced Form Card - Glassmorphism Effect (mobile only) */}
        <motion.div
          initial={{ opacity: 0, y: 30, scale: 0.95 }}
          animate={{ opacity: 1, y: 0, scale: 1 }}
          transition={{ duration: 0.8, delay: 0.6, ease: "easeOut" }}
          whileHover={{
            y: -5,
            boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(99, 102, 241, 0.1)"
          }}
          className="lg:hidden relative bg-white/90 backdrop-blur-xl p-8 rounded-3xl shadow-2xl border border-white/20
                     before:absolute before:inset-0 before:-z-10 before:translate-x-3 before:translate-y-3
                     before:bg-gradient-to-br before:from-indigo-100/60 before:to-purple-100/60
                     before:rounded-3xl before:blur-lg hover:before:translate-x-4 hover:before:translate-y-4
                     before:transition-transform before:duration-300"
        >
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.8 }}
            className="mb-6"
          >
            <p className="text-slate-600 text-center">E-posta adresinizi girerek şifre sıfırlama bağlantısı alın.</p>
          </motion.div>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 1.0 }}
          >
            <ForgotPasswordForm />
          </motion.div>
        </motion.div>
        {/* Enhanced Desktop Form */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.4 }}
          className="hidden lg:block"
        >
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="mb-8 lg:mt-8"
          >
            <motion.h2
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.8 }}
              className="text-2xl font-bold text-slate-900 mb-2"
            >
              Şifrenizi Sıfırlayın
            </motion.h2>
            <motion.p
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 1.0 }}
              className="text-slate-600"
            >
              E-posta adresinizi girerek şifre sıfırlama bağlantısı alın.
            </motion.p>
          </motion.div>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 1.2 }}
          >
            <ForgotPasswordForm />
          </motion.div>
        </motion.div>
      </motion.div>
    </main>
  )
}