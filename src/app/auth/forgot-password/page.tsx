import Link from "next/link"
import { ForgotPasswordForm } from "./components/ForgotPasswordForm"

export default function ForgotPasswordPage() {
  return (
    <main className="h-screen flex flex-col lg:flex-row items-stretch">
      {/* Branding & Info Section (desktop only) */}
      <div className="relative bg-gradient-to-br from-indigo-50 via-purple-50 to-slate-50 w-full lg:w-1/2 p-8 lg:p-12 xl:p-16 flex-col justify-center overflow-hidden hidden lg:flex">
        {/* Background patterns */}
        <div className="absolute top-0 left-0 right-0 h-16 bg-gradient-to-r from-indigo-500/10 to-purple-600/10"></div>
        <div className="absolute -top-24 -right-24 w-96 h-96 rounded-full bg-indigo-500/5"></div>
        <div className="absolute top-1/3 -left-24 w-80 h-80 rounded-full bg-purple-500/5 backdrop-blur-3xl"></div>
        <div className="absolute bottom-0 right-0 w-full h-16 bg-gradient-to-r from-indigo-500/10 to-purple-600/10"></div>
        {/* Branding Content (desktop only) */}
        <div className="relative z-10">
          <div className="inline-block mb-6">
            <div className="flex items-center gap-2 text-xl font-semibold text-indigo-700">
              {/* ...existing SVG... */}
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                <polyline points="7.5 4.21 12 6.81 16.5 4.21"></polyline>
                <polyline points="7.5 19.79 7.5 14.6 3 12"></polyline>
                <polyline points="21 12 16.5 14.6 16.5 19.79"></polyline>
                <polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline>
                <line x1="12" y1="22.08" x2="12" y2="12"></line>
              </svg>
              <span>Adnomio</span>
            </div>
          </div>
          <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 text-slate-900">
            Şifrenizi mi <br/>
            <span className="text-indigo-600">unuttunuz?</span>
          </h1>
          <p className="text-base text-slate-600 mb-8 max-w-lg">
            Endişelenmeyin, e-posta adresinizi girerek şifrenizi sıfırlayabilirsiniz. Size şifre sıfırlama bağlantısı göndereceğiz.
          </p>
          {/* Steps (desktop only) */}
          <div className="space-y-6">
            {/* ...existing steps... */}
            <div className="flex items-start gap-3">
              <div className="w-8 h-8 rounded-full bg-indigo-100 flex items-center justify-center flex-shrink-0 mt-0.5">
                {/* ...existing SVG... */}
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                </svg>
              </div>
              <div>
                <h3 className="font-medium text-slate-900 mb-1">E-posta adresinizi girin</h3>
                <p className="text-sm text-slate-600">Hesabınızla ilişkilendirilmiş e-posta adresinizi giriniz.</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-8 h-8 rounded-full bg-indigo-100 flex items-center justify-center flex-shrink-0 mt-0.5">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
              <div>
                <h3 className="font-medium text-slate-900 mb-1">E-postanızı kontrol edin</h3>
                <p className="text-sm text-slate-600">Size şifre sıfırlama bağlantısı içeren bir e-posta göndereceğiz.</p>
              </div>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-8 h-8 rounded-full bg-indigo-100 flex items-center justify-center flex-shrink-0 mt-0.5">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>
              <div>
                <h3 className="font-medium text-slate-900 mb-1">Yeni şifre oluşturun</h3>
                <p className="text-sm text-slate-600">Bağlantıya tıklayarak güvenli bir yeni şifre oluşturun.</p>
              </div>
            </div>
          </div>
        </div>
        {/* Brand patterns */}
        <div className="hidden md:block absolute bottom-0 right-0 opacity-90 z-0">
          {/* ...existing SVG... */}
          <svg width="320" height="320" viewBox="0 0 320 320" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="160" cy="160" r="160" fill="url(#paint0_radial)" fillOpacity="0.1" />
            <circle cx="160" cy="160" r="110" fill="url(#paint1_radial)" fillOpacity="0.05" />
            <circle cx="160" cy="160" r="60" fill="url(#paint2_radial)" fillOpacity="0.1" />
            <defs>
              <radialGradient id="paint0_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(160 160) rotate(90) scale(160)">
                <stop stopColor="#6366F1" />
                <stop offset="1" stopColor="#6366F1" stopOpacity="0" />
              </radialGradient>
              <radialGradient id="paint1_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(160 160) rotate(90) scale(110)">
                <stop stopColor="#6366F1" />
                <stop offset="1" stopColor="#6366F1" stopOpacity="0" />
              </radialGradient>
              <radialGradient id="paint2_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(160 160) rotate(90) scale(60)">
                <stop stopColor="#6366F1" />
                <stop offset="1" stopColor="#6366F1" stopOpacity="0" />
              </radialGradient>
            </defs>
          </svg>
        </div>
      </div>
      {/* Forgot Password Form Section (mobile & desktop) */}
      <div className="relative w-full lg:w-1/2 p-6 lg:p-12 xl:p-16 flex flex-col justify-center min-h-screen lg:min-h-0">
        {/* Mobile Background Patterns */}
        <div className="absolute inset-0 lg:hidden overflow-hidden">
          <div className="absolute -top-24 -right-24 w-96 h-96 rounded-full bg-indigo-500/5"></div>
          <div className="absolute top-1/2 -left-24 w-80 h-80 rounded-full bg-purple-500/5"></div>
          <div className="absolute bottom-0 right-0 left-0 h-16 bg-gradient-to-r from-indigo-500/10 to-purple-600/10"></div>
          <div className="absolute top-0 right-0 left-0 h-16 bg-gradient-to-r from-purple-600/10 to-indigo-500/10"></div>
        </div>
        {/* Mobile-only Header */}
        <div className="lg:hidden mb-8">
          
          <div className="flex flex-col items-center gap-2">
            <div className="flex items-center gap-2 text-2xl font-semibold text-indigo-700">
              {/* ...existing SVG... */}
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                <polyline points="7.5 4.21 12 6.81 16.5 4.21"></polyline>
                <polyline points="7.5 19.79 7.5 14.6 3 12"></polyline>
                <polyline points="21 12 16.5 14.6 16.5 19.79"></polyline>
                <polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline>
                <line x1="12" y1="22.08" x2="12" y2="12"></line>
              </svg>
              <span>Adnomio</span>
            </div>
            <h2 className="text-2xl font-bold text-slate-900">Şifrenizi Sıfırlayın</h2>
          </div>
        </div>
        {/* Form Card - 3D Effect (mobile only) */}
        <div className="lg:hidden relative bg-white p-6 rounded-2xl shadow-lg border border-indigo-100 before:absolute before:inset-0 before:-z-10 before:translate-x-2 before:translate-y-2 before:bg-indigo-100/50 before:rounded-2xl before:blur-sm">
          <div className="mb-6">
            <p className="text-slate-600">E-posta adresinizi girerek şifre sıfırlama bağlantısı alın.</p>
          </div>
          <ForgotPasswordForm />
        </div>
        {/* Desktop Form - Original */}
        <div className="hidden lg:block">
          <div className="mb-8 lg:mt-8">
            <h2 className="text-2xl font-bold text-slate-900 mb-2">Şifrenizi Sıfırlayın</h2>
            <p className="text-slate-600">E-posta adresinizi girerek şifre sıfırlama bağlantısı alın.</p>
          </div>
          <ForgotPasswordForm />
        </div>
      </div>
    </main>
  )
}