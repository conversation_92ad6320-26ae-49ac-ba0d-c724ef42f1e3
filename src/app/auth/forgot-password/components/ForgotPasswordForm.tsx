"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { toast } from "@/components/ui/use-toast"
import { Mail, ArrowLeft, CheckCircle, Loader2 } from "lucide-react"
import Link from "next/link"
import { useFormValidation } from "@/shared/hooks/useFormValidation"
import { useFormValidationContext } from "@/shared/hooks/FormValidationProvider"
import { FormField } from "@/components/ui/form/FormField"
import { FormErrorBoundary } from "@/components/ui/form/FormErrorBoundary"
import { ProgressIndicator } from "@/components/ui/progress-indicator"
import { motion, AnimatePresence } from "framer-motion"

export function ForgotPasswordForm() {
  const router = useRouter()
  const [submitted, setSubmitted] = useState(false)
  const [formData, setFormData] = useState({
    email: ""
  })
  const [formFocus, setFormFocus] = useState(false)
  const [currentStep, setCurrentStep] = useState(0)

  const steps = ["E-posta Gir", "E-posta Gönder", "Tamamlandı"]

  const { errors, isSubmitting, handleChange, handleSubmit } = useFormValidation(
    useFormValidationContext().getFieldRules('forgotPassword')
  )
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    handleChange(name, value)
  }

  const handleFocus = () => {
    setFormFocus(true)
  }

  const handleBlur = () => {
    setFormFocus(false)
  }

  const onSubmitForm = async () => {
    // Step 1: Start sending
    setCurrentStep(1)

    // Gerçek projede bu kısım API çağrısı olacak
    await new Promise(resolve => setTimeout(resolve, 1500)) // Simulate API call

    // Step 2: Complete
    setCurrentStep(2)

    toast({
      title: "Bağlantı gönderildi",
      description: "E-posta adresinize şifre sıfırlama bağlantısı gönderdik.",
      className: "bg-green-50 border-green-200 text-green-800"
    })

    setSubmitted(true)
  }

  const handleFormSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    await handleSubmit(formData, onSubmitForm, 'forgot-password')
  }

  if (submitted) {
    return (
      <AnimatePresence mode="wait">
        <motion.div
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.9 }}
          transition={{ duration: 0.5, ease: "easeOut" }}
          className="space-y-6"
        >
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="p-8 rounded-2xl bg-gradient-to-br from-green-50 to-emerald-50 border border-green-200/60 text-center space-y-4 backdrop-blur-sm"
          >
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{
                type: "spring",
                stiffness: 200,
                damping: 15,
                delay: 0.4
              }}
              className="flex justify-center"
            >
              <motion.div
                animate={{
                  boxShadow: [
                    "0 0 0 0 rgba(34, 197, 94, 0.4)",
                    "0 0 0 10px rgba(34, 197, 94, 0)",
                    "0 0 0 0 rgba(34, 197, 94, 0)"
                  ]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
                className="w-20 h-20 rounded-full bg-gradient-to-br from-green-100 to-emerald-100 flex items-center justify-center"
              >
                <CheckCircle className="h-10 w-10 text-green-600" />
              </motion.div>
            </motion.div>
            <motion.h3
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.6 }}
              className="text-xl font-semibold text-green-800"
            >
              E-posta Gönderildi
            </motion.h3>
            <motion.p
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.8 }}
              className="text-green-700"
            >
              Şifre sıfırlama bağlantısı <span className="font-semibold">{formData.email}</span> adresine gönderildi.
              Lütfen e-postanızı kontrol edin.
            </motion.p>
            <motion.p
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 1.0 }}
              className="text-sm text-green-600"
            >
              E-posta spam klasörünüzde değilse, birkaç dakika içinde gelmesi gerekir.
            </motion.p>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 1.2 }}
            className="space-y-4"
          >
            <motion.div
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Button
                type="button"
                variant="outline"
                className="w-full mobile-enhanced touch-manipulation tap-highlight-none border-blue-200 text-blue-700 hover:text-blue-800 hover:bg-blue-50
                          hover:border-blue-300 transition-all duration-300 rounded-xl font-medium py-6 md:py-5"
                onClick={() => {
                  setSubmitted(false)
                  setCurrentStep(0)
                }}
              >
                Farklı bir e-posta adresi dene
              </Button>
            </motion.div>

            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.5, delay: 1.4 }}
              whileHover={{ scale: 1.05 }}
              className="flex justify-center"
            >
              <Link href="/auth/login" className="flex items-center gap-2 text-sm text-blue-600 hover:text-blue-800
                                                  hover:underline transition-all duration-200 px-4 py-3 rounded-lg
                                                  hover:bg-blue-50 touch-manipulation tap-highlight-none touch-target">
                <ArrowLeft className="h-3 w-3" />
                <span>Giriş sayfasına dön</span>
              </Link>
            </motion.div>
          </motion.div>
        </motion.div>
      </AnimatePresence>
    )
  }

  return (
    <FormErrorBoundary formName="Şifre Sıfırlama">
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6 }}
        className="space-y-6"
      >
        {/* Progress Indicator */}
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.1 }}
          className="mb-8"
        >
          <ProgressIndicator
            steps={steps}
            currentStep={currentStep}
            className="max-w-md mx-auto"
          />
        </motion.div>

        <motion.form
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          onSubmit={handleFormSubmit}
          className="space-y-6"
        >
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
          >
            <FormField
              id="email"
              name="email"
              type="email"
              label="E-posta Adresi"
              value={formData.email}
              error={errors.email}
              icon={<Mail className="h-4 w-4" strokeWidth={2} />}
              placeholder="<EMAIL>"
              required
              autoComplete="email"
              focused={formFocus}
              onChange={handleInputChange}
              onFocus={handleFocus}
              onBlur={handleBlur}
            />
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <Button
              type="submit"
              className={`w-full mobile-enhanced touch-manipulation tap-highlight-none font-semibold transition-all duration-500 ${
                isSubmitting
                  ? 'bg-blue-600 cursor-not-allowed py-7'
                  : 'bg-gradient-to-r from-blue-500 via-indigo-600 to-purple-600 hover:from-blue-600 hover:via-indigo-700 hover:to-purple-700 hover:shadow-2xl hover:shadow-blue-500/25 py-7 md:py-6'
              } text-white rounded-2xl border-0 relative overflow-hidden group text-base md:text-sm lg:text-base`}
              disabled={isSubmitting}
            >
              {/* Shimmer effect */}
              <div className="absolute inset-0 -top-[1px] bg-gradient-to-r from-transparent via-white/20 to-transparent
                            translate-x-[-100%] group-hover:translate-x-[100%] transition-transform duration-1000"></div>

              <AnimatePresence mode="wait">
                {isSubmitting ? (
                  <motion.div
                    key="loading"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="flex items-center justify-center gap-3"
                  >
                    <Loader2 className="h-5 w-5 animate-spin" />
                    <span>Bağlantı Gönderiliyor...</span>
                  </motion.div>
                ) : (
                  <motion.span
                    key="submit"
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                  >
                    Şifre Sıfırlama Bağlantısı Gönder
                  </motion.span>
                )}
              </AnimatePresence>
            </Button>
          </motion.div>
        </motion.form>

        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.8 }}
          className="pt-6 flex justify-center"
        >
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Link href="/auth/login" className="flex items-center gap-2 text-sm text-blue-600 hover:text-blue-800
                                              transition-all duration-200 px-4 py-3 rounded-lg hover:bg-blue-50
                                              hover:shadow-sm group touch-manipulation tap-highlight-none touch-target">
              <ArrowLeft className="h-3 w-3 group-hover:-translate-x-1 transition-transform duration-200" />
              <span>Giriş sayfasına dön</span>
            </Link>
          </motion.div>
        </motion.div>
      </motion.div>
    </FormErrorBoundary>
  )
}