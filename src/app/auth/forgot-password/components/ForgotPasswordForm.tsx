"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { toast } from "@/components/ui/use-toast"
import { Mail, ArrowLeft, CheckCircle } from "lucide-react"
import Link from "next/link"
import { useFormValidation } from "@/shared/hooks/useFormValidation"
import { useFormValidationContext } from "@/shared/hooks/FormValidationProvider"
import { FormField } from "@/components/ui/form/FormField"
import { FormErrorBoundary } from "@/components/ui/form/FormErrorBoundary"

export function ForgotPasswordForm() {
  const router = useRouter()
  const [submitted, setSubmitted] = useState(false)
  const [formData, setFormData] = useState({
    email: ""
  })
  const [formFocus, setFormFocus] = useState(false)

  const { errors, isSubmitting, handleChange, handleSubmit } = useFormValidation(
    useFormValidationContext().getFieldRules('forgotPassword')
  )
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    handleChange(name, value)
  }

  const handleFocus = () => {
    setFormFocus(true)
  }

  const handleBlur = () => {
    setFormFocus(false)
  }

  const onSubmitForm = async () => {
    // Gerçek projede bu kısım API çağrısı olacak
    await new Promise(resolve => setTimeout(resolve, 1500)) // Simulate API call
    
    toast({
      title: "Bağlantı gönderildi",
      description: "E-posta adresinize şifre sıfırlama bağlantısı gönderdik.",
      className: "bg-green-50 border-green-200 text-green-800"
    })
    
    setSubmitted(true)
  }

  const handleFormSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    await handleSubmit(formData, onSubmitForm, 'forgot-password')
  }

  if (submitted) {
    return (
      <div className="space-y-6">
        <div className="p-8 rounded-xl bg-green-50 border border-green-100 text-center space-y-3">
          <div className="flex justify-center">
            <div className="w-16 h-16 rounded-full bg-green-100 flex items-center justify-center">
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </div>
          <h3 className="text-xl font-medium text-green-800">E-posta Gönderildi</h3>
          <p className="text-green-700">
            Şifre sıfırlama bağlantısı <span className="font-medium">{formData.email}</span> adresine gönderildi.
            Lütfen e-postanızı kontrol edin.
          </p>
          <p className="text-sm text-green-600 mt-2">
            E-posta spam klasörünüzde değilse, birkaç dakika içinde gelmesi gerekir.
          </p>
        </div>
        
        <div className="space-y-4">
          <Button 
            type="button" 
            variant="outline" 
            className="w-full py-5 border-blue-200 text-blue-700 hover:text-blue-800 hover:bg-blue-50"
            onClick={() => setSubmitted(false)}
          >
            Farklı bir e-posta adresi dene
          </Button>
          
          <Link href="/auth/login" className="flex items-center justify-center gap-2 text-sm text-blue-600 hover:text-blue-800 hover:underline mt-4">
            <ArrowLeft className="h-3 w-3" />
            <span>Giriş sayfasına dön</span>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <FormErrorBoundary formName="Şifre Sıfırlama">
      <div className="space-y-6">
        <form onSubmit={handleFormSubmit} className="space-y-5">
          <FormField
            id="email"
            name="email"
            type="email"
            label="E-posta Adresi"
            value={formData.email}
            error={errors.email}
            icon={<Mail className="h-4 w-4" strokeWidth={2} />}
            placeholder="<EMAIL>"
            required
            autoComplete="email"
            focused={formFocus}
            onChange={handleInputChange}
            onFocus={handleFocus}
            onBlur={handleBlur}
          />
          
          <Button 
            type="submit" 
            className={`w-full py-6 mt-3 text-base transition-all duration-300 ${
              isSubmitting 
                ? 'bg-blue-600'
                : 'bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 hover:scale-[1.02]'
            } text-white font-medium shadow-lg shadow-blue-500/20 rounded-xl`}
            disabled={isSubmitting}
          >
            {isSubmitting ? (
              <div className="flex items-center justify-center gap-2">
                <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span>Bağlantı Gönderiliyor...</span>
              </div>
            ) : (
              <span>Şifre Sıfırlama Bağlantısı Gönder</span>
            )}
          </Button>
        </form>
        
        <div className="pt-4">
          <Link href="/auth/login" className="flex items-center gap-2 text-sm text-blue-600 hover:text-blue-800 hover:underline">
            <ArrowLeft className="h-3 w-3" />
            <span>Giriş sayfasına dön</span>
          </Link>
        </div>
      </div>
    </FormErrorBoundary>
  )
} 