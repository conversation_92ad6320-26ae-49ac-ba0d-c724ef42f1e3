import Link from "next/link"
import { PublisherSignupForm } from "./components/PublisherSignupForm"

export default function PublisherSignupPage() {
  return (
    <main className="h-screen flex flex-col lg:flex-row items-stretch">
      {/* Branding & Info Section */}
      <div className="relative bg-gradient-to-br from-emerald-50 via-teal-50 to-slate-50 w-full lg:w-1/2 p-8 lg:p-12 xl:p-16 flex flex-col justify-center overflow-hidden hidden lg:flex">
        {/* Background patterns */}
        <div className="absolute top-0 left-0 right-0 h-16 bg-gradient-to-r from-emerald-500/10 to-teal-600/10"></div>
        <div className="absolute -top-24 -right-24 w-96 h-96 rounded-full bg-emerald-500/5"></div>
        <div className="absolute top-1/3 -left-24 w-80 h-80 rounded-full bg-teal-500/5 backdrop-blur-3xl"></div>
        <div className="absolute bottom-0 right-0 w-full h-16 bg-gradient-to-r from-emerald-500/10 to-teal-600/10"></div>
        
        {/* Branding Content (desktop only) */}
        <div className="relative z-10">
          <div className="inline-block mb-6">
            <div className="flex items-center gap-2 text-xl font-semibold text-emerald-700">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                <polyline points="7.5 4.21 12 6.81 16.5 4.21"></polyline>
                <polyline points="7.5 19.79 7.5 14.6 3 12"></polyline>
                <polyline points="21 12 16.5 14.6 16.5 19.79"></polyline>
                <polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline>
                <line x1="12" y1="22.08" x2="12" y2="12"></line>
              </svg>
              <span>Adnomio</span>
            </div>
          </div>
          
          <h1 className="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 text-slate-900">
            İçerik üretiminizi <br/> 
            <span className="text-emerald-600">değere dönüştürün</span>
          </h1>
          
          {/* Desktop info and features only */}
          <p className="text-base text-slate-600 mb-8 max-w-lg hidden lg:block">
            Adnomio ile içeriklerinizi değere dönüştürün, takipçilerinizi artırın ve markalarla doğrudan bağlantı kurun.
          </p>
          <div className="space-y-6 hidden lg:block">
            {/* ...feature blocks... */}
          </div>
        </div>
        
        {/* Brand patterns */}
        <div className="hidden md:block absolute bottom-0 right-0 opacity-90 z-0">
          <svg width="320" height="320" viewBox="0 0 320 320" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="160" cy="160" r="160" fill="url(#paint0_radial)" fillOpacity="0.1" />
            <circle cx="160" cy="160" r="110" fill="url(#paint1_radial)" fillOpacity="0.05" />
            <circle cx="160" cy="160" r="60" fill="url(#paint2_radial)" fillOpacity="0.1" />
            <defs>
              <radialGradient id="paint0_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(160 160) rotate(90) scale(160)">
                <stop stopColor="#10B981" />
                <stop offset="1" stopColor="#10B981" stopOpacity="0" />
              </radialGradient>
              <radialGradient id="paint1_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(160 160) rotate(90) scale(110)">
                <stop stopColor="#10B981" />
                <stop offset="1" stopColor="#10B981" stopOpacity="0" />
              </radialGradient>
              <radialGradient id="paint2_radial" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(160 160) rotate(90) scale(60)">
                <stop stopColor="#10B981" />
                <stop offset="1" stopColor="#10B981" stopOpacity="0" />
              </radialGradient>
            </defs>
          </svg>
        </div>
      </div>
      
      {/* Signup Form Section (mobile & desktop) */}
      <div className="relative w-full lg:w-1/2 p-6 lg:p-12 xl:p-16 flex flex-col justify-center min-h-screen lg:min-h-0">
        {/* Mobil Arka Plan Desenleri */}
        <div className="absolute inset-0 lg:hidden overflow-hidden">
          <div className="absolute -top-24 -right-24 w-96 h-96 rounded-full bg-emerald-500/5"></div>
          <div className="absolute top-1/2 -left-24 w-80 h-80 rounded-full bg-teal-500/5"></div>
          <div className="absolute bottom-0 right-0 left-0 h-16 bg-gradient-to-r from-emerald-500/10 to-teal-600/10"></div>
          <div className="absolute top-0 right-0 left-0 h-16 bg-gradient-to-r from-teal-600/10 to-emerald-500/10"></div>
        </div>
        
        {/* Form Container */}
        <div className="relative z-10 max-w-md mx-auto w-full">
          {/* Mobile-only Header */}
          <div className="lg:hidden mb-8">
            <div className="flex items-center mb-4">
              <Link href="/auth/login" className="inline-flex items-center text-sm text-emerald-600 hover:text-emerald-800 transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Geri Git
              </Link>
            </div>
            <div className="flex flex-col items-center gap-2">
              <div className="flex items-center gap-2 text-2xl font-semibold text-emerald-700">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path>
                  <polyline points="7.5 4.21 12 6.81 16.5 4.21"></polyline>
                  <polyline points="7.5 19.79 7.5 14.6 3 12"></polyline>
                  <polyline points="21 12 16.5 14.6 16.5 19.79"></polyline>
                  <polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline>
                  <line x1="12" y1="22.08" x2="12" y2="12"></line>
                </svg>
                <span>Adnomio</span>
              </div>
              <h2 className="text-2xl font-bold text-slate-900">İçerik Üreticisi Hesabı Oluşturun</h2>
            </div>
          </div>
          
          {/* Form Card - 3D Effect */}
          <div className="lg:hidden relative bg-white p-6 rounded-2xl shadow-lg border border-emerald-100 before:absolute before:inset-0 before:-z-10 before:translate-x-2 before:translate-y-2 before:bg-emerald-100/50 before:rounded-2xl before:blur-sm">
            <div className="mb-6">
              <p className="text-slate-600">Markalarla buluşmak için Adnomio'ya ücretsiz kayıt olun.</p>
            </div>
            
            <PublisherSignupForm />
          </div>
          
          {/* Desktop Form - Original */}
          <div className="hidden lg:block">
            <div className="mb-8 lg:mt-8">
              <h2 className="text-2xl font-bold text-slate-900 mb-2">İçerik Üreticisi Hesabı Oluşturun</h2>
              <p className="text-slate-600">Markalarla buluşmak için Adnomio'ya ücretsiz kayıt olun.</p>
            </div>
            
            <PublisherSignupForm />
          </div>
        </div>
      </div>
    </main>
  )
}