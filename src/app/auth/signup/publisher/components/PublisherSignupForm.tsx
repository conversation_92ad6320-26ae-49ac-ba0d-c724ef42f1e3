"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { toast } from "@/components/ui/use-toast"
import { Eye, EyeOff, Mail, Lock, User, Check } from "lucide-react"
import { Checkbox } from "@/components/ui/checkbox"
import { publisherRegister } from "@/shared/services/publisher/publisher-register"
import { authService } from "@/shared/services/auth-service"
import { useFormValidation, commonValidationRules, ValidationRules, ValidationRule } from "@/shared/hooks/useFormValidation"
import { FormField } from "@/components/ui/form/FormField"

export function PublisherSignupForm() {
  const router = useRouter()
  const [loading, setLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    password: "",
    agreeTerms: false
  })
  const [formFocus, setFormFocus] = useState({
    firstName: false,
    lastName: false,
    email: false,
    password: false
  })
  const [errors, setErrors] = useState({
    firstName: "",
    lastName: "",
    email: "",
    password: "",
    agreeTerms: ""
  })

  // Form validasyon kuralları
  const validationRules: ValidationRules = {
    firstName: [commonValidationRules.required, commonValidationRules.name],
    lastName: [commonValidationRules.required, commonValidationRules.name],
    email: [commonValidationRules.required, commonValidationRules.email],
    password: [commonValidationRules.required, commonValidationRules.password],
    agreeTerms: [commonValidationRules.agreeTerms],
  }

  const {
    isSubmitting,
    handleChange,
    handleSubmit,
  } = useFormValidation(validationRules)
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target
    const newValue = type === 'checkbox' ? checked : value
    
    setFormData(prev => ({
      ...prev,
      [name]: newValue
    }))
    
    handleChange(name, type === 'checkbox' ? checked : value)
  }

  const handleFocus = (field: keyof typeof formFocus) => {
    setFormFocus(prev => ({
      ...prev,
      [field]: true
    }))
  }

  const handleBlur = (field: keyof typeof formFocus) => {
    setFormFocus(prev => ({
      ...prev,
      [field]: false
    }))
  }

  const onSubmitForm = async () => {
    try {
      const response = await publisherRegister({
        email: formData.email,
        password: formData.password,
        name: formData.firstName,
        surname: formData.lastName,
        created_at: Math.floor(Date.now() / 1000)
      });

      if (response.status) {
        const loginResponse = await authService.login(formData.email, formData.password);
        
        if (loginResponse.status && loginResponse.result && loginResponse.result.user && loginResponse.result.token) {
          localStorage.setItem("user", JSON.stringify({ ...loginResponse.result.user, token: loginResponse.result.token }));
          
          toast({
            title: "Kayıt ve giriş başarılı",
            description: "Hesabınız oluşturuldu ve giriş yapıldı.",
            className: "bg-green-50 border-green-200 text-green-800"
          });
          
          router.push("/publisher/community-onboarding");
        } else {
          toast({
            title: "Kayıt başarılı, giriş başarısız",
            description: "Lütfen giriş yapmayı deneyin.",
            variant: "destructive"
          });
          router.push("/auth/login");
        }
      } else {
        if (response.message?.toLowerCase().includes('already exists') || 
            response.message?.toLowerCase().includes('already registered') ||
            response.message?.toLowerCase().includes('zaten kayıtlı')) {
          toast({
            title: "Kayıt Başarısız",
            description: "Bu e-posta adresi ile daha önce kayıt yapılmış. Lütfen giriş yapmayı deneyin veya farklı bir e-posta adresi kullanın.",
            variant: "destructive"
          });
          setErrors(prev => ({
            ...prev,
            email: "Bu e-posta adresi ile daha önce kayıt yapılmış"
          }));
        } else {
          toast({
            title: "Kayıt Başarısız",
            description: response.message || "Kayıt işlemi sırasında bir hata oluştu. Lütfen tekrar deneyin.",
            variant: "destructive"
          });
        }
        throw new Error(response.message || "Kayıt işlemi başarısız");
      }
    } catch (error: any) {
      if (error.message?.toLowerCase().includes('already exists') || 
          error.message?.toLowerCase().includes('already registered') ||
          error.message?.toLowerCase().includes('zaten kayıtlı')) {
        toast({
          title: "Kayıt Başarısız",
          description: "Bu e-posta adresi ile daha önce kayıt yapılmış. Lütfen giriş yapmayı deneyin veya farklı bir e-posta adresi kullanın.",
          variant: "destructive"
        });
        setErrors(prev => ({
          ...prev,
          email: "Bu e-posta adresi ile daha önce kayıt yapılmış"
        }));
      } else {
        toast({
          title: "Kayıt Başarısız",
          description: "Kayıt işlemi sırasında bir hata oluştu. Lütfen tekrar deneyin.",
          variant: "destructive"
        });
      }
      throw error;
    }
  }

  const handleFormSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    
    // Form validasyonunu manuel olarak tetikle
    const isValid = Object.keys(validationRules).every(field => {
      const value = formData[field as keyof typeof formData]
      const error = validationRules[field].some((rule: ValidationRule) => !rule.test(value))
      if (error) {
        handleChange(field, value)
      }
      return !error
    })

    if (!isValid) {
      toast({
        title: "Hata",
        description: "Lütfen tüm zorunlu alanları doldurun ve kullanım koşullarını kabul edin",
        variant: "destructive"
      })
      return
    }

    await handleSubmit(formData, onSubmitForm, 'publisher-signup');
  }

  return (
    <form onSubmit={handleFormSubmit} className="space-y-5">
      <div className="grid grid-cols-2 gap-4">
        <FormField
          id="firstName"
          name="firstName"
          type="text"
          label="Ad"
          value={formData.firstName}
          error={errors.firstName}
          icon={<User className="h-4 w-4" strokeWidth={2} />}
          placeholder="Adınız"
          required
          focused={formFocus.firstName}
          onChange={handleInputChange}
          onFocus={() => handleFocus('firstName')}
          onBlur={() => handleBlur('firstName')}
        />
        
        <FormField
          id="lastName"
          name="lastName"
          type="text"
          label="Soyad"
          value={formData.lastName}
          error={errors.lastName}
          icon={<User className="h-4 w-4" strokeWidth={2} />}
          placeholder="Soyadınız"
          required
          focused={formFocus.lastName}
          onChange={handleInputChange}
          onFocus={() => handleFocus('lastName')}
          onBlur={() => handleBlur('lastName')}
        />
      </div>
      
      <FormField
        id="email"
        name="email"
        type="email"
        label="E-posta Adresi"
        value={formData.email}
        error={errors.email}
        icon={<Mail className="h-4 w-4" strokeWidth={2} />}
        placeholder="<EMAIL>"
        required
        autoComplete="email"
        focused={formFocus.email}
        onChange={handleInputChange}
        onFocus={() => handleFocus('email')}
        onBlur={() => handleBlur('email')}
      />
      
      <div className="space-y-1.5 relative">
        <FormField
          id="password"
          name="password"
          type={showPassword ? "text" : "password"}
          label="Şifre"
          value={formData.password}
          error={errors.password}
          icon={<Lock className="h-4 w-4" strokeWidth={2} />}
          placeholder="En az 8 karakter"
          required
          autoComplete="new-password"
          focused={formFocus.password}
          onChange={handleInputChange}
          onFocus={() => handleFocus('password')}
          onBlur={() => handleBlur('password')}
        />
        <button
          type="button"
          className={`absolute right-3 top-[38px] flex items-center transition-colors duration-200 z-20 ${formFocus.password ? 'text-emerald-600' : 'text-slate-600'} hover:text-slate-800`}
          onClick={() => setShowPassword(!showPassword)}
        >
          {showPassword ? <EyeOff className="h-4 w-4" strokeWidth={2} /> : <Eye className="h-4 w-4" strokeWidth={2} />}
        </button>
      </div>
      
      <div className="grid gap-1.5">
        <label
          htmlFor="agreeTerms"
          className="text-sm text-slate-700 font-normal leading-snug flex items-start gap-2"
        >
          <Checkbox
            id="agreeTerms"
            name="agreeTerms"
            checked={formData.agreeTerms}
            onCheckedChange={(checked) => {
              const newValue = checked === true
              setFormData(prev => ({
                ...prev,
                agreeTerms: newValue
              }))
              handleChange('agreeTerms', newValue)
            }}
            className={`mt-0.5 ${errors.agreeTerms ? 'border-red-300' : ''}`}
          />
          <span>
            <a href="#" className="text-emerald-600 hover:text-emerald-700 hover:underline">Kullanım Koşulları</a>{" "}
            ve{" "}
            <a href="#" className="text-emerald-600 hover:text-emerald-700 hover:underline">Gizlilik Politikası</a>
            'nı okudum ve kabul ediyorum.
          </span>
        </label>
        {errors.agreeTerms && (
          <p className="text-xs text-red-500 pl-6">{errors.agreeTerms}</p>
        )}
      </div>
      
      <Button 
        type="submit" 
        className={`w-full py-6 mt-3 text-base transition-all duration-300 ${
          isSubmitting 
            ? 'bg-emerald-600'
            : 'bg-gradient-to-r from-emerald-500 to-teal-600 hover:from-emerald-600 hover:to-teal-700 hover:scale-[1.02]'
        } text-white font-medium shadow-lg shadow-emerald-500/20 rounded-xl`}
        disabled={isSubmitting}
      >
        {isSubmitting ? (
          <div className="flex items-center justify-center gap-2">
            <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span>Kaydoluyor...</span>
          </div>
        ) : (
          <span className="flex items-center justify-center gap-2">
            <span>Yayıncı Olarak Kaydol</span>
            <Check className="h-5 w-5 ml-1" />
          </span>
        )}
      </Button>
      
      <p className="text-xs text-center text-slate-500 mt-5">
        Kaydolarak, Adnomio'ya size ürün güncellemeleri ve pazarlama e-postaları göndermesine izin vermiş olursunuz. İstediğiniz zaman abonelikten çıkabilirsiniz.
      </p>
    </form>
  );
}
