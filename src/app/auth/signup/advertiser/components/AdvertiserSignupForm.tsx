"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { toast } from "@/components/ui/use-toast"
import { Eye, EyeOff, Mail, Lock, User, Building2, BarChart3, Check } from "lucide-react"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { adminRegister } from "@/shared/services/admin/admin-register"
import { useFormValidation, commonValidationRules, ValidationRules } from "@/shared/hooks/useFormValidation"
import { useFormValidationContext } from "@/shared/hooks/FormValidationProvider"
import { FormField } from "@/components/ui/form/FormField"
import { FormErrorBoundary } from "@/components/ui/form/FormErrorBoundary"

export function AdvertiserSignupForm() {
  const router = useRouter()
  const [showPassword, setShowPassword] = useState(false)
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    password: "",
    companyName: "",
    industry: "",
    agreeTerms: false
  })
  const [formFocus, setFormFocus] = useState({
    firstName: false,
    lastName: false,
    email: false,
    password: false,
    companyName: false,
    industry: false
  })

  const [errors, setErrors] = useState<{[key: string]: string}>({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Form validasyon kuralları
  const validationRules: ValidationRules = {
    firstName: [commonValidationRules.required],
    lastName: [commonValidationRules.required],
    email: [commonValidationRules.required, commonValidationRules.email],
    password: [commonValidationRules.required, commonValidationRules.password],
    companyName: [commonValidationRules.required],
    industry: [commonValidationRules.required],
    agreeTerms: [commonValidationRules.agreeTerms],
  }

  const validateField = (name: string, value: any): string => {
    const rules = validationRules[name]
    if (!rules) return ''

    for (const rule of rules) {
      if (!rule.test(value)) {
        return rule.message
      }
    }

    return ''
  }

  const handleChange = (name: string, value: any) => {
    const error = validateField(name, value)
    setErrors(prev => ({ ...prev, [name]: error }))
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target
    const newValue = type === 'checkbox' ? checked : value
    
    setFormData(prev => ({
      ...prev,
      [name]: newValue
    }))
    
    handleChange(name, type === 'checkbox' ? checked : value)
  }

  const handleSelectChange = (value: string) => {
    setFormData(prev => ({
      ...prev,
      industry: value
    }))
    handleChange('industry', value)
  }

  const handleFocus = (field: keyof typeof formFocus) => {
    setFormFocus(prev => ({
      ...prev,
      [field]: true
    }))
  }

  const handleBlur = (field: keyof typeof formFocus) => {
    setFormFocus(prev => ({
      ...prev,
      [field]: false
    }))
  }

  const validateForm = (): boolean => {
    const newErrors: {[key: string]: string} = {}
    let isValid = true

    Object.keys(validationRules).forEach((fieldName) => {
      const value = formData[fieldName as keyof typeof formData]
      const error = validateField(fieldName, value)
      
      if (error) {
        newErrors[fieldName] = error
        isValid = false
      }
    })

    setErrors(newErrors)
    return isValid
  }

  const handleFormSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    
    if (!validateForm()) {
      toast({
        title: "Hata",
        description: "Lütfen tüm zorunlu alanları doldurun ve kullanım koşullarını kabul edin",
        variant: "destructive"
      })
      return
    }

    setIsSubmitting(true)

    try {
      const response = await adminRegister({
        email: formData.email,
        password: formData.password,
        name: formData.firstName,
        surname: formData.lastName,
        company_name: formData.companyName,
        sector: formData.industry
      });

      if (response.status) {
        toast({
          title: "Kayıt başarılı",
          description: "Hesabınız oluşturuldu, yönlendiriliyorsunuz",
          className: "bg-blue-50 border-blue-200 text-blue-800"
        })
        router.push("/auth/login")
      } else {
        if (response.httpStatus === 409 || response.desc?.toLowerCase().includes('daha önce kayıt')) {
          setErrors(prev => ({
            ...prev,
            email: "Bu e-posta adresi ile daha önce kayıt yapılmış"
          }))
          toast({
            title: "Kayıt Başarısız",
            description: "Bu e-posta adresi ile daha önce kayıt yapılmış. Lütfen giriş yapmayı deneyin veya farklı bir e-posta adresi kullanın.",
            variant: "destructive"
          })
        } else {
          toast({
            title: "Kayıt Başarısız",
            description: response.desc || "Kayıt işlemi sırasında bir hata oluştu. Lütfen tekrar deneyin.",
            variant: "destructive"
          })
        }
      }
    } catch (error: any) {
      console.error('Kayıt hatası:', error)
      toast({
        title: "Kayıt Başarısız",
        description: "Kayıt işlemi sırasında bir hata oluştu. Lütfen tekrar deneyin.",
        variant: "destructive"
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <FormErrorBoundary formName="Reklamveren Kaydı">
      <form onSubmit={handleFormSubmit} className="space-y-5">
        <div className="grid grid-cols-2 gap-4">
          <FormField
            id="firstName"
            name="firstName"
            type="text"
            label="Ad"
            value={formData.firstName}
            error={errors.firstName}
            icon={<User className="h-4 w-4" strokeWidth={2} />}
            placeholder="Adınız"
            required
            focused={formFocus.firstName}
            onChange={handleInputChange}
            onFocus={() => handleFocus('firstName')}
            onBlur={() => handleBlur('firstName')}
          />
          
          <FormField
            id="lastName"
            name="lastName"
            type="text"
            label="Soyad"
            value={formData.lastName}
            error={errors.lastName}
            icon={<User className="h-4 w-4" strokeWidth={2} />}
            placeholder="Soyadınız"
            required
            focused={formFocus.lastName}
            onChange={handleInputChange}
            onFocus={() => handleFocus('lastName')}
            onBlur={() => handleBlur('lastName')}
          />
        </div>
        
        <FormField
          id="email"
          name="email"
          type="email"
          label="E-posta Adresi"
          value={formData.email}
          error={errors.email}
          icon={<Mail className="h-4 w-4" strokeWidth={2} />}
          placeholder="<EMAIL>"
          required
          autoComplete="email"
          focused={formFocus.email}
          onChange={handleInputChange}
          onFocus={() => handleFocus('email')}
          onBlur={() => handleBlur('email')}
        />
        
        <div className="space-y-1.5 relative">
          <FormField
            id="password"
            name="password"
            type={showPassword ? "text" : "password"}
            label="Şifre"
            value={formData.password}
            error={errors.password}
            icon={<Lock className="h-4 w-4" strokeWidth={2} />}
            placeholder="En az 8 karakter"
            required
            autoComplete="new-password"
            focused={formFocus.password}
            onChange={handleInputChange}
            onFocus={() => handleFocus('password')}
            onBlur={() => handleBlur('password')}
          />
          <button
            type="button"
            className={`absolute right-3 top-[38px] flex items-center transition-colors duration-200 z-20 ${formFocus.password ? 'text-blue-600' : 'text-slate-600'} hover:text-slate-800`}
            onClick={() => setShowPassword(!showPassword)}
          >
            {showPassword ? <EyeOff className="h-4 w-4" strokeWidth={2} /> : <Eye className="h-4 w-4" strokeWidth={2} />}
          </button>
        </div>
        
        <FormField
          id="companyName"
          name="companyName"
          type="text"
          label="Şirket Adı"
          value={formData.companyName}
          error={errors.companyName}
          icon={<Building2 className="h-4 w-4" strokeWidth={2} />}
          placeholder="Şirket adınız"
          required
          focused={formFocus.companyName}
          onChange={handleInputChange}
          onFocus={() => handleFocus('companyName')}
          onBlur={() => handleBlur('companyName')}
        />
        
        <div className="space-y-1.5">
          <Select
            value={formData.industry}
            onValueChange={handleSelectChange}
          >
            <SelectTrigger 
              id="industry"
              className={`pl-10 py-5 text-base bg-slate-50 border ${errors.industry ? 'border-red-300' : 'border-slate-200'} rounded-xl`}
            >
              <div className="absolute inset-y-0 left-0 flex items-center pl-3.5 pointer-events-none text-slate-600 z-10">
                <BarChart3 className="h-4 w-4" strokeWidth={2} />
              </div>
              <SelectValue placeholder="Sektör seçin" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="technology">Teknoloji</SelectItem>
              <SelectItem value="ecommerce">E-Ticaret</SelectItem>
              <SelectItem value="finance">Finans</SelectItem>
              <SelectItem value="healthcare">Sağlık</SelectItem>
              <SelectItem value="education">Eğitim</SelectItem>
              <SelectItem value="travel">Turizm / Seyahat</SelectItem>
              <SelectItem value="retail">Perakende</SelectItem>
              <SelectItem value="food">Gıda / Restoran</SelectItem>
              <SelectItem value="automotive">Otomotiv</SelectItem>
              <SelectItem value="realestate">Gayrimenkul</SelectItem>
              <SelectItem value="entertainment">Eğlence / Medya</SelectItem>
              <SelectItem value="other">Diğer</SelectItem>
            </SelectContent>
          </Select>
          {errors.industry && (
            <p className="text-xs text-red-500 mt-1">{errors.industry}</p>
          )}
        </div>
        
        <div className="grid gap-1.5">
          <label
            htmlFor="agreeTerms"
            className="text-sm text-slate-700 font-normal leading-snug flex items-start gap-2"
          >
            <Checkbox
              id="agreeTerms"
              name="agreeTerms"
              checked={formData.agreeTerms}
              onCheckedChange={(checked) => {
                const newValue = checked === true
                setFormData(prev => ({
                  ...prev,
                  agreeTerms: newValue
                }))
                handleChange('agreeTerms', newValue)
              }}
              className={`mt-0.5 ${errors.agreeTerms ? 'border-red-300' : ''}`}
            />
            <span>
              <a href="#" className="text-blue-600 hover:text-blue-700 hover:underline">Kullanım Koşulları</a>{" "}
              ve{" "}
              <a href="#" className="text-blue-600 hover:text-blue-700 hover:underline">Gizlilik Politikası</a>
              'nı okudum ve kabul ediyorum.
            </span>
          </label>
          {errors.agreeTerms && (
            <p className="text-xs text-red-500 pl-6">{errors.agreeTerms}</p>
          )}
        </div>
        
        <Button 
          type="submit" 
          className={`w-full py-6 mt-3 text-base transition-all duration-300 ${
            isSubmitting 
              ? 'bg-blue-600'
              : 'bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 hover:scale-[1.02]'
          } text-white font-medium shadow-lg shadow-blue-500/20 rounded-xl`}
          disabled={isSubmitting}
        >
          {isSubmitting ? (
            <div className="flex items-center justify-center gap-2">
              <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <span>Kaydoluyor...</span>
            </div>
          ) : (
            <span className="flex items-center justify-center gap-2">
              <span>Reklamveren Olarak Kaydol</span>
              <Check className="h-5 w-5 ml-1" />
            </span>
          )}
        </Button>
        
        <p className="text-xs text-center text-slate-500 mt-5">
          Kaydolarak, Adnomio size ürün güncellemeleri ve pazarlama e-postaları göndermesine izin vermiş olursunuz. İstediğiniz zaman abonelikten çıkabilirsiniz.
        </p>
      </form>
    </FormErrorBoundary>
  )
} 