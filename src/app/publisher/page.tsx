"use client";

import React, { useState, useEffect } from 'react';
import { DashboardLayout } from "@/app/publisher/components/layout/dashboard-layout";
import { StatsCards } from "@/app/publisher/components/dashboard/stats-cards";
import { GroupsOverview } from "@/app/publisher/components/dashboard/groups-overview";
import { EarningsChart } from "@/app/publisher/components/dashboard/earnings-chart";
import { RecentAds } from "@/app/publisher/components/dashboard/recent-ads";
import { TopPerformers } from "@/app/publisher/components/dashboard/top-performers";
import { ConnectChannelCTA } from "@/app/publisher/components/dashboard/connect-channel-cta";
import { getPublisherEarnings } from '@/shared/services/publisher/earnings';
import { getDashboardStatistics, type DashboardStatisticsResponse } from '@/shared/services/publisher/dashboard-statistics';
import { getTopCommunities, type TopCommunity } from '@/shared/services/publisher/community-service';

export default function PublisherDashboard() {
  const [earnings, setEarnings] = useState<number>(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [statistics, setStatistics] = useState<DashboardStatisticsResponse['result'] | null>(null);
  const [statsLoading, setStatsLoading] = useState(true);
  const [statsError, setStatsError] = useState<string | null>(null);
  const [topCommunities, setTopCommunities] = useState<TopCommunity[] | null>(null);
  const [topCommunitiesLoading, setTopCommunitiesLoading] = useState(true);
  const [topCommunitiesError, setTopCommunitiesError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setStatsLoading(true);
        setTopCommunitiesLoading(true);
        
        const [earningsData, statsData, topComData] = await Promise.all([
          getPublisherEarnings(),
          getDashboardStatistics(),
          getTopCommunities()
        ]);
        
        setEarnings(earningsData);
        setStatistics(statsData);
        setTopCommunities(topComData);
        setError(null);
        setStatsError(null);
        setTopCommunitiesError(null);
      } catch (err) {
        console.error("Dashboard veri yükleme hatası:", err);
        const errorMessage = err instanceof Error ? err.message : 'Veriler yüklenirken bir hata oluştu';
        setError(errorMessage);
        setStatsError(errorMessage);
        setTopCommunitiesError(errorMessage);
      } finally {
        setLoading(false);
        setStatsLoading(false);
        setTopCommunitiesLoading(false);
      }
    };

    fetchData();
  }, []);

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <StatsCards 
          earnings={earnings} 
          loading={loading} 
          error={error}
          statistics={statistics}
          statsLoading={loading}
          statsError={error}
        />
        
        <div className="grid grid-cols-1 gap-6">
          <div>
            <EarningsChart earnings={earnings} loading={loading} error={error} earningsDetails={null} />
          </div>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          <div className="lg:col-span-3">
            <RecentAds campaigns={[]} loading={false} error={null} />
          </div>
          
          <div className="h-full">
            <TopPerformers 
              data={topCommunities} 
              loading={topCommunitiesLoading} 
              error={topCommunitiesError} 
            />
          </div>
        </div>
        
        <ConnectChannelCTA />
      </div>
    </DashboardLayout>
  );
} 