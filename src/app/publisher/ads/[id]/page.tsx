"use client";

import React, { useState, useEffect } from 'react';
import { DashboardLayout } from "@/app/publisher/components/layout/dashboard-layout";
import { 
  Tag,
  ArrowLeft,
  Eye,
  MousePointerClick,
  BarChart,
  CalendarDays,
  AlertCircle,
  BarChart2
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Link from 'next/link';
import { getCampaignList, type Campaign } from '@/shared/services/publisher/campaign-list';
import { useParams } from 'next/navigation';

export default function CampaignDetailPage() {
  const params = useParams();
  const [campaign, setCampaign] = useState<Campaign | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchCampaignDetails = async () => {
      try {
        setLoading(true);
        const result = await getCampaignList({
          limit: 1,
          skip: 0,
          campaign_id: params.id as string
        });
        
        if (result.data.length > 0) {
          setCampaign(result.data[0]);
        } else {
          setError('Reklam bulunamadı');
        }
      } catch (err) {
        console.error(err);
        setError('Reklam detayları yüklenirken bir hata oluştu');
      } finally {
        setLoading(false);
      }
    };

    fetchCampaignDetails();
  }, [params.id]);

  // Durum rozeti için fonksiyon
  const getStatusBadge = (status: string) => {
    const variants = {
      ACTIVE: { bg: 'bg-green-600/80 hover:bg-green-600', label: 'Aktif' },
      PLANNED: { bg: 'bg-amber-600/80 hover:bg-amber-600', label: 'Planlandı' },
      STOP: { bg: 'bg-red-600/80 hover:bg-red-600', label: 'Durduruldu' },
      FINISHED: { bg: 'bg-blue-600/80 hover:bg-blue-600', label: 'Tamamlandı' }
    };
    
    const { bg, label } = variants[status as keyof typeof variants];
    
    return (
      <Badge className={`${bg} text-[10px] h-5`}>
        {label}
      </Badge>
    );
  };

  // Tarih formatı için yardımcı fonksiyon
  const formatDate = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleDateString('tr-TR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="space-y-6">
          <div className="flex items-center gap-4">
            <Link href="/publisher/ads">
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <ArrowLeft className="h-4 w-4" />
              </Button>
            </Link>
            <div className="h-8 bg-slate-100 animate-pulse rounded w-48"></div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="border-slate-200 bg-white shadow-md">
              <CardContent className="p-6">
                <div className="space-y-4">
                  <div className="h-4 bg-slate-100 animate-pulse rounded w-1/4"></div>
                  <div className="h-6 bg-slate-100 animate-pulse rounded w-3/4"></div>
                  <div className="h-4 bg-slate-100 animate-pulse rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
            <Card className="border-slate-200 bg-white shadow-md">
              <CardContent className="p-6">
                <div className="space-y-4">
                  <div className="h-4 bg-slate-100 animate-pulse rounded w-1/4"></div>
                  <div className="h-6 bg-slate-100 animate-pulse rounded w-3/4"></div>
                  <div className="h-4 bg-slate-100 animate-pulse rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  if (error || !campaign) {
    return (
      <DashboardLayout>
        <div className="space-y-6">
          <div className="flex items-center gap-4">
            <Link href="/publisher/ads">
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <ArrowLeft className="h-4 w-4" />
              </Button>
            </Link>
            <h1 className="text-2xl font-bold text-slate-800">Reklam Detayları</h1>
          </div>
          <Card className="border-slate-200 bg-white shadow-md">
            <CardContent className="p-8 text-center">
              <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
                <AlertCircle className="h-8 w-8 text-red-500" />
              </div>
              <h3 className="text-lg font-medium text-slate-800">Hata Oluştu</h3>
              <p className="text-slate-500 mt-2">{error || 'Reklam bulunamadı'}</p>
            </CardContent>
          </Card>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Üst başlık kısmı */}
        <div className="flex items-center gap-4">
          <Link href="/publisher/ads">
            <Button variant="ghost" size="icon" className="h-8 w-8">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
          <h1 className="text-2xl font-bold text-slate-800">Reklam Detayları</h1>
        </div>

        {/* Reklam Detayları */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Sol Taraf - Temel Bilgiler */}
          <Card className="border-slate-200 bg-white shadow-md">
            <CardContent className="p-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Badge className="bg-blue-500 hover:bg-blue-600 text-[10px] h-5">
                    {campaign.community_name}
                  </Badge>
                  {getStatusBadge(campaign.campaign_status)}
                </div>
                
                <div>
                  <h2 className="text-xl font-semibold text-slate-800">{campaign.campaign_title}</h2>
                  <p className="text-sm text-slate-600 mt-1">{campaign.campaign_desc}</p>
                </div>

                <div className="flex items-center text-sm text-slate-500">
                  <CalendarDays className="h-4 w-4 mr-2" />
                  <span>{formatDate(campaign.starts_at)} - {formatDate(campaign.ends_at)}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Sağ Taraf - İstatistikler */}
          <Card className="border-slate-200 bg-white shadow-md">
            <CardContent className="p-6">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <p className="text-xs text-slate-500">Görüntülenme</p>
                  <p className="text-slate-800 font-medium flex items-center">
                    <Eye className="h-4 w-4 mr-2 text-indigo-500" />
                    {campaign.views.toLocaleString()}
                  </p>
                </div>
                <div className="space-y-2">
                  <p className="text-xs text-slate-500">Tıklama</p>
                  <p className="text-slate-800 font-medium flex items-center">
                    <MousePointerClick className="h-4 w-4 mr-2 text-blue-500" />
                    {campaign.clicks.toLocaleString()}
                  </p>
                </div>
                <div className="space-y-2">
                  <p className="text-xs text-slate-500">CTR</p>
                  <p className="text-slate-800 font-medium flex items-center">
                    <BarChart2 className="h-4 w-4 mr-2 text-green-500" />
                    %{campaign.ctr.toFixed(1)}
                  </p>
                </div>
                <div className="space-y-2">
                  <p className="text-xs text-slate-500">Kazanç</p>
                  <p className="text-green-600 font-medium">
                    ₺{campaign.earnings.toFixed(2)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Reklam Görseli */}
        <Card className="border-slate-200 bg-white shadow-md">
          <CardContent className="p-6">
            <h3 className="text-lg font-semibold text-slate-800 mb-4">Reklam Görseli</h3>
            <div className="aspect-video bg-gradient-to-br from-indigo-50 to-purple-50 rounded-lg flex items-center justify-center">
              <div className="text-slate-400">Reklam Görseli</div>
            </div>
          </CardContent>
        </Card>
      </div>
    </DashboardLayout>
  );
} 