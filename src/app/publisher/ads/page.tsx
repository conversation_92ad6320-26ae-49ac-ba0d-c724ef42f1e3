"use client";

import React, { useState, useEffect } from 'react';
import { DashboardLayout } from "@/app/publisher/components/layout/dashboard-layout";
import { 
  Tag,
  Search,
  Filter,
  ArrowUpDown,
  BarChart2,
  AlertCircle,
  Eye,
  MousePointerClick,
  Calendar,
  Check,
  X,
  Clock,
  ArrowRight,
  CalendarDays,
  BarChart
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Link from 'next/link';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import Image from 'next/image';
import { getCampaignStatistics, type CampaignStatisticsResponse } from '@/shared/services/publisher/campaign-statistics';
import { getCampaignList, type Campaign, type CampaignStatus } from '@/shared/services/publisher/campaign-list';
import { getCommunityList, type Community } from '@/shared/services/publisher/community-list';
import { useLanguage } from '@/shared/hooks/useLanguage';

export default function AdsPage() {
  const { t } = useLanguage();
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<CampaignStatus | 'all'>('all');
  const [groupFilter, setGroupFilter] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('date');
  const [statistics, setStatistics] = useState<CampaignStatisticsResponse['result'] | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [campaignsLoading, setCampaignsLoading] = useState(true);
  const [campaignsError, setCampaignsError] = useState<string | null>(null);
  const [totalCampaigns, setTotalCampaigns] = useState(0);
  const [uniqueGroups, setUniqueGroups] = useState<string[]>([]);
  const [communities, setCommunities] = useState<Community[]>([]);
  const [communitiesLoading, setCommunitiesLoading] = useState(true);
  const [communitiesError, setCommunitiesError] = useState<string | null>(null);

  // İstatistikleri getir
  useEffect(() => {
    const fetchStatistics = async () => {
      try {
        setLoading(true);
        const data = await getCampaignStatistics();
        setStatistics(data);
        setError(null);
      } catch (err) {
        console.error(err);
        setError('İstatistik verileri yüklenirken bir hata oluştu');
      } finally {
        setLoading(false);
      }
    };

    fetchStatistics();
  }, []);

  // Reklamları getir
  useEffect(() => {
    const fetchCampaigns = async () => {
      try {
        setCampaignsLoading(true);
        const params = {
          limit: 10,
          skip: 0,
          ...(statusFilter !== 'all' && { campaign_status: statusFilter }),
          ...(groupFilter !== 'all' && { community_username: groupFilter }),
          ...(searchQuery && { search: searchQuery })
        };

        const result = await getCampaignList(params);
        setCampaigns(result.data);
        setTotalCampaigns(result.stats.total);
        
        // Benzersiz grupları güncelle
        const groups = Array.from(new Set(result.data.map(campaign => campaign.community_username)));
        setUniqueGroups(groups);
        
        setCampaignsError(null);
      } catch (err) {
        console.error(err);
        setCampaignsError('Reklam listesi yüklenirken bir hata oluştu');
      } finally {
        setCampaignsLoading(false);
      }
    };

    fetchCampaigns();
  }, [statusFilter, groupFilter, searchQuery]);

  // Toplulukları getir
  useEffect(() => {
    const fetchCommunities = async () => {
      try {
        setCommunitiesLoading(true);
        const result = await getCommunityList({
          limit: 0,
          skip: 0
        });
        setCommunities(result.data);
        setCommunitiesError(null);
      } catch (err) {
        console.error(err);
        setCommunitiesError('Topluluk listesi yüklenirken bir hata oluştu');
      } finally {
        setCommunitiesLoading(false);
      }
    };

    fetchCommunities();
  }, []);

  // Durum rozeti için fonksiyon
  const getStatusBadge = (status: CampaignStatus) => {
    const variants = {
      ACTIVE: { bg: 'bg-green-600/80 hover:bg-green-600', label: t('ads.status.active') },
      PLANNED: { bg: 'bg-amber-600/80 hover:bg-amber-600', label: t('ads.status.pending') },
      STOP: { bg: 'bg-red-600/80 hover:bg-red-600', label: t('ads.status.stopped') },
      FINISHED: { bg: 'bg-blue-600/80 hover:bg-blue-600', label: t('ads.status.completed') }
    };
    
    const { bg, label } = variants[status];
    
    return (
      <Badge className={`${bg} text-[10px] h-5`}>
        {label}
      </Badge>
    );
  };

  // Tarih formatı için yardımcı fonksiyon
  const formatDate = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleDateString('tr-TR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  };

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Üst başlık kısmı */}
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
          <div>
            <h1 className="text-2xl md:text-3xl font-bold text-slate-800 flex items-center gap-2">
              <Tag className="h-7 w-7 text-purple-500" />
              <span>{t('ads.title')}</span>
            </h1>
            <p className="text-slate-600 mt-1">{t('ads.subtitle')}</p>
          </div>
        </div>
        
        {/* İstatistik kartları */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          <Card className="border-indigo-200 bg-white shadow-md">
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-2">
                <p className="text-xs font-medium text-slate-500">{t('ads.metrics.views')}</p>
                <div className="h-8 w-8 rounded-lg bg-indigo-100 flex items-center justify-center">
                  <Eye className="h-4 w-4 text-indigo-500" />
                </div>
              </div>
              {loading ? (
                <div className="h-8 bg-slate-100 animate-pulse rounded"></div>
              ) : error ? (
                <p className="text-red-500 text-sm">{error}</p>
              ) : (
                <h3 className="text-2xl font-bold text-slate-800">
                  {statistics?.total_views.toLocaleString() || '0'}
                </h3>
              )}
              <p className="text-xs text-slate-500 mt-1">{t('ads.metrics.allAds')}</p>
            </CardContent>
          </Card>
          
          <Card className="border-blue-200 bg-white shadow-md">
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-2">
                <p className="text-xs font-medium text-slate-500">{t('ads.metrics.clicks')}</p>
                <div className="h-8 w-8 rounded-lg bg-blue-100 flex items-center justify-center">
                  <MousePointerClick className="h-4 w-4 text-blue-500" />
                </div>
              </div>
              {loading ? (
                <div className="h-8 bg-slate-100 animate-pulse rounded"></div>
              ) : error ? (
                <p className="text-red-500 text-sm">{error}</p>
              ) : (
                <h3 className="text-2xl font-bold text-slate-800">
                  {statistics?.total_clicks.toLocaleString() || '0'}
                </h3>
              )}
              <p className="text-xs text-slate-500 mt-1">{t('ads.metrics.allAds')}</p>
            </CardContent>
          </Card>
          
          <Card className="border-green-200 bg-white shadow-md">
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-2">
                <p className="text-xs font-medium text-slate-500">{t('ads.metrics.ctr')}</p>
                <div className="h-8 w-8 rounded-lg bg-green-100 flex items-center justify-center">
                  <BarChart2 className="h-4 w-4 text-green-500" />
                </div>
              </div>
              {loading ? (
                <div className="h-8 bg-slate-100 animate-pulse rounded"></div>
              ) : error ? (
                <p className="text-red-500 text-sm">{error}</p>
              ) : (
                <h3 className="text-2xl font-bold text-slate-800">
                  %{(statistics?.avg_ctr || 0).toFixed(1)}
                </h3>
              )}
              <p className="text-xs text-slate-500 mt-1">{t('ads.metrics.clickRate')}</p>
            </CardContent>
          </Card>
          
          <Card className="border-purple-200 bg-white shadow-md">
            <CardContent className="p-4">
              <div className="flex items-center justify-between mb-2">
                <p className="text-xs font-medium text-slate-500">{t('ads.metrics.earnings')}</p>
                <div className="h-8 w-8 rounded-lg bg-purple-100 flex items-center justify-center">
                  <AlertCircle className="h-4 w-4 text-purple-500" />
                </div>
              </div>
              {loading ? (
                <div className="h-8 bg-slate-100 animate-pulse rounded"></div>
              ) : error ? (
                <p className="text-red-500 text-sm">{error}</p>
              ) : (
                <h3 className="text-2xl font-bold text-green-600">
                  ₺{(statistics?.total_earnings || 0).toFixed(2)}
                </h3>
              )}
              <p className="text-xs text-slate-500 mt-1">{t('ads.metrics.allAds')}</p>
            </CardContent>
          </Card>
        </div>
        
        {/* Filtreler ve kontroller */}
        <Card className="border-slate-200 bg-white shadow-md">
          <CardContent className="p-4">
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                <Input 
                  placeholder={t('ads.filters.searchPlaceholder')}
                  className="pl-9 bg-white border-slate-200 text-slate-800 placeholder:text-slate-400"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>
              
              <div className="flex gap-3">
                <Select value={statusFilter} onValueChange={(value) => setStatusFilter(value as CampaignStatus | 'all')}>
                  <SelectTrigger className="bg-white border-slate-200 text-slate-800 w-[140px]">
                    <SelectValue placeholder={t('ads.filters.status')} />
                  </SelectTrigger>
                  <SelectContent className="bg-white border-slate-200 text-slate-800">
                    <SelectItem value="all">{t('ads.filters.all')}</SelectItem>
                    <SelectItem value="ACTIVE">{t('ads.status.active')}</SelectItem>
                    <SelectItem value="PLANNED">{t('ads.status.pending')}</SelectItem>
                    <SelectItem value="STOP">{t('ads.status.stopped')}</SelectItem>
                    <SelectItem value="FINISHED">{t('ads.status.completed')}</SelectItem>
                  </SelectContent>
                </Select>
                
                <Select value={groupFilter} onValueChange={setGroupFilter}>
                  <SelectTrigger className="bg-white border-slate-200 text-slate-800 w-[140px]">
                    <SelectValue placeholder={t('ads.filters.group')} />
                  </SelectTrigger>
                  <SelectContent className="bg-white border-slate-200 text-slate-800 max-h-60 overflow-y-auto" side="bottom">
                    <SelectItem value="all">{t('ads.filters.allGroups')}</SelectItem>
                    {communitiesLoading ? (
                      <SelectItem value="loading" disabled>{t('common.loading')}</SelectItem>
                    ) : communitiesError ? (
                      <SelectItem value="error" disabled>{t('common.error')}</SelectItem>
                    ) : (
                      communities.map(community => (
                        <SelectItem key={community._id} value={community.community_username}>
                          {community.community_name}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>
        
        {/* Reklamlar Listesi */}
        <div className="space-y-5">
          {campaignsLoading ? (
            <div className="space-y-4">
              {[...Array(3)].map((_, index) => (
                <Card key={index} className="border-slate-200 bg-white shadow-md">
                  <CardContent className="p-0">
                    <div className="grid md:grid-cols-[250px_1fr] gap-4">
                      <div className="h-48 md:h-full bg-slate-100 animate-pulse"></div>
                      <div className="p-4 space-y-4">
                        <div className="space-y-2">
                          <div className="h-4 bg-slate-100 animate-pulse rounded w-1/4"></div>
                          <div className="h-6 bg-slate-100 animate-pulse rounded w-3/4"></div>
                          <div className="h-4 bg-slate-100 animate-pulse rounded w-1/2"></div>
                        </div>
                        <div className="grid grid-cols-4 gap-4 pt-2">
                          {[...Array(4)].map((_, i) => (
                            <div key={i} className="space-y-2">
                              <div className="h-3 bg-slate-100 animate-pulse rounded w-1/2"></div>
                              <div className="h-4 bg-slate-100 animate-pulse rounded w-3/4"></div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : campaignsError ? (
            <Card className="border-slate-200 bg-white shadow-md">
              <CardContent className="p-8 text-center">
                <div className="mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4">
                  <AlertCircle className="h-8 w-8 text-red-500" />
                </div>
                <h3 className="text-lg font-medium text-slate-800">{t('common.error')}</h3>
                <p className="text-slate-500 mt-2">{campaignsError}</p>
              </CardContent>
            </Card>
          ) : campaigns.length === 0 ? (
            <Card className="border-slate-200 bg-white shadow-md">
              <CardContent className="p-8 text-center">
                <div className="mx-auto w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mb-4">
                  <Tag className="h-8 w-8 text-purple-500" />
                </div>
                <h3 className="text-lg font-medium text-slate-800">{t('ads.empty.title')}</h3>
                <p className="text-slate-500 mt-2">{t('ads.empty.description')}</p>
              </CardContent>
            </Card>
          ) : (
            campaigns.map(campaign => (
              <Card 
                key={campaign._id} 
                className="border-slate-200 bg-white shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden"
              >
                <CardContent className="p-0">
                  <div className="grid md:grid-cols-[250px_1fr] gap-4">
                    {/* Reklam Görseli */}
                    <div className="h-48 md:h-full bg-gradient-to-br from-indigo-50 to-purple-50 relative group overflow-hidden border-b md:border-b-0 md:border-r border-slate-200">
                      <div className="w-full h-full flex items-center justify-center">
                        <div className="text-slate-400">{t('ads.image')}</div>
                      </div>
                      <div className="absolute top-2 left-2">
                        {getStatusBadge(campaign.campaign_status)}
                      </div>
                    </div>
                    
                    {/* Reklam Detayları */}
                    <div className="p-4 space-y-4">
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <Badge className="bg-blue-500 hover:bg-blue-600 text-[10px] h-5">
                            {campaign.community_name}
                          </Badge>
                          <div className="flex items-center text-xs text-slate-500">
                            <CalendarDays className="h-3 w-3 mr-1" />
                            <span>{formatDate(campaign.starts_at)} - {formatDate(campaign.ends_at)}</span>
                          </div>
                        </div>
                        
                        <h3 className="text-lg font-medium text-slate-800">{campaign.campaign_title}</h3>
                        <p className="text-sm text-slate-600">{campaign.campaign_desc}</p>
                      </div>
                      
                      <div className="grid grid-cols-4 gap-4 pt-2 border-t border-slate-200">
                        <div>
                          <p className="text-xs text-slate-500 mb-1">{t('ads.metrics.views')}</p>
                          <p className="text-slate-800 font-medium flex items-center">
                            <Eye className="h-3 w-3 mr-1 text-indigo-500" />
                            {campaign.views.toLocaleString()}
                          </p>
                        </div>
                        <div>
                          <p className="text-xs text-slate-500 mb-1">{t('ads.metrics.clicks')}</p>
                          <p className="text-slate-800 font-medium flex items-center">
                            <MousePointerClick className="h-3 w-3 mr-1 text-blue-500" />
                            {campaign.clicks.toLocaleString()}
                          </p>
                        </div>
                        <div>
                          <p className="text-xs text-slate-500 mb-1">{t('ads.metrics.ctr')}</p>
                          <p className="text-slate-800 font-medium flex items-center">
                            <BarChart className="h-3 w-3 mr-1 text-green-500" />
                            %{campaign.ctr.toFixed(1)}
                          </p>
                        </div>
                        <div>
                          <p className="text-xs text-slate-500 mb-1">{t('ads.metrics.earnings')}</p>
                          <p className="text-green-600 font-medium">
                            ₺{campaign.earnings.toFixed(2)}
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex justify-end">
                        <Link href={`/publisher/ads/${campaign._id}`}>
                          <Button className="h-8 text-xs bg-purple-500 hover:bg-purple-600 text-white">
                            {t('ads.viewDetails')}
                            <ArrowRight className="ml-1 h-3 w-3" />
                          </Button>
                        </Link>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </div>
    </DashboardLayout>
  );
} 