"use client";

import { useState, useEffect, useCallback } from 'react';
import { DashboardLayout } from "@/app/publisher/components/layout/dashboard-layout";
import { Plus } from 'lucide-react';
import { Card } from '@/components/ui/card';
import { getCommunityList, type Community as APICommunity } from '@/shared/services/publisher/community-list';
import { type Community } from './types';
import ConnectCommunityStepper from './connect/components/ConnectCommunityStepper';
import { CommunitiesHeader } from './components/CommunitiesHeader';
import { CommunitiesFilters } from './components/CommunitiesFilters';
import { CommunityCard } from './components/CommunityCard';
import { CommunitiesList } from './components/CommunitiesList';
import { InfoCard } from './components/InfoCard';
import { authService } from '@/shared/services/auth-service';
import { toast } from '@/components/ui/use-toast';
import { useLanguage } from '@/shared/hooks/useLanguage';
import { API_CONFIG } from '@/shared/services/api-config';
import { deleteCommunity } from '@/shared/services/publisher/community-list';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

export default function CommunitiesPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [filter, setFilter] = useState<'all' | 'active' | 'pending' | 'suspended'>('all');
  
  const [communities, setCommunities] = useState<Community[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showConnectModal, setShowConnectModal] = useState(false);
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [communityToDeleteId, setCommunityToDeleteId] = useState<string | null>(null);
  const { t } = useLanguage();
  
  const statusMap = {
    'active': 'ACTIVE',
    'pending': 'APPROVAL_PENDING',
    'suspended': 'SUSPENDED',
    'all': ''
  };

  const fetchCommunities = useCallback(async () => {
    try {
      setLoading(true);
      const result = await getCommunityList({
        limit: 0,
        skip: 0,
        ...(filter !== 'all' && { status: statusMap[filter] }),
        ...(searchQuery && { search: searchQuery })
      });
      
      const transformedCommunities: Community[] = result.data.map(apiCommunity => {
        let status: 'active' | 'pending' | 'suspended';
        switch (apiCommunity.status.toLowerCase()) {
          case 'active':
          case 'approved':
            status = 'active';
            break;
          case 'approval_pending':
          case 'pending':
            status = 'pending';
            break;
          default:
            status = 'suspended';
        }

        return {
          id: apiCommunity.community_id,
          name: apiCommunity.community_name,
          icon: '👥',
          platform: 'telegram',
          members: apiCommunity.member_count || 0,
          views: apiCommunity.view_count || 0,
          ctr: apiCommunity.ctr || 0,
          earnings: apiCommunity.earning_price || 0,
          status,
          dateAdded: new Date(apiCommunity.created_at * 1000).toISOString(),
          url: `https://t.me/${apiCommunity.community_username}`
        };
      });

      setCommunities(transformedCommunities);
      setError(null);
    } catch (err) {
      console.error(err);
      setError('Topluluk listesi yüklenirken bir hata oluştu');
    } finally {
      setLoading(false);
    }
  }, [filter, searchQuery]);

  useEffect(() => {
    fetchCommunities();
  }, [fetchCommunities]);

  const handleDeleteClick = (communityId: string) => {
    setCommunityToDeleteId(communityId);
    setShowConfirmModal(true);
  };

  const handleConfirmDelete = async () => {
    if (communityToDeleteId) {
      try {
        await deleteCommunity(communityToDeleteId);
        setCommunities(prevCommunities => 
          prevCommunities.filter(community => community.id !== communityToDeleteId)
        );

        toast({
          title: t('common.success'),
          description: t('communities.deleteSuccess'),
        });
      } catch (error) {
        console.error('Delete error:', error);
        toast({
          title: t('common.error'),
          description: t('communities.errors.delete'),
          variant: "destructive",
        });
      } finally {
        setCommunityToDeleteId(null);
        setShowConfirmModal(false);
      }
    }
  };

  const handleCancelDelete = () => {
    setCommunityToDeleteId(null);
    setShowConfirmModal(false);
  };

  // API sadece status filtresini desteklediği için diğer filtrelemeler (search, type) frontend'de yapılacak.
  const filteredCommunities = communities.filter(community => {
    let isMatch = true;
    
    // Arama filtresi
    if (searchQuery) {
      isMatch = isMatch && community.name.toLowerCase().includes(searchQuery.toLowerCase());
    }

    // Filtre
    if (filter !== 'all') {
      isMatch = isMatch && community.status === filter;
    }

    return isMatch;
  })
  .sort((a, b) => {
    // 1. Öncelik: Durum (Aktif > Beklemede > Askıda)
    const statusOrder = { 'active': 1, 'pending': 2, 'suspended': 3 };
    if (statusOrder[a.status] !== statusOrder[b.status]) {
      return statusOrder[a.status] - statusOrder[b.status];
    }

    // 2. Öncelik: Kazanç (Çoktan Aza)
    if (b.earnings !== a.earnings) {
      return b.earnings - a.earnings; // Azalan sıralama
    }

    // 3. Öncelik: Kayıt Tarihi (Eskiden Yeniye - en son kayıt edilen en altta)
    const dateA = new Date(a.dateAdded).getTime();
    const dateB = new Date(b.dateAdded).getTime();
    return dateA - dateB; // Artan sıralama
  });

  // unreadCount hesaplaması artık API response'undaki isRead alanına göre yapılacak
  // const unreadCount = notifications.filter(n => !nRead).length; 
  
  // Tüm bildirimleri okundu olarak işaretle (API çağrısı eklenecek)
  // const markAllAsRead = async () => {
  // ... existing code ...
  // }

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <CommunitiesHeader onAddCommunity={() => setShowConnectModal(true)} />
        
        <CommunitiesFilters
          searchQuery={searchQuery}
          onSearchChange={setSearchQuery}
          filter={filter}
          onFilterChange={setFilter}
        />
        
        {loading ? (
          <div className="text-center py-10 text-slate-500">{t('common.loading')}</div>
        ) : error ? (
          <div className="text-center py-10 text-red-500">{error}</div>
        ) : communities.length === 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-4">
            <Card 
              className="border-dashed border-indigo-200/40 bg-gradient-to-br from-white/95 to-blue-50/80 hover:to-blue-100/80 transition-all duration-300 hover:translate-y-[-2px] group cursor-pointer min-h-[250px] flex flex-col items-center justify-center text-center p-6"
              onClick={() => setShowConnectModal(true)}
            >
              <div className="h-14 w-14 rounded-full bg-indigo-100 border border-indigo-200/50 flex items-center justify-center group-hover:scale-110 transition-transform duration-300 mb-4">
                <Plus className="h-6 w-6 text-indigo-500" />
              </div>
              <h3 className="text-slate-800 font-medium">{t('communities.connect.addNew')}</h3>
              <p className="text-slate-500 text-sm mt-2 max-w-[300px]">
                {t('communities.connect.addNewDescription')}
              </p>
            </Card>
          </div>
        ) : (
          <>
            <CommunitiesList 
              communities={filteredCommunities}
              onDelete={handleDeleteClick}
              loading={loading}
              error={error}
            />
          </>
        )}
        <InfoCard />

        <ConnectCommunityStepper 
          isOpen={showConnectModal} 
          onClose={() => setShowConnectModal(false)}
          onSuccess={() => {
            setShowConnectModal(false);
            fetchCommunities();
          }}
        />

        <AlertDialog open={showConfirmModal} onOpenChange={setShowConfirmModal}>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Bu topluluğu silmek istediğinizden emin misiniz?</AlertDialogTitle>
              <AlertDialogDescription>
                Bu işlem geri alınamaz. Topluluk kalıcı olarak silinecektir.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel onClick={handleCancelDelete}>İptal</AlertDialogCancel>
              <AlertDialogAction onClick={handleConfirmDelete}>Sil</AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </DashboardLayout>
  );
} 