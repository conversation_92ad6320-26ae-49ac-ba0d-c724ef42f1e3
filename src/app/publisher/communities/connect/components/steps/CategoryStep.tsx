import React, { useState } from 'react';
import { useLanguage } from '@/shared/hooks/useLanguage';

interface CategoryStepProps {
  selectedCategories: string[];
  onSelect: (category: string) => void;
  onRemove: (category: string) => void;
}

export function CategoryStep({ selectedCategories, onSelect, onRemove }: CategoryStepProps) {
  const [searchInput, setSearchInput] = useState("");
  const { t } = useLanguage();

  const categories = [
    "shopping", "cars", "friendship", "science", "blog", "magazines",
    "languages", "movies", "tech", "ecommerce", "economy"
  ];

  const categoryIcons: Record<string, string> = {
    "shopping": "🛒",
    "cars": "🚗",
    "friendship": "🤝",
    "science": "🔬",
    "blog": "✍️",
    "magazines": "📰",
    "languages": "🌐",
    "movies": "🎬",
    "tech": "💻",
    "ecommerce": "🛍️",
    "economy": "💸"
  };

  return (
    <div className="w-full flex flex-col items-start gap-8">
      <h2 className="text-2xl font-bold text-slate-800 mb-2">
        {t('communities.connect.steps.category.stepTitle')}
      </h2>
      <div className="w-full flex flex-col gap-4">
        <div className="relative">
          <input
            type="text"
            placeholder={t('communities.connect.steps.category.searchPlaceholder')}
            className="w-full pl-10 pr-4 py-3 rounded-xl border-2 border-slate-200 bg-slate-50 focus:border-emerald-400 outline-none"
            value={searchInput}
            onChange={e => setSearchInput(e.target.value)}
          />
          <span className="absolute left-3 top-1/2 -translate-y-1/2 text-emerald-400 text-xl">🔍</span>
        </div>
        <div className="grid grid-cols-1 gap-4 max-h-[400px] overflow-y-auto">
          {categories
            .filter(c => !searchInput || t(`communities.connect.steps.category.list.${c}`).toLowerCase().includes(searchInput.toLowerCase()))
            .map(c => (
              <button
                key={c}
                className={`flex items-center gap-3 p-4 rounded-xl border-2 transition-all
                  ${selectedCategories.includes(c) ? 'bg-emerald-50 border-emerald-500 text-emerald-700' : 'bg-white border-slate-200 hover:border-emerald-400 hover:bg-emerald-50'}`}
                onClick={() => onSelect(c)}
              >
                <span>{categoryIcons[c] || "📁"}</span>
                <span className="flex-1 text-left">{t(`communities.connect.steps.category.list.${c}`)}</span>
                {selectedCategories.includes(c) && <span className="text-emerald-500 text-xl">✓</span>}
              </button>
            ))}
        </div>
      </div>
    </div>
  );
} 