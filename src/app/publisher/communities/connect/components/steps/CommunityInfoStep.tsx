import React, { useRef, useState } from 'react';
import { SiTelegram } from "react-icons/si";
import { FaCheckCircle, FaExclamationCircle, FaRegClipboard } from "react-icons/fa";
import { useLanguage } from '@/shared/hooks/useLanguage';

interface CommunityInfoStepProps {
  username: string;
  onUsernameChange: (username: string) => void;
  error?: boolean;
}

export function CommunityInfoStep({ username, onUsernameChange, error }: CommunityInfoStepProps) {
  const [copied, setCopied] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const { t } = useLanguage();

  const handleCopy = () => {
    navigator.clipboard.writeText(`https://t.me/${username}`);
    setCopied(true);
    setTimeout(() => setCopied(false), 1200);
  };

  const hasError = username.includes("/") || username.includes(" ");

  return (
    <div className="w-full flex flex-col items-start gap-8">
      <h2 className="text-2xl font-bold text-slate-800 mb-2">
        {t('communities.connect.steps.info.stepTitle')}
      </h2>
      <div className="w-full flex flex-col gap-4">
        <div className={`relative w-full flex items-center rounded-xl bg-white transition-all duration-200 border-2
          ${username ? (hasError
            ? 'border-red-500 shadow-[0_2px_12px_0_rgba(239,68,68,0.10)]'
            : 'border-emerald-500 shadow-[0_2px_12px_0_rgba(16,185,129,0.10)]'
            ) : 'border-slate-300 shadow'}`}
        >
          <span className="pl-4 pr-2 flex items-center text-2xl text-[#229ED9]">
            <SiTelegram />
          </span>
          <span className="text-slate-400 select-none pointer-events-none">https://t.me/</span>
          <input
            ref={inputRef}
            type="text"
            placeholder={t('communities.connect.steps.info.placeholder')}
            className="flex-1 py-3 bg-transparent outline-none pl-1 pr-10"
            value={username}
            onChange={e => onUsernameChange(e.target.value.replace(/\s/g, "").toLowerCase())}
            style={{fontFamily: 'inherit'}}
          />
          {username && !hasError && (
            <button
              type="button"
              className="absolute right-10 top-1/2 -translate-y-1/2 text-slate-400 hover:text-emerald-500 transition p-1"
              onClick={handleCopy}
            >
              <FaRegClipboard size={18} />
            </button>
          )}
          {username && !hasError && (
            <span className="absolute right-4 top-1/2 -translate-y-1/2 text-emerald-500">
              <FaCheckCircle size={20} />
            </span>
          )}
          {username && hasError && (
            <span className="absolute right-4 top-1/2 -translate-y-1/2 text-red-500">
              <FaExclamationCircle size={20} />
            </span>
          )}
          {copied && (
            <span className="absolute right-12 top-0 -translate-y-full bg-emerald-500 text-white text-xs font-semibold px-3 py-1 rounded-xl shadow">
              {t('communities.connect.steps.info.copied')}
            </span>
          )}
        </div>
        <span className="text-xs text-slate-400">
          {t('communities.connect.steps.info.hint')} <span className="font-semibold text-slate-500">adnomiochannel</span>
        </span>
        {username && hasError && (
          <span className="text-xs text-red-500 font-medium">
            {t('communities.connect.steps.info.error')}
          </span>
        )}
      </div>
    </div>
  );
} 