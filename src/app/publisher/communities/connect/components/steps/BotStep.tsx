import { useLanguage } from '@/shared/hooks/useLanguage';
import { FaTelegram, FaCheck, FaInfoCircle } from 'react-icons/fa';
import Image from 'next/image';

interface BotStepProps {
  botToken: string;
  onBotTokenChange: (token: string) => void;
  error?: boolean;
}

export function BotStep({ botToken, onBotTokenChange, error }: BotStepProps) {
  const { t } = useLanguage();

  return (
    <div className="w-full flex flex-col items-start gap-8">
      <div className="w-full">
        <h2 className="text-2xl font-bold text-slate-800 mb-2">
          {t('communities.connect.steps.bot.title')}
        </h2>
        <p className="text-slate-500">
          {t('communities.connect.steps.bot.stepDescription')}
        </p>
      </div>
      
      <div className="w-full bg-white rounded-2xl border-2 border-slate-100 shadow-lg overflow-hidden">
        {/* <PERSON><PERSON><PERSON><PERSON><PERSON>ı<PERSON>ı */}
        <div className="bg-[#229ED9]/10 p-5 border-b border-slate-100 flex items-center gap-3">
          <div className="w-12 h-12 rounded-full bg-[#229ED9] flex items-center justify-center text-white text-xl">
            <FaTelegram size={24} />
          </div>
          <div>
            <h3 className="font-bold text-lg text-slate-800">
              {t('communities.connect.steps.bot.description')} 
            </h3>
            <p className="text-[#229ED9] font-medium">
              {t('communities.connect.steps.bot.botName')}
            </p>
          </div>
        </div>
        
        {/* İçerik Kısmı */}
        <div className="p-6">
          <div className="flex flex-col gap-6">
            {/* Bot hakkında bilgi */}
            <div className="bg-blue-50 p-4 rounded-xl flex items-start gap-3">
              <FaInfoCircle className="text-blue-500 mt-1 flex-shrink-0" />
              <p className="text-sm text-slate-700">
                Topluluğunuzun doğrulanması ve reklamların gösterilmesi için Adnomio Telegram botu grubunuzda yönetici olarak eklenmelidir. Bot hiçbir mesajınıza erişmez, sadece reklamları yönetir.
              </p>
            </div>
            
            {/* Adımlar */}
            <div className="space-y-4">
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 rounded-full bg-[#229ED9] text-white flex items-center justify-center flex-shrink-0 mt-0.5">1</div>
                <div>
                  <p className="font-medium text-slate-800">Telegram uygulamanızı açın</p>
                  <p className="text-sm text-slate-600">Grubunuza giriş yapın</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 rounded-full bg-[#229ED9] text-white flex items-center justify-center flex-shrink-0 mt-0.5">2</div>
                <div>
                  <p className="font-medium text-slate-800">Adnomio botunu gruba ekleyin</p>
                  <p className="text-sm text-slate-600">Aşağıdaki butona tıklayarak botu ekleyin</p>
                </div>
              </div>
              
              <div className="flex items-start gap-3">
                <div className="w-6 h-6 rounded-full bg-[#229ED9] text-white flex items-center justify-center flex-shrink-0 mt-0.5">3</div>
                <div>
                  <p className="font-medium text-slate-800">Botu yönetici yapın</p>
                  <p className="text-sm text-slate-600">Grup ayarlarından botu yönetici olarak atayın</p>
                </div>
              </div>
            </div>
            
            {/* Butonlar */}
            <div className="flex flex-col sm:flex-row gap-3 mt-4">
              <a
                href="https://t.me/AdnomioBot?startgroup=true"
                target="_blank"
                rel="noopener noreferrer"
                className="flex-1 flex items-center justify-center gap-2 px-6 py-3 rounded-xl bg-[#229ED9] text-white font-semibold hover:bg-[#1a8ec7] transition-all shadow-sm"
              >
                <FaTelegram size={20} />
                {t('communities.connect.steps.bot.telegramButton')}
              </a>
              
              <button
                type="button"
                onClick={onBotAdd}
                className={`flex-1 flex items-center justify-center gap-2 px-6 py-3 rounded-xl font-semibold transition-all shadow-sm
                  ${botAdded 
                    ? 'bg-emerald-100 text-emerald-700 border border-emerald-300' 
                    : 'bg-white text-slate-700 border border-slate-200 hover:border-emerald-400 hover:text-emerald-600'
                  }`}
              >
                {botAdded && <FaCheck className="text-emerald-500" />}
                {t('communities.connect.steps.bot.addedButton')}
              </button>
            </div>
          </div>
        </div>
        
        {/* Başarı Mesajı */}
        {botAdded && (
          <div className="border-t border-emerald-200 bg-emerald-50 p-4 flex items-center justify-center gap-2 text-emerald-700 font-medium">
            <FaCheck className="text-emerald-500" />
            {t('communities.connect.steps.bot.successMessage')}
          </div>
        )}
        {error && (
          <div className="text-red-600 font-semibold">{t('communities.connect.steps.bot.errorMessage')}</div>
        )}
      </div>
    </div>
  );
} 