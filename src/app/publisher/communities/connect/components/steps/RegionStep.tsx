import React, { useState } from 'react';
import { useLanguage } from '@/shared/hooks/useLanguage';

interface RegionStepProps {
  selectedRegion: string;
  onSelect: (region: string) => void;
}

const regions = [
  { key: "tr", flag: "🇹🇷" },
  { key: "es", flag: "🇪🇸" },
  { key: "us", flag: "🇺🇸" },
  { key: "de", flag: "🇩🇪" },
  { key: "fr", flag: "🇫🇷" },
  { key: "gb", flag: "🇬🇧" },
  { key: "ru", flag: "🇷🇺" }
];

export function RegionStep({ selectedRegion, onSelect }: RegionStepProps) {
  const [searchInput, setSearchInput] = useState("");
  const { t } = useLanguage();

  return (
    <div className="w-full flex flex-col items-start gap-8">
      <h2 className="text-2xl font-bold text-slate-800 mb-2">
        {t('communities.connect.steps.region.title')}
      </h2>
      <div className="w-full flex flex-col gap-4">
        <div className="relative">
          <input
            type="text"
            placeholder={t('communities.connect.steps.region.searchPlaceholder')}
            className="w-full pl-10 pr-4 py-3 rounded-xl border-2 border-slate-200 bg-slate-50 focus:border-emerald-400 outline-none"
            value={searchInput}
            onChange={e => setSearchInput(e.target.value)}
          />
          <span className="absolute left-3 top-1/2 -translate-y-1/2 text-emerald-400 text-xl">🔍</span>
        </div>
        <div className="grid grid-cols-1 gap-4 max-h-[400px] overflow-y-auto">
          {regions
            .filter(r => !searchInput || t(`communities.connect.steps.region.list.${r.key}`).toLowerCase().includes(searchInput.toLowerCase()))
            .map(r => (
              <button
                key={r.key}
                className={`flex items-center gap-3 p-4 rounded-xl border-2 transition-all
                  ${selectedRegion === r.key ? 'bg-emerald-50 border-emerald-500 text-emerald-700' : 'bg-white border-slate-200 hover:border-emerald-400 hover:bg-emerald-50'}`}
                onClick={() => onSelect(r.key)}
              >
                <span className="text-2xl">{r.flag}</span>
                <span className="flex-1 text-left">{t(`communities.connect.steps.region.list.${r.key}`)}</span>
                {selectedRegion === r.key && <span className="text-emerald-500 text-xl">✓</span>}
              </button>
            ))}
        </div>
      </div>
    </div>
  );
} 