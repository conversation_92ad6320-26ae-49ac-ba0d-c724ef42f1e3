import React, { useState } from 'react';
import { useLanguage } from '@/shared/hooks/useLanguage';


interface Language {
  code: string;
  label: string;
}

interface LanguageStepProps {
  selectedLanguages: Language[];
  onSelect: (language: Language) => void;
  onRemove: (language: Language) => void;
}

export function LanguageStep({ selectedLanguages, onSelect, onRemove }: LanguageStepProps) {
  const [searchInput, setSearchInput] = useState("");
  const { t } = useLanguage();

  const languages = [
    { code: "tr", label: t('language.list.tr') },
    { code: "en", label: t('language.list.en') },
    { code: "ru", label: t('language.list.ru') },
    { code: "es", label: t('language.list.es') },
    { code: "de", label: t('language.list.de') },
    { code: "fr", label: t('language.list.fr') }
  ];

  const languageFlags: Record<string, string> = {
    "tr": "🇹🇷",
    "en": "🇬🇧",
    "ru": "🇷🇺",
    "es": "🇪🇸",
    "de": "🇩🇪",
    "fr": "🇫🇷"
  };

  return (
    <div className="w-full flex flex-col items-start gap-8">
      <h2 className="text-2xl font-bold text-slate-800 mb-2">
        {t('communities.connect.steps.language.stepTitle')}
      </h2>
      <div className="w-full flex flex-col gap-4">
        <div className="relative">
          <input
            type="text"
            placeholder={t('communities.connect.steps.language.searchPlaceholder')}
            className="w-full pl-10 pr-4 py-3 rounded-xl border-2 border-slate-200 bg-slate-50 focus:border-emerald-400 outline-none"
            value={searchInput}
            onChange={e => setSearchInput(e.target.value)}
          />
          <span className="absolute left-3 top-1/2 -translate-y-1/2 text-emerald-400 text-xl">🔍</span>
        </div>
        <div className="grid grid-cols-1 gap-4 max-h-[400px] overflow-y-auto">
          {languages
            .filter(l => !searchInput || l.label.toLowerCase().includes(searchInput.toLowerCase()))
            .map(l => {
              const isSelected = selectedLanguages.find(sl => sl.code === l.code);
              return (
                <button
                  key={l.code}
                  className={`flex items-center gap-3 p-4 rounded-xl border-2 transition-all
                    ${isSelected ? 'bg-emerald-50 border-emerald-500 text-emerald-700' : 'bg-white border-slate-200 hover:border-emerald-400 hover:bg-emerald-50'}`}
                  onClick={() => isSelected ? onRemove(l) : onSelect(l)}
                >
                  <span>{languageFlags[l.code] || "🌐"}</span>
                  <span className="flex-1 text-left">{l.label}</span>
                  {isSelected && <span className="text-emerald-500 text-xl">✓</span>}
                </button>
              );
            })}
        </div>
      </div>
    </div>
  );
} 