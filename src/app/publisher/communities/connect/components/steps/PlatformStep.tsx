import { Si<PERSON><PERSON>gram, Si<PERSON><PERSON>rd, SiInstagram, SiX } from "react-icons/si"
import { useLanguage } from '@/shared/hooks/useLanguage';

interface PlatformStepProps {
  selectedPlatform: string;
  onSelect: (platform: string) => void;
  error?: boolean;
}

const communityTypes = [
  { key: "telegram", icon: "📢" },
  { key: "discord", icon: "💬" },
  { key: "instagram", icon: "📸" },
  { key: "x", icon: "🐦" },
]

const communityTypeIcons: Record<string, JSX.Element> = {
  telegram: <SiTelegram className="w-8 h-8 text-[#229ED9]" />,
  discord: <SiDiscord className="w-8 h-8 text-[#5865F2]" />,
  instagram: <SiInstagram className="w-8 h-8 text-[#E1306C]" />,
  x: <SiX className="w-8 h-8 text-black" />
}

export function PlatformStep({ selectedPlatform, onSelect, error }: PlatformStepProps) {
  const { t } = useLanguage();

  return (
    <div className="w-full flex flex-col items-start gap-8">
      <h2 className="text-2xl font-bold text-slate-800 mb-2">
        {t('communities.connect.steps.platform.title')}
      </h2>
      <div className="w-full grid grid-cols-1 gap-4">
        {communityTypes.map((type) => (
          <button
            key={type.key}
            className={`w-full flex items-center gap-4 px-4 py-4 rounded-xl border-2 text-lg font-medium transition-all text-left select-none
              ${selectedPlatform === type.key 
                ? 'bg-emerald-50 border-emerald-500 text-emerald-700' 
                : 'bg-white border-slate-200 hover:border-emerald-400 hover:bg-emerald-50'
              }`}
            onClick={() => onSelect(type.key)}
          >
            <span className="w-8 h-8 flex items-center justify-center">
              {communityTypeIcons[type.key]}
            </span>
            <span>{t(`communities.platform.${type.key}`)}</span>
            {selectedPlatform === type.key && <span className="ml-auto text-emerald-500 text-xl">✓</span>}
          </button>
        ))}
      </div>
    </div>
  )
} 