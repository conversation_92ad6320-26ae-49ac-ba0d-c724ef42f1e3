"use client"

import { Dialog, DialogContent } from "@/components/ui/dialog"
import CommunityOnboardingStepper from "@/app/publisher/community-onboarding/components/CommunityOnboardingStepper"

interface StepperProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

export default function ConnectCommunityStepper({ isOpen, onClose, onSuccess }: StepperProps) {
  return (
    <Dialog open={isOpen} onOpenChange={(open) => {
      if (!open) {
        onClose()
      }
    }}>
      <DialogContent className="max-w-4xl h-[90vh] sm:h-[85vh] overflow-hidden flex flex-col p-0">
        <div className="flex-1 overflow-y-auto">
          <CommunityOnboardingStepper onSuccess={onSuccess} />
        </div>
      </DialogContent>
    </Dialog>
  )
} 