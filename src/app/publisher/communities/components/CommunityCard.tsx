import { Calendar, Link2 } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { Badge } from '@/components/ui/badge';
import { Community } from '../types';
import { formatDate } from '../utils';
import { useLanguage } from '@/shared/hooks/useLanguage';
import { SiTelegram, SiDiscord, SiInstagram, SiX } from 'react-icons/si';

interface CommunityCardProps {
  community: Community;
  onDelete?: (communityId: string) => void;
}

export function CommunityCard({ community, onDelete }: CommunityCardProps) {
  const { t } = useLanguage();

  // Platform rozeti için fonksiyon
  const getPlatformBadge = (platform: Community['platform']) => {
    const variants = {
      telegram: { bg: 'bg-[#229ED9]/80 hover:bg-[#229ED9]', label: t('communities.platform.telegram') },
      discord: { bg: 'bg-[#5865F2]/80 hover:bg-[#5865F2]', label: t('communities.platform.discord') },
      instagram: { bg: 'bg-[#E1306C]/80 hover:bg-[#E1306C]', label: t('communities.platform.instagram') },
      x: { bg: 'bg-black/80 hover:bg-black', label: t('communities.platform.x') }
    };
    
    const { bg, label } = variants[platform];
    
    return (
      <Badge className={`${bg} text-[10px] h-5`}>
        {label}
      </Badge>
    );
  };

  // Platform URL formatı için fonksiyon
  const getPlatformUrl = (platform: Community['platform'], username: string) => {
    const formats = {
      telegram: `https://t.me/${username}`,
      discord: `https://discord.gg/${username}`,
      instagram: `https://instagram.com/${username}`,
      x: `https://x.com/${username}`
    };
    
    return formats[platform];
  };

  // Platform ikonu için fonksiyon
  const getPlatformIcon = (platform: Community['platform']) => {
    const icons = {
      telegram: <SiTelegram className="w-8 h-8 text-[#229ED9]" />,
      discord: <SiDiscord className="w-8 h-8 text-[#5865F2]" />,
      instagram: <SiInstagram className="w-8 h-8 text-[#E1306C]" />,
      x: <SiX className="w-8 h-8 text-black" />
    };
    
    return icons[platform];
  };
  
  // Durum rozeti için fonksiyon
  const getStatusBadge = (status: Community['status']) => {
    const variants = {
      active: { bg: 'bg-green-600/80 hover:bg-green-600', label: t('communities.status.active') },
      pending: { bg: 'bg-amber-600/80 hover:bg-amber-600', label: t('communities.status.pending') },
      suspended: { bg: 'bg-red-600/80 hover:bg-red-600', label: t('communities.status.suspended') }
    };
    
    const { bg, label } = variants[status];
    
    return (
      <Badge className={`${bg} text-[10px] h-5`}>
        {label}
      </Badge>
    );
  };

  return (
    <Card className="border border-indigo-200/40 bg-gradient-to-br from-white/95 to-blue-50/95 shadow-xl hover:shadow-indigo-100 transition-all duration-300 hover:translate-y-[-2px] overflow-hidden">
      <CardContent className="p-5">
        <div className="flex items-start gap-4">
          <div className="h-12 w-12 rounded-lg bg-gradient-to-br from-blue-100 to-indigo-100 flex items-center justify-center text-xl shadow-sm">
            {getPlatformIcon(community.platform)}
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex flex-wrap items-center gap-2 mb-1">
              {getPlatformBadge(community.platform)}
              {getStatusBadge(community.status)}
            </div>
            
            <h3 className="text-slate-800 font-medium truncate mb-1">{community.name}</h3>
            
            <div className="flex items-center gap-2 text-xs text-slate-500">
              <Calendar className="h-3 w-3" />
              <span>{t('communities.card.addedOn')}: {formatDate(community.dateAdded)}</span>
            </div>
          </div>
        </div>
        
        <div className="mt-3 bg-indigo-50/80 p-2 rounded-md border border-indigo-100/50">
          <div className="flex items-center gap-2 text-xs text-slate-600">
            <Link2 className="h-3 w-3 text-indigo-500" />
            <a href={getPlatformUrl(community.platform, community.name)} target="_blank" rel="noopener noreferrer" className="text-indigo-500 hover:text-indigo-700 truncate">
              {getPlatformUrl(community.platform, community.name)}
            </a>
          </div>
        </div>
        
        <div className="mt-5 pt-5 border-t border-slate-200 grid grid-cols-2 gap-4">
          <div className="text-center">
            <div className="text-xs text-slate-500 mb-1">{t('communities.card.metrics.members')}</div>
            <div className="text-slate-800 font-medium">{community.members.toLocaleString()}</div>
            <div className="mt-1 h-2.5 bg-slate-200 rounded-full overflow-hidden w-full group">
              <div 
                className="h-full bg-blue-500 rounded-full transition-all duration-300"
                style={{ width: `${Math.min(100, (community.members / 10000) * 100)}%` }}
              ></div>
            </div>
          </div>
          <div className="text-center">
            <div className="text-xs text-slate-500 mb-1">{t('communities.card.metrics.views')}</div>
            <div className="text-slate-800 font-medium">{community.views.toLocaleString()}</div>
            <div className="mt-1 h-2.5 bg-slate-200 rounded-full overflow-hidden w-full group">
              <div 
                className="h-full bg-indigo-500 rounded-full transition-all duration-300"
                style={{ width: `${Math.min(100, (community.views / 30000) * 100)}%` }}
              ></div>
            </div>
          </div>
          <div className="text-center">
            <div className="text-xs text-slate-500 mb-1">{t('communities.card.metrics.ctr')}</div>
            <div className="text-slate-800 font-medium">%{community.ctr}</div>
            <div className="mt-1 h-2.5 bg-slate-200 rounded-full overflow-hidden w-full group">
              <div 
                className="h-full bg-green-500 rounded-full transition-all duration-300"
                style={{ width: `${Math.min(100, (community.ctr / 5) * 100)}%` }}
              ></div>
            </div>
          </div>
          <div className="text-center">
            <div className="text-xs text-slate-500 mb-1">{t('communities.card.metrics.earnings')}</div>
            <div className="text-green-600 font-medium">₺{community.earnings.toFixed(2)}</div>
            <div className="mt-1 h-2.5 bg-slate-200 rounded-full overflow-hidden w-full group">
              <div 
                className="h-full bg-purple-500 rounded-full transition-all duration-300"
                style={{ width: `${Math.min(100, (community.earnings / 700) * 100)}%` }}
              ></div>
            </div>
          </div>
        </div>
        
        <div className="mt-5 flex justify-between">
          <Link href={community.url} target="_blank">
            <Button variant="link" className="text-xs text-slate-500 hover:text-indigo-600 p-0 h-6">
              <Link2 className="h-3 w-3 mr-1" />
              {t('communities.card.openPage')}
            </Button>
          </Link>
          
          <Link href={`/publisher/communities/${community.id}`}>
            <Button className="bg-indigo-500 hover:bg-indigo-600 text-white text-xs h-7 px-3 rounded-full">
              {t('communities.card.details')}
            </Button>
          </Link>
        </div>
      </CardContent>
    </Card>
  );
} 