import { useState } from 'react';
import { Users, Eye, TrendingUp, CircleDollarSign, Trash2, ExternalLink } from 'lucide-react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { Badge } from '@/components/ui/badge';
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from '@/components/ui/table';
import { CommunityDetailsModal } from './CommunityDetailsModal';
import { useLanguage } from '@/shared/hooks/useLanguage';
import { type Community } from '../types';
import { SiTelegram, SiDiscord, SiInstagram, SiX } from 'react-icons/si';

interface CommunitiesListProps {
  communities: Community[];
  loading: boolean;
  error: string | null;
  onDelete: (id: string) => void;
}

export function CommunitiesList({ communities, loading, error, onDelete }: CommunitiesListProps) {
  const [selectedCommunity, setSelectedCommunity] = useState<Community | null>(null);
  const { t } = useLanguage();

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, index) => (
          <div key={index} className="h-24 bg-slate-100 animate-pulse rounded-lg"></div>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-500">{error}</p>
      </div>
    );
  }

  if (communities.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-slate-500">{t('communities.connect.noCommunities')}</p>
      </div>
    );
  }

  // Platform rozeti için fonksiyon
  const getPlatformBadge = (platform: Community['platform']) => {
    const variants = {
      telegram: { bg: 'bg-[#229ED9]/80 hover:bg-[#229ED9]', label: t('communities.platform.telegram') },
      discord: { bg: 'bg-[#5865F2]/80 hover:bg-[#5865F2]', label: t('communities.platform.discord') },
      instagram: { bg: 'bg-[#E1306C]/80 hover:bg-[#E1306C]', label: t('communities.platform.instagram') },
      x: { bg: 'bg-black/80 hover:bg-black', label: t('communities.platform.x') }
    };
    
    const { bg, label } = variants[platform];
    
    return (
      <Badge className={`${bg} text-[10px] h-5`}>
        {label}
      </Badge>
    );
  };

  // Platform URL formatı için fonksiyon
  const getPlatformUrl = (platform: Community['platform'], username: string) => {
    const formats = {
      telegram: `https://t.me/${username}`,
      discord: `https://discord.gg/${username}`,
      instagram: `https://instagram.com/${username}`,
      x: `https://x.com/${username}`
    };
    
    return formats[platform];
  };

  // Platform ikonu için fonksiyon
  const getPlatformIcon = (platform: Community['platform']) => {
    const icons = {
      telegram: <SiTelegram className="w-8 h-8 text-[#229ED9]" />,
      discord: <SiDiscord className="w-8 h-8 text-[#5865F2]" />,
      instagram: <SiInstagram className="w-8 h-8 text-[#E1306C]" />,
      x: <SiX className="w-8 h-8 text-black" />
    };
    
    return icons[platform];
  };
  
  // Durum rozeti için fonksiyon
  const getStatusBadge = (status: Community['status']) => {
    const variants = {
      active: { bg: 'bg-green-600/80 hover:bg-green-600', label: t('communities.status.active') },
      pending: { bg: 'bg-amber-600/80 hover:bg-amber-600', label: t('communities.status.pending') },
      suspended: { bg: 'bg-red-600/80 hover:bg-red-600', label: t('communities.status.suspended') }
    };
    
    const { bg, label } = variants[status];
    
    return (
      <Badge className={`${bg} text-[10px] h-5`}>
        {label}
      </Badge>
    );
  };

  return (
    <div className="space-y-4">
      {communities.map((community) => (
        <div
          key={community.id}
          className="group bg-white rounded-lg shadow-sm border border-slate-200 p-4 hover:shadow-md transition-all duration-200 hover:border-slate-300"
        >
          {/* Community Header - Always Visible */}
          <div className="flex flex-col sm:flex-row sm:items-center gap-4">
            <div className="flex items-center gap-4 flex-1">
              <div className="h-12 w-12 flex-shrink-0 rounded-full bg-purple-100 flex items-center justify-center group-hover:scale-105 transition-transform">
                {getPlatformIcon(community.platform)}
              </div>
              <div className="min-w-0 flex-1">
                <div className="flex items-center gap-2">
                  <h3 className="font-medium text-slate-800 truncate">{community.name}</h3>
                  {getPlatformBadge(community.platform)}
                </div>
                <div className="flex items-center gap-2">
                  <p className="text-sm text-slate-500 truncate">{getPlatformUrl(community.platform, community.name)}</p>
                  <ExternalLink className="w-4 h-4 text-slate-400" />
                </div>
              </div>
            </div>
            
            {/* Actions - Moved to flex-end on mobile */}
            <div className="flex items-center gap-2 sm:ml-auto justify-end">
              {getStatusBadge(community.status)}
              <Button
                variant="ghost"
                size="sm"
                className="text-red-500 hover:text-red-600 hover:bg-red-50"
                onClick={() => onDelete(community.id)}
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            </div>
          </div>

          {/* Stats Grid - Responsive Layout */}
          <div className="grid grid-cols-2 sm:grid-cols-4 gap-4 mt-4">
            <div className="p-3 rounded-lg bg-slate-50 group-hover:bg-slate-100/80 transition-colors">
              <div className="flex items-center gap-2 text-slate-600">
                <Users className="w-4 h-4" />
                <p className="text-sm">{t('communities.list.columns.members')}</p>
              </div>
              <p className="font-medium text-slate-800 mt-1">{community.members.toLocaleString()}</p>
            </div>
            
            <div className="p-3 rounded-lg bg-slate-50 group-hover:bg-slate-100/80 transition-colors">
              <div className="flex items-center gap-2 text-slate-600">
                <Eye className="w-4 h-4" />
                <p className="text-sm">{t('communities.list.columns.views')}</p>
              </div>
              <p className="font-medium text-slate-800 mt-1">{community.views.toLocaleString()}</p>
            </div>
            
            <div className="p-3 rounded-lg bg-slate-50 group-hover:bg-slate-100/80 transition-colors">
              <div className="flex items-center gap-2 text-slate-600">
                <TrendingUp className="w-4 h-4" />
                <p className="text-sm">{t('communities.list.columns.ctr')}</p>
              </div>
              <p className="font-medium text-slate-800 mt-1">%{community.ctr.toFixed(1)}</p>
            </div>
            
            <div className="p-3 rounded-lg bg-slate-50 group-hover:bg-slate-100/80 transition-colors">
              <div className="flex items-center gap-2 text-slate-600">
                <CircleDollarSign className="w-4 h-4" />
                <p className="text-sm">{t('communities.list.columns.earnings')}</p>
              </div>
              <p className="font-medium text-green-600 mt-1">₺{community.earnings.toFixed(2)}</p>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
} 