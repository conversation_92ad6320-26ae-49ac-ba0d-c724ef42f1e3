import { Community } from '../types';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogClose,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Button } from "@/components/ui/button";
import { Users, Eye, MousePointerClick, TrendingUp, Calendar, Globe2, Languages, Tag, Trash2, X } from 'lucide-react';
import { formatDate } from '../utils';
import { useState } from 'react';
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { useLanguage } from '@/shared/hooks/useLanguage';

interface CommunityDetailsModalProps {
  community: Community;
  isOpen: boolean;
  onClose: () => void;
  onDelete?: (communityId: string) => void;
}

export function CommunityDetailsModal({ community, isOpen, onClose, onDelete }: CommunityDetailsModalProps) {
  const [showDeleteAlert, setShowDeleteAlert] = useState(false);
  const { t } = useLanguage();

  // Platform rozeti için fonksiyon
  const getPlatformBadge = (platform: Community['platform']) => {
    const variants = {
      telegram: { bg: 'bg-blue-600/80 hover:bg-blue-600', label: t('communities.platform.telegram') },
      whatsapp: { bg: 'bg-green-600/80 hover:bg-green-600', label: t('communities.platform.whatsapp') },
      discord: { bg: 'bg-indigo-600/80 hover:bg-indigo-600', label: t('communities.platform.discord') },
      instagram: { bg: 'bg-pink-600/80 hover:bg-pink-600', label: t('communities.platform.instagram') }
    };
    
    const { bg, label } = variants[platform];
    
    return (
      <Badge className={`${bg} text-[10px] h-5`}>
        {label}
      </Badge>
    );
  };

  // Durum rozeti için fonksiyon
  const getStatusBadge = (status: Community['status']) => {
    const variants = {
      active: { bg: 'bg-green-600/80 hover:bg-green-600', label: t('communities.status.active') },
      pending: { bg: 'bg-amber-600/80 hover:bg-amber-600', label: t('communities.status.pending') },
      suspended: { bg: 'bg-red-600/80 hover:bg-red-600', label: t('communities.status.suspended') }
    };
    
    const { bg, label } = variants[status];
    
    return (
      <Badge className={`${bg} text-[10px] h-5`}>
        {label}
      </Badge>
    );
  };

  const handleDelete = () => {
    if (onDelete) {
      onDelete(community.id);
    }
    setShowDeleteAlert(false);
    onClose();
  };

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-4xl p-0 h-[95vh] sm:h-auto">
          {/* Close Button */}
          <DialogClose className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
            <X className="h-4 w-4" />
            <span className="sr-only">{t('communities.details.modal.close')}</span>
          </DialogClose>

          <ScrollArea className="h-full">
            <div className="p-6 md:p-8">
              {/* Header */}
              <div className="mb-6">
                <div className="flex items-start gap-4">
                  <div className="h-14 w-14 rounded-xl bg-gradient-to-br from-blue-100 to-indigo-100 flex items-center justify-center text-2xl shadow-sm flex-shrink-0">
                    {community.icon}
                  </div>
                  <div className="min-w-0 flex-1">
                    <h2 className="text-xl sm:text-2xl font-semibold mb-3 text-slate-900">{community.name}</h2>
                    <div className="flex flex-wrap items-center gap-2 mb-3">
                      {getPlatformBadge(community.platform)}
                      {getStatusBadge(community.status)}
                    </div>
                    <div className="flex items-center gap-2 text-sm text-slate-500">
                      <Calendar className="h-4 w-4 shrink-0" />
                      {t('communities.card.addedOn')}: {formatDate(community.dateAdded)}
                    </div>
                  </div>
                </div>
              </div>

              {/* Ana Metrikler */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                <Card>
                  <CardContent className="pt-6">
                    <div className="flex items-center gap-2 mb-2">
                      <Users className="h-5 w-5 text-blue-500 shrink-0" />
                      <h3 className="font-medium">{t('communities.card.metrics.members')}</h3>
                    </div>
                    <div className="text-2xl font-semibold text-blue-600 mb-2">
                      {community.members.toLocaleString()}
                    </div>
                    <Progress 
                      value={Math.min(100, (community.members / 10000) * 100)} 
                      className="h-2"
                      indicatorClassName="bg-blue-500"
                    />
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="pt-6">
                    <div className="flex items-center gap-2 mb-2">
                      <Eye className="h-5 w-5 text-indigo-500 shrink-0" />
                      <h3 className="font-medium">{t('communities.card.metrics.views')}</h3>
                    </div>
                    <div className="text-2xl font-semibold text-indigo-600 mb-2">
                      {community.views.toLocaleString()}
                    </div>
                    <Progress 
                      value={Math.min(100, (community.views / 30000) * 100)} 
                      className="h-2"
                      indicatorClassName="bg-indigo-500"
                    />
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="pt-6">
                    <div className="flex items-center gap-2 mb-2">
                      <MousePointerClick className="h-5 w-5 text-green-500 shrink-0" />
                      <h3 className="font-medium">{t('communities.card.metrics.ctr')}</h3>
                    </div>
                    <div className="text-2xl font-semibold text-green-600 mb-2">
                      %{community.ctr}
                    </div>
                    <Progress 
                      value={Math.min(100, (community.ctr / 5) * 100)} 
                      className="h-2"
                      indicatorClassName="bg-green-500"
                    />
                  </CardContent>
                </Card>

                <Card>
                  <CardContent className="pt-6">
                    <div className="flex items-center gap-2 mb-2">
                      <TrendingUp className="h-5 w-5 text-purple-500 shrink-0" />
                      <h3 className="font-medium">{t('communities.card.metrics.earnings')}</h3>
                    </div>
                    <div className="text-2xl font-semibold text-purple-600 mb-2">
                      ₺{community.earnings.toFixed(2)}
                    </div>
                    <Progress 
                      value={Math.min(100, (community.earnings / 700) * 100)} 
                      className="h-2"
                      indicatorClassName="bg-purple-500"
                    />
                  </CardContent>
                </Card>
              </div>

              {/* Topluluk Detayları ve Metrikler */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
                {/* Topluluk Detayları */}
                <Card>
                  <CardContent className="pt-6">
                    <h3 className="font-medium mb-4 flex items-center gap-2">
                      <Tag className="h-5 w-5 text-slate-500" />
                      {t('communities.details.modal.info.title')}
                    </h3>
                    
                    <div className="space-y-4">
                      <div className="flex items-center gap-3 pb-2 border-b border-slate-100">
                        <Globe2 className="h-4 w-4 text-slate-400" />
                        <span className="text-slate-600">{t('communities.details.modal.info.country')}</span>
                        <span className="font-medium ml-auto">Türkiye</span>
                      </div>
                      
                      <div className="flex items-center gap-3 pb-2 border-b border-slate-100">
                        <Languages className="h-4 w-4 text-slate-400" />
                        <span className="text-slate-600">{t('communities.details.modal.info.language')}</span>
                        <span className="font-medium ml-auto">Türkçe</span>
                      </div>
                      
                      <div className="flex items-center gap-3 pb-2 border-b border-slate-100">
                        <Tag className="h-4 w-4 text-slate-400" />
                        <span className="text-slate-600">{t('communities.details.modal.info.category')}</span>
                        <span className="font-medium ml-auto">Teknoloji</span>
                      </div>
                      
                      <div className="flex items-center gap-3 pb-2 border-b border-slate-100">
                        <Users className="h-4 w-4 text-slate-400" />
                        <span className="text-slate-600">{t('communities.details.modal.info.targetAudience')}</span>
                        <span className="font-medium ml-auto">18-35 yaş</span>
                      </div>
                      
                      <div className="flex items-center gap-3">
                        <Globe2 className="h-4 w-4 text-slate-400" />
                        <span className="text-slate-600">{t('communities.details.modal.info.url')}</span>
                        <a href={community.url} target="_blank" rel="noopener noreferrer" className="font-medium ml-auto text-blue-600 hover:text-blue-700">
                          {t('communities.details.modal.info.visitCommunity')}
                        </a>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Detaylı Metrikler */}
                <Card>
                  <CardContent className="pt-6">
                    <h3 className="font-medium mb-4">{t('communities.details.modal.metrics.title')}</h3>
                    
                    <div className="space-y-4">
                      <div className="flex justify-between items-center pb-2 border-b border-slate-100">
                        <span className="text-slate-600">{t('communities.details.modal.metrics.engagementRate')}</span>
                        <span className="font-medium">%3.2</span>
                      </div>
                      
                      <div className="flex justify-between items-center pb-2 border-b border-slate-100">
                        <span className="text-slate-600">{t('communities.details.modal.metrics.activeUsers')}</span>
                        <span className="font-medium">%68</span>
                      </div>
                      
                      <div className="flex justify-between items-center pb-2 border-b border-slate-100">
                        <span className="text-slate-600">{t('communities.details.modal.metrics.dailyViews')}</span>
                        <span className="font-medium">1,240</span>
                      </div>
                      
                      <div className="flex justify-between items-center pb-2 border-b border-slate-100">
                        <span className="text-slate-600">{t('communities.details.modal.metrics.monthlyGrowth')}</span>
                        <span className="font-medium text-green-600">+%5.3</span>
                      </div>
                      
                      <div className="flex justify-between items-center">
                        <span className="text-slate-600">{t('communities.details.modal.metrics.adScore')}</span>
                        <span className="font-medium text-blue-600">8.4/10</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Delete Section */}
              <div className="mt-8 pt-6 border-t border-slate-200">
                <div className="flex flex-col space-y-2">
                  <h3 className="text-sm font-medium text-slate-900">{t('communities.details.modal.delete.title')}</h3>
                  <p className="text-sm text-slate-500">
                    {t('communities.details.modal.delete.description')}
                  </p>
                  <Button
                    variant="ghost"
                    className="w-full sm:w-auto justify-start text-red-600 hover:text-red-700 hover:bg-red-50"
                    onClick={() => setShowDeleteAlert(true)}
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    {t('communities.details.modal.delete')}
                  </Button>
                </div>
              </div>
            </div>
          </ScrollArea>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Alert */}
      <AlertDialog open={showDeleteAlert} onOpenChange={setShowDeleteAlert}>
        <AlertDialogContent className="max-w-[90vw] sm:max-w-lg">
          <AlertDialogHeader>
            <AlertDialogTitle>{t('communities.details.modal.delete.confirm.title')}</AlertDialogTitle>
            <AlertDialogDescription className="space-y-2">
              <p>{t('communities.details.modal.delete.confirm.description')}</p>
              <p className="font-medium text-slate-900">{community.name}</p>
              <p className="text-sm text-red-600">{t('communities.details.modal.delete.confirm.warning')}</p>
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter className="flex-col-reverse sm:flex-row gap-2">
            <AlertDialogCancel className="w-full sm:w-auto">{t('communities.details.modal.delete.confirm.cancel')}</AlertDialogCancel>
            <AlertDialogAction
              className="w-full sm:w-auto bg-red-600 hover:bg-red-700 text-white"
              onClick={handleDelete}
            >
              {t('communities.details.modal.delete.confirm.confirm')}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
} 