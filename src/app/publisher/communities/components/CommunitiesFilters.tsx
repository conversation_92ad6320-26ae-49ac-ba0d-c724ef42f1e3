import { Search, GalleryVertical, Table2 } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent } from '@/components/ui/card';
import { useLanguage } from '@/shared/hooks/useLanguage';

interface CommunitiesFiltersProps {
  searchQuery: string;
  onSearchChange: (value: string) => void;
  filter: 'all' | 'active' | 'pending' | 'suspended';
  onFilterChange: (value: 'all' | 'active' | 'pending' | 'suspended') => void;
}

export function CommunitiesFilters({
  searchQuery,
  onSearchChange,
  filter,
  onFilterChange,
}: CommunitiesFiltersProps) {
  const { t } = useLanguage();
  return (
    <Card className="border border-indigo-200/40 bg-gradient-to-br from-white/95 to-blue-50/95 shadow-md">
      <CardContent className="p-4">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
            <Input 
              placeholder={t('communities.filters.search')}
              className="pl-9 bg-white border-slate-200 text-slate-800 placeholder:text-slate-400"
              value={searchQuery}
              onChange={(e) => onSearchChange(e.target.value)}
            />
          </div>
          
          <div className="flex gap-2">
            <Tabs value={filter} onValueChange={(v) => onFilterChange(v as 'all' | 'active' | 'pending' | 'suspended')}>
              <TabsList className="grid grid-cols-4 h-9 bg-white border border-slate-200">
                <TabsTrigger value="all" className="text-xs">{t('communities.filters.status.all')}</TabsTrigger>
                <TabsTrigger value="active" className="text-xs">{t('communities.filters.status.active')}</TabsTrigger>
                <TabsTrigger value="pending" className="text-xs">{t('communities.filters.status.pending')}</TabsTrigger>
                <TabsTrigger value="suspended" className="text-xs">{t('communities.filters.status.suspended')}</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>
          
          <div className="flex justify-end gap-2">
          </div>
        </div>
      </CardContent>
    </Card>
  );
} 