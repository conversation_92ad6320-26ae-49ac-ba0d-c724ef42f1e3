import { Users, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useLanguage } from '@/shared/hooks/useLanguage';

interface CommunitiesHeaderProps {
  onAddCommunity: () => void;
}

export function CommunitiesHeader({ onAddCommunity }: CommunitiesHeaderProps) {
  const { t } = useLanguage();
  return (
    <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
      <div>
        <h1 className="text-2xl md:text-3xl font-bold flex items-center gap-2">
          <Users className="h-7 w-7 text-purple-400" />
          <span>{t('communities.title')}</span>
        </h1>
        <p className="text-gray-400 mt-1">{t('communities.subtitle')}</p>
      </div>
      
      <Button 
        className="bg-gradient-to-r from-violet-600 to-indigo-600 hover:from-violet-700 hover:to-indigo-700 rounded-lg border-none shadow-md hover:shadow-lg transition-all duration-300"
        onClick={onAddCommunity}
      >
        <Plus className="h-4 w-4 mr-2" />
        {t('communities.connect.title')}
      </Button>
    </div>
  );
} 