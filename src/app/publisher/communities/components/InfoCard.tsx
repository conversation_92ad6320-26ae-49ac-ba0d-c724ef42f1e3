import { Info } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { useLanguage } from '@/shared/hooks/useLanguage';

export function InfoCard() {
  const { t } = useLanguage();

  return (
    <Card className="border border-indigo-200/40 bg-gradient-to-br from-white/95 to-blue-50/95 shadow-md">
      <CardContent className="p-5">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div className="flex items-start gap-4">
            <div className="h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center flex-shrink-0">
              <Info className="h-5 w-5 text-indigo-500" />
            </div>
            <div>
              <h3 className="text-slate-800 font-medium">{t('communities.info.title')}</h3>
              <p className="text-slate-500 text-sm mt-1">{t('communities.info.description')}</p>
            </div>
          </div>
          
          <Link href="/publisher/help/communities">
            <Button variant="outline" className="border-indigo-200 text-indigo-700 bg-indigo-50 hover:bg-indigo-100 rounded-md">
              {t('communities.info.button')}
            </Button>
          </Link>
        </div>
      </CardContent>
    </Card>
  );
} 