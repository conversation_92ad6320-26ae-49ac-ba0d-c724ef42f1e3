/**
 * Publisher mod<PERSON><PERSON>ü için temel tema yapılandırması
 * B<PERSON> dosya, publisher sayfalarında kullanılan renkleri ve stilleri tanımlar
 */

// Tema modları
export type ThemeMode = 'light' | 'dark';

// Tema yapılandırması
export const themeConfig = {
  // Varsayılan tema modu
  defaultMode: 'light' as ThemeMode,
  
  // Tema renkleri
  colors: {
    light: {
      // Arka plan renkleri
      background: {
        primary: '#FFFFFF',
        secondary: '#F9FAFB',
        tertiary: '#F3F4F6',
        accent: '#F5F3FF',
      },
      
      // Panel/kart arka planları
      panel: {
        primary: '#FFFFFF',
        secondary: '#F9FAFB',
        accent: 'linear-gradient(to bottom right, #FFFFFF, #EBF4FF)',
      },
      
      // Kenar renkleri
      border: {
        light: '#E2E8F0',
        medium: '#CBD5E1',
        accent: '#E0E7FF',
      },
      
      // Metin renkleri
      text: {
        primary: '#1E293B',
        secondary: '#475569',
        muted: '#64748B',
        accent: '#4F46E5',
      },
      
      // Aksiyon renkleri
      action: {
        primary: '#4F46E5',
        primaryHover: '#3730A3',
        secondary: '#F5F3FF',
        secondaryHover: '#E0E7FF',
      },
    },
  },
  
  // Tailwind için genel sınıflar
  classes: {
    // Sayfa arka planı
    page: 'bg-white text-slate-800',
    
    // Kart sınıfları
    card: {
      base: 'border border-slate-200 bg-white shadow-sm rounded-lg',
      hover: 'hover:border-indigo-200/60 hover:shadow-md transition-all duration-300',
      gradient: 'bg-gradient-to-br from-white to-blue-50/80 border border-slate-200',
    },
    
    // Buton sınıfları
    button: {
      primary: 'bg-indigo-600 hover:bg-indigo-700 text-white',
      secondary: 'bg-indigo-100 hover:bg-indigo-200 text-indigo-800',
      outline: 'border border-slate-200 bg-white hover:bg-slate-50 text-slate-800',
    },
    
    // Başlık ve metin sınıfları
    text: {
      title: 'text-slate-800 font-medium',
      body: 'text-slate-600',
      muted: 'text-slate-500 text-sm',
    },
    
    // Form elemanları
    input: 'border border-slate-200 bg-white text-slate-800 placeholder:text-slate-400',
    
    // Bildirim ve durum sınıfları
    status: {
      success: 'bg-emerald-500 text-white',
      warning: 'bg-amber-500 text-white',
      error: 'bg-red-500 text-white',
      info: 'bg-blue-500 text-white',
    },
  },
};

export default themeConfig;





