"use client"
import { useState, useRef, useEffect } from "react"
import { Si<PERSON><PERSON>gram, SiD<PERSON>rd, <PERSON><PERSON><PERSON><PERSON>ram, SiX } from "react-icons/si"
import { useRouter } from "next/navigation"
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  <PERSON>alogFooter,
  DialogTitle,
  DialogDescription
} from "@/components/ui/dialog"
import { FaCheckCircle, FaExclamationCircle, FaRegClipboard } from "react-icons/fa"
import { createPublisherCommunityFirst } from "@/shared/services/publisher/community-create-first"
import { useLanguage } from '@/shared/hooks/useLanguage';
import { getCountries, getLanguages } from '@/shared/services/system/locales';

const steps = [
  "communities.connect.steps.platform.stepTitle",
  "communities.connect.steps.region.stepTitle",
  "communities.connect.steps.language.stepTitle",
  "communities.connect.steps.category.stepTitle",
  "communities.connect.steps.info.stepTitle",
  "communities.connect.steps.bot.stepTitle"
]

const communityTypes = [
  { key: "telegram", label: "Telegram", icon: "📢" },
  { key: "discord", label: "Discord", icon: "💬" },
  { key: "instagram", label: "Instagram", icon: "📸" },
  { key: "x", label: "X", icon: "🐦" },
]

const regions = [
  { key: "tr", label: "Türkiye", flag: "🇹🇷" },
  { key: "es", label: "İspanya", flag: "🇪🇸" },
  { key: "us", label: "Amerika Birleşik Devletleri", flag: "🇺🇸" },
  { key: "de", label: "Almanya", flag: "🇩🇪" },
  { key: "fr", label: "Fransa", flag: "🇫🇷" },
  { key: "gb", label: "İngiltere", flag: "🇬🇧" },
  { key: "ru", label: "Rusya", flag: "🇷🇺" },
  { key: "it", label: "İtalya", flag: "🇮🇹" },
  { key: "br", label: "Brezilya", flag: "🇧🇷" },
  { key: "jp", label: "Japonya", flag: "🇯🇵" },
  { key: "cn", label: "Çin", flag: "🇨🇳" },
  { key: "in", label: "Hindistan", flag: "🇮🇳" },
  { key: "ca", label: "Kanada", flag: "🇨🇦" },
  { key: "au", label: "Avustralya", flag: "🇦🇺" },
  { key: "ar", label: "Arjantin", flag: "🇦🇷" },
  { key: "mx", label: "Meksika", flag: "🇲🇽" },
  { key: "kr", label: "Güney Kore", flag: "🇰🇷" },
  { key: "nl", label: "Hollanda", flag: "🇳🇱" },
  { key: "se", label: "İsveç", flag: "🇸🇪" },
  { key: "no", label: "Norveç", flag: "🇳🇴" },
  { key: "fi", label: "Finlandiya", flag: "🇫🇮" },
  { key: "dk", label: "Danimarka", flag: "🇩🇰" },
  { key: "pl", label: "Polonya", flag: "🇵🇱" },
  { key: "gr", label: "Yunanistan", flag: "🇬🇷" },
  { key: "ua", label: "Ukrayna", flag: "🇺🇦" },
  { key: "bg", label: "Bulgaristan", flag: "🇧🇬" },
  { key: "ro", label: "Romanya", flag: "🇷🇴" },
  { key: "cz", label: "Çekya", flag: "🇨🇿" },
  { key: "hu", label: "Macaristan", flag: "🇭🇺" },
  { key: "pt", label: "Portekiz", flag: "🇵🇹" },
  { key: "be", label: "Belçika", flag: "🇧🇪" },
  { key: "ch", label: "İsviçre", flag: "🇨🇭" },
  { key: "at", label: "Avusturya", flag: "🇦🇹" },
  { key: "il", label: "İsrail", flag: "🇮🇱" },
  { key: "sa", label: "Suudi Arabistan", flag: "🇸🇦" },
  { key: "ae", label: "Birleşik Arap Emirlikleri", flag: "🇦🇪" },
  { key: "eg", label: "Mısır", flag: "🇪🇬" },
  { key: "za", label: "Güney Afrika", flag: "🇿🇦" },
  { key: "ng", label: "Nijerya", flag: "🇳🇬" },
  { key: "ma", label: "Fas", flag: "🇲🇦" },
  { key: "id", label: "Endonezya", flag: "🇮🇩" },
  { key: "th", label: "Tayland", flag: "🇹🇭" },
  { key: "sg", label: "Singapur", flag: "🇸🇬" },
  { key: "my", label: "Malezya", flag: "🇲🇾" },
  { key: "ph", label: "Filipinler", flag: "🇵🇭" },
  { key: "vn", label: "Vietnam", flag: "🇻🇳" },
  { key: "cl", label: "Şili", flag: "🇨🇱" },
  { key: "co", label: "Kolombiya", flag: "🇨🇴" },
  { key: "pe", label: "Peru", flag: "🇵🇪" },
  { key: "ve", label: "Venezuela", flag: "🇻🇪" },
  // Daha fazla ülke eklenebilir
]

const languages = [
  { code: "tr", label: "Türkçe - Turkish" },
  { code: "en", label: "English - English" },
  { code: "ru", label: "Русский - Russian" },
  { code: "es", label: "Español - Spanish" },
  { code: "de", label: "Deutsch - German" },
  { code: "fr", label: "Français - French" },
  { code: "kk", label: "Қазақша - Kazakh" },
  { code: "uz", label: "Ўзбек - Uzbek" },
  { code: "az", label: "Azərbaycan / آذربایجان - Azerbaijani" },
  { code: "kg", label: "Kırgızca / Кыргызча - Kirghiz" },
  { code: "be", label: "Беларуская - Belarusian" }
]

const categories = [
  "Alışveriş", "Arabalar", "Arkadaşlık", "Bilim", "Blog", "Dergiler & Magazin", "Diller", "Dizi & Film", "Donanım & Yazılım", "E-Ticaret", "Ekonomi"
]

const categoryIcons: Record<string, string> = {
  "Alışveriş": "🛒",
  "Arabalar": "🚗",
  "Arkadaşlık": "🤝",
  "Bilim": "🔬",
  "Blog": "✍️",
  "Dergiler & Magazin": "📰",
  "Diller": "🌐",
  "Dizi & Film": "🎬",
  "Donanım & Yazılım": "💻",
  "E-Ticaret": "🛍️",
  "Ekonomi": "💸"
}
const languageFlags: Record<string, string> = {
  "Türkçe - Turkish": "🇹🇷",
  "English - English": "🇬🇧",
  "Русский - Russian": "🇷🇺",
  "Español - Spanish": "🇪🇸",
  "Deutsch - German": "🇩🇪",
  "Français - French": "🇫🇷",
  "Қазақша - Kazakh": "🇰🇿",
  "Ўзбек - Uzbek": "🇺🇿",
  "Azərbaycan / آذربایجان - Azerbaijani": "🇦🇿",
  "Kırgızca / Кыргызча - Kirghiz": "🇰🇬",
  "Беларуская - Belarusian": "🇧🇾"
}
const popularCategories = ["Alışveriş", "Bilim", "Blog"]
const popularLanguages = ["Türkçe - Turkish", "English - English", "Русский - Russian", "Español - Spanish", "Deutsch - German"]

const communityTypeIcons: Record<string, JSX.Element> = {
  telegram: <SiTelegram className="w-8 h-8 text-[#229ED9]" />,
  discord: <SiDiscord className="w-8 h-8 text-[#5865F2]" />,
  instagram: <SiInstagram className="w-8 h-8 text-[#E1306C]" />,
  x: <SiX className="w-8 h-8 text-black" />
}

interface CommunityOnboardingStepperProps {
  onSuccess?: () => void;
}

export default function CommunityOnboardingStepper({ onSuccess }: CommunityOnboardingStepperProps) {
  const { t } = useLanguage();
  const [step, setStep] = useState(0)
  const [communityType, setCommunityType] = useState<string | null>(null)
  const [region, setRegion] = useState<string | null>(null)
  const [selectedLanguages, setSelectedLanguages] = useState<string[]>([])
  const [languageInput, setLanguageInput] = useState("")
  const [category, setCategory] = useState<string | null>(null)
  const [categoryInput, setCategoryInput] = useState("")
  const [communityLink, setCommunityLink] = useState("")
  const [communityUsername, setCommunityUsername] = useState("")
  const [botAdded, setBotAdded] = useState(false)
  const [showExitModal, setShowExitModal] = useState(false)
  const [regionInput, setRegionInput] = useState("")
  const [copied, setCopied] = useState(false)
  const [countries, setCountries] = useState<string[]>([])
  const [languages, setLanguages] = useState<string[]>([])
  const [isLoadingCountries, setIsLoadingCountries] = useState(false)
  const [isLoadingLanguages, setIsLoadingLanguages] = useState(false)
  const inputRef = useRef<HTMLInputElement>(null)
  const router = useRouter();
  const [submitLoading, setSubmitLoading] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  const [submitSuccess, setSubmitSuccess] = useState(false);

  // Ülkeleri yükle
  useEffect(() => {
    const loadCountries = async () => {
      try {
        setIsLoadingCountries(true);
        const response = await getCountries();
        if (response.status) {
          setCountries(response.result);
        }
      } catch (error) {
        console.error('Error loading countries:', error);
      } finally {
        setIsLoadingCountries(false);
      }
    };

    loadCountries();
  }, []);

  // Dilleri yükle
  useEffect(() => {
    const loadLanguages = async () => {
      try {
        setIsLoadingLanguages(true);
        const languages = await getLanguages();
        setLanguages(languages);
      } catch (error) {
        console.error('Error loading languages:', error);
      } finally {
        setIsLoadingLanguages(false);
      }
    };

    loadLanguages();
  }, []);

  // Adımların toplam sayısını hesapla
  const getTotalSteps = () => {
    return communityType === "telegram" ? 6 : 5;
  }

  // Son adım mı kontrolü
  const isLastStep = () => {
    if (communityType === "telegram") {
      return step === 5;
    }
    return step === 4;
  }

  // Telegram seçilmezse bölge adımını atla
  const nextStep = () => {
    if (step === 0 && communityType !== "telegram") {
      setStep(2)
    } else {
      setStep(s => Math.min(getTotalSteps() - 1, s + 1))
    }
  }

  const prevStep = () => {
    if (step === 2 && communityType !== "telegram") {
      setStep(0)
    } else {
      setStep(s => Math.max(0, s - 1))
    }
  }

  // Dil ekleme
  const handleAddLanguage = (lang: string) => {
    if (!selectedLanguages.includes(lang)) {
      setSelectedLanguages([...selectedLanguages, lang]);
    }
    setLanguageInput("");
  }

  // Dil silme
  const handleRemoveLanguage = (lang: string) => {
    setSelectedLanguages(selectedLanguages.filter(l => l !== lang));
  }

  // Validasyonlar
  const isNextDisabled = () => {
    if (step === 0) return !communityType
    if (step === 1 && communityType === "telegram") return !region
    if (step === 2) return selectedLanguages.length === 0
    if (step === 3) return !category
    if (step === 4) return !communityUsername || communityUsername.includes("/") || communityUsername.includes(" ")
    if (step === 5) return !botAdded
    return false
  }

  // Filtreli diller ve kategoriler
  const filteredLanguages = [
    ...selectedLanguages.filter(l => !languageInput || l.toLowerCase().includes(languageInput.toLowerCase())),
    ...languages.filter(l =>
      !selectedLanguages.includes(l) && (!languageInput || l.toLowerCase().includes(languageInput.toLowerCase()))
    )
  ];
  const filteredCategories = categories.filter(c => !categoryInput || c.toLowerCase().includes(categoryInput.toLowerCase()))

  // Topluluk oluşturma fonksiyonu
  const handleSubmit = async () => {
    if (!selectedLanguages[0]) {
      setSubmitError("Lütfen en az bir dil seçiniz.");
      return;
    }

    // Validasyonlar
    if (!communityUsername) {
      setSubmitError("Topluluk kullanıcı adı gereklidir.");
      return;
    }

    if (!category) {
      setSubmitError("Kategori seçimi gereklidir.");
      return;
    }

    if (communityType === "telegram" && !region) {
      setSubmitError("Bölge seçimi gereklidir.");
      return;
    }

    setSubmitLoading(true);
    setSubmitError(null);

    const payload = {
      community_username: communityUsername.replace(/^@/, ""),
      community_name: communityUsername.replace(/^@/, ""), // Kullanıcı adını topluluk adı olarak kullan
      category: category,
      country: region || "tr", // Eğer bölge seçilmemişse varsayılan olarak Türkiye
      language: selectedLanguages[0]
    };

    console.log("Topluluk oluşturma için gönderilen veriler:", payload);

    try {
      const res = await createPublisherCommunityFirst(payload);
      console.log("API yanıtı:", res);
      if (res.status) {
        setSubmitSuccess(true);
        setTimeout(() => {
          router.push("/publisher");
          if (onSuccess) {
            onSuccess();
          }
        }, 1500);
      } else {
        setSubmitError(res.message || "Bir hata oluştu.");
      }
    } catch (e) {
      setSubmitError("Bir hata oluştu.");
    }
    setSubmitLoading(false);
  };

  return (
    <div className="flex flex-col min-h-screen bg-slate-50">
      {/* Stepper adımları en üstte, daha gelişmiş */}
      <div className="w-full flex justify-center pt-8 pb-4">
        <div className="flex items-center gap-0">
          {steps.slice(0, getTotalSteps()).map((stepTitle, idx, arr) => (
            <div key={stepTitle} className="flex items-center">
              <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 text-base font-bold transition-all duration-300
                ${step === idx ? 'bg-emerald-500 border-emerald-500 text-white shadow-lg scale-110' : step > idx ? 'bg-emerald-100 border-emerald-300 text-emerald-500' : 'bg-white border-slate-300 text-slate-400'}
              `}>
                {step > idx ? <FaCheckCircle className="text-emerald-500" size={18} /> : idx + 1}
              </div>
              {idx < arr.length - 1 && (
                <div className={`w-10 h-1 mx-1 rounded transition-all duration-300
                  ${step > idx ? 'bg-emerald-400' : 'bg-slate-200'}`}></div>
              )}
            </div>
          ))}
        </div>
      </div>
      <div className="flex-1 flex flex-col items-center justify-center w-full max-w-lg mx-auto px-4" style={{ minHeight: 'calc(100vh - 120px)' }}>
        {/* Stepper başlık ve açıklama yukarıda */}
        <div className="w-full flex flex-col items-center mb-2 mt-0">
          <h1 className="text-3xl font-extrabold text-slate-800 mb-1 mt-2 tracking-tight drop-shadow-sm">
            {t(steps[step])}
          </h1>
          <p className="text-slate-500 text-base font-medium mb-2 text-center max-w-md">
            {t(`communities.connect.steps.${['platform','region','language','category','info','bot'][step]}.stepDescription`)}
          </p>
        </div>
        {/* 1. Adım: Topluluk Türü */}
        {step === 0 && (
          <div className="w-full flex flex-col items-center gap-8 mt-2">
            <div className="w-full max-w-md flex flex-col gap-6">
              {communityTypes.filter(type => type.key === "telegram" || type.key === "discord").map((type) => {
                const isDiscord = type.key === 'discord';
                const isDiscordDisabled = isDiscord;
                return (
                  <button
                    key={type.key}
                    className={`flex items-center gap-4 px-6 py-7 rounded-2xl border-2 text-lg font-semibold transition-all text-left select-none shadow-md hover:scale-[1.03] active:scale-100 focus:outline-none focus:ring-2 focus:ring-emerald-300
                      ${communityType === type.key ? 'bg-emerald-50 border-emerald-500 text-emerald-700 shadow-emerald-100 scale-105' : 'bg-white border-slate-200 hover:border-emerald-400 hover:bg-emerald-50'}
                      ${isDiscordDisabled ? 'opacity-60 cursor-not-allowed' : ''}`}
                    onClick={() => !isDiscordDisabled && setCommunityType(type.key)}
                    style={{ boxShadow: communityType === type.key ? '0 4px 24px 0 rgba(16,185,129,0.10)' : '0 2px 8px 0 rgba(100,116,139,0.06)' }}
                    disabled={isDiscordDisabled}
                    type="button"
                  >
                    <span className={`w-12 h-12 flex items-center justify-center rounded-xl text-3xl ${type.key === 'telegram' ? 'bg-[#229ED9]/10 text-[#229ED9]' : 'bg-[#5865F2]/10 text-[#5865F2]'}` }>
                      {communityTypeIcons[type.key]}
                    </span>
                    <div className="flex flex-col">
                      <span className="font-bold text-lg flex items-center gap-2">
                        {type.label}
                        {isDiscord && (
                          <span className="ml-1 text-lg"></span>
                        )}
                      </span>
                      <span className="text-slate-500 text-sm">
                        {type.key === 'telegram' ? 'Kanal ve gruplarınızı tam otomasyonla yönetin ve kazanç elde edin.' : 'Topluluk yönetimi ve roller'}
                      </span>
                      {isDiscord && (
                        <span className="flex items-center gap-1 text-xs text-red-500 font-semibold mt-1">
                          <FaExclamationCircle className="inline mr-1" /> 🇹🇷 Türkiye'de aktif değil
                        </span>
                      )}
                    </div>
                    {communityType === type.key && <span className="ml-auto text-emerald-500 text-2xl"><FaCheckCircle /></span>}
                  </button>
                );
              })}
            </div>
          </div>
        )}
        {/* 2-4. Adımlar: Orta seçim alanı */}
        {((step === 1 && communityType === "telegram") || step === 2 || step === 3) && (
          <div className="w-full flex flex-col items-center justify-center">
            <div className="w-full max-w-xl min-h-[420px] bg-white rounded-3xl border border-slate-100 shadow-lg p-6 flex flex-col gap-4">
              {/* 2. Adım: Bölge (sadece Telegram) */}
              {step === 1 && communityType === "telegram" && (
                <div className="w-full flex flex-col items-start gap-8">
                  <div className="w-full flex flex-col gap-2">
                    <div className="relative mb-2">
                      <input
                        type="text"
                        placeholder={t('communities.connect.steps.region.searchPlaceholder')}
                        className="w-full pl-12 pr-4 py-3 rounded-2xl border-2 border-slate-200 bg-slate-50 focus:border-emerald-400 focus:ring-2 focus:ring-emerald-200 outline-none text-base transition-all"
                        value={regionInput}
                        onChange={e => setRegionInput(e.target.value)}
                      />
                      <span className="absolute left-4 top-1/2 -translate-y-1/2 text-emerald-400 text-2xl">🔍</span>
                    </div>
                    <div className="w-full h-[340px] overflow-y-auto rounded-2xl border border-slate-100 bg-white divide-y divide-slate-50 flex flex-col justify-start shadow-sm">
                      {isLoadingCountries ? (
                        <div className="flex items-center justify-center py-12">
                          <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-emerald-500"></div>
                        </div>
                      ) : countries.length === 0 ? (
                        <div className="flex flex-col items-center justify-center py-10 text-slate-400">
                          <span className="text-3xl mb-2">😕</span>
                          <span>{t('common.noResults')}</span>
                        </div>
                      ) : (
                        countries
                          .filter(country => !regionInput || country.toLowerCase().includes(regionInput.toLowerCase()))
                          .map(country => (
                            <button
                              key={country}
                              className={`w-full flex items-center gap-3 text-left px-4 py-3 hover:bg-emerald-50 transition text-slate-700 text-base rounded-xl ${region === country ? 'bg-emerald-50 text-emerald-700 font-semibold border-emerald-500' : ''}`}
                              onClick={() => setRegion(country)}
                            >
                              <span className="text-xl">{regions.find(r => r.label === country)?.flag || '🌍'}</span>
                              <span>{country}</span>
                              {region === country && <span className="ml-auto text-emerald-500 text-xl"><FaCheckCircle /></span>}
                            </button>
                          ))
                      )}
                    </div>
                  </div>
                </div>
              )}
              {/* 3. Adım: Dil */}
              {((step === 2 && communityType !== "telegram") || (step === 2 && communityType === "telegram")) && (
                <div className="w-full flex flex-col items-start gap-8">
                  <div className="w-full flex flex-col gap-2">
                    <div className="relative mb-2">
                      <input
                        type="text"
                        placeholder={t('communities.connect.steps.language.searchPlaceholder')}
                        className="w-full pl-12 pr-4 py-3 rounded-2xl border-2 border-slate-200 bg-slate-50 focus:border-emerald-400 focus:ring-2 focus:ring-emerald-200 outline-none text-base transition-all"
                        value={languageInput}
                        onChange={e => setLanguageInput(e.target.value)}
                      />
                      <span className="absolute left-4 top-1/2 -translate-y-1/2 text-emerald-400 text-2xl">🔍</span>
                    </div>
                    <div className="w-full h-[340px] overflow-y-auto rounded-2xl border border-slate-100 bg-white divide-y divide-slate-50 flex flex-col justify-start shadow-sm">
                      {isLoadingLanguages ? (
                        <div className="flex items-center justify-center py-12">
                          <div className="animate-spin rounded-full h-10 w-10 border-b-2 border-emerald-500"></div>
                        </div>
                      ) : languages.length === 0 ? (
                        <div className="flex flex-col items-center justify-center py-10 text-slate-400">
                          <span className="text-3xl mb-2">😕</span>
                          <span>{t('common.noResults')}</span>
                        </div>
                      ) : (
                        languages
                          .filter(lang => !languageInput || lang.toLowerCase().includes(languageInput.toLowerCase()))
                          .map(lang => (
                            <button
                              key={lang}
                              type="button"
                              className={`w-full flex items-center gap-3 text-left px-4 py-3 hover:bg-emerald-50 transition text-slate-700 text-base rounded-xl ${selectedLanguages.includes(lang) ? 'bg-emerald-50 text-emerald-700 font-semibold border-emerald-500' : ''}`}
                              onClick={() => selectedLanguages.includes(lang) ? handleRemoveLanguage(lang) : handleAddLanguage(lang)}
                            >
                              <span className="text-xl">{languageFlags[lang] || '🌐'}</span>
                              <span>{lang}</span>
                              {selectedLanguages.includes(lang) && <span className="ml-auto text-emerald-500 text-xl"><FaCheckCircle /></span>}
                            </button>
                          ))
                      )}
                    </div>
                  </div>
                </div>
              )}
              {/* 4. Adım: Kategori */}
              {((step === 3 && communityType !== "telegram") || (step === 3 && communityType === "telegram")) && (
                <div className="w-full flex flex-col items-start gap-8">
                  <div className="w-full flex flex-col gap-2">
                    <div className="relative mb-2">
                      <input
                        type="text"
                        placeholder={t('communities.connect.steps.category.searchPlaceholder')}
                        className="w-full pl-12 pr-4 py-3 rounded-2xl border-2 border-slate-200 bg-slate-50 focus:border-emerald-400 focus:ring-2 focus:ring-emerald-200 outline-none text-base transition-all"
                        value={categoryInput}
                        onChange={e => setCategoryInput(e.target.value)}
                      />
                      <span className="absolute left-4 top-1/2 -translate-y-1/2 text-emerald-400 text-2xl">🔍</span>
                    </div>
                    <div className="w-full h-[340px] overflow-y-auto rounded-2xl border border-slate-100 bg-white divide-y divide-slate-50 flex flex-col justify-start shadow-sm">
                      {filteredCategories.length === 0 && (
                        <div className="flex flex-col items-center justify-center py-10 text-slate-400">
                          <span className="text-3xl mb-2">😕</span>
                          <span>{t('common.noResults')}</span>
                        </div>
                      )}
                      {filteredCategories.map(cat => (
                        <button
                          key={cat}
                          className={`w-full flex items-center gap-3 text-left px-4 py-3 hover:bg-emerald-50 transition text-slate-700 text-base rounded-xl ${category === cat ? 'bg-emerald-50 text-emerald-700 font-semibold border-emerald-500' : ''}`}
                          onClick={() => setCategory(cat)}
                        >
                          <span className="text-xl">{categoryIcons[cat] || '📁'}</span>
                          <span>{cat}</span>
                          {category === cat && <span className="ml-auto text-emerald-500 text-xl"><FaCheckCircle /></span>}
                        </button>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
        {/* 5. Adım: Topluluk Linki */}
        {((step === 4 && communityType !== "telegram") || (step === 4 && communityType === "telegram")) && (
          <div className="w-full flex flex-col items-start gap-8">
            <h2 className="text-2xl font-bold text-slate-800 mb-2">{t('communities.connect.steps.info.stepDescription')}</h2>
            <div className="w-full flex flex-col gap-2">
              {communityType === "telegram" ? (
                // Telegram için mevcut input
              <div
                className={`relative w-full flex items-center rounded-xl bg-white transition-all duration-200 border-2
                  ${communityUsername ? (communityUsername.includes("/") || communityUsername.includes(" "))
                    ? 'border-red-500 shadow-[0_2px_12px_0_rgba(239,68,68,0.10)]'
                    : 'border-emerald-500 shadow-[0_2px_12px_0_rgba(16,185,129,0.10)]'
                    : 'border-slate-300 shadow'}
                  ${communityUsername && !(communityUsername.includes("/") || communityUsername.includes(" ")) ? 'focus-within:border-emerald-600 focus-within:shadow-[0_4px_16px_0_rgba(16,185,129,0.15)]' : 'focus-within:border-emerald-400 focus-within:shadow-md'}
                  hover:border-emerald-200
                `}
              >
                <span className="pl-4 pr-2 flex items-center text-2xl text-[#229ED9]">
                  <SiTelegram />
                </span>
                <span className="text-slate-400 select-none pointer-events-none text-base pr-1">https://t.me/</span>
                <input
                  ref={inputRef}
                  type="text"
                  placeholder={t('communities.connect.steps.info.placeholder')}
                  className="flex-1 py-3 bg-transparent outline-none text-base pl-1 pr-10 rounded-xl min-w-0"
                  value={communityUsername}
                  onChange={e => setCommunityUsername(e.target.value.replace(/\s/g, "").toLowerCase())}
                  style={{fontFamily: 'inherit'}}
                  autoComplete="off"
                  spellCheck={false}
                />
                {/* Kopyala butonu */}
                {communityUsername && !(communityUsername.includes("/") || communityUsername.includes(" ")) && (
                  <button
                    type="button"
                    className="absolute right-10 top-1/2 -translate-y-1/2 text-slate-400 hover:text-emerald-500 transition p-1"
                    aria-label="Kopyala"
                    onClick={() => {
                      navigator.clipboard.writeText(`https://t.me/${communityUsername}`)
                      setCopied(true)
                      setTimeout(() => setCopied(false), 1200)
                    }}
                  >
                    <FaRegClipboard size={18} />
                  </button>
                )}
                {/* Validasyon ikonları */}
                {communityUsername && !(communityUsername.includes("/") || communityUsername.includes(" ")) && (
                  <span className="absolute right-4 top-1/2 -translate-y-1/2 text-emerald-500 animate-fade-in"><FaCheckCircle size={20} /></span>
                )}
                {communityUsername && (communityUsername.includes("/") || communityUsername.includes(" ")) && (
                  <span className="absolute right-4 top-1/2 -translate-y-1/2 text-red-500 animate-fade-in"><FaExclamationCircle size={20} /></span>
                )}
                {/* Kopyalandı mesajı */}
                {copied && (
                  <span className="absolute right-12 top-0 -translate-y-full bg-emerald-500 text-white text-xs font-semibold px-3 py-1 rounded-xl shadow animate-fade-in">{t('communities.connect.steps.info.copied')}</span>
                )}
              </div>
              ) : communityType === "discord" ? (
                // Discord için input
                <div
                  className={`relative w-full flex items-center rounded-xl bg-white transition-all duration-200 border-2
                    ${communityUsername ? (communityUsername.includes("/") || communityUsername.includes(" "))
                      ? 'border-red-500 shadow-[0_2px_12px_0_rgba(239,68,68,0.10)]'
                      : 'border-emerald-500 shadow-[0_2px_12px_0_rgba(16,185,129,0.10)]'
                      : 'border-slate-300 shadow'}
                    ${communityUsername && !(communityUsername.includes("/") || communityUsername.includes(" ")) ? 'focus-within:border-emerald-600 focus-within:shadow-[0_4px_16px_0_rgba(16,185,129,0.15)]' : 'focus-within:border-emerald-400 focus-within:shadow-md'}
                    hover:border-emerald-200
                  `}
                >
                  <span className="pl-4 pr-2 flex items-center text-2xl text-[#5865F2]">
                    <SiDiscord />
                  </span>
                  <span className="text-slate-400 select-none pointer-events-none text-base pr-1">discord.gg/</span>
                  <input
                    ref={inputRef}
                    type="text"
                    placeholder="Sunucu davet kodu"
                    className="flex-1 py-3 bg-transparent outline-none text-base pl-1 pr-10 rounded-xl min-w-0"
                    value={communityUsername}
                    onChange={e => setCommunityUsername(e.target.value.replace(/\s/g, "").toLowerCase())}
                    style={{fontFamily: 'inherit'}}
                    autoComplete="off"
                    spellCheck={false}
                  />
                  {/* Kopyala butonu */}
                  {communityUsername && !(communityUsername.includes("/") || communityUsername.includes(" ")) && (
                    <button
                      type="button"
                      className="absolute right-10 top-1/2 -translate-y-1/2 text-slate-400 hover:text-emerald-500 transition p-1"
                      aria-label="Kopyala"
                      onClick={() => {
                        navigator.clipboard.writeText(`https://discord.gg/${communityUsername}`)
                        setCopied(true)
                        setTimeout(() => setCopied(false), 1200)
                      }}
                    >
                      <FaRegClipboard size={18} />
                    </button>
                  )}
                  {/* Validasyon ikonları */}
                  {communityUsername && !(communityUsername.includes("/") || communityUsername.includes(" ")) && (
                    <span className="absolute right-4 top-1/2 -translate-y-1/2 text-emerald-500 animate-fade-in"><FaCheckCircle size={20} /></span>
                  )}
                  {communityUsername && (communityUsername.includes("/") || communityUsername.includes(" ")) && (
                    <span className="absolute right-4 top-1/2 -translate-y-1/2 text-red-500 animate-fade-in"><FaExclamationCircle size={20} /></span>
                  )}
                  {/* Kopyalandı mesajı */}
                  {copied && (
                    <span className="absolute right-12 top-0 -translate-y-full bg-emerald-500 text-white text-xs font-semibold px-3 py-1 rounded-xl shadow animate-fade-in">{t('communities.connect.steps.info.copied')}</span>
                  )}
                </div>
              ) : communityType === "instagram" ? (
                // Instagram için input
                <div
                  className={`relative w-full flex items-center rounded-xl bg-white transition-all duration-200 border-2
                    ${communityUsername ? (communityUsername.includes("/") || communityUsername.includes(" "))
                      ? 'border-red-500 shadow-[0_2px_12px_0_rgba(239,68,68,0.10)]'
                      : 'border-emerald-500 shadow-[0_2px_12px_0_rgba(16,185,129,0.10)]'
                      : 'border-slate-300 shadow'}
                    ${communityUsername && !(communityUsername.includes("/") || communityUsername.includes(" ")) ? 'focus-within:border-emerald-600 focus-within:shadow-[0_4px_16px_0_rgba(16,185,129,0.15)]' : 'focus-within:border-emerald-400 focus-within:shadow-md'}
                    hover:border-emerald-200
                  `}
                >
                  <span className="pl-4 pr-2 flex items-center text-2xl text-[#E1306C]">
                    <SiInstagram />
                  </span>
                  <span className="text-slate-400 select-none pointer-events-none text-base pr-1">instagram.com/</span>
                  <input
                    ref={inputRef}
                    type="text"
                    placeholder="Kullanıcı adı"
                    className="flex-1 py-3 bg-transparent outline-none text-base pl-1 pr-10 rounded-xl min-w-0"
                    value={communityUsername}
                    onChange={e => setCommunityUsername(e.target.value.replace(/\s/g, "").toLowerCase())}
                    style={{fontFamily: 'inherit'}}
                    autoComplete="off"
                    spellCheck={false}
                  />
                  {/* Kopyala butonu */}
                  {communityUsername && !(communityUsername.includes("/") || communityUsername.includes(" ")) && (
                    <button
                      type="button"
                      className="absolute right-10 top-1/2 -translate-y-1/2 text-slate-400 hover:text-emerald-500 transition p-1"
                      aria-label="Kopyala"
                      onClick={() => {
                        navigator.clipboard.writeText(`https://instagram.com/${communityUsername}`)
                        setCopied(true)
                        setTimeout(() => setCopied(false), 1200)
                      }}
                    >
                      <FaRegClipboard size={18} />
                    </button>
                  )}
                  {/* Validasyon ikonları */}
                  {communityUsername && !(communityUsername.includes("/") || communityUsername.includes(" ")) && (
                    <span className="absolute right-4 top-1/2 -translate-y-1/2 text-emerald-500 animate-fade-in"><FaCheckCircle size={20} /></span>
                  )}
                  {communityUsername && (communityUsername.includes("/") || communityUsername.includes(" ")) && (
                    <span className="absolute right-4 top-1/2 -translate-y-1/2 text-red-500 animate-fade-in"><FaExclamationCircle size={20} /></span>
                  )}
                  {/* Kopyalandı mesajı */}
                  {copied && (
                    <span className="absolute right-12 top-0 -translate-y-full bg-emerald-500 text-white text-xs font-semibold px-3 py-1 rounded-xl shadow animate-fade-in">{t('communities.connect.steps.info.copied')}</span>
                  )}
                </div>
              ) : communityType === "x" ? (
                // X (Twitter) için input
                <div
                  className={`relative w-full flex items-center rounded-xl bg-white transition-all duration-200 border-2
                    ${communityUsername ? (communityUsername.includes("/") || communityUsername.includes(" "))
                      ? 'border-red-500 shadow-[0_2px_12px_0_rgba(239,68,68,0.10)]'
                      : 'border-emerald-500 shadow-[0_2px_12px_0_rgba(16,185,129,0.10)]'
                      : 'border-slate-300 shadow'}
                    ${communityUsername && !(communityUsername.includes("/") || communityUsername.includes(" ")) ? 'focus-within:border-emerald-600 focus-within:shadow-[0_4px_16px_0_rgba(16,185,129,0.15)]' : 'focus-within:border-emerald-400 focus-within:shadow-md'}
                    hover:border-emerald-200
                  `}
                >
                  <span className="pl-4 pr-2 flex items-center text-2xl text-black">
                    <SiX />
                  </span>
                  <span className="text-slate-400 select-none pointer-events-none text-base pr-1">x.com/</span>
                  <input
                    ref={inputRef}
                    type="text"
                    placeholder="Kullanıcı adı"
                    className="flex-1 py-3 bg-transparent outline-none text-base pl-1 pr-10 rounded-xl min-w-0"
                    value={communityUsername}
                    onChange={e => setCommunityUsername(e.target.value.replace(/\s/g, "").toLowerCase())}
                    style={{fontFamily: 'inherit'}}
                    autoComplete="off"
                    spellCheck={false}
                  />
                  {/* Kopyala butonu */}
                  {communityUsername && !(communityUsername.includes("/") || communityUsername.includes(" ")) && (
                    <button
                      type="button"
                      className="absolute right-10 top-1/2 -translate-y-1/2 text-slate-400 hover:text-emerald-500 transition p-1"
                      aria-label="Kopyala"
                      onClick={() => {
                        navigator.clipboard.writeText(`https://x.com/${communityUsername}`)
                        setCopied(true)
                        setTimeout(() => setCopied(false), 1200)
                      }}
                    >
                      <FaRegClipboard size={18} />
                    </button>
                  )}
                  {/* Validasyon ikonları */}
                  {communityUsername && !(communityUsername.includes("/") || communityUsername.includes(" ")) && (
                    <span className="absolute right-4 top-1/2 -translate-y-1/2 text-emerald-500 animate-fade-in"><FaCheckCircle size={20} /></span>
                  )}
                  {communityUsername && (communityUsername.includes("/") || communityUsername.includes(" ")) && (
                    <span className="absolute right-4 top-1/2 -translate-y-1/2 text-red-500 animate-fade-in"><FaExclamationCircle size={20} /></span>
                  )}
                  {/* Kopyalandı mesajı */}
                  {copied && (
                    <span className="absolute right-12 top-0 -translate-y-full bg-emerald-500 text-white text-xs font-semibold px-3 py-1 rounded-xl shadow animate-fade-in">{t('communities.connect.steps.info.copied')}</span>
                  )}
                </div>
              ) : null}
              <span className="text-xs text-slate-400 mt-1">
                {communityType === "telegram" && t('communities.connect.steps.info.hint')} 
                {communityType === "discord" && "Discord sunucu davet kodunuzu girin"} 
                {communityType === "instagram" && "Instagram kullanıcı adınızı girin"} 
                {communityType === "x" && "X (Twitter) kullanıcı adınızı girin"}
              </span>
              <div className={`transition-all duration-200 ${communityUsername && (communityUsername.includes("/") || communityUsername.includes(" ")) ? 'opacity-100 max-h-10' : 'opacity-0 max-h-0'} overflow-hidden`}>
                <span className="text-xs text-red-500 font-medium mt-1 block">
                  {communityType === "telegram" && t('communities.connect.steps.info.error')}
                  {communityType === "discord" && "Geçersiz Discord davet kodu"}
                  {communityType === "instagram" && "Geçersiz Instagram kullanıcı adı"}
                  {communityType === "x" && "Geçersiz X kullanıcı adı"}
                </span>
              </div>
            </div>
          </div>
        )}
        {/* 6. Adım: Telegram Botu */}
        {((step === 5 && communityType === "telegram")) && (
          <div className="w-full flex flex-col items-center gap-8">
            <div className="w-full max-w-xl bg-white rounded-2xl border-2 border-slate-100 shadow-lg overflow-hidden flex flex-col items-center p-0">
              {/* Başlık ve Bot Bilgisi */}
              <div className="flex flex-col items-center w-full p-8 pb-4">
               
               
              
                <div className="bg-blue-50 p-3 rounded-xl flex items-center gap-2 text-blue-700 text-sm w-full max-w-md mx-auto mb-2">
                  <FaExclamationCircle className="text-blue-500" />
                  <span>{t('communities.connect.steps.bot.infoBox')}</span>
                </div>
              </div>
              {/* Minimal Adımlar */}
              <div className="flex flex-row items-center justify-center gap-6 w-full px-6 py-2">
                <div className="flex flex-col items-center">
                  <div className="w-8 h-8 rounded-full bg-[#229ED9] text-white flex items-center justify-center font-bold mb-1">1</div>
                  <span className="text-xs text-slate-600 text-center">{t('communities.connect.steps.bot.step1')}</span>
                </div>
                <div className="h-0.5 w-6 bg-slate-200 rounded-full" />
                <div className="flex flex-col items-center">
                  <div className="w-8 h-8 rounded-full bg-[#229ED9] text-white flex items-center justify-center font-bold mb-1">2</div>
                  <span className="text-xs text-slate-600 text-center">{t('communities.connect.steps.bot.step2')}</span>
                </div>
                <div className="h-0.5 w-6 bg-slate-200 rounded-full" />
                <div className="flex flex-col items-center">
                  <div className="w-8 h-8 rounded-full bg-[#229ED9] text-white flex items-center justify-center font-bold mb-1">3</div>
                  <span className="text-xs text-slate-600 text-center">{t('communities.connect.steps.bot.step3')}</span>
                </div>
              </div>
              {/* Butonlar */}
              <div className="flex flex-col sm:flex-row gap-3 mt-6 w-full px-8 pb-8">
                <a
                  href="https://t.me/AdnomioBot?startgroup=true"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex-1 flex items-center justify-center gap-2 px-6 py-3 rounded-xl bg-[#229ED9] text-white font-semibold hover:bg-[#1a8ec7] transition-all shadow-sm text-base"
                >
                  <SiTelegram size={20} />
                  {t('communities.connect.steps.bot.telegramButton')}
                </a>
                <button
                  type="button"
                  onClick={() => setBotAdded(true)}
                  className={`flex-1 flex items-center justify-center gap-2 px-6 py-3 rounded-xl font-semibold transition-all shadow-sm text-base
                    ${botAdded 
                      ? 'bg-emerald-100 text-emerald-700 border border-emerald-300' 
                      : 'bg-white text-slate-700 border border-slate-200 hover:border-emerald-400 hover:text-emerald-600'
                    }`}
                >
                  {botAdded && <FaCheckCircle className="text-emerald-500" />}
                  {t('communities.connect.steps.bot.addedButton')}
                </button>
              </div>
              {/* Başarı Mesajı */}
              {botAdded && (
                <div className="border-t border-emerald-200 bg-emerald-50 p-4 flex items-center justify-center gap-2 text-emerald-700 font-medium w-full">
                  <FaCheckCircle className="text-emerald-500" />
                  {t('communities.connect.steps.bot.successMessage')}
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Stepper kontrol butonları */}
      <div className="sticky bottom-0 left-0 right-0 bg-white border-t border-slate-200 p-4">
        <div className="flex justify-between w-full max-w-lg mx-auto">
        <div className="flex items-center gap-2">
          <button
            className="w-10 h-10 flex items-center justify-center rounded-full bg-slate-100 text-slate-500 font-bold text-xl shadow hover:bg-red-100 hover:text-red-500 transition-all mr-2"
            onClick={() => setShowExitModal(true)}
            type="button"
          >
            ×
          </button>
          <button
            className="px-8 py-3 rounded-xl bg-slate-100 text-slate-500 font-semibold disabled:opacity-50 shadow"
            onClick={prevStep}
            disabled={step === 0}
          >
            {t('communities.common.back')}
          </button>
        </div>
        <button
          className="px-8 py-3 rounded-xl bg-emerald-500 text-white font-bold hover:bg-emerald-600 transition-all disabled:opacity-50 shadow-lg"
            onClick={isLastStep() ? handleSubmit : nextStep}
            disabled={isNextDisabled() || submitLoading || (isLastStep() && submitSuccess)}
          >
            {isLastStep() 
              ? (submitLoading 
                  ? t('communities.common.saving') 
                  : submitSuccess 
                    ? t('communities.common.success') 
                    : t('communities.common.save')
                ) 
              : t('communities.common.continue')
            }
        </button>
        </div>
      </div>

      {/* Çıkış Modalı */}
      <Dialog open={showExitModal} onOpenChange={setShowExitModal}>
        <DialogContent>
          <DialogHeader >
            <DialogTitle >{t('communities.onboarding.exitModal.title')}</DialogTitle>
            <DialogDescription>
              {t('communities.onboarding.exitModal.description')}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <button
              className="px-6 py-2 mt-5 rounded-lg bg-slate-100 text-slate-600 font-semibold hover:bg-slate-200 transition"
              onClick={() => setShowExitModal(false)}
            >
              {t('communities.common.cancel')}
            </button>
            <button
              className="px-6 py-2 mt-5 rounded-lg bg-green-500 text-white font-bold hover:bg-green-600 transition"
              onClick={() => { setShowExitModal(false); router.push("/publisher"); }}
            >
              {t('communities.common.yes')}
            </button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      {/* Hata veya başarı mesajı */}
      {submitError && <div className="text-red-500 text-center font-semibold mb-4">{submitError}</div>}
      {submitSuccess && <div className="text-green-600 text-center font-semibold mb-4">{t('communities.common.success')}</div>}
    </div>
  )
}