"use client";

import React, { useState, useEffect } from 'react';
import { DashboardLayout } from "@/app/publisher/components/layout/dashboard-layout";
import { 
  Wallet,
  ArrowRight,
  ArrowDown,
  Calendar,
  Check,
  CircleDollarSign,
  Plus,
  CreditCard,
  ArrowUpDown,
  Search
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Link from 'next/link';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { useLanguage } from '@/shared/hooks/useLanguage';
import { getBalanceAndEarnings, type BalanceAndEarningsResponse } from '@/shared/services/publisher/balance-and-earnings';
import { getTransactionHistory, type TransactionHistoryItem, type TransactionStatus, type TransactionHistoryRequest } from '@/shared/services/publisher/transaction-history';

interface Transaction {
  id: string;
  type: 'earning' | 'withdrawal';
  amount: number;
  status: 'completed' | 'pending' | 'failed';
  date: string;
  description: string;
  method?: string;
}

interface PaymentMethod {
  id: string;
  type: 'bank' | 'card';
  name: string;
  last4: string;
  isPrimary: boolean;
  icon: React.ReactNode;
}

export default function PaymentsPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<TransactionStatus | 'all'>('all');
  const [dateRange, setDateRange] = useState<string>('all');
  const { t } = useLanguage();
  const [balanceData, setBalanceData] = useState<BalanceAndEarningsResponse['result'] | null>(null);
  const [balanceLoading, setBalanceLoading] = useState(true);
  const [balanceError, setBalanceError] = useState<string | null>(null);
  const [transactions, setTransactions] = useState<TransactionHistoryItem[]>([]);
  const [transactionsLoading, setTransactionsLoading] = useState(true);
  const [transactionsError, setTransactionsError] = useState<string | null>(null);
  const [totalTransactions, setTotalTransactions] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const pageSize = 10;

  // Örnek ödeme metodları
  const paymentMethods: PaymentMethod[] = [
    {
      id: '1',
      type: 'bank',
      name: 'Ziraat Bankası',
      last4: '1234',
      isPrimary: true,
      icon: <CreditCard className="h-4 w-4 text-green-500" />
    },
    {
      id: '2',
      type: 'card',
      name: 'Papara',
      last4: '5678',
      isPrimary: false,
      icon: <CreditCard className="h-4 w-4 text-purple-500" />
    }
  ];
  
  useEffect(() => {
    const fetchBalance = async () => {
      try {
        setBalanceLoading(true);
        const data = await getBalanceAndEarnings();
        setBalanceData(data);
        setBalanceError(null);
      } catch (err) {
        setBalanceError('Bakiye ve kazançlar alınırken hata oluştu');
      } finally {
        setBalanceLoading(false);
      }
    };
    fetchBalance();
  }, []);

  useEffect(() => {
    const fetchTransactions = async () => {
      try {
        setTransactionsLoading(true);
        const params: TransactionHistoryRequest = {
          limit: pageSize,
          skip: (currentPage - 1) * pageSize,
          ...(statusFilter !== 'all' && { status: statusFilter }),
          ...(searchQuery && { search: searchQuery })
        };
        const result = await getTransactionHistory(params);
        setTransactions(result.data);
        setTotalTransactions(result.stats.total);
        setTransactionsError(null);
      } catch (err) {
        setTransactionsError('İşlem geçmişi alınırken hata oluştu');
      } finally {
        setTransactionsLoading(false);
      }
    };
    fetchTransactions();
  }, [statusFilter, searchQuery, currentPage]);
  
  // İstatistik hesaplamaları (pendingWithdrawals hariç hepsi backend'den)
  const stats = {
    currentBalance: balanceData?.balance ?? 0,
    monthlyEarnings: balanceData?.monthly_earnings ?? 0,
    totalEarnings: balanceData?.total_earnings ?? 0,
    pendingWithdrawals: transactions
      .filter(t => t.status === 'PENDING')
      .reduce((sum, t) => sum + t.amount, 0)
  };
  
  // Para çekme durumu
  const minimumWithdrawalAmount = 100;
  const withdrawalAvailable = stats.currentBalance >= minimumWithdrawalAmount;
  const withdrawalProgress = (stats.currentBalance / minimumWithdrawalAmount) * 100;

  // Pagination butonları
  const totalPages = Math.ceil(totalTransactions / pageSize);

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Üst başlık kısmı */}
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
          <div>
            <h1 className="text-2xl sm:text-3xl font-bold text-slate-800 flex items-center gap-2">
              <Wallet className="h-7 w-7 text-purple-500" />
              <span>{t('payments.title')}</span>
            </h1>
            <p className="text-slate-500 mt-1">{t('payments.subtitle')}</p>
          </div>
          
          <Link href="/publisher/payments/withdraw" className="w-full sm:w-auto">
            <Button 
              className={`${
                withdrawalAvailable 
                  ? 'bg-purple-500 hover:bg-purple-600 text-white' 
                  : 'bg-slate-200 hover:bg-slate-300 text-slate-500 cursor-not-allowed'
              } rounded-lg shadow-sm hover:shadow transition-all duration-300 w-full sm:w-auto`}
              disabled={!withdrawalAvailable}
            >
              <CircleDollarSign className="h-4 w-4 mr-2" />
              {t('payments.withdraw.title')}
            </Button>
          </Link>
        </div>
        
        {/* Ana Kart / Bakiye */}
        <Card className="border-purple-100 bg-gradient-to-br from-purple-50 to-indigo-50 shadow-sm overflow-hidden">
          <CardContent className="p-4 sm:p-6 relative">
            <div className="absolute top-0 right-0 w-64 h-64 bg-purple-100/30 rounded-full -mt-16 -mr-16 hidden sm:block"></div>
            <div className="absolute bottom-0 left-0 w-32 h-32 bg-purple-100/30 rounded-full -mb-10 -ml-10 hidden sm:block"></div>
            
            <div className="grid gap-6 md:grid-cols-2">
              <div className="space-y-4">
                <div>
                  <h2 className="text-slate-600 text-sm">{t('payments.balance.current')}</h2>
                  <div className="text-2xl sm:text-4xl font-bold text-slate-800 flex flex-wrap items-center gap-2">
                    {balanceLoading ? (
                      <span className="animate-pulse bg-slate-100 rounded w-32 h-8 inline-block"></span>
                    ) : balanceError ? (
                      <span className="text-red-500 text-base">{balanceError}</span>
                    ) : (
                      <>
                        <span>₺ {stats.currentBalance.toFixed(2)}</span>
                        <Badge className="bg-indigo-100 text-indigo-700 h-auto text-xs font-normal">
                          {t('payments.balance.available')}
                        </Badge>
                      </>
                    )}
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4 pt-2">
                  <div>
                    <p className="text-xs text-slate-600">{t('payments.balance.monthly')}</p>
                    <div className="text-lg sm:text-xl font-medium text-slate-800">
                      {balanceLoading ? (
                        <span className="animate-pulse bg-slate-100 rounded w-20 h-6 inline-block"></span>
                      ) : balanceError ? (
                        <span className="text-red-500 text-base">{balanceError}</span>
                      ) : (
                        <>₺ {stats.monthlyEarnings.toFixed(2)}</>
                      )}
                    </div>
                  </div>
                  <div>
                    <p className="text-xs text-slate-600">{t('payments.balance.total')}</p>
                    <div className="text-lg sm:text-xl font-medium text-slate-800">
                      {balanceLoading ? (
                        <span className="animate-pulse bg-slate-100 rounded w-20 h-6 inline-block"></span>
                      ) : balanceError ? (
                        <span className="text-red-500 text-base">{balanceError}</span>
                      ) : (
                        <>₺ {stats.totalEarnings.toFixed(2)}</>
                      )}
                    </div>
                  </div>
                </div>
                
                <div className="flex flex-col sm:flex-row gap-3">
                  <Link href="/publisher/payments/withdraw" className="w-full sm:w-auto">
                    <Button className="bg-purple-500 text-white hover:bg-purple-600 w-full sm:w-auto">
                      <CircleDollarSign className="h-4 w-4 mr-2" />
                      {t('payments.withdraw.title')}
                    </Button>
                  </Link>
                  <Link href="/publisher/payments/methods" className="w-full sm:w-auto">
                    <Button variant="outline" className="border-purple-200 text-purple-600 hover:bg-purple-50 hover:text-purple-700 w-full sm:w-auto">
                      <CreditCard className="h-4 w-4 mr-2" />
                      {t('payments.methods.title')}
                    </Button>
                  </Link>
                </div>
              </div>
              
              <div className="space-y-4">
                <h3 className="text-slate-800 font-medium">{t('payments.withdraw.status.title')}</h3>
                
                {withdrawalAvailable ? (
                  <div className="bg-green-50 border border-green-100 p-4 rounded-lg">
                    <div className="flex items-center gap-2 mb-3">
                      <Check className="h-5 w-5 text-green-500" />
                      <p className="text-slate-800 font-medium">{t('payments.withdraw.status.available')}</p>
                    </div>
                    <p className="text-sm text-slate-600 mb-3">
                      {t('payments.withdraw.status.availableDesc')}
                    </p>
                    <Link href="/publisher/payments/withdraw">
                      <Button className="bg-green-500 hover:bg-green-600 text-white w-full">
                        {t('payments.withdraw.status.withdrawNow')}
                      </Button>
                    </Link>
                  </div>
                ) : (
                  <div className="bg-blue-50 border border-blue-100 p-4 rounded-lg">
                    <p className="text-slate-800 font-medium mb-2">
                      ₺{stats.currentBalance.toFixed(2)} / ₺{minimumWithdrawalAmount}
                    </p>
                    <Progress value={withdrawalProgress} className="h-2 bg-slate-200" />
                    <p className="text-sm text-slate-600 mt-3">
                      {t('payments.withdraw.form.minimumRequired', {
                        minimum: minimumWithdrawalAmount,
                        remaining: (minimumWithdrawalAmount - stats.currentBalance).toFixed(2)
                      })}
                    </p>
                  </div>
                )}
                
                {stats.pendingWithdrawals > 0 && (
                  <div className="bg-amber-50 p-3 rounded-lg border border-amber-100">
                    <p className="text-amber-700 text-sm flex items-start gap-2">
                      <span className="mt-0.5"><Calendar className="h-4 w-4" /></span>
                      <span>
                        <span className="font-medium">{t('payments.withdraw.status.pending')}</span> 
                        <br />
                        {t('payments.withdraw.status.pendingAmount', {
                          amount: stats.pendingWithdrawals.toFixed(2)
                        })}
                      </span>
                    </p>
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
        
        {/* Ödeme Metodları */}
        <div className="grid gap-6 grid-cols-1 lg:grid-cols-3">
          <div className="lg:col-span-1 space-y-6">
            <Card className="bg-white shadow-sm">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg text-slate-800">{t('payments.methods.title')}</CardTitle>
                <CardDescription className="text-slate-500">{t('payments.methods.subtitle')}</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {paymentMethods.map(method => (
                  <div 
                    key={method.id}
                    className={`p-3 rounded-lg ${method.isPrimary ? 'bg-purple-50 border border-purple-100' : 'bg-white border border-slate-200 hover:bg-slate-50'} transition-colors duration-200`}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 rounded-full bg-white border border-slate-200 flex items-center justify-center">
                          {method.icon}
                        </div>
                        <div>
                          <h3 className="text-slate-800 font-medium text-sm">{method.name}</h3>
                          <p className="text-slate-500 text-xs">**** {method.last4}</p>
                        </div>
                      </div>
                      
                      {method.isPrimary && (
                        <Badge className="bg-purple-100 text-purple-600 text-[10px] h-5">
                          {t('payments.methods.primary')}
                        </Badge>
                      )}
                    </div>
                    
                    <div className="flex justify-end gap-2">
                      <Button variant="ghost" size="sm" className="h-7 text-xs text-slate-500 hover:text-slate-700">
                        {t('communities.common.edit')}
                      </Button>
                      <Button variant="ghost" size="sm" className="h-7 text-xs text-red-500 hover:text-red-600">
                      {t('communities.common.delete')}
                      </Button>
                    </div>
                  </div>
                ))}
                
                <Link href="/publisher/payments/methods/add">
                  <Button variant="outline" className="w-full border-dashed border-slate-300 bg-transparent hover:bg-slate-50 hover:border-slate-400 text-slate-500 hover:text-slate-700 h-16">
                    <Plus className="h-4 w-4 mr-2" />
                    {t('payments.methods.addNew')}
                  </Button>
                </Link>
              </CardContent>
            </Card>
            

          </div>
          
          {/* İşlem Geçmişi */}
          <div className="lg:col-span-2 space-y-6">
            <Card className="bg-white shadow-sm">
              <CardHeader className="pb-3">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                  <div>
                    <CardTitle className="text-lg text-slate-800">{t('payments.history.title')}</CardTitle>
                    <CardDescription className="text-slate-500">{t('payments.history.subtitle')}</CardDescription>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Filtreler */}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-3 w-3 text-slate-400" />
                    <Input 
                      placeholder={t('payments.history.filters.search')}
                      className="pl-8 h-8 text-xs bg-slate-50 border-slate-200 text-slate-700 placeholder:text-slate-400"
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                    />
                  </div>
                  <Select value={statusFilter} onValueChange={v => setStatusFilter(v as TransactionStatus | 'all')}>
                    <SelectTrigger className="h-8 text-xs bg-slate-50 border-slate-200 text-slate-700">
                      <SelectValue placeholder="Durum" />
                    </SelectTrigger>
                    <SelectContent className="bg-white border-slate-200 text-slate-700">
                      <SelectItem value="all">Tüm Durumlar</SelectItem>
                      <SelectItem value="PENDING">Bekliyor</SelectItem>
                      <SelectItem value="PROCESSING">İşleniyor</SelectItem>
                      <SelectItem value="COMPLETED">Tamamlandı</SelectItem>
                      <SelectItem value="REJECTED">Reddedildi</SelectItem>
                      <SelectItem value="CANCELED">İptal Edildi</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                {/* İşlem Listesi */}
                <div className="-mx-4 sm:mx-0 overflow-x-auto">
                  <div className="inline-block min-w-full align-middle">
                    <table className="min-w-full divide-y divide-slate-200">
                      <thead>
                        <tr className="border-b border-slate-200">
                          <th className="px-3 sm:px-4 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                            {t('payments.history.table.transaction')}
                          </th>
                          <th className="px-3 sm:px-4 py-3 text-left text-xs font-medium text-slate-500 uppercase tracking-wider">
                            {t('payments.history.table.description')}
                          </th>
                          <th className="px-3 sm:px-4 py-3 text-right text-xs font-medium text-slate-500 uppercase tracking-wider hidden sm:table-cell">
                            <div className="flex items-center justify-end">
                              <span>{t('payments.history.table.date')}</span>
                              <ArrowUpDown className="ml-1 h-3 w-3" />
                            </div>
                          </th>
                          <th className="px-3 sm:px-4 py-3 text-right text-xs font-medium text-slate-500 uppercase tracking-wider">
                            {t('payments.history.table.amount')}
                          </th>
                          <th className="px-3 sm:px-4 py-3 text-center text-xs font-medium text-slate-500 uppercase tracking-wider">
                            {t('payments.history.table.status')}
                          </th>
                        </tr>
                      </thead>
                      <tbody className="divide-y divide-slate-200">
                        {transactionsLoading ? (
                          <tr>
                            <td colSpan={5} className="px-3 sm:px-4 py-6 text-center text-slate-500">
                              Yükleniyor...
                            </td>
                          </tr>
                        ) : transactionsError ? (
                          <tr>
                            <td colSpan={5} className="px-3 sm:px-4 py-6 text-center text-red-500">
                              {transactionsError}
                            </td>
                          </tr>
                        ) : transactions.length === 0 ? (
                          <tr>
                            <td colSpan={5} className="px-3 sm:px-4 py-6 text-center text-slate-500">
                              {t('payments.history.table.noResults')}
                            </td>
                          </tr>
                        ) : (
                          transactions.map((transaction) => (
                            <tr key={transaction._id} className="hover:bg-slate-50">
                              <td className="px-3 sm:px-4 py-3 whitespace-nowrap">
                                {transaction.payment_method === 'BANK_TRANSFER' ? (
                                  <ArrowDown className="h-3 w-3 mr-1 text-green-600" />
                                ) : (
                                  <ArrowRight className="h-3 w-3 mr-1 text-slate-700" />
                                )}
                              </td>
                              <td className="px-3 sm:px-4 py-3">
                                <div className="max-w-xs sm:max-w-none">
                                  <p className="text-sm text-slate-700 truncate">{transaction.description || '-'}</p>
                                  {transaction.bank_name && (
                                    <p className="text-xs text-slate-500 truncate">{transaction.bank_name} {transaction.iban ? `(${transaction.iban})` : ''}</p>
                                  )}
                                </div>
                              </td>
                              <td className="px-3 sm:px-4 py-3 text-sm text-right text-slate-500 hidden sm:table-cell whitespace-nowrap">
                                {transaction.created_at ? new Date(transaction.created_at).toLocaleDateString('tr-TR', { day: '2-digit', month: '2-digit', year: 'numeric' }) : '-'}
                              </td>
                              <td className="px-3 sm:px-4 py-3 text-sm text-right font-medium text-green-600 whitespace-nowrap">
                                ₺{transaction.amount.toFixed(2)}
                              </td>
                              <td className="px-3 sm:px-4 py-3 text-sm text-center">
                                <Badge className={`
                                  ${transaction.status === 'COMPLETED' ? 'bg-green-500' : 
                                    transaction.status === 'PENDING' ? 'bg-amber-500' : 
                                    transaction.status === 'PROCESSING' ? 'bg-blue-500' : 
                                    transaction.status === 'REJECTED' ? 'bg-red-500' : 
                                    'bg-gray-400'} text-white text-xs whitespace-nowrap`}>
                                  {transaction.status === 'COMPLETED' ? 'Tamamlandı' :
                                    transaction.status === 'PENDING' ? 'Bekliyor' :
                                    transaction.status === 'PROCESSING' ? 'İşleniyor' :
                                    transaction.status === 'REJECTED' ? 'Reddedildi' :
                                    'İptal Edildi'}
                                </Badge>
                              </td>
                            </tr>
                          ))
                        )}
                      </tbody>
                    </table>
                  </div>
                </div>
                
                {/* Pagination */}
                <div className="flex flex-col sm:flex-row justify-between items-center gap-4 pt-2 text-sm text-slate-500">
                  <div className="order-2 sm:order-1">
                    {t('payments.history.table.totalCount', { count: totalTransactions })}
                  </div>
                  
                  <div className="flex gap-1 order-1 sm:order-2">
                    {Array.from({ length: totalPages }, (_, i) => (
                      <Button
                        key={i + 1}
                        className={`h-8 w-8 p-0 ${currentPage === i + 1 ? 'bg-purple-500 hover:bg-purple-600 text-white' : 'text-slate-500 hover:text-slate-700 hover:bg-slate-100'}`}
                        variant={currentPage === i + 1 ? 'default' : 'ghost'}
                        onClick={() => setCurrentPage(i + 1)}
                      >
                        {i + 1}
                      </Button>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
} 