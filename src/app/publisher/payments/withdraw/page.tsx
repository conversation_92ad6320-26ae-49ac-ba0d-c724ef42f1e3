"use client";

import React, { useState, useEffect } from 'react';
import { DashboardLayout } from "@/app/publisher/components/layout/dashboard-layout";
import { 
  Wallet,
  ArrowLeft,
  CircleDollarSign,
  CreditCard,
  Info,
  AlertTriangle,
  Check,
  ChevronRight,
  Plus
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Link from 'next/link';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Separator } from '@/components/ui/separator';
import { useLanguage } from '@/shared/hooks/useLanguage';
import { getEarningsDetails, type EarningsDetailsResponse } from '@/shared/services/publisher/balance-and-earnings';
import { getPaymentMethodList, type PaymentMethodItem, type PaymentMethodListResponse } from '@/shared/services/publisher/payment-methods';
import { requestWithdrawal, type WithdrawalRequest, type WithdrawalResponse } from '@/shared/services/publisher/transaction-history';
import { toast } from '@/components/ui/use-toast';
import { useRouter } from 'next/navigation';

interface PaymentMethod {
  id: string;
  type: 'bank' | 'card';
  name: string;
  last4: string;
  isPrimary: boolean;
  icon: React.ReactNode;
  iban?: string;
  accountHolderName?: string;
  accountType?: string;
  status?: string;
}

export default function WithdrawPage() {
  const [selectedMethod, setSelectedMethod] = useState<string>('');
  const [amount, setAmount] = useState<string>('');
  const [withdrawalStep, setWithdrawalStep] = useState<number>(1);
  const [verificationCode, setVerificationCode] = useState<string>('');
  const { t } = useLanguage();
  const router = useRouter();
  
  const [currentEarnings, setCurrentEarnings] = useState<number | null>(null);
  const [loadingDetails, setLoadingDetails] = useState(true);
  const [detailsError, setDetailsError] = useState<string | null>(null);
  
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [loadingMethods, setLoadingMethods] = useState(true);
  const [methodsError, setMethodsError] = useState<string | null>(null);
  
  const [isRequestingWithdrawal, setIsRequestingWithdrawal] = useState(false);
  const [withdrawalError, setWithdrawalError] = useState<string | null>(null);
  const [withdrawalSuccessData, setWithdrawalSuccessData] = useState<WithdrawalResponse['result'] | null>(null);
  
  const getSelectedMethod = () => {
    return paymentMethods.find(method => method.id === selectedMethod);
  };
  
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoadingDetails(true);
        const detailsResult = await getEarningsDetails();
        setCurrentEarnings(detailsResult.earnings);
        setDetailsError(null);
      } catch (err) {
        console.error('Failed to fetch earnings details:', err);
        setDetailsError('Kazanç detayları yüklenemedi.');
      } finally {
        setLoadingDetails(false);
      }
      
      try {
        setLoadingMethods(true);
        const paymentItems = await getPaymentMethodList();

        const transformedMethods: PaymentMethod[] = paymentItems.map((item: PaymentMethodItem) => ({
          id: item.payment_method_id, 
          type: item.payment_method === 'BANK_TRANSFER' ? 'bank' : 'card', 
          name: item.bank_name || item.account_holder_name || (item.payment_method === 'BANK_TRANSFER' ? 'Banka Hesabı' : 'Ödeme Yöntemi'),
          last4: item.iban ? item.iban.slice(-4) : '',
          isPrimary: false,
          icon: item.payment_method === 'BANK_TRANSFER' ? <CreditCard className="h-4 w-4 text-green-500" /> : <Wallet className="h-4 w-4 text-purple-500" />,
          iban: item.iban,
          accountHolderName: item.account_holder_name,
          accountType: item.account_type,
          status: item.status
        }));
        setPaymentMethods(transformedMethods);
        setMethodsError(null);
        if (transformedMethods.length > 0) {
          setSelectedMethod(transformedMethods[0].id);
        }
      } catch (err) {
        console.error('Failed to fetch payment methods:', err);
        setMethodsError('Ödeme yöntemleri yüklenemedi.');
      } finally {
        setLoadingMethods(false);
      }
    };
    
    fetchData();
  }, []);
  
  const MINIMUM_WITHDRAWAL_AMOUNT_HARDCODED = 50;

  const canWithdraw = () => {
    const numAmount = parseFloat(amount);
    if (currentEarnings === null || !selectedMethod) return false;
    
    return (
      numAmount > 0 &&
      numAmount >= MINIMUM_WITHDRAWAL_AMOUNT_HARDCODED &&
      numAmount <= currentEarnings
    );
  };
  
  const getAmountError = () => {
    const numAmount = parseFloat(amount);
    if (currentEarnings === null) return '';
    
    if (!amount || isNaN(numAmount) || numAmount <= 0) {
      return 'payments.withdraw.errors.enterAmount';
    }
    
    if (numAmount < MINIMUM_WITHDRAWAL_AMOUNT_HARDCODED) {
      return 'payments.withdraw.errors.minimumAmount';
    }
    
    if (numAmount > currentEarnings) {
      return 'payments.withdraw.errors.insufficientBalance';
    }
    
    return '';
  };
  
  const withdrawAll = () => {
    if (currentEarnings !== null) {
      setAmount(currentEarnings.toString());
    }
  };

  const handleSubmitWithdrawal = async () => {
    if (getAmountError() || !selectedMethod) {
      toast({
        title: t('common.error'),
        description: t('payments.withdraw.errors.validation'),
        variant: "destructive",
      });
      return;
    }

    setIsRequestingWithdrawal(true);
    setWithdrawalError(null);
    setWithdrawalSuccessData(null);

    const withdrawalRequest: WithdrawalRequest = {
      amount: parseFloat(amount),
      payment_method_id: selectedMethod,
    };

    try {
      const result = await requestWithdrawal(withdrawalRequest);
      setWithdrawalSuccessData(result);
      setWithdrawalStep(2);
      toast({
        title: t('payments.withdraw.success.title'),
        description: t('payments.withdraw.success.description', { amount: parseFloat(amount).toFixed(2) }),
      });
    } catch (err) {
      console.error('Withdrawal request failed:', err);
      setWithdrawalError((err instanceof Error ? err.message : String(err)) || 'Para çekme talebi oluşturulurken bir hata oluştu.');
      toast({
        title: t('common.error'),
        description: err instanceof Error ? err.message : String(err) || t('payments.withdraw.errors.requestFailed'),
        variant: "destructive",
      });
    } finally {
      setIsRequestingWithdrawal(false);
    }
  };

  return (
    <DashboardLayout>
      <div className="space-y-6 max-w-3xl mx-auto">
        <div className="flex flex-col gap-4">
          <div className="flex items-center gap-2">
            <Link href="/publisher/payments">
              <Button variant="ghost" size="icon" className="h-8 w-8 text-slate-500 hover:text-slate-700">
                <ArrowLeft className="h-4 w-4" />
              </Button>
            </Link>
            <h1 className="text-2xl md:text-3xl font-bold text-slate-800 flex items-center gap-2">
              <Wallet className="h-7 w-7 text-purple-500" />
              <span>{t('payments.withdraw.title')}</span>
            </h1>
          </div>
          <p className="text-slate-500">{t('payments.withdraw.subtitle')}</p>
        </div>
        
        {withdrawalStep === 1 && (
          <>
            <Card className="border-purple-100 bg-gradient-to-br from-purple-50 to-indigo-50 shadow-sm">
              <CardContent className="p-6">
                <div className="grid gap-6 md:grid-cols-2">
                  <div className="space-y-3">
                    <div>
                      <h2 className="text-slate-600 text-sm">{t('payments.withdraw.availableBalance')}</h2>
                      {loadingDetails ? (
                        <span className="animate-pulse bg-slate-100 rounded w-32 h-8 inline-block"></span>
                      ) : detailsError ? (
                        <span className="text-red-500 text-base">{detailsError}</span>
                      ) : (
                        <p className="text-3xl font-bold text-slate-800">
                          {currentEarnings !== null
                            ? `₺ ${currentEarnings.toFixed(2)}`
                            : '₺ -'}
                        </p>
                      )}
                    </div>
                    
                    <div className="text-sm">
                      <div className="flex justify-between">
                        <span className="text-slate-500">{t('payments.withdraw.fee')}:</span>
                        <span className="text-slate-800 font-medium">₺0.00</span>
                      </div>
                      <div className="flex justify-between mt-1">
                        <span className="text-slate-500">{t('payments.withdraw.processingTime')}:</span>
                        <span className="text-slate-800 font-medium">{t('payments.withdraw.processingDays')}</span>
                      </div>
                    </div>
                    
                    <Alert className="bg-blue-50 border-blue-100 text-blue-700">
                      <Info className="h-4 w-4" />
                      <AlertDescription className="text-blue-700 text-sm">
                        {t('payments.withdraw.processingInfo')}
                      </AlertDescription>
                    </Alert>
                  </div>
                  
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <CircleDollarSign className="h-5 w-5 text-purple-500" />
                      <h3 className="text-slate-700 font-medium">{t('payments.withdraw.paymentInfo')}</h3>
                    </div>
                    
                    <div className="text-sm">
                      <div className="flex justify-between">
                        <span className="text-slate-500">{t('payments.withdraw.fee')}:</span>
                        <span className="text-slate-800 font-medium">₺0.00</span>
                      </div>
                      <div className="flex justify-between mt-1">
                        <span className="text-slate-500">{t('payments.withdraw.processingTime')}:</span>
                        <span className="text-slate-800 font-medium">{t('payments.withdraw.processingDays')}</span>
                      </div>
                    </div>
                    
                    <Alert className="bg-blue-50 border-blue-100 text-blue-700">
                      <Info className="h-4 w-4" />
                      <AlertDescription className="text-blue-700 text-sm">
                        {t('payments.withdraw.processingInfo')}
                      </AlertDescription>
                    </Alert>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card className="bg-white shadow-sm">
              <CardHeader>
                <CardTitle className="text-lg text-slate-800">{t('payments.withdraw.form.title')}</CardTitle>
                <CardDescription className="text-slate-500">
                  {t('payments.withdraw.form.description')}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <Label htmlFor="amount" className="text-sm text-slate-700">{t('payments.withdraw.form.amount')}</Label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 flex items-center pl-3">
                      <span className="text-slate-500">₺</span>
                    </div>
                    <Input 
                      id="amount"
                      placeholder="0.00"
                      type="number"
                      className="pl-8 bg-slate-50 border-slate-200 text-slate-700"
                      value={amount}
                      onChange={(e) => setAmount(e.target.value)}
                    />
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      className="absolute right-2 top-1/2 transform -translate-y-1/2 h-7 text-xs text-purple-600 hover:text-purple-700"
                      onClick={withdrawAll}
                    >
                      {t('payments.withdraw.form.withdrawAll')}
                    </Button>
                  </div>
                  
                  {getAmountError() && getAmountError() !== '' && (
                    <p className="text-xs text-red-500 font-medium mt-1">{t(getAmountError())}</p>
                  )}
                  
                  <p className="text-xs text-slate-500">
                    {t('payments.withdraw.form.minimumAmountInfo', { minimum: MINIMUM_WITHDRAWAL_AMOUNT_HARDCODED.toFixed(2) })}
                  </p>
                </div>
                
                <div className="space-y-2">
                  <Label className="text-sm text-slate-700">{t('payments.withdraw.form.paymentMethod')}</Label>
                  <RadioGroup value={selectedMethod} onValueChange={setSelectedMethod} className="grid gap-4">
                    {loadingMethods ? (
                      <div className="text-center text-slate-500 text-sm">{t('common.loading')}</div>
                    ) : methodsError ? (
                      <div className="text-center text-red-500 text-sm">{methodsError}</div>
                    ) : paymentMethods.length === 0 ? (
                      <div className="text-center text-slate-500 text-sm">{t('payments.methods.noMethods')}</div>
                    ) : (
                      paymentMethods.map((method) => (
                        <div 
                          key={method.id}
                          className={`flex items-center border rounded-lg p-3 cursor-pointer transition-colors ${
                            selectedMethod === method.id
                              ? 'border-purple-200 bg-purple-50'
                              : 'border-slate-200 bg-slate-50 hover:bg-slate-100'
                          }`}
                        >
                          <RadioGroupItem id={method.id} value={method.id} className="sr-only" />
                          <Label htmlFor={method.id} className="flex items-center gap-3 cursor-pointer flex-1">
                            <div className="w-8 h-8 rounded-full bg-white border border-slate-200 flex items-center justify-center">
                              {method.icon}
                            </div>
                            <div>
                              <p className="text-sm font-medium text-slate-800">{method.name}</p>
                              {method.type === 'bank' && method.iban && <p className="text-xs text-slate-500">IBAN: {method.iban}</p>}
                              {method.type === 'card' && method.last4 && <p className="text-xs text-slate-500">**** {method.last4}</p>}
                            </div>
                          </Label>
                        </div>
                      ))
                    )}
                  </RadioGroup>
                  
                  <Link href="/publisher/payments/methods/add" className="text-xs text-purple-600 hover:text-purple-700 flex items-center gap-1">
                    <Plus className="h-3 w-3" />
                    <span>{t('payments.methods.addNew')}</span>
                  </Link>
                </div>
              </CardContent>
              <CardFooter className="flex justify-end gap-3">
                <Button variant="outline" onClick={() => router.push('/publisher/payments')}>{t('common.cancel')}</Button>
                <Button 
                  className="bg-purple-500 hover:bg-purple-600 text-white"
                  disabled={!canWithdraw() || !selectedMethod || isRequestingWithdrawal}
                  onClick={handleSubmitWithdrawal}
                >
                  {isRequestingWithdrawal ? t('common.saving') : t('payments.withdraw.form.startWithdrawal')}
                </Button>
              </CardFooter>
            </Card>
          </>
        )}
        
        {withdrawalStep === 2 && (
          <Card className="bg-white shadow-sm">
            <CardHeader>
              <CardTitle className="text-lg text-slate-800">{t('payments.withdraw.verification.title')}</CardTitle>
              <CardDescription className="text-slate-500">
                {t('payments.withdraw.verification.description')}
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="bg-slate-50 p-4 rounded-lg border border-slate-200 space-y-3">
                <div className="flex justify-between">
                  <span className="text-slate-500">{t('payments.withdraw.verification.transaction')}:</span>
                  <span className="text-slate-800 font-medium">{t('payments.withdraw.title')}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-500">{t('payments.withdraw.verification.amount')}:</span>
                  <span className="text-slate-800 font-medium">₺ {amount}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-500">{t('payments.withdraw.verification.method')}:</span>
                  <span className="text-slate-800 font-medium flex items-center gap-2">
                    {getSelectedMethod()?.icon}
                    {getSelectedMethod()?.name}
                    {getSelectedMethod()?.type === 'bank' && getSelectedMethod()?.iban ? `(IBAN: ...${getSelectedMethod()?.iban?.slice(-4)})` : 
                     getSelectedMethod()?.type === 'card' && getSelectedMethod()?.last4 ? `(*${getSelectedMethod()?.last4})` : ''}
                  </span>
                </div>
                <Separator className="bg-slate-200" />
                <p className="text-xs text-slate-500">
                  {t('payments.withdraw.verification.smsInfo', { phone: '+90******1234' })}
                </p>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="verificationCode" className="text-sm text-slate-700">{t('payments.withdraw.verification.code')}</Label>
                <Input 
                  id="verificationCode"
                  placeholder={t('payments.withdraw.verification.codePlaceholder')}
                  type="text"
                  maxLength={6}
                  className="bg-slate-50 border-slate-200 text-slate-700"
                  value={verificationCode}
                  onChange={(e) => setVerificationCode(e.target.value)}
                />
                <div className="flex justify-between items-center text-xs">
                  <div className="text-slate-500">
                    {t('payments.withdraw.verification.noCode')}{' '}
                    <Button variant="link" className="text-xs p-0 h-auto text-purple-600 hover:text-purple-700">
                      {t('payments.withdraw.verification.resend')}
                    </Button>
                  </div>
                  <div className="text-slate-500">
                    {t('payments.withdraw.verification.timeRemaining')}: <span className="text-slate-800 font-medium">2:59</span>
                  </div>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex justify-end gap-3">
              <Button 
                variant="outline" 
                className="border-slate-200 bg-white hover:bg-slate-50 text-slate-600 hover:text-slate-800"
                onClick={() => setWithdrawalStep(1)}
              >
                {t('common.back')}
              </Button>
              <Button 
                className="bg-purple-500 hover:bg-purple-600 text-white"
                disabled={verificationCode.length !== 6}
                onClick={() => setWithdrawalStep(3)}
              >
                {t('payments.withdraw.verification.confirm')}
              </Button>
            </CardFooter>
          </Card>
        )}
        
        {withdrawalStep === 3 && (
          <Card className="bg-white shadow-sm">
            <CardContent className="p-8 flex flex-col items-center justify-center text-center">
              <div className="w-20 h-20 rounded-full bg-green-100 flex items-center justify-center mb-6">
                <Check className="h-10 w-10 text-green-500" />
              </div>
              
              <h2 className="text-2xl font-bold text-slate-800 mb-2">
                {t('payments.withdraw.success.title')}
              </h2>
              
              <p className="text-slate-500 mb-6 max-w-md">
                {t('payments.withdraw.success.description', { amount })}
              </p>
              
              <div className="bg-slate-50 p-4 rounded-lg border border-slate-200 w-full max-w-md space-y-3 mb-8">
                <div className="flex justify-between">
                  <span className="text-slate-500">{t('payments.withdraw.success.transactionId')}:</span>
                  <span className="text-slate-800 font-medium">WD12345678</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-500">{t('payments.withdraw.success.amount')}:</span>
                  <span className="text-slate-800 font-medium">₺ {amount}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-500">{t('payments.withdraw.success.status')}:</span>
                  <Badge className="bg-amber-500 text-white text-[10px] h-5">{t('payments.withdraw.success.processing')}</Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-500">{t('payments.withdraw.success.estimatedCompletion')}:</span>
                  <span className="text-slate-800 font-medium">{t('payments.withdraw.success.businessDays')}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-500">{t('payments.withdraw.success.method')}:</span>
                  <span className="text-slate-800 font-medium flex items-center gap-2">
                    {getSelectedMethod()?.icon}
                    {getSelectedMethod()?.name}
                    {getSelectedMethod()?.type === 'bank' && getSelectedMethod()?.iban ? `(IBAN: ...${getSelectedMethod()?.iban?.slice(-4)})` : 
                     getSelectedMethod()?.type === 'card' && getSelectedMethod()?.last4 ? `(*${getSelectedMethod()?.last4})` : ''}
                  </span>
                </div>
              </div>
              
              <div className="flex gap-3">
                <Link href="/publisher/payments">
                  <Button className="bg-purple-500 hover:bg-purple-600 text-white">
                    {t('payments.withdraw.success.goToPayments')}
                  </Button>
                </Link>
                <Link href="/publisher">
                  <Button variant="outline" className="border-slate-200 bg-white hover:bg-slate-50 text-slate-600 hover:text-slate-800">
                    {t('payments.withdraw.success.goToHome')}
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        )}
        
        <div className="flex justify-center">
          <Link href="/publisher/payments" className="text-sm text-purple-600 hover:text-purple-700 flex items-center">
            {t('payments.withdraw.viewHistory')}
            <ChevronRight className="h-4 w-4" />
          </Link>
        </div>
      </div>
    </DashboardLayout>
  );
} 