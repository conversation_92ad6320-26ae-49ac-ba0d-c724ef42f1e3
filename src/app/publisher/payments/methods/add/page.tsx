"use client";

import React, { useState } from 'react';
import { DashboardLayout } from "@/app/publisher/components/layout/dashboard-layout";
import { 
  Wallet,
  ArrowLeft,
  Plus,
  CreditCard,
  Building,
  BanknoteIcon,
  BarChart,
  Check,
  ChevronRight,
  ChevronDown,
  QrCode,
  AlertCircle,
  Info
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { <PERSON><PERSON>, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Checkbox } from '@/components/ui/checkbox';
import { useLanguage } from '@/shared/hooks/useLanguage';
import { useRouter } from 'next/navigation';
import { toast } from '@/components/ui/use-toast';
import { 
  addPaymentMethod, 
  type PaymentMethodType, 
  type AccountType
} from '@/shared/services/publisher/payment-methods';

export default function AddPaymentMethodPage() {
  const { t } = useLanguage();
  const router = useRouter();
  
  // Ödeme metodu tipi için tabs
  const [paymentType, setPaymentType] = useState<string>('bank');
  // Banka hesabı ekleme formu state'leri
  const [bankName, setBankName] = useState<string>('');
  const [accountHolder, setAccountHolder] = useState<string>('');
  const [iban, setIban] = useState<string>('');
  const [accountType, setAccountType] = useState<string>('individual');
  const [isDefault, setIsDefault] = useState<boolean>(false);
  
  // Kart/Dijital Cüzdan ekleme formu state'leri
  const [cardName, setCardName] = useState<string>('');
  const [cardNumber, setCardNumber] = useState<string>('');
  const [cardHolder, setCardHolder] = useState<string>('');
  
  // Form gönderme durumu
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [isSuccess, setIsSuccess] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  
  // IBAN formatı
  const formatIBAN = (value: string) => {
    // Sadece alfanumerik karakterlere izin ver
    const alphanumeric = value.replace(/[^a-zA-Z0-9]/g, '');
    // Her 4 karakterden sonra boşluk ekle
    return alphanumeric.replace(/(.{4})/g, '$1 ').trim();
  };
  
  // Kart numarası formatı
  const formatCardNumber = (value: string) => {
    // Sadece numaralara izin ver
    const numbers = value.replace(/[^\d]/g, '');
    // Her 4 rakamdan sonra boşluk ekle
    return numbers.replace(/(.{4})/g, '$1 ').trim();
  };
  
  // UI değerlerini API için dönüştür
  const mapPaymentTypeToApi = (uiType: string): PaymentMethodType => {
    switch (uiType) {
      case 'bank':
        return 'BANK_TRANSFER';
      case 'card':
        return 'CREDIT_CARD';
      case 'paypal':
        return 'PAYPAL';
      default:
        return 'BANK_TRANSFER';
    }
  };
  
  const mapAccountTypeToApi = (uiType: string): AccountType => {
    return uiType === 'individual' ? 'INDIVIDUAL' : 'CORPORATE';
  };
  
  // API için banka adını dönüştür
  const mapBankNameToApi = (uiBankName: string): string => {
    const bankNameMap: Record<string, string> = {
      'ziraat': 'Ziraat Bankası',
      'garanti': 'Garanti BBVA',
      'isbank': 'İş Bankası',
      'akbank': 'Akbank',
      'yapikredi': 'Yapı Kredi',
      'vakifbank': 'VakıfBank',
      'halkbank': 'Halk Bankası',
      'qnb': 'QNB Finansbank',
      'denizbank': 'Denizbank',
      'other': 'Diğer'
    };
    
    return bankNameMap[uiBankName] || uiBankName;
  };
  
  // Form gönderme fonksiyonu
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);
    
    try {
      if (paymentType === 'bank') {
        // IBAN formatını düzelt (boşlukları kaldır)
        const formattedIban = iban.replace(/\s/g, '');
        
        // API isteği gönder
        const result = await addPaymentMethod({
          payment_method: mapPaymentTypeToApi(paymentType),
          iban: formattedIban,
          bank_name: mapBankNameToApi(bankName),
          account_holder_name: accountHolder,
          account_type: mapAccountTypeToApi(accountType)
        });
        
        setIsSuccess(true);
        toast({
          title: t('common.success') || 'Başarılı',
          description: t('payments.methods.success.description.bank', { bankName: mapBankNameToApi(bankName) }) || 'Banka hesabı başarıyla eklendi',
        });
      } else if (paymentType === 'card') {
        // Kart numarasını formatla (boşlukları kaldır)
        const formattedCardNumber = cardNumber.replace(/\s/g, '');
        
        // API isteği gönder
        const result = await addPaymentMethod({
          payment_method: mapPaymentTypeToApi(paymentType),
          account_holder_name: cardHolder,
          // NOT: Gerçek bir API'de kart bilgileri için ek alanlar olabilir
        });
        
        setIsSuccess(true);
        toast({
          title: t('common.success') || 'Başarılı',
          description: t('payments.methods.success.description.card', { cardName }) || 'Kart başarıyla eklendi',
        });
      }
    } catch (err) {
      console.error('Ödeme yöntemi eklenirken hata oluştu:', err);
      setError(err instanceof Error ? err.message : 'Ödeme yöntemi eklenirken bir hata oluştu');
      
      toast({
        title: t('common.error') || 'Hata',
        description: err instanceof Error ? err.message : (t('payments.methods.errors.addFailed') || 'Ödeme yöntemi eklenemedi'),
        variant: "destructive"
      });
    } finally {
      setIsSubmitting(false);
    }
  };
  
  // Form inputlarının doluluğunu kontrol etme
  const isBankFormValid = () => {
    return bankName !== '' && accountHolder !== '' && iban.replace(/\s/g, '').length >= 20;
  };
  
  const isCardFormValid = () => {
    return cardName !== '' && cardHolder !== '' && cardNumber.replace(/\s/g, '').length >= 16;
  };
  
  // Aktif form için geçerlilik kontrolü
  const isFormValid = () => {
    if (paymentType === 'bank') {
      return isBankFormValid();
    } else {
      return isCardFormValid();
    }
  };
  
  // Form içeriği için koşullu render
  const renderFormContent = () => {
    if (isSuccess) {
      return (
        <div className="py-8 text-center">
          <div className="w-16 h-16 rounded-full bg-green-100 mx-auto flex items-center justify-center mb-4">
            <Check className="h-8 w-8 text-green-500" />
          </div>
          
          <h2 className="text-xl font-bold text-slate-800 mb-2">{t('payments.methods.success.title')}</h2>
          <p className="text-slate-600 mb-6 max-w-md mx-auto">
            {paymentType === 'bank' 
              ? t('payments.methods.success.description.bank', { bankName: mapBankNameToApi(bankName) })
              : t('payments.methods.success.description.card', { cardName })}
          </p>
          
          <div className="flex flex-col md:flex-row gap-3 justify-center">
            <Link href="/publisher/payments/methods">
              <Button className="bg-purple-500 hover:bg-purple-600 text-white">
                {t('payments.methods.success.buttons.methods')}
              </Button>
            </Link>
            <Link href="/publisher/payments">
              <Button variant="outline" className="border-slate-200 bg-white hover:bg-slate-50 text-slate-600 hover:text-slate-800">
                {t('payments.methods.success.buttons.payments')}
              </Button>
            </Link>
          </div>
        </div>
      );
    }
    
    return (
      <Tabs value={paymentType} onValueChange={setPaymentType} className="w-full">
        <TabsList className="grid grid-cols-2 sm:grid-cols-4 h-auto p-1 bg-slate-100 rounded-lg mb-6">
          <TabsTrigger value="bank" className="py-2 data-[state=active]:bg-white rounded data-[state=active]:text-slate-800 text-slate-500">
            <Building className="h-4 w-4 mr-2" />
            {t('payments.methods.bankAccount')}
          </TabsTrigger>
          <TabsTrigger value="card" className="py-2 data-[state=active]:bg-white rounded data-[state=active]:text-slate-800 text-slate-500">
            <CreditCard className="h-4 w-4 mr-2" />
            {t('payments.methods.digitalWallet')}
          </TabsTrigger>
          <TabsTrigger value="paypal" className="py-2 data-[state=active]:bg-white rounded data-[state=active]:text-slate-800 text-slate-500">
            <Wallet className="h-4 w-4 mr-2" />
            PayPal
          </TabsTrigger>
          <TabsTrigger value="crypto" className="py-2 data-[state=active]:bg-white rounded data-[state=active]:text-slate-800 text-slate-500">
            <QrCode className="h-4 w-4 mr-2" />
            Kripto
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="bank" className="space-y-6">
          {error && (
            <Alert variant="destructive" className="bg-red-50 border-red-100">
              <AlertCircle className="h-4 w-4 text-red-500" />
              <AlertDescription className="text-red-700 text-sm">{error}</AlertDescription>
            </Alert>
          )}
          
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="bank-name" className="text-sm text-slate-700">{t('payments.methods.bankNames.title')}</Label>
              <Select value={bankName} onValueChange={setBankName}>
                <SelectTrigger className="bg-slate-50 border-slate-200 text-slate-700">
                  <SelectValue placeholder={t('payments.methods.bankNames.select')} />
                </SelectTrigger>
                <SelectContent className="bg-white border-slate-200 text-slate-800">
                  <SelectItem value="ziraat">{t('payments.methods.bankNames.ziraat')}</SelectItem>
                  <SelectItem value="garanti">{t('payments.methods.bankNames.garanti')}</SelectItem>
                  <SelectItem value="isbank">{t('payments.methods.bankNames.isbank')}</SelectItem>
                  <SelectItem value="akbank">{t('payments.methods.bankNames.akbank')}</SelectItem>
                  <SelectItem value="yapikredi">{t('payments.methods.bankNames.yapikredi')}</SelectItem>
                  <SelectItem value="vakifbank">{t('payments.methods.bankNames.vakifbank')}</SelectItem>
                  <SelectItem value="halkbank">{t('payments.methods.bankNames.halkbank')}</SelectItem>
                  <SelectItem value="qnb">{t('payments.methods.bankNames.qnb')}</SelectItem>
                  <SelectItem value="denizbank">{t('payments.methods.bankNames.denizbank')}</SelectItem>
                  <SelectItem value="other">{t('payments.methods.bankNames.other')}</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="account-holder" className="text-sm text-slate-700">
                {t('payments.methods.form.accountHolder.label')}
              </Label>
              <Input 
                id="account-holder"
                placeholder={t('payments.methods.form.accountHolder.placeholder')}
                className="bg-slate-50 border-slate-200 text-slate-700"
                value={accountHolder}
                onChange={(e) => setAccountHolder(e.target.value)}
              />
              <p className="text-xs text-slate-500">
                {t('payments.methods.form.accountHolder.hint')}
              </p>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="iban" className="text-sm text-slate-700">
                {t('payments.methods.form.iban.label')}
              </Label>
              <Input 
                id="iban"
                placeholder={t('payments.methods.form.iban.placeholder')}
                className="bg-slate-50 border-slate-200 text-slate-700 font-mono"
                value={iban}
                onChange={(e) => setIban(formatIBAN(e.target.value))}
                maxLength={31}
              />
              <p className="text-xs text-slate-500">
                {t('payments.methods.form.iban.hint')}
              </p>
            </div>
            
            <div className="space-y-2">
              <Label className="text-sm text-slate-700">
                {t('payments.methods.form.accountType.label')}
              </Label>
              <RadioGroup value={accountType} onValueChange={setAccountType} className="flex gap-4">
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="individual" id="individual" className="text-purple-500" />
                  <Label htmlFor="individual" className="text-slate-600 cursor-pointer">
                    {t('payments.methods.form.accountType.individual')}
                  </Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="corporate" id="corporate" className="text-purple-500" />
                  <Label htmlFor="corporate" className="text-slate-600 cursor-pointer">
                    {t('payments.methods.form.accountType.business')}
                  </Label>
                </div>
              </RadioGroup>
            </div>
            
            <div className="flex items-center space-x-2 pt-2">
              <Checkbox 
                id="default-payment" 
                className="text-purple-500 border-slate-300"
                checked={isDefault}
                onCheckedChange={() => setIsDefault(!isDefault)}
              />
              <Label htmlFor="default-payment" className="text-sm text-slate-600 cursor-pointer">
                {t('payments.methods.form.defaultPayment')}
              </Label>
            </div>
          </div>
          
          <Alert className="bg-blue-50 border-blue-100">
            <Info className="h-4 w-4 text-blue-500" />
            <AlertDescription className="text-blue-700 text-xs">
              {t('payments.methods.alerts.bank.description')}
            </AlertDescription>
          </Alert>
        </TabsContent>
        
        <TabsContent value="card" className="space-y-6">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="card-name" className="text-sm text-slate-700">{t('payments.methods.walletTypes.title')}</Label>
              <Select value={cardName} onValueChange={setCardName}>
                <SelectTrigger className="bg-slate-50 border-slate-200 text-slate-700">
                  <SelectValue placeholder={t('payments.methods.walletTypes.select')} />
                </SelectTrigger>
                <SelectContent className="bg-white border-slate-200 text-slate-800">
                  <SelectItem value="papara">{t('payments.methods.walletTypes.papara')}</SelectItem>
                  <SelectItem value="ininal">{t('payments.methods.walletTypes.ininal')}</SelectItem>
                  <SelectItem value="paycell">{t('payments.methods.walletTypes.paycell')}</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="card-holder" className="text-sm text-slate-700">
                {t('payments.methods.form.cardHolder.label')}
              </Label>
              <Input 
                id="card-holder"
                placeholder={t('payments.methods.form.cardHolder.placeholder')}
                className="bg-slate-50 border-slate-200 text-slate-700"
                value={cardHolder}
                onChange={(e) => setCardHolder(e.target.value)}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="card-number" className="text-sm text-slate-700">
                {t('payments.methods.form.cardNumber.label')}
              </Label>
              <Input 
                id="card-number"
                placeholder={t('payments.methods.form.cardNumber.placeholder')}
                className="bg-slate-50 border-slate-200 text-slate-700 font-mono"
                value={cardNumber}
                onChange={(e) => setCardNumber(formatCardNumber(e.target.value))}
                maxLength={19}
              />
            </div>
            
            <div className="flex items-center space-x-2 pt-2">
              <Checkbox 
                id="default-payment-card" 
                className="text-purple-500 border-slate-300"
                checked={isDefault}
                onCheckedChange={() => setIsDefault(!isDefault)}
              />
              <Label htmlFor="default-payment-card" className="text-sm text-slate-600 cursor-pointer">
                {t('payments.methods.form.defaultPayment')}
              </Label>
            </div>
          </div>
          
          <Alert className="bg-amber-50 border-amber-100">
            <AlertCircle className="h-4 w-4 text-amber-500" />
            <AlertDescription className="text-amber-700 text-xs">
              {t('payments.methods.alerts.card.description')}
            </AlertDescription>
          </Alert>
        </TabsContent>
        
        <TabsContent value="paypal" className="space-y-6">
          <div className="py-8 text-center">
            <Wallet className="h-16 w-16 text-blue-500 mx-auto mb-4" />
            <h3 className="text-slate-800 font-medium mb-2">{t('payments.methods.comingSoon.paypal.title')}</h3>
            <p className="text-slate-600 text-sm max-w-md mx-auto mb-6">
              {t('payments.methods.comingSoon.paypal.description')}
            </p>
            
            <Button variant="outline" className="border-slate-200 bg-white hover:bg-slate-50 text-slate-600 hover:text-slate-800">
              {t('payments.methods.comingSoon.notify')}
            </Button>
          </div>
        </TabsContent>
        
        <TabsContent value="crypto" className="space-y-6">
          <div className="py-8 text-center">
            <QrCode className="h-16 w-16 text-amber-500 mx-auto mb-4" />
            <h3 className="text-slate-800 font-medium mb-2">{t('payments.methods.comingSoon.crypto.title')}</h3>
            <p className="text-slate-600 text-sm max-w-md mx-auto mb-6">
              {t('payments.methods.comingSoon.crypto.description')}
            </p>
            
            <Button variant="outline" className="border-slate-200 bg-white hover:bg-slate-50 text-slate-600 hover:text-slate-800">
              {t('payments.methods.comingSoon.notify')}
            </Button>
          </div>
        </TabsContent>
      </Tabs>
    );
  };

  return (
    <DashboardLayout>
      <div className="space-y-6 max-w-2xl mx-auto">
        {/* Üst başlık kısmı */}
        <div className="flex flex-col gap-4">
          <div className="flex items-center gap-2">
            <Link href="/publisher/payments/methods">
              <Button variant="ghost" size="icon" className="h-8 w-8 text-slate-500 hover:text-slate-700">
                <ArrowLeft className="h-4 w-4" />
              </Button>
            </Link>
            <h1 className="text-2xl md:text-3xl font-bold text-slate-800 flex items-center gap-2">
              <Plus className="h-7 w-7 text-purple-500" />
              <span>{t('payments.methods.addNew')}</span>
            </h1>
          </div>
          <p className="text-slate-500">{t('payments.methods.subtitle')}</p>
        </div>
        
        {/* Ana Kart */}
        <Card className="bg-white shadow-sm">
          <CardHeader className="pb-0">
            <CardTitle className="text-lg text-slate-800">{t('payments.methods.title')}</CardTitle>
            <CardDescription className="text-slate-500">
              {t('payments.methods.subtitle')}
            </CardDescription>
          </CardHeader>
          <CardContent className="pt-6">
            <form onSubmit={handleSubmit}>
              {renderFormContent()}
              
              {!isSuccess && (paymentType === 'bank' || paymentType === 'card') && (
                <div className="flex justify-end gap-3 mt-8">
                  <Link href="/publisher/payments/methods">
                    <Button variant="outline" className="border-slate-200 bg-white hover:bg-slate-50 text-slate-600 hover:text-slate-800">
                      {t('payments.methods.form.buttons.cancel')}
                    </Button>
                  </Link>
                  <Button 
                    type="submit" 
                    className="bg-purple-500 hover:bg-purple-600 text-white"
                    disabled={!isFormValid() || isSubmitting}
                  >
                    {isSubmitting ? (
                      <>
                        <div className="h-4 w-4 border-2 border-white/20 border-t-white rounded-full animate-spin mr-2"></div>
                        {t('payments.methods.form.buttons.adding')}
                      </>
                    ) : (
                      <>
                        <Plus className="h-4 w-4 mr-2" />
                        {t('payments.methods.form.buttons.add')}
                      </>
                    )}
                  </Button>
                </div>
              )}
            </form>
          </CardContent>
        </Card>
        
        {/* Bilgilendirme Kartı */}
        {!isSuccess && (
          <Card className="bg-slate-50 shadow-sm border-slate-200">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg text-slate-800 flex items-center gap-2">
                <Info className="h-5 w-5 text-blue-500" />
                {t('payments.methods.info.title')}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-sm text-slate-600">
                {t('payments.methods.info.description')}
              </p>
              
              <div className="space-y-2">
                <div className="flex items-start gap-2">
                  <Check className="h-4 w-4 mt-0.5 text-green-500" />
                  <p className="text-xs text-slate-700">
                    {t('payments.methods.info.features.multiple')}
                  </p>
                </div>
                <div className="flex items-start gap-2">
                  <Check className="h-4 w-4 mt-0.5 text-green-500" />
                  <p className="text-xs text-slate-700">
                    {t('payments.methods.info.features.edit')}
                  </p>
                </div>
                <div className="flex items-start gap-2">
                  <Check className="h-4 w-4 mt-0.5 text-green-500" />
                  <p className="text-xs text-slate-700">
                    {t('payments.methods.info.features.secure')}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </DashboardLayout>
  );
} 