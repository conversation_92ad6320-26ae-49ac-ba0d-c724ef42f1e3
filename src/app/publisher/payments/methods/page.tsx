"use client";

import React, { useState, useEffect } from 'react';
import { DashboardLayout } from "@/app/publisher/components/layout/dashboard-layout";
import { 
  Wallet,
  ArrowLeft,
  Plus,
  CreditCard,
  Trash2,
  Pencil,
  CheckCircle2,
  Shield,
  Info,
  ChevronRight,
  QrCode,
  AlertTriangle
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import Link from 'next/link';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useLanguage } from '@/shared/hooks/useLanguage';
import { getPaymentMethodList, type PaymentMethodItem, deletePaymentMethod } from '@/shared/services/publisher/payment-methods';
import { toast } from '@/components/ui/use-toast';

interface PaymentMethod {
  id: string;
  type: 'bank' | 'card' | 'paypal' | 'crypto';
  name: string;
  details: string;
  last4: string;
  isPrimary: boolean;
  dateAdded: string;
  apiItem?: PaymentMethodItem;
}

export default function PaymentMethodsPage() {
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [methodToDelete, setMethodToDelete] = useState<PaymentMethod | null>(null);
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { t } = useLanguage();
  
  useEffect(() => {
    const fetchPaymentMethods = async () => {
      setLoading(true);
      setError(null);
      
      try {
        const apiMethods = await getPaymentMethodList();
        
        const mappedMethods: PaymentMethod[] = apiMethods.map((item, index) => {
          let type: PaymentMethod['type'] = 'bank';
          if (item.payment_method === 'BANK_TRANSFER') {
            type = 'bank';
          } else if (item.payment_method === 'DIGITAL_WALLET') {
            type = 'card';
          }
          
          let details = '';
          if (item.account_type === 'INDIVIDUAL') {
            details = 'Bireysel Hesap';
          } else if (item.account_type === 'CORPORATE') {
            details = 'Kurumsal Hesap';
          } else {
            details = item.account_type || 'Hesap';
          }
          
          const last4 = item.iban ? item.iban.slice(-4) : '';
          
          const createdDate = new Date(item.created_at);
          const dateAdded = createdDate.toLocaleDateString('tr-TR', {
            day: '2-digit',
            month: '2-digit',
            year: 'numeric'
          });
          
          const isPrimary = index === 0;
          
          return {
            id: item.payment_method_id,
            type,
            name: item.bank_name || item.account_holder_name || 'Ödeme Yöntemi',
            details,
            last4,
            isPrimary,
            dateAdded,
            apiItem: item
          };
        });
        
        setPaymentMethods(mappedMethods);
      } catch (err) {
        console.error('Ödeme yöntemleri yüklenirken hata oluştu:', err);
        setError('Ödeme yöntemleri yüklenirken bir hata oluştu.');
        
        toast({
          title: t('common.error') || 'Hata',
          description: t('payments.methods.errors.loadFailed') || 'Ödeme yöntemleri yüklenemedi.',
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };
    
    fetchPaymentMethods();
  }, []);
  
  const getMethodIcon = (type: PaymentMethod['type']) => {
    switch (type) {
      case 'bank':
        return <CreditCard className="h-4 w-4 text-green-500" />;
      case 'card':
        return <CreditCard className="h-4 w-4 text-purple-500" />;
      case 'paypal':
        return <Wallet className="h-4 w-4 text-blue-500" />;
      case 'crypto':
        return <QrCode className="h-4 w-4 text-orange-500" />;
      default:
        return <CreditCard className="h-4 w-4 text-slate-500" />;
    }
  };
  
  const handleDelete = (method: PaymentMethod) => {
    setMethodToDelete(method);
    setDeleteDialogOpen(true);
  };
  
  const setPrimaryMethod = (id: string) => {
    console.log(`Ödeme metodu ${id} birincil olarak ayarlandı`);
    
    setPaymentMethods(prevMethods => 
      prevMethods.map(method => ({
        ...method,
        isPrimary: method.id === id
      }))
    );
    
    toast({
      title: t('common.success') || 'Başarılı',
      description: t('payments.methods.setPrimarySuccess') || 'Birincil ödeme yöntemi güncellendi.',
    });
  };
  
  const confirmDelete = async () => {
    if (methodToDelete) {
      try {
        setLoading(true);
        
        await deletePaymentMethod({
          payment_method_id: methodToDelete.id
        });
        
        setPaymentMethods(prevMethods => 
          prevMethods.filter(method => method.id !== methodToDelete.id)
        );
        
        toast({
          title: t('common.success') || 'Başarılı',
          description: t('payments.methods.delete.successMessage') || 'Ödeme yöntemi silindi.',
        });
      } catch (err) {
        console.error('Ödeme yöntemi silinirken hata oluştu:', err);
        
        toast({
          title: t('common.error') || 'Hata',
          description: err instanceof Error ? err.message : (t('payments.methods.errors.deleteFailed') || 'Ödeme yöntemi silinemedi.'),
          variant: "destructive"
        });
      } finally {
        setLoading(false);
        setDeleteDialogOpen(false);
        setMethodToDelete(null);
      }
    }
  };
  
  const faqItems = [
    {
      question: "Hangi ödeme yöntemlerini ekleyebilirim?",
      answer: "Şu anda Türkiye'deki tüm banka hesaplarını, Papara, Paypal ve kripto cüzdanlarını destekliyoruz."
    },
    {
      question: "Para çekme işlemi ne kadar sürer?",
      answer: "Banka hesaplarına yapılan ödemeler genellikle 1-3 iş günü içinde, diğer yöntemler ise 24 saat içinde tamamlanır."
    },
    {
      question: "Minimum para çekme tutarı nedir?",
      answer: "Minimum para çekme tutarı ₺100'dür. Daha düşük miktarlar için bakiyenizin artmasını beklemeniz gerekir."
    },
    {
      question: "Birden fazla ödeme yöntemi ekleyebilir miyim?",
      answer: "Evet, istediğiniz kadar ödeme yöntemi ekleyebilirsiniz, ancak bunlardan yalnızca biri birincil olarak belirlenebilir."
    }
  ];

  return (
    <DashboardLayout>
      <div className="space-y-6 max-w-5xl mx-auto">
        <div className="flex flex-col gap-4">
          <div className="flex items-center gap-2">
            <Link href="/publisher/payments">
              <Button variant="ghost" size="icon" className="h-8 w-8 text-slate-500 hover:text-slate-700">
                <ArrowLeft className="h-4 w-4" />
              </Button>
            </Link>
            <h1 className="text-2xl md:text-3xl font-bold text-slate-800 flex items-center gap-2">
              <CreditCard className="h-7 w-7 text-purple-500" />
              <span>{t('payments.methods.title')}</span>
            </h1>
          </div>
          <p className="text-slate-500">{t('payments.methods.subtitle')}</p>
        </div>
        
        <div className="grid gap-6 md:grid-cols-3">
          <div className="md:col-span-2 space-y-6">
            <Card className="bg-white shadow-sm">
              <CardHeader className="pb-3">
                <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
                  <div>
                    <CardTitle className="text-lg text-slate-800">{t('payments.methods.registeredMethods')}</CardTitle>
                    <CardDescription className="text-slate-500">{t('payments.methods.description')}</CardDescription>
                  </div>
                  
                  <Link href="/publisher/payments/methods/add">
                    <Button className="bg-purple-500 hover:bg-purple-600 text-white h-9">
                      <Plus className="h-4 w-4 mr-2" />
                      {t('payments.methods.addNew')}
                    </Button>
                  </Link>
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                {loading ? (
                  <div className="text-center py-8">
                    <p className="text-slate-500">{t('common.loading') || 'Yükleniyor...'}</p>
                  </div>
                ) : error ? (
                  <Alert className="bg-red-50 border-red-100">
                    <AlertTriangle className="h-4 w-4 text-red-500" />
                    <AlertDescription className="text-red-700 text-sm">{error}</AlertDescription>
                  </Alert>
                ) : paymentMethods.length === 0 ? (
                  <div className="text-center py-8">
                    <div className="w-16 h-16 rounded-full bg-slate-100 flex items-center justify-center mx-auto mb-4">
                      <CreditCard className="h-8 w-8 text-slate-400" />
                    </div>
                    <h3 className="text-slate-800 font-medium mb-2">{t('payments.methods.empty.title')}</h3>
                    <p className="text-slate-500 text-sm max-w-md mx-auto mb-4">
                      {t('payments.methods.empty.description')}
                    </p>
                    <Link href="/publisher/payments/methods/add">
                      <Button className="bg-purple-500 hover:bg-purple-600 text-white">
                        <Plus className="h-4 w-4 mr-2" />
                        {t('payments.methods.addNew')}
                      </Button>
                    </Link>
                  </div>
                ) :
                  <div className="space-y-3">
                    {paymentMethods.map((method) => (
                      <div 
                        key={method.id}
                        className={`p-4 rounded-lg border ${
                          method.isPrimary 
                            ? 'bg-purple-50 border-purple-100' 
                            : 'bg-white border-slate-200 hover:bg-slate-50'
                        } transition-colors duration-200`}
                      >
                        <div className="flex items-start justify-between">
                          <div className="flex items-start gap-3">
                            <div className="w-10 h-10 rounded-full bg-white border border-slate-200 flex items-center justify-center">
                              {getMethodIcon(method.type)}
                            </div>
                            <div>
                              <div className="flex items-center gap-2">
                                <h3 className="text-slate-800 font-medium text-base">{method.name}</h3>
                                {method.isPrimary && (
                                  <Badge className="bg-purple-100 text-purple-600 text-[10px] h-5">
                                    {t('payments.methods.primary')}
                                  </Badge>
                                )}
                              </div>
                              <p className="text-slate-600 text-sm">{method.details}</p>
                              <p className="text-slate-500 text-xs mt-1">
                                **** **** **** {method.last4} • {t('payments.methods.addedOn')}: {method.dateAdded}
                              </p>
                            </div>
                          </div>
                          
                          <div className="flex gap-2">
                            <Button 
                              variant="ghost" 
                              size="icon"
                              className="h-8 w-8 text-red-500 hover:text-red-600 hover:bg-red-50"
                              onClick={() => handleDelete(method)}
                              disabled={method.isPrimary || loading}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                        
                        {!method.isPrimary && (
                          <div className="mt-3 pt-3 border-t border-slate-200 flex justify-end">
                            <Button 
                              variant="ghost" 
                              className="h-8 text-xs text-purple-600 hover:text-purple-700 hover:bg-purple-50"
                              onClick={() => setPrimaryMethod(method.id)}
                            >
                              <CheckCircle2 className="h-3 w-3 mr-1" />
                              {t('payments.methods.setAsPrimary')}
                            </Button>
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                }
              </CardContent>
            </Card>
            
            <Card className="bg-white shadow-sm">
              <CardHeader>
                <CardTitle className="text-lg text-slate-800">{t('payments.methods.faq.title')}</CardTitle>
                <CardDescription className="text-slate-500">
                  {t('payments.methods.faq.description')}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {faqItems.map((item, index) => (
                    <div key={index} className="border-b border-slate-200 pb-4 last:border-0 last:pb-0">
                      <h4 className="font-medium text-slate-800 mb-2">{t(item.question)}</h4>
                      <p className="text-slate-600 text-sm">{t(item.answer)}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
          
          <div className="space-y-6">
            <Card className="bg-white shadow-sm">
              <CardHeader>
                <CardTitle className="text-lg text-slate-800">{t('payments.methods.security.title')}</CardTitle>
                <CardDescription className="text-slate-500">
                  {t('payments.methods.security.description')}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex gap-3">
                  <div className="w-10 h-10 rounded-full bg-green-100 flex items-center justify-center">
                    <Shield className="h-5 w-5 text-green-500" />
                  </div>
                  <div>
                    <h4 className="font-medium text-slate-800 mb-1">{t('payments.methods.security.securePayment')}</h4>
                    <p className="text-slate-600 text-sm">
                      {t('payments.methods.security.securePaymentDesc')}
                    </p>
                  </div>
                </div>
                
                <div className="flex gap-3">
                  <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                    <CheckCircle2 className="h-5 w-5 text-blue-500" />
                  </div>
                  <div>
                    <h4 className="font-medium text-slate-800 mb-1">{t('payments.methods.security.verifiedTransactions')}</h4>
                    <p className="text-slate-600 text-sm">
                      {t('payments.methods.security.verifiedTransactionsDesc')}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card className="bg-white shadow-sm">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg text-slate-800">{t('payments.methods.tips.title')}</CardTitle>
                <CardDescription className="text-slate-500">
                  {t('payments.methods.tips.description')}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <Alert className="bg-blue-50 border-blue-100">
                  <Info className="h-4 w-4 text-blue-500" />
                  <AlertDescription className="text-blue-700 text-sm">
                    {t('payments.methods.tips.bankAccountTip')}
                  </AlertDescription>
                </Alert>
                
                <Alert className="bg-purple-50 border-purple-100">
                  <Info className="h-4 w-4 text-purple-500" />
                  <AlertDescription className="text-purple-700 text-sm">
                    {t('payments.methods.tips.matchingDetailsTip')}
                  </AlertDescription>
                </Alert>
                
                <div className="rounded-lg border border-slate-200 p-4">
                  <h4 className="font-medium text-slate-800 mb-2 flex items-center gap-2">
                    <span className="bg-indigo-100 text-indigo-600 w-5 h-5 rounded-full text-xs flex items-center justify-center font-bold">?</span>
                    {t('payments.methods.help.title')}
                  </h4>
                  <p className="text-slate-600 text-sm mb-4">
                    {t('payments.methods.help.description')}
                  </p>
                  <Button variant="outline" className="w-full border-slate-200 text-slate-600 hover:text-slate-800">
                    {t('payments.methods.help.supportCenter')}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
        
        <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
          <DialogContent className="bg-white border-slate-200 text-slate-800">
            <DialogHeader>
              <DialogTitle>{t('payments.methods.delete.title')}</DialogTitle>
              <DialogDescription className="text-slate-500">
                {t('payments.methods.delete.description')}
              </DialogDescription>
            </DialogHeader>
            
            {methodToDelete && (
              <div className="bg-slate-50 p-4 rounded-lg border border-slate-200 mb-4 flex items-center gap-3">
                <div className="w-10 h-10 rounded-full bg-white border border-slate-200 flex items-center justify-center">
                  {getMethodIcon(methodToDelete.type)}
                </div>
                <div>
                  <h3 className="text-slate-800 font-medium">{methodToDelete.name}</h3>
                  <p className="text-slate-600 text-sm">{methodToDelete.details}</p>
                  <p className="text-slate-500 text-xs">**** {methodToDelete.last4}</p>
                </div>
              </div>
            )}
            
            <div className="text-sm text-amber-700 bg-amber-50 p-3 rounded-lg border border-amber-100 flex items-start gap-2">
              <Info className="h-4 w-4 mt-0.5" />
              <p>{t('payments.methods.delete.warning')}</p>
            </div>
            
            <DialogFooter>
              <Button 
                variant="outline" 
                className="border-slate-200 text-slate-600"
                onClick={() => setDeleteDialogOpen(false)}
                disabled={loading}
              >
                {t('common.cancel')}
              </Button>
              <Button 
                className="bg-red-500 hover:bg-red-600 text-white" 
                onClick={confirmDelete}
                disabled={loading}
              >
                {loading ? (
                  <>
                    <span className="animate-spin mr-2">⏳</span>
                    {t('common.loading') || 'Siliniyor...'}
                  </>
                ) : (
                  <>
                    <Trash2 className="h-4 w-4 mr-2" />
                    {t('payments.methods.delete')}
                  </>
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </DashboardLayout>
  );
} 