"use client";

import React, { useState, useEffect, useCallback } from 'react';
import { DashboardLayout } from "@/app/publisher/components/layout/dashboard-layout";
import { 
  Bell,
  Check,
  CheckCheck,
  Filter,
  Search,
  SlidersHorizontal,
  Trash2,
  Mail,
  BellOff
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { NotificationList } from './components/notification-list';
import { Dialog, DialogContent, DialogDescription, DialogFooter, Di<PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { toast } from '@/components/ui/use-toast';
import { useLanguage } from '@/shared/hooks/useLanguage';
import { getNotificationList, readAllNotifications, deleteAllNotifications, readNotification, type Notification as APINotification, type NotificationListRequest } from '@/shared/services/publisher/publisher-notifications';

// NotificationList bileşeninin beklediği bildirim tipleri
type FrontendNotificationType = 'system' | 'payment' | 'community' | 'ads';

// Bildirim tipi tanımı (API'den gelene göre ve frontend ihtiyacına göre ayarlandı)
interface Notification {
  id: string; // frontend'de kullanmak için _id'yi id olarak eşle
  title: string; // API response'undan
  message: string; // API response'undan
  date: string; // createdAt'i Date string'e çevireceğiz
  read: boolean; // API response'undan isRead alanı (frontend'de read olarak kullanılacak)
  type: FrontendNotificationType; // NotificationList bileşeninin beklediği tip
  icon?: React.ReactNode; // Frontend'de NotificationList bileşeninde belirlenebilir
  // API'den gelen diğer alanlar buraya eklenebilir
  _id: string; // API'den gelen _id'yi de tutabiliriz
  isRead: boolean; // API'den gelen isRead'i de tutabiliriz
  createdAt: number; // API'den gelen createdAt'i de tutabiliriz
}

interface NotificationFilter {
  search: string;
  type: 'all' | FrontendNotificationType; // Filtre tipini güncelle
  status: 'all' | 'read' | 'unread';
  sortBy: 'newest' | 'oldest';
}

export default function NotificationsPage() {
  const { t } = useLanguage();
  const [activeTab, setActiveTab] = useState<'all' | 'unread'>('all');
  const [filters, setFilters] = useState<NotificationFilter>({
    search: '',
    type: 'all',
    status: 'all',
    sortBy: 'newest'
  });
  const [showFilters, setShowFilters] = useState(false);
  const [confirmDeleteDialog, setConfirmDeleteDialog] = useState(false);
  const [notificationToDeleteId, setNotificationToDeleteId] = useState<string | null>(null);
  
  // API'den gelen bildirim verileri için state
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Bildirim verilerini çekme fonksiyonu
  const fetchNotifications = useCallback(async () => {
    try {
      setLoading(true);
      
      // API'nin desteklediği filtreler: limit, skip, status
      const params: NotificationListRequest = {
        limit: 100, // Örnek limit
        skip: 0,
        // status filtresini activeTab veya filters.status'tan alacağız
        // activeTab 'unread' ise status: 'unread' gönder, değilse filters.status'a bak
        ...(activeTab === 'unread' ? { status: 'unread' } : (filters.status !== 'all' ? { status: filters.status as 'read' | 'unread' } : {}))
        // API search ve type filtrelerini doğrudan desteklemiyor gibi görünüyor, frontend'de filtreleyeceğiz.
      };
      
      const result = await getNotificationList(params);
      
      // API'den gelen veriyi Notification tipine dönüştür
      const transformedNotifications: Notification[] = result.data.map(apiNotification => ({
        ...apiNotification, // API'den gelen tüm alanları kopyala
        id: apiNotification._id, // _id'yi id olarak eşle
        date: new Date(apiNotification.createdAt * 1000).toISOString(), // Unix timestamp'ten Date string'e
        read: apiNotification.isRead, // isRead'i read olarak eşle
        type: apiNotification.type as FrontendNotificationType, // API'den gelen tipi frontend tipine dönüştür
        // icon gibi alanlar burada değil, muhtemelen NotificationList bileşeninde tipine göre belirlenecek
      }));

      setNotifications(transformedNotifications);
      setError(null);
    } catch (err: any) {
      console.error('Bildirimler yüklenirken hata oluştu:', err);
      setError(err.message || 'Bildirimler yüklenirken bir hata oluştu');
      setNotifications([]); // Hata durumunda listeyi temizle
    } finally {
      setLoading(false);
    }
  }, [filters, activeTab]); // activeTab veya filtreler değiştiğinde tekrar çek

  // Sayfa yüklendiğinde veya filtreler/activeTab değiştiğinde bildirimleri çek
  useEffect(() => {
    fetchNotifications();
  }, [fetchNotifications]);
  
  // API sadece status filtresini desteklediği için diğer filtrelemeler (search, type) frontend'de yapılacak.
  const filteredNotifications = notifications.filter(notification => {
    let isMatch = true;
    
    // Arama filtresi
    if (filters.search) {
      const searchTerm = filters.search.toLowerCase();
      isMatch = isMatch && (
        notification.title.toLowerCase().includes(searchTerm) || 
        notification.message.toLowerCase().includes(searchTerm)
        // API response'unda type alanı varsa burada type üzerinde de arama yapılabilir
      );
    }
    
    // Tür filtresi (API response'unda type alanı varsa ve filtre açıksa)
    if (filters.type !== 'all') {
      // Notification interface'inde type alanı string olarak tanımlandı. API response'unda type alanı string geliyorsa bu şekilde filtreleyebiliriz.
       isMatch = isMatch && notification.type === filters.type;
    }
    
    // activeTab 'unread' ise ve filters.status 'all' değilse, API call içinde status filtresi gönderildiği için burada tekrar filtrelemeye gerek yok.
    // Eğer activeTab 'all' ise ve filters.status 'read' veya 'unread' ise (ki bu durumda API call içinde status filtresi gönderilmez), o zaman burada filtreleme yapmalıyız.
    // Ancak current logic API call içinde status filtresi gönderiyor, bu yüzden buradaki durum filtrelemesi (activeTab ve filters.status için) kaldırılabilir veya sadece activeTab === 'all' ve filters.status !== 'all' durumu için bırakılabilir.
    // Mevcut durumda activeTab unread iken API call sadece unread getirir, activeTab all iken API filters.status neyse onu getirir (all ise filtre yok).
    // Bu durumda frontend filtrelemesine gerek kalmıyor gibi görünüyor, sadece activeTab unread iken ve filters.status da unread iken API unread getirir. Diğer durumlar için API'nin davranışına bakmak lazım.
    // API'nin sadece `status` filtresini desteklediğini varsayarak frontend filtrelemeyi sadece `search` ve `type` için bırakıyorum.

    return isMatch;
  }).sort((a, b) => { // Sıralama frontend'de yapılıyor
      const dateA = new Date(a.date).getTime();
      const dateB = new Date(b.date).getTime();
      return filters.sortBy === 'newest' ? dateB - dateA : dateA - dateB;
    });

  // unreadCount hesaplaması artık API response'undaki isRead alanına göre yapılacak
  const unreadCount = notifications.filter(n => !n.isRead).length; 
  
  // Tüm bildirimleri okundu olarak işaretle (API çağrısı eklenecek)
  const markAllAsRead = async () => {
    try {
      await readAllNotifications();
      // Başarılı olursa frontend state'ini güncelle: isRead: true yap
      setNotifications(notifications.map(notification => ({
        ...notification,
        isRead: true
      })));
      toast({
        title: t('notifications.markAllReadSuccess'),
        description: t('notifications.markAllReadSuccessDesc'),
      });
    } catch (err: any) {
      console.error('Tüm bildirimleri okundu işaretlerken hata:', err);
      toast({
        title: t('common.error'),
        description: err.message || t('notifications.markAllReadError'),
        variant: "destructive",
      });
    }
  };
  
  // Tek bir bildirimi okundu olarak işaretle (API çağrısı eklenecek)
  const markAsRead = async (id: string) => {
    try {
      await readNotification(id);
      // Başarılı olursa frontend state'ini güncelle: ilgili bildirimin isRead: true yap
      setNotifications(notifications.map(notification => 
        notification.id === id 
          ? { ...notification, isRead: true }
          : notification
      ));
    } catch (err: any) {
      console.error('Bildirimi okundu işaretlerken hata:', err);
      toast({
        title: t('common.error'),
        description: err.message || t('notifications.readError'),
        variant: "destructive",
      });
    }
  };
  
  // Tüm bildirimleri temizle (API çağrısı eklenecek)
  const clearAllNotifications = async () => {
    try {
      await deleteAllNotifications();
      // Başarılı olursa frontend state'ini temizle
      setNotifications([]);
      toast({
        title: t('notifications.deleteAllSuccess'),
        description: t('notifications.deleteAllSuccessDesc'),
      });
    } catch (err: any) {
      console.error('Tüm bildirimleri silerken hata:', err);
      toast({
        title: t('common.error'),
        description: err.message || t('notifications.deleteAllError'),
        variant: "destructive",
      });
    } finally {
      setConfirmDeleteDialog(false);
    }
  };
  
  // Filtreleri sıfırla
  const resetFilters = () => {
    setFilters({
      search: '',
      type: 'all',
      status: 'all',
      sortBy: 'newest'
    });
    setActiveTab('all');
  };

  // Bildirim yok durumu için tasarım
  const renderNoNotifications = () => (
    <Card 
      className="border-dashed border-indigo-200/40 bg-gradient-to-br from-white/95 to-blue-50/80 transition-all duration-300 min-h-[300px] flex flex-col items-center justify-center text-center p-6"
    >
      <div className="h-16 w-16 rounded-full bg-purple-100 border border-purple-200/50 flex items-center justify-center mb-4">
        <BellOff className="h-8 w-8 text-purple-500" />
      </div>
      <h3 className="text-slate-800 font-medium text-lg">{t('notifications.noNotifications')}</h3>
      <p className="text-slate-500 text-sm mt-2 max-w-[400px]">
        {t('notifications.empty.description')}
      </p>
      {/* İsterseniz buraya bildirim ayarları sayfasına yönlendiren bir buton eklenebilir */}
      {/* <Button variant="outline" className="mt-4">{t('notifications.settings')}</Button> */}
    </Card>
  );

  return (
    <DashboardLayout>
      <div className="space-y-6 max-w-5xl mx-auto">
        {/* Üst başlık kısmı */}
        <div className="flex flex-col gap-4">
          <div className="flex flex-col md:flex-row md:items-end md:justify-between gap-4">
            <div>
              <h1 className="text-2xl md:text-3xl font-bold text-slate-800 flex items-center gap-2">
                <Bell className="h-7 w-7 text-purple-500" />
                <span>{t('notifications.title')}</span>
                {unreadCount > 0 && (
                  <Badge variant="outline" className="ml-2 bg-purple-50 text-purple-600 border-purple-200">
                    {unreadCount} {t('notifications.unreadCount')}
                  </Badge>
                )}
              </h1>
              <p className="text-slate-500 mt-1">{t('notifications.subtitle')}</p>
            </div>
            
            <div className="flex flex-wrap gap-2">
              <Button 
                variant="outline" 
                size="sm" 
                className="h-9 text-slate-600 border-slate-200"
                onClick={markAllAsRead}
                disabled={loading || notifications.length === 0 || unreadCount === 0}
              >
                <CheckCheck className="h-4 w-4 mr-2" />
                <span>{t('notifications.markAllRead')}</span>
              </Button>
              <Button 
                variant="outline" 
                size="sm" 
                className="h-9 text-slate-600 border-slate-200"
                onClick={() => setConfirmDeleteDialog(true)}
                disabled={loading || notifications.length === 0}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                <span>{t('notifications.deleteAll')}</span>
              </Button>
              <Button 
                variant="outline" 
                size="sm" 
                className="h-9 text-slate-600 border-slate-200" 
                onClick={() => setShowFilters(!showFilters)}
              >
                <SlidersHorizontal className="h-4 w-4 mr-2" />
                <span>{t('notifications.notFiltre')}</span>
              </Button>
            </div>
          </div>
          
          {/* Filtre alanı */}
          {showFilters && (
            <Card className="border border-indigo-200/40 bg-gradient-to-br from-white/95 to-blue-50/95 shadow-md">
              <CardContent className="p-4">
                <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4">
                  {/* Arama */}
                  <div className="relative col-span-full md:col-span-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                    <Input 
                      placeholder={t('notifications.filters.searchPlaceholder')}
                      className="pl-9 bg-white border-slate-200 text-slate-800 placeholder:text-slate-400"
                      value={filters.search}
                      onChange={(e) => setFilters({...filters, search: e.target.value})}
                    />
                  </div>
                  
                  {/* Tür Filtresi */}
                   <Select onValueChange={(value) => setFilters({...filters, type: value as 'all' | FrontendNotificationType})} value={filters.type}>
                     <SelectTrigger className="w-full bg-white border-slate-200 text-slate-800">
                       <SelectValue placeholder={t('notifications.filters.typePlaceholder')} />
                     </SelectTrigger>
                     <SelectContent>
                       <SelectItem value="all">{t('notifications.filters.type.all')}</SelectItem>
                       {/* API'den gelen bildirim tiplerine göre burayı dinamik yapabiliriz */}
                       <SelectItem value="system">{t('notifications.filters.type.system')}</SelectItem>
                       <SelectItem value="payment">{t('notifications.filters.type.payment')}</SelectItem>
                       <SelectItem value="community">{t('notifications.filters.type.community')}</SelectItem>
                       <SelectItem value="ads">{t('notifications.filters.type.ads')}</SelectItem>
                     </SelectContent>
                   </Select>

                  {/* Durum Filtresi */}
                   <Select onValueChange={(value) => setFilters({...filters, status: value as 'all' | 'read' | 'unread'})} value={filters.status}>
                     <SelectTrigger className="w-full bg-white border-slate-200 text-slate-800">
                       <SelectValue placeholder={t('notifications.filters.statusPlaceholder')} />
                     </SelectTrigger>
                     <SelectContent>
                       <SelectItem value="all">{t('notifications.filters.status.all')}</SelectItem>
                       <SelectItem value="read">{t('notifications.filters.status.read')}</SelectItem>
                       <SelectItem value="unread">{t('notifications.filters.status.unread')}</SelectItem>
                     </SelectContent>
                   </Select>

                  {/* Sıralama */}
                   <Select onValueChange={(value) => setFilters({...filters, sortBy: value as 'newest' | 'oldest'})} value={filters.sortBy}>
                     <SelectTrigger className="w-full bg-white border-slate-200 text-slate-800">
                       <SelectValue placeholder={t('notifications.filters.sortByPlaceholder')} />
                     </SelectTrigger>
                     <SelectContent>
                       <SelectItem value="newest">{t('notifications.filters.sortBy.newest')}</SelectItem>
                       <SelectItem value="oldest">{t('notifications.filters.sortBy.oldest')}</SelectItem>
                     </SelectContent>
                   </Select>
                 </div>
               </CardContent>
             </Card>
           )}
        </div>
        
        {/* Ana içerik alanı */}
        {loading ? (
          <div className="text-center py-10 text-slate-500">{t('common.loading')}</div>
        ) : error ? (
          <div className="text-center py-10 text-red-500">{error}</div>
        ) : filteredNotifications.length === 0 ? (
          renderNoNotifications() // Boş durum tasarımını göster
        ) : (
          <Card className="border border-indigo-200/40 bg-gradient-to-br from-white/95 to-blue-50/95 shadow-md">
            <CardHeader className="border-b border-indigo-200/40 p-4">
              <Tabs value={activeTab} onValueChange={(v) => setActiveTab(v as 'all' | 'unread')}>
                <TabsList className="grid grid-cols-2 h-9 bg-white border border-slate-200">
                  <TabsTrigger value="all" className="text-xs">{t('notifications.tabs.all')}</TabsTrigger>
                  <TabsTrigger value="unread" className="text-xs relative">
                    {t('notifications.tabs.unread')}
                    {unreadCount > 0 && (
                       <Badge variant="destructive" className="ml-1 px-1 py-0 text-[10px] h-4 leading-none">{unreadCount}</Badge>
                    )}
                  </TabsTrigger>
                </TabsList>
                <TabsContent value={activeTab} className="mt-4">
                   {/* Bildirim Listesi Bileşeni */}
                   <NotificationList notifications={filteredNotifications} onMarkAsRead={markAsRead} />
                </TabsContent>
              </Tabs>
            </CardHeader>
          </Card>
        )}

        {/* Tümünü Sil Onay Dialogu */}
        <Dialog open={confirmDeleteDialog} onOpenChange={setConfirmDeleteDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>{t('notifications.deleteAllConfirmTitle')}</DialogTitle>
              <DialogDescription>{t('notifications.deleteAllConfirmDesc')}</DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button variant="outline" onClick={() => setConfirmDeleteDialog(false)}>{t('common.cancel')}</Button>
              <Button variant="destructive" onClick={clearAllNotifications}>{t('common.delete')}</Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
    </div>
  </DashboardLayout>
);
} 