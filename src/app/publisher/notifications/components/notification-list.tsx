"use client";

import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Check } from 'lucide-react';
import { useLanguage } from '@/shared/hooks/useLanguage';

interface Notification {
  id: string;
  title: string;
  message: string;
  date: string;
  read: boolean;
  type: 'system' | 'payment' | 'community' | 'ads';
  icon?: React.ReactNode;
}

interface NotificationListProps {
  notifications: Notification[];
  onMarkAsRead?: (id: string) => void;
}

export function NotificationList({ notifications, onMarkAsRead }: NotificationListProps) {
  const { t } = useLanguage();

  // Bildirimi okundu olarak işaretle
  const handleMarkAsRead = (id: string) => {
    if (onMarkAsRead) {
      onMarkAsRead(id);
    }
  };

  // Bildirim türüne göre rozetleri sınıflandır
  const getTypeBadgeClass = (type: string): string => {
    switch (type) {
      case 'system':
        return 'bg-slate-50 text-slate-600 border-slate-200';
      case 'payment':
        return 'bg-green-50 text-green-600 border-green-200';
      case 'community':
        return 'bg-purple-50 text-purple-600 border-purple-200';
      case 'ads':
        return 'bg-blue-50 text-blue-600 border-blue-200';
      default:
        return 'bg-slate-50 text-slate-600 border-slate-200';
    }
  };

  // Bildirim türüne göre etiketleri getir
  const getTypeLabel = (type: string): string => {
    switch (type) {
      case 'system':
        return t('notifications.types.system');
      case 'payment':
        return t('notifications.types.payment');
      case 'community':
        return t('notifications.types.community');
      case 'ads':
        return t('notifications.types.ads');
      default:
        return t('notifications.types.general');
    }
  };

  // Tarih formatını düzenle
  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 0) {
      return t('notifications.time.today', {
        time: `${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
      });
    } else if (diffDays === 1) {
      return t('notifications.time.yesterday');
    } else if (diffDays < 7) {
      const days = [
        t('notifications.time.days.sunday'),
        t('notifications.time.days.monday'),
        t('notifications.time.days.tuesday'),
        t('notifications.time.days.wednesday'),
        t('notifications.time.days.thursday'),
        t('notifications.time.days.friday'),
        t('notifications.time.days.saturday')
      ];
      return days[date.getDay()];
    } else {
      return t('notifications.time.date', {
        date: `${date.getDate().toString().padStart(2, '0')}.${(date.getMonth() + 1).toString().padStart(2, '0')}.${date.getFullYear()}`
      });
    }
  };

  if (notifications.length === 0) {
    return (
      <div className="text-center py-6">
        <p className="text-slate-500">{t('notifications.empty')}</p>
      </div>
    );
  }

  return (
    <div className="divide-y divide-slate-100">
      {notifications.map((notification) => (
        <div 
          key={notification.id} 
          className={`py-4 first:pt-2 last:pb-2 ${!notification.read ? 'bg-purple-50/40' : ''}`}
        >
          <div className="flex gap-3">
            <div className="mt-1">
              <div className="w-10 h-10 rounded-full bg-slate-100 flex items-center justify-center">
                {notification.icon}
              </div>
            </div>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between mb-1">
                <h3 className={`text-base ${!notification.read ? 'font-semibold text-slate-900' : 'font-medium text-slate-800'}`}>
                  {notification.title}
                </h3>
                <Badge 
                  variant="outline" 
                  className={`text-xs ${getTypeBadgeClass(notification.type)}`}
                >
                  {getTypeLabel(notification.type)}
                </Badge>
              </div>
              
              <p className="text-sm text-slate-600 mb-2">{notification.message}</p>
              
              <div className="flex flex-wrap items-center justify-between gap-2">
                <span className="text-xs text-slate-500">
                  {formatDate(notification.date)}
                </span>
                
                <div className="flex gap-2">
                  {!notification.read && (
                    <Button 
                      size="sm" 
                      variant="ghost" 
                      className="h-7 text-xs px-2 text-purple-600"
                      onClick={() => handleMarkAsRead(notification.id)}
                    >
                      <Check className="h-3 w-3 mr-1" />
                      {t('notifications.actions.markAsRead')}
                    </Button>
                  )}
                  
                  {/* actionUrl kaldırıldığı için bu buton yorum satırı yapıldı */}
                  {/* 
                  <Button size="sm" variant="outline" className="h-7 text-xs px-2 border-slate-200" asChild>
                    <a href={notification.actionUrl}>{t('notifications.actions.details')}</a>
                  </Button>
                  */}
                </div>
              </div>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
} 