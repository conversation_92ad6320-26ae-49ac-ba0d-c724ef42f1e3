"use client";

import React, { createContext, useContext } from 'react';

export interface ProfileData {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  avatar?: string;
  bio?: string;
  website?: string;
  location?: string;
  timezone: string;
  language: string;
  
  // Account settings
  emailVerified: boolean;
  phoneVerified: boolean;
  twoFactorEnabled: boolean;
  
  // Notification preferences
  notifications: {
    email: {
      campaigns: boolean;
      payments: boolean;
      security: boolean;
      marketing: boolean;
    };
    push: {
      campaigns: boolean;
      payments: boolean;
      security: boolean;
    };
    sms: {
      payments: boolean;
      security: boolean;
    };
  };
  
  // Privacy settings
  privacy: {
    profileVisibility: 'public' | 'private' | 'contacts';
    showEmail: boolean;
    showPhone: boolean;
    allowMessages: boolean;
    dataProcessing: boolean;
  };
  
  // Publisher-specific settings
  publisher: {
    businessName?: string;
    businessType: string;
    taxId?: string;
    categories: string[];
    contentTypes: string[];
    minCampaignBudget: number;
    autoApproval: boolean;
    contentGuidelines?: string;
  };
  
  // Payment information
  payment: {
    preferredMethod: 'bank' | 'paypal' | 'crypto';
    bankAccount?: {
      accountName: string;
      accountNumber: string;
      bankName: string;
      iban?: string;
      swift?: string;
    };
    paypalEmail?: string;
    cryptoWallet?: {
      type: string;
      address: string;
    };
    taxInfo: {
      country: string;
      taxNumber?: string;
      vatNumber?: string;
    };
  };
  
  // Security
  security: {
    lastPasswordChange: string;
    activeSessions: Array<{
      id: string;
      device: string;
      location: string;
      lastActive: string;
      current: boolean;
    }>;
    loginHistory: Array<{
      date: string;
      device: string;
      location: string;
      success: boolean;
    }>;
  };
  
  // Metadata
  createdAt: string;
  updatedAt: string;
  lastLogin: string;
}

interface ProfileContextType {
  profileData: ProfileData | null;
  updateProfile: (updates: Partial<ProfileData>) => Promise<void>;
  loading: boolean;
  error: string | null;
}

const ProfileContext = createContext<ProfileContextType | undefined>(undefined);

export const ProfileProvider: React.FC<{
  children: React.ReactNode;
  value: ProfileContextType;
}> = ({ children, value }) => {
  return (
    <ProfileContext.Provider value={value}>
      {children}
    </ProfileContext.Provider>
  );
};

export const useProfile = () => {
  const context = useContext(ProfileContext);
  if (context === undefined) {
    throw new Error('useProfile must be used within a ProfileProvider');
  }
  return context;
};

export default ProfileContext;
