"use client";

import { useState, useEffect } from 'react';
import { ProfileData } from '../context/ProfileContext';

// Mock data for development
const mockProfileData: ProfileData = {
  id: 'pub_123456',
  firstName: 'Ahmet',
  lastName: 'Yılmaz',
  email: '<EMAIL>',
  phone: '+90 ************',
  avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
  bio: 'İçerik üreticisi ve sosyal medya uzmanı. Teknoloji, yaşam tarzı ve seyahat konularında içerik üretiyorum.',
  website: 'https://ahmetyilmaz.com',
  location: 'İstanbul, Türkiye',
  timezone: 'Europe/Istanbul',
  language: 'tr',
  
  emailVerified: true,
  phoneVerified: false,
  twoFactorEnabled: false,
  
  notifications: {
    email: {
      campaigns: true,
      payments: true,
      security: true,
      marketing: false,
    },
    push: {
      campaigns: true,
      payments: true,
      security: true,
    },
    sms: {
      payments: true,
      security: true,
    },
  },
  
  privacy: {
    profileVisibility: 'public',
    showEmail: false,
    showPhone: false,
    allowMessages: true,
    dataProcessing: true,
  },
  
  publisher: {
    businessName: 'Ahmet Yılmaz Media',
    businessType: 'individual',
    taxId: '***********',
    categories: ['technology', 'lifestyle', 'travel'],
    contentTypes: ['blog', 'social', 'video'],
    minCampaignBudget: 1000,
    autoApproval: false,
    contentGuidelines: 'Kaliteli, özgün içerik üretimi konusunda deneyimliyim. Marka değerleriyle uyumlu içerikler oluşturmaya özen gösteririm.',
  },
  
  payment: {
    preferredMethod: 'bank',
    bankAccount: {
      accountName: 'Ahmet Yılmaz',
      accountNumber: '**********',
      bankName: 'Türkiye İş Bankası',
      iban: 'TR12 0006 ************** 6789 01',
      swift: 'ISBKTRIS',
    },
    taxInfo: {
      country: 'TR',
      taxNumber: '***********',
    },
  },
  
  security: {
    lastPasswordChange: '2024-01-15T10:30:00Z',
    activeSessions: [
      {
        id: 'session_1',
        device: 'Chrome on Windows',
        location: 'İstanbul, Türkiye',
        lastActive: '2024-01-20T14:30:00Z',
        current: true,
      },
      {
        id: 'session_2',
        device: 'Safari on iPhone',
        location: 'İstanbul, Türkiye',
        lastActive: '2024-01-19T09:15:00Z',
        current: false,
      },
    ],
    loginHistory: [
      {
        date: '2024-01-20T14:30:00Z',
        device: 'Chrome on Windows',
        location: 'İstanbul, Türkiye',
        success: true,
      },
      {
        date: '2024-01-19T09:15:00Z',
        device: 'Safari on iPhone',
        location: 'İstanbul, Türkiye',
        success: true,
      },
    ],
  },
  
  createdAt: '2023-06-15T10:00:00Z',
  updatedAt: '2024-01-20T14:30:00Z',
  lastLogin: '2024-01-20T14:30:00Z',
};

export const useProfileData = () => {
  const [profileData, setProfileData] = useState<ProfileData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Simulate API call
    const fetchProfileData = async () => {
      try {
        setLoading(true);
        setError(null);
        
        // Simulate network delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // In a real app, this would be an API call
        // const response = await fetch('/api/publisher/profile');
        // const data = await response.json();
        
        setProfileData(mockProfileData);
      } catch (err) {
        setError('Profil bilgileri yüklenirken bir hata oluştu.');
        console.error('Profile fetch error:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchProfileData();
  }, []);

  const updateProfile = async (updates: Partial<ProfileData>) => {
    try {
      setLoading(true);
      setError(null);
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // In a real app, this would be an API call
      // const response = await fetch('/api/publisher/profile', {
      //   method: 'PATCH',
      //   headers: { 'Content-Type': 'application/json' },
      //   body: JSON.stringify(updates),
      // });
      
      setProfileData(prev => prev ? { ...prev, ...updates, updatedAt: new Date().toISOString() } : null);
    } catch (err) {
      setError('Profil güncellenirken bir hata oluştu.');
      console.error('Profile update error:', err);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  return {
    profileData,
    loading,
    error,
    updateProfile,
  };
};
