"use client";

import React, { useState, useEffect } from 'react';
import { DashboardLayout } from "@/app/publisher/components/layout/dashboard-layout";
import { ProfileHeader } from './components/ProfileHeader';
import { PersonalInfoSection } from './components/PersonalInfoSection';
import { AccountSettingsSection } from './components/AccountSettingsSection';
import { NotificationPreferencesSection } from './components/NotificationPreferencesSection';
import { PrivacySettingsSection } from './components/PrivacySettingsSection';
import { PublisherSettingsSection } from './components/PublisherSettingsSection';
import { PaymentInfoSection } from './components/PaymentInfoSection';
import { SecuritySection } from './components/SecuritySection';
import { DangerZoneSection } from './components/DangerZoneSection';
import { ProfileSidebar } from './components/ProfileSidebar';
import { useProfileData } from './hooks/useProfileData';
import { ProfileProvider } from './context/ProfileContext';

export default function PublisherProfilePage() {
  const [activeSection, setActiveSection] = useState('personal');
  const [isMobile, setIsMobile] = useState(false);
  const { profileData, loading, error, updateProfile } = useProfileData();

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);



  const renderActiveSection = () => {
    switch (activeSection) {
      case 'personal':
        return <PersonalInfoSection />;
      case 'account':
        return <AccountSettingsSection />;
      case 'notifications':
        return <NotificationPreferencesSection />;
      case 'privacy':
        return <PrivacySettingsSection />;
      case 'publisher':
        return <PublisherSettingsSection />;
      case 'payment':
        return <PaymentInfoSection />;
      case 'security':
        return <SecuritySection />;
      case 'danger':
        return <DangerZoneSection />;
      default:
        return <PersonalInfoSection />;
    }
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
        </div>
      </DashboardLayout>
    );
  }

  if (error) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <p className="text-red-600 mb-4">Profil bilgileri yüklenirken bir hata oluştu.</p>
            <button 
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            >
              Tekrar Dene
            </button>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <ProfileProvider value={{ profileData, updateProfile, loading, error }}>
      <DashboardLayout>
        <div className="space-y-6">
          {/* Profile Header */}
          <div>
            <ProfileHeader />
          </div>

          {/* Main Content */}
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            {/* Sidebar */}
            <div className="lg:col-span-1">
              <ProfileSidebar
                activeSection={activeSection}
                onSectionChange={setActiveSection}
                isMobile={isMobile}
              />
            </div>

            {/* Content Area */}
            <div className="lg:col-span-3">
              <div
                key={activeSection}
                className="min-h-[600px]"
              >
                {renderActiveSection()}
              </div>
            </div>
          </div>
        </div>
      </DashboardLayout>
    </ProfileProvider>
  );
}
