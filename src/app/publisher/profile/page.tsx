"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { DashboardLayout } from "@/app/publisher/components/layout/dashboard-layout";
import { ProfileHeader } from './components/ProfileHeader';
import { PersonalInfoSection } from './components/PersonalInfoSection';
import { AccountSettingsSection } from './components/AccountSettingsSection';
import { NotificationPreferencesSection } from './components/NotificationPreferencesSection';
import { PrivacySettingsSection } from './components/PrivacySettingsSection';
import { PublisherSettingsSection } from './components/PublisherSettingsSection';
import { PaymentInfoSection } from './components/PaymentInfoSection';
import { SecuritySection } from './components/SecuritySection';
import { DangerZoneSection } from './components/DangerZoneSection';
import { ProfileSidebar } from './components/ProfileSidebar';
import { useProfileData } from './hooks/useProfileData';
import { ProfileProvider } from './context/ProfileContext';

export default function PublisherProfilePage() {
  const [activeSection, setActiveSection] = useState('personal');
  const [isMobile, setIsMobile] = useState(false);
  const { profileData, loading, error, updateProfile } = useProfileData();

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024);
    };
    
    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  };

  const renderActiveSection = () => {
    switch (activeSection) {
      case 'personal':
        return <PersonalInfoSection />;
      case 'account':
        return <AccountSettingsSection />;
      case 'notifications':
        return <NotificationPreferencesSection />;
      case 'privacy':
        return <PrivacySettingsSection />;
      case 'publisher':
        return <PublisherSettingsSection />;
      case 'payment':
        return <PaymentInfoSection />;
      case 'security':
        return <SecuritySection />;
      case 'danger':
        return <DangerZoneSection />;
      default:
        return <PersonalInfoSection />;
    }
  };

  if (loading) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[60vh]">
          <motion.div
            animate={{ rotate: 360 }}
            transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full"
          />
        </div>
      </DashboardLayout>
    );
  }

  if (error) {
    return (
      <DashboardLayout>
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <p className="text-red-600 mb-4">Profil bilgileri yüklenirken bir hata oluştu.</p>
            <button 
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            >
              Tekrar Dene
            </button>
          </div>
        </div>
      </DashboardLayout>
    );
  }

  return (
    <ProfileProvider value={{ profileData, updateProfile, loading, error }}>
      <DashboardLayout>
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="space-y-6"
        >
          {/* Profile Header */}
          <motion.div variants={itemVariants}>
            <ProfileHeader />
          </motion.div>

          {/* Main Content */}
          <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
            {/* Sidebar */}
            <motion.div 
              variants={itemVariants}
              className="lg:col-span-1"
            >
              <ProfileSidebar 
                activeSection={activeSection}
                onSectionChange={setActiveSection}
                isMobile={isMobile}
              />
            </motion.div>

            {/* Content Area */}
            <motion.div 
              variants={itemVariants}
              className="lg:col-span-3"
            >
              <motion.div
                key={activeSection}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.3 }}
                className="min-h-[600px]"
              >
                {renderActiveSection()}
              </motion.div>
            </motion.div>
          </div>
        </motion.div>
      </DashboardLayout>
    </ProfileProvider>
  );
}
