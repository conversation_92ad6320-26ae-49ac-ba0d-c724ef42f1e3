"use client";

import React, { useState } from 'react';
import { Lock, Shield, Smartphone, Monitor, MapPin, Clock, Key } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { useProfile } from '../context/ProfileContext';
import { toast } from '@/components/ui/use-toast';

export function SecuritySection() {
  const { profileData, updateProfile, loading } = useProfile();
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(profileData?.twoFactorEnabled || false);

  const handleTwoFactorToggle = async (enabled: boolean) => {
    try {
      await updateProfile({ twoFactorEnabled: enabled });
      setTwoFactorEnabled(enabled);
      toast({
        title: enabled ? "2FA Etkinleştirildi" : "2FA Devre Dışı Bırakıldı",
        description: enabled 
          ? "İki faktörlü doğrulama başarıyla etkinleştirildi."
          : "İki faktörlü doğrulama devre dışı bırakıldı.",
        className: "bg-green-50 border-green-200 text-green-800"
      });
    } catch (error) {
      toast({
        title: "Hata",
        description: "Ayar güncellenirken bir hata oluştu.",
        className: "bg-red-50 border-red-200 text-red-800"
      });
    }
  };

  const handleTerminateSession = (sessionId: string) => {
    toast({
      title: "Oturum Sonlandırıldı",
      description: "Seçilen oturum başarıyla sonlandırıldı.",
      className: "bg-green-50 border-green-200 text-green-800"
    });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('tr-TR');
  };

  return (
    <div className="space-y-6">
      {/* Password & 2FA */}
      <div>
        <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Lock className="w-5 h-5 text-blue-600" />
              Şifre ve Güvenlik
            </CardTitle>
          </CardHeader>
          
          <CardContent className="space-y-6">
            {/* Password */}
            <div className="flex items-center justify-between p-4 bg-slate-50 rounded-lg">
              <div className="flex items-center gap-3">
                <Key className="w-5 h-5 text-slate-500" />
                <div>
                  <h4 className="font-medium text-slate-900">Şifre</h4>
                  <p className="text-sm text-slate-500">
                    Son değişiklik: {profileData?.security.lastPasswordChange && formatDate(profileData.security.lastPasswordChange)}
                  </p>
                </div>
              </div>
              <Button variant="outline" size="sm">
                Şifre Değiştir
              </Button>
            </div>

            {/* Two Factor Authentication */}
            <div className="flex items-center justify-between p-4 bg-slate-50 rounded-lg">
              <div className="flex items-center gap-3">
                <Smartphone className="w-5 h-5 text-slate-500" />
                <div>
                  <h4 className="font-medium text-slate-900">İki Faktörlü Doğrulama</h4>
                  <p className="text-sm text-slate-500">
                    Hesabınız için ek güvenlik katmanı
                  </p>
                </div>
              </div>
              <Switch
                checked={twoFactorEnabled}
                onCheckedChange={handleTwoFactorToggle}
                disabled={loading}
              />
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Active Sessions */}
      <div>
        <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Monitor className="w-5 h-5 text-blue-600" />
              Aktif Oturumlar
            </CardTitle>
          </CardHeader>
          
          <CardContent className="space-y-4">
            {profileData?.security.activeSessions.map((session) => (
              <motion.div
                key={session.id}
                variants={itemVariants}
                className="flex items-center justify-between p-4 bg-slate-50 rounded-lg"
              >
                <div className="flex items-center gap-3">
                  <Monitor className="w-5 h-5 text-slate-500" />
                  <div>
                    <div className="flex items-center gap-2">
                      <h4 className="font-medium text-slate-900">{session.device}</h4>
                      {session.current && (
                        <Badge variant="secondary" className="bg-green-100 text-green-800">
                          Mevcut
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center gap-4 text-sm text-slate-500 mt-1">
                      <div className="flex items-center gap-1">
                        <MapPin className="w-3 h-3" />
                        <span>{session.location}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="w-3 h-3" />
                        <span>Son aktivite: {formatDate(session.lastActive)}</span>
                      </div>
                    </div>
                  </div>
                </div>
                {!session.current && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleTerminateSession(session.id)}
                    className="text-red-600 border-red-200 hover:bg-red-50"
                  >
                    Sonlandır
                  </Button>
                )}
              </motion.div>
            ))}
          </CardContent>
        </Card>
      </motion.div>

      {/* Login History */}
      <motion.div variants={itemVariants}>
        <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="w-5 h-5 text-blue-600" />
              Giriş Geçmişi
            </CardTitle>
          </CardHeader>
          
          <CardContent className="space-y-4">
            {profileData?.security.loginHistory.slice(0, 5).map((login, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className="flex items-center justify-between p-4 bg-slate-50 rounded-lg"
              >
                <div className="flex items-center gap-3">
                  <div className={`w-3 h-3 rounded-full ${
                    login.success ? 'bg-green-500' : 'bg-red-500'
                  }`} />
                  <div>
                    <div className="flex items-center gap-2">
                      <span className="font-medium text-slate-900">{login.device}</span>
                      <Badge variant={login.success ? "secondary" : "destructive"} className="text-xs">
                        {login.success ? 'Başarılı' : 'Başarısız'}
                      </Badge>
                    </div>
                    <div className="flex items-center gap-4 text-sm text-slate-500 mt-1">
                      <div className="flex items-center gap-1">
                        <MapPin className="w-3 h-3" />
                        <span>{login.location}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="w-3 h-3" />
                        <span>{formatDate(login.date)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </motion.div>
            ))}
          </CardContent>
        </Card>
      </motion.div>

      {/* Security Tips */}
      <motion.div variants={itemVariants}>
        <Card className="bg-gradient-to-br from-blue-50 to-purple-50 border-blue-200">
          <CardContent className="p-6">
            <div className="flex items-start gap-3">
              <Shield className="w-6 h-6 text-blue-600 mt-1" />
              <div>
                <h4 className="font-medium text-blue-900 mb-2">Güvenlik İpuçları</h4>
                <ul className="text-sm text-blue-700 space-y-1">
                  <li>• Güçlü ve benzersiz şifreler kullanın</li>
                  <li>• İki faktörlü doğrulamayı etkinleştirin</li>
                  <li>• Şüpheli aktiviteleri hemen bildirin</li>
                  <li>• Düzenli olarak oturum geçmişinizi kontrol edin</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </motion.div>
  );
}
