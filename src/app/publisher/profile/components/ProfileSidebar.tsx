"use client";

import React from 'react';
import { motion } from 'framer-motion';
import {
  User,
  Settings,
  Bell,
  Shield,
  Briefcase,
  CreditCard,
  Lock,
  AlertTriangle,
  ChevronRight
} from 'lucide-react';
import { Card } from '@/components/ui/card';

interface ProfileSidebarProps {
  activeSection: string;
  onSectionChange: (section: string) => void;
  isMobile: boolean;
}

const sidebarItems = [
  {
    id: 'personal',
    label: 'Kişisel Bilgiler',
    icon: User,
    description: 'Ad, soyad, iletişim bilgileri'
  },
  {
    id: 'account',
    label: 'Hesap <PERSON>',
    icon: Settings,
    description: 'Dil, zaman dilimi, tercihler'
  },
  {
    id: 'notifications',
    label: 'Bildirimler',
    icon: Bell,
    description: 'E-posta, push, SMS bildirimleri'
  },
  {
    id: 'privacy',
    label: 'Gizlilik',
    icon: Shield,
    description: '<PERSON>il <PERSON>, veri i<PERSON>'
  },
  {
    id: 'publisher',
    label: '<PERSON><PERSON><PERSON><PERSON><PERSON> A<PERSON>ları',
    icon: Briefcase,
    description: '<PERSON>ş bilgileri, içerik tercihleri'
  },
  {
    id: 'payment',
    label: 'Ödeme Bilgileri',
    icon: CreditCard,
    description: 'Banka hesabı, vergi bilgileri'
  },
  {
    id: 'security',
    label: 'Güvenlik',
    icon: Lock,
    description: 'Şifre, 2FA, oturum yönetimi'
  },
  {
    id: 'danger',
    label: 'Tehlikeli Bölge',
    icon: AlertTriangle,
    description: 'Hesap silme, veri dışa aktarma'
  }
];

export function ProfileSidebar({ activeSection, onSectionChange, isMobile }: ProfileSidebarProps) {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.05
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, x: -20 },
    visible: {
      opacity: 1,
      x: 0,
      transition: { duration: 0.3 }
    }
  };

  if (isMobile) {
    return (
      <Card className="p-4 bg-white/80 backdrop-blur-sm border-0 shadow-lg">
        <div className="flex overflow-x-auto gap-2 pb-2">
          {sidebarItems.map((item) => {
            const Icon = item.icon;
            const isActive = activeSection === item.id;
            
            return (
              <motion.button
                key={item.id}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => onSectionChange(item.id)}
                className={`flex-shrink-0 flex items-center gap-2 px-4 py-2 rounded-xl transition-all duration-300 ${
                  isActive
                    ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg'
                    : 'bg-slate-100 text-slate-600 hover:bg-slate-200'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span className="text-sm font-medium whitespace-nowrap">{item.label}</span>
              </motion.button>
            );
          })}
        </div>
      </Card>
    );
  }

  return (
    <Card className="p-6 bg-white/80 backdrop-blur-sm border-0 shadow-lg sticky top-6">
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="space-y-2"
      >
        <h3 className="text-lg font-semibold text-slate-900 mb-4">Profil Ayarları</h3>
        
        {sidebarItems.map((item) => {
          const Icon = item.icon;
          const isActive = activeSection === item.id;
          
          return (
            <motion.div key={item.id} variants={itemVariants}>
              <motion.button
                whileHover={{ scale: 1.02, x: 4 }}
                whileTap={{ scale: 0.98 }}
                onClick={() => onSectionChange(item.id)}
                className={`w-full flex items-center gap-3 p-4 rounded-xl transition-all duration-300 group ${
                  isActive
                    ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg'
                    : 'text-slate-600 hover:bg-slate-50 hover:text-slate-900'
                }`}
              >
                <div className={`p-2 rounded-lg transition-colors duration-300 ${
                  isActive
                    ? 'bg-white/20'
                    : 'bg-slate-100 group-hover:bg-slate-200'
                }`}>
                  <Icon className="w-4 h-4" />
                </div>
                
                <div className="flex-1 text-left">
                  <div className="font-medium">{item.label}</div>
                  <div className={`text-xs mt-0.5 ${
                    isActive ? 'text-white/80' : 'text-slate-500'
                  }`}>
                    {item.description}
                  </div>
                </div>
                
                <ChevronRight className={`w-4 h-4 transition-transform duration-300 ${
                  isActive ? 'rotate-90' : 'group-hover:translate-x-1'
                }`} />
              </motion.button>
            </motion.div>
          );
        })}
      </motion.div>
    </Card>
  );
}
