"use client";

import React, { useState } from 'react';
import { <PERSON>, Eye, EyeOff, Save, Edit3 } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useProfile } from '../context/ProfileContext';
import { toast } from '@/components/ui/use-toast';

export function PrivacySettingsSection() {
  const { profileData, updateProfile, loading } = useProfile();
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    privacy: profileData?.privacy || {
      profileVisibility: 'public' as 'public' | 'private' | 'contacts',
      showEmail: false,
      showPhone: false,
      allowMessages: true,
      dataProcessing: true,
    }
  });

  const handleSwitchChange = (key: string, value: boolean) => {
    setFormData(prev => ({
      ...prev,
      privacy: {
        ...prev.privacy,
        [key]: value
      }
    }));
  };

  const handleSelectChange = (key: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      privacy: {
        ...prev.privacy,
        [key]: value
      }
    }));
  };

  const handleSave = async () => {
    try {
      await updateProfile(formData);
      setIsEditing(false);
      toast({
        title: "Başarılı",
        description: "Gizlilik ayarlarınız güncellendi.",
        className: "bg-green-50 border-green-200 text-green-800"
      });
    } catch (error) {
      toast({
        title: "Hata",
        description: "Ayarlar güncellenirken bir hata oluştu.",
        className: "bg-red-50 border-red-200 text-red-800"
      });
    }
  };

  const handleCancel = () => {
    setFormData({
      privacy: profileData?.privacy || {
        profileVisibility: 'public',
        showEmail: false,
        showPhone: false,
        allowMessages: true,
        dataProcessing: true,
      }
    });
    setIsEditing(false);
  };

  return (
    <div className="space-y-6">
      <div>
        <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Shield className="w-5 h-5 text-blue-600" />
              Gizlilik Ayarları
            </CardTitle>
            
            {!isEditing ? (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsEditing(true)}
                className="hover:bg-blue-50 hover:border-blue-300"
              >
                <Edit3 className="w-4 h-4 mr-2" />
                Düzenle
              </Button>
            ) : (
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCancel}
                  disabled={loading}
                >
                  İptal
                </Button>
                <Button
                  size="sm"
                  onClick={handleSave}
                  disabled={loading}
                  className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
                >
                  <Save className="w-4 h-4 mr-2" />
                  {loading ? 'Kaydediliyor...' : 'Kaydet'}
                </Button>
              </div>
            )}
          </CardHeader>
          
          <CardContent className="space-y-6">
            {/* Profile Visibility */}
            <div className="space-y-3">
              <Label className="flex items-center gap-2">
                <Eye className="w-4 h-4 text-slate-500" />
                Profil Görünürlüğü
              </Label>
              <Select
                value={formData.privacy.profileVisibility}
                onValueChange={(value) => handleSelectChange('profileVisibility', value)}
                disabled={!isEditing}
              >
                <SelectTrigger className="bg-slate-50 border-slate-200 focus:bg-white">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="public">
                    <div className="flex items-center gap-2">
                      <Eye className="w-4 h-4" />
                      <div>
                        <div className="font-medium">Herkese Açık</div>
                        <div className="text-xs text-slate-500">Profiliniz herkese görünür</div>
                      </div>
                    </div>
                  </SelectItem>
                  <SelectItem value="contacts">
                    <div className="flex items-center gap-2">
                      <Shield className="w-4 h-4" />
                      <div>
                        <div className="font-medium">Sadece Bağlantılar</div>
                        <div className="text-xs text-slate-500">Sadece bağlantılarınız görebilir</div>
                      </div>
                    </div>
                  </SelectItem>
                  <SelectItem value="private">
                    <div className="flex items-center gap-2">
                      <EyeOff className="w-4 h-4" />
                      <div>
                        <div className="font-medium">Gizli</div>
                        <div className="text-xs text-slate-500">Profiliniz gizli</div>
                      </div>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Contact Information Visibility */}
            <div className="space-y-4">
              <h4 className="font-medium text-slate-900 border-b border-slate-200 pb-2">
                İletişim Bilgileri Görünürlüğü
              </h4>
              
              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                  <div>
                    <Label className="text-sm font-medium text-slate-900">E-posta Adresini Göster</Label>
                    <p className="text-xs text-slate-500 mt-1">
                      E-posta adresiniz profil sayfanızda görünür olur
                    </p>
                  </div>
                  <Switch
                    checked={formData.privacy.showEmail}
                    onCheckedChange={(checked) => handleSwitchChange('showEmail', checked)}
                    disabled={!isEditing}
                  />
                </div>

                <div className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                  <div>
                    <Label className="text-sm font-medium text-slate-900">Telefon Numarasını Göster</Label>
                    <p className="text-xs text-slate-500 mt-1">
                      Telefon numaranız profil sayfanızda görünür olur
                    </p>
                  </div>
                  <Switch
                    checked={formData.privacy.showPhone}
                    onCheckedChange={(checked) => handleSwitchChange('showPhone', checked)}
                    disabled={!isEditing}
                  />
                </div>
              </div>
            </div>

            {/* Communication Preferences */}
            <div className="space-y-4">
              <h4 className="font-medium text-slate-900 border-b border-slate-200 pb-2">
                İletişim Tercihleri
              </h4>
              
              <div className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                <div>
                  <Label className="text-sm font-medium text-slate-900">Mesaj Almaya İzin Ver</Label>
                  <p className="text-xs text-slate-500 mt-1">
                    Diğer kullanıcılar size mesaj gönderebilir
                  </p>
                </div>
                <Switch
                  checked={formData.privacy.allowMessages}
                  onCheckedChange={(checked) => handleSwitchChange('allowMessages', checked)}
                  disabled={!isEditing}
                />
              </div>
            </div>

            {/* Data Processing */}
            <div className="space-y-4">
              <h4 className="font-medium text-slate-900 border-b border-slate-200 pb-2">
                Veri İşleme
              </h4>
              
              <div className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                <div>
                  <Label className="text-sm font-medium text-slate-900">Veri İşleme İzni</Label>
                  <p className="text-xs text-slate-500 mt-1">
                    Hizmet kalitesini artırmak için verilerinizin işlenmesine izin verin
                  </p>
                </div>
                <Switch
                  checked={formData.privacy.dataProcessing}
                  onCheckedChange={(checked) => handleSwitchChange('dataProcessing', checked)}
                  disabled={!isEditing}
                />
              </div>
            </div>

            {/* Privacy Notice */}
            <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-start gap-3">
                <Shield className="w-5 h-5 text-blue-600 mt-0.5" />
                <div>
                  <h5 className="font-medium text-blue-900 mb-1">Gizlilik Politikası</h5>
                  <p className="text-sm text-blue-700">
                    Verilerinizin nasıl toplandığı, kullanıldığı ve korunduğu hakkında daha fazla bilgi için{' '}
                    <a href="/privacy" className="underline hover:no-underline">
                      Gizlilik Politikamızı
                    </a>{' '}
                    inceleyebilirsiniz.
                  </p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
