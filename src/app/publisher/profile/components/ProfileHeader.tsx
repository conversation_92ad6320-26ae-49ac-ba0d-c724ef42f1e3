"use client";

import React, { useState } from 'react';
import { Camera, MapPin, Globe, Calendar, Verified, Shield } from 'lucide-react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useProfile } from '../context/ProfileContext';

export function ProfileHeader() {
  const { profileData } = useProfile();
  const [showAvatarModal, setShowAvatarModal] = useState(false);

  if (!profileData) return null;

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('tr-TR', {
      year: 'numeric',
      month: 'long',
    });
  };

  return (
    <>
      <Card className="relative overflow-hidden bg-gradient-to-br from-white via-blue-50/30 to-purple-50/30 border-0 shadow-xl">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.4'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
          }} />
        </div>

        {/* Floating Decorative Elements */}
        <div className="absolute top-8 right-8 w-3 h-3 rounded-full bg-blue-400/40" />
        <div className="absolute bottom-12 right-16 w-2 h-2 rounded-full bg-purple-400/40" />

        <div className="relative z-10 p-8">
          <div className="flex flex-col lg:flex-row lg:items-center gap-6">
            {/* Avatar Section */}
            <div className="relative group">
              <div className="relative">
                <div className="w-32 h-32 rounded-2xl overflow-hidden bg-gradient-to-br from-blue-100 to-purple-100 shadow-lg hover:scale-105 transition-transform duration-300">
                  {profileData.avatar ? (
                    <img
                      src={profileData.avatar}
                      alt={`${profileData.firstName} ${profileData.lastName}`}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center text-4xl font-bold text-blue-600">
                      {profileData.firstName[0]}{profileData.lastName[0]}
                    </div>
                  )}
                </div>

                {/* Camera Overlay */}
                <button
                  onClick={() => setShowAvatarModal(true)}
                  className="absolute inset-0 bg-black/50 rounded-2xl flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300 hover:scale-110"
                >
                  <Camera className="w-8 h-8 text-white" />
                </button>
              </div>

              {/* Verification Badge */}
              {profileData.emailVerified && (
                <div className="absolute -bottom-2 -right-2 w-10 h-10 bg-green-500 rounded-full flex items-center justify-center shadow-lg">
                  <Verified className="w-5 h-5 text-white" />
                </div>
              )}
            </div>

            {/* Profile Info */}
            <div className="flex-1 space-y-4">
              <div>
                <h1 className="text-3xl font-bold text-slate-900 mb-2">
                  {profileData.firstName} {profileData.lastName}
                </h1>
                
                <div className="flex flex-wrap items-center gap-3 mb-3">
                  <Badge variant="secondary" className="bg-blue-100 text-blue-800 hover:bg-blue-200">
                    <Shield className="w-3 h-3 mr-1" />
                    Publisher
                  </Badge>
                  
                  {profileData.emailVerified && (
                    <Badge variant="secondary" className="bg-green-100 text-green-800 hover:bg-green-200">
                      <Verified className="w-3 h-3 mr-1" />
                      Doğrulanmış
                    </Badge>
                  )}
                  
                  {profileData.twoFactorEnabled && (
                    <Badge variant="secondary" className="bg-purple-100 text-purple-800 hover:bg-purple-200">
                      <Shield className="w-3 h-3 mr-1" />
                      2FA Aktif
                    </Badge>
                  )}
                </div>

                {profileData.bio && (
                  <p className="text-slate-600 text-lg leading-relaxed max-w-2xl">
                    {profileData.bio}
                  </p>
                )}
              </div>

              {/* Meta Information */}
              <div className="flex flex-wrap items-center gap-6 text-sm text-slate-500">
                {profileData.location && (
                  <div className="flex items-center gap-1">
                    <MapPin className="w-4 h-4" />
                    <span>{profileData.location}</span>
                  </div>
                )}
                
                {profileData.website && (
                  <div className="flex items-center gap-1">
                    <Globe className="w-4 h-4" />
                    <a 
                      href={profileData.website} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:text-blue-800 hover:underline"
                    >
                      Website
                    </a>
                  </div>
                )}
                
                <div className="flex items-center gap-1">
                  <Calendar className="w-4 h-4" />
                  <span>{formatDate(profileData.createdAt)} tarihinde katıldı</span>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row lg:flex-col gap-3">
              <Button
                variant="outline"
                className="whitespace-nowrap hover:bg-blue-50 hover:border-blue-300 transition-all duration-300"
              >
                Profili Görüntüle
              </Button>

              <Button
                className="whitespace-nowrap bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 transition-all duration-300"
              >
                Profili Paylaş
              </Button>
            </div>
          </div>
        </div>
      </Card>

      
    </>
  );
}
