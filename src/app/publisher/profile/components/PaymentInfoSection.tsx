"use client";

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { CreditCard, Building, Hash, Save, Edit3, Plus } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useProfile } from '../context/ProfileContext';
import { toast } from '@/components/ui/use-toast';

export function PaymentInfoSection() {
  const { profileData, updateProfile, loading } = useProfile();
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    payment: profileData?.payment || {
      preferredMethod: 'bank' as 'bank' | 'paypal' | 'crypto',
      bankAccount: {
        accountName: '',
        accountNumber: '',
        bankName: '',
        iban: '',
        swift: '',
      },
      taxInfo: {
        country: 'TR',
        taxNumber: '',
      },
    }
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    const [section, field] = name.split('.');
    
    setFormData(prev => ({
      ...prev,
      payment: {
        ...prev.payment,
        [section]: {
          ...((prev.payment[section as keyof typeof prev.payment] as object) || {}),
          [field]: value
        }
      }
    }));
  };

  const handleSave = async () => {
    try {
      await updateProfile(formData);
      setIsEditing(false);
      toast({
        title: "Başarılı",
        description: "Ödeme bilgileriniz güncellendi.",
        className: "bg-green-50 border-green-200 text-green-800"
      });
    } catch (error) {
      toast({
        title: "Hata",
        description: "Bilgiler güncellenirken bir hata oluştu.",
        className: "bg-red-50 border-red-200 text-red-800"
      });
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="space-y-6"
    >
      <motion.div variants={itemVariants}>
        <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <CreditCard className="w-5 h-5 text-blue-600" />
              Ödeme Bilgileri
            </CardTitle>
            
            {!isEditing ? (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsEditing(true)}
                className="hover:bg-blue-50 hover:border-blue-300"
              >
                <Edit3 className="w-4 h-4 mr-2" />
                Düzenle
              </Button>
            ) : (
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsEditing(false)}
                  disabled={loading}
                >
                  İptal
                </Button>
                <Button
                  size="sm"
                  onClick={handleSave}
                  disabled={loading}
                  className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
                >
                  <Save className="w-4 h-4 mr-2" />
                  {loading ? 'Kaydediliyor...' : 'Kaydet'}
                </Button>
              </div>
            )}
          </CardHeader>
          
          <CardContent className="space-y-6">
            {/* Bank Account Information */}
            <motion.div variants={itemVariants} className="space-y-4">
              <h4 className="font-medium text-slate-900 border-b border-slate-200 pb-2">
                Banka Hesap Bilgileri
              </h4>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="bankAccount.accountName">Hesap Sahibi</Label>
                  <Input
                    id="bankAccount.accountName"
                    name="bankAccount.accountName"
                    value={formData.payment.bankAccount?.accountName || ''}
                    onChange={handleInputChange}
                    disabled={!isEditing}
                    className="bg-slate-50 border-slate-200 focus:bg-white"
                    placeholder="Ad Soyad"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="bankAccount.bankName">Banka Adı</Label>
                  <div className="relative">
                    <Building className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
                    <Input
                      id="bankAccount.bankName"
                      name="bankAccount.bankName"
                      value={formData.payment.bankAccount?.bankName || ''}
                      onChange={handleInputChange}
                      disabled={!isEditing}
                      className="pl-10 bg-slate-50 border-slate-200 focus:bg-white"
                      placeholder="Banka adı"
                    />
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="bankAccount.iban">IBAN</Label>
                <Input
                  id="bankAccount.iban"
                  name="bankAccount.iban"
                  value={formData.payment.bankAccount?.iban || ''}
                  onChange={handleInputChange}
                  disabled={!isEditing}
                  className="bg-slate-50 border-slate-200 focus:bg-white font-mono"
                  placeholder="TR00 0000 0000 0000 0000 0000 00"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="bankAccount.accountNumber">Hesap Numarası</Label>
                  <Input
                    id="bankAccount.accountNumber"
                    name="bankAccount.accountNumber"
                    value={formData.payment.bankAccount?.accountNumber || ''}
                    onChange={handleInputChange}
                    disabled={!isEditing}
                    className="bg-slate-50 border-slate-200 focus:bg-white font-mono"
                    placeholder="**********"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="bankAccount.swift">SWIFT Kodu</Label>
                  <Input
                    id="bankAccount.swift"
                    name="bankAccount.swift"
                    value={formData.payment.bankAccount?.swift || ''}
                    onChange={handleInputChange}
                    disabled={!isEditing}
                    className="bg-slate-50 border-slate-200 focus:bg-white"
                    placeholder="SWIFT"
                  />
                </div>
              </div>
            </motion.div>

            {/* Tax Information */}
            <motion.div variants={itemVariants} className="space-y-4">
              <h4 className="font-medium text-slate-900 border-b border-slate-200 pb-2">
                Vergi Bilgileri
              </h4>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label>Ülke</Label>
                  <Select
                    value={formData.payment.taxInfo.country}
                    onValueChange={(value) => setFormData(prev => ({
                      ...prev,
                      payment: {
                        ...prev.payment,
                        taxInfo: {
                          ...prev.payment.taxInfo,
                          country: value
                        }
                      }
                    }))}
                    disabled={!isEditing}
                  >
                    <SelectTrigger className="bg-slate-50 border-slate-200 focus:bg-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="TR">Türkiye</SelectItem>
                      <SelectItem value="US">Amerika Birleşik Devletleri</SelectItem>
                      <SelectItem value="DE">Almanya</SelectItem>
                      <SelectItem value="FR">Fransa</SelectItem>
                      <SelectItem value="GB">Birleşik Krallık</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="taxInfo.taxNumber">Vergi Numarası</Label>
                  <div className="relative">
                    <Hash className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
                    <Input
                      id="taxInfo.taxNumber"
                      name="taxInfo.taxNumber"
                      value={formData.payment.taxInfo.taxNumber || ''}
                      onChange={handleInputChange}
                      disabled={!isEditing}
                      className="pl-10 bg-slate-50 border-slate-200 focus:bg-white"
                      placeholder="**********1"
                    />
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Security Notice */}
            <motion.div variants={itemVariants} className="p-4 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-start gap-3">
                <CreditCard className="w-5 h-5 text-blue-600 mt-0.5" />
                <div>
                  <h5 className="font-medium text-blue-900 mb-1">Güvenlik Bildirimi</h5>
                  <p className="text-sm text-blue-700">
                    Ödeme bilgileriniz güvenli bir şekilde şifrelenir ve saklanır. 
                    Bu bilgiler sadece ödeme işlemleri için kullanılır ve üçüncü taraflarla paylaşılmaz.
                  </p>
                </div>
              </div>
            </motion.div>
          </CardContent>
        </Card>
      </motion.div>
    </motion.div>
  );
}
