"use client";

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { AlertTriangle, Download, Trash2, X } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useProfile } from '../context/ProfileContext';
import { toast } from '@/components/ui/use-toast';

export function DangerZoneSection() {
  const { profileData } = useProfile();
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deleteConfirmation, setDeleteConfirmation] = useState('');
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDataExport = async () => {
    try {
      // Simulate data export
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Create a mock data export
      const exportData = {
        profile: profileData,
        exportDate: new Date().toISOString(),
        version: '1.0'
      };
      
      const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `adnomio-profile-export-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
      
      toast({
        title: "Veri Dışa Aktarıldı",
        description: "Profil verileriniz başarıyla indirildi.",
        className: "bg-green-50 border-green-200 text-green-800"
      });
    } catch (error) {
      toast({
        title: "Hata",
        description: "Veri dışa aktarılırken bir hata oluştu.",
        className: "bg-red-50 border-red-200 text-red-800"
      });
    }
  };

  const handleAccountDelete = async () => {
    if (deleteConfirmation !== 'DELETE') {
      toast({
        title: "Hata",
        description: "Lütfen 'DELETE' yazarak onaylayın.",
        className: "bg-red-50 border-red-200 text-red-800"
      });
      return;
    }

    setIsDeleting(true);
    try {
      // Simulate account deletion
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      toast({
        title: "Hesap Silindi",
        description: "Hesabınız başarıyla silindi. Yönlendiriliyorsunuz...",
        className: "bg-green-50 border-green-200 text-green-800"
      });
      
      // In a real app, redirect to login or home page
      setTimeout(() => {
        window.location.href = '/';
      }, 2000);
    } catch (error) {
      toast({
        title: "Hata",
        description: "Hesap silinirken bir hata oluştu.",
        className: "bg-red-50 border-red-200 text-red-800"
      });
    } finally {
      setIsDeleting(false);
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  };

  return (
    <>
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        className="space-y-6"
      >
        {/* Data Export */}
        <motion.div variants={itemVariants}>
          <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Download className="w-5 h-5 text-blue-600" />
                Veri Dışa Aktarma
              </CardTitle>
            </CardHeader>
            
            <CardContent className="space-y-4">
              <p className="text-slate-600">
                Profil verilerinizi JSON formatında indirebilirsiniz. Bu dosya tüm kişisel bilgilerinizi, 
                ayarlarınızı ve hesap geçmişinizi içerir.
              </p>
              
              <Button
                onClick={handleDataExport}
                variant="outline"
                className="hover:bg-blue-50 hover:border-blue-300"
              >
                <Download className="w-4 h-4 mr-2" />
                Verilerimi İndir
              </Button>
            </CardContent>
          </Card>
        </motion.div>

        {/* Account Deletion */}
        <motion.div variants={itemVariants}>
          <Card className="bg-red-50/80 backdrop-blur-sm border-red-200 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-red-700">
                <AlertTriangle className="w-5 h-5" />
                Tehlikeli Bölge
              </CardTitle>
            </CardHeader>
            
            <CardContent className="space-y-4">
              <div className="p-4 bg-red-100 rounded-lg border border-red-200">
                <h4 className="font-medium text-red-900 mb-2">Hesabı Sil</h4>
                <p className="text-sm text-red-700 mb-4">
                  Bu işlem geri alınamaz. Hesabınızı sildiğinizde:
                </p>
                <ul className="text-sm text-red-700 space-y-1 mb-4">
                  <li>• Tüm profil bilgileriniz kalıcı olarak silinir</li>
                  <li>• Aktif kampanyalarınız iptal edilir</li>
                  <li>• Ödeme geçmişiniz ve kazançlarınız silinir</li>
                  <li>• Bu işlem geri alınamaz</li>
                </ul>
                
                <Button
                  onClick={() => setShowDeleteModal(true)}
                  variant="destructive"
                  className="bg-red-600 hover:bg-red-700"
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  Hesabımı Sil
                </Button>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </motion.div>

      {/* Delete Confirmation Modal */}
      {showDeleteModal && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          onClick={() => setShowDeleteModal(false)}
        >
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            exit={{ scale: 0.9, opacity: 0 }}
            transition={{ type: "spring", stiffness: 300, damping: 30 }}
            className="bg-white rounded-2xl shadow-2xl max-w-md w-full p-6"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-red-700">Hesabı Sil</h2>
              <button
                onClick={() => setShowDeleteModal(false)}
                className="p-2 hover:bg-slate-100 rounded-lg transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <div className="space-y-4">
              <div className="p-4 bg-red-50 rounded-lg border border-red-200">
                <AlertTriangle className="w-6 h-6 text-red-600 mb-2" />
                <p className="text-sm text-red-700">
                  Bu işlem geri alınamaz! Hesabınızı silmek istediğinizden emin misiniz?
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="deleteConfirmation">
                  Onaylamak için <strong>DELETE</strong> yazın:
                </Label>
                <Input
                  id="deleteConfirmation"
                  value={deleteConfirmation}
                  onChange={(e) => setDeleteConfirmation(e.target.value)}
                  placeholder="DELETE"
                  className="font-mono"
                />
              </div>

              <div className="flex gap-3 pt-4">
                <Button
                  variant="outline"
                  onClick={() => setShowDeleteModal(false)}
                  disabled={isDeleting}
                  className="flex-1"
                >
                  İptal
                </Button>
                <Button
                  variant="destructive"
                  onClick={handleAccountDelete}
                  disabled={deleteConfirmation !== 'DELETE' || isDeleting}
                  className="flex-1 bg-red-600 hover:bg-red-700"
                >
                  {isDeleting ? 'Siliniyor...' : 'Hesabı Sil'}
                </Button>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </>
  );
}
