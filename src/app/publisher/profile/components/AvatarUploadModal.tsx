"use client";

import React, { useState, useRef } from 'react';
import { Upload, X, Camera, Trash2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useProfile } from '../context/ProfileContext';
import { toast } from '@/components/ui/use-toast';

interface AvatarUploadModalProps {
  isOpen: boolean;
  onClose: () => void;
  currentAvatar?: string;
}

export function AvatarUploadModal({ isOpen, onClose, currentAvatar }: AvatarUploadModalProps) {
  const { updateProfile, loading } = useProfile();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = (file: File) => {
    if (file.type.startsWith('image/')) {
      setSelectedFile(file);
      const url = URL.createObjectURL(file);
      setPreviewUrl(url);
    } else {
      toast({
        title: "Hata",
        description: "Lütfen geçerli bir resim dosyası seçin.",
        className: "bg-red-50 border-red-200 text-red-800"
      });
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFileSelect(files[0]);
    }
  };

  const handleUpload = async () => {
    if (!selectedFile) return;

    try {
      // In a real app, you would upload the file to a server
      // For now, we'll simulate this with a delay and use the preview URL
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      await updateProfile({ avatar: previewUrl || undefined });
      
      toast({
        title: "Başarılı",
        description: "Profil fotoğrafınız güncellendi.",
        className: "bg-green-50 border-green-200 text-green-800"
      });
      
      handleClose();
    } catch (error) {
      toast({
        title: "Hata",
        description: "Fotoğraf yüklenirken bir hata oluştu.",
        className: "bg-red-50 border-red-200 text-red-800"
      });
    }
  };

  const handleRemove = async () => {
    try {
      await updateProfile({ avatar: undefined });
      
      toast({
        title: "Başarılı",
        description: "Profil fotoğrafınız kaldırıldı.",
        className: "bg-green-50 border-green-200 text-green-800"
      });
      
      handleClose();
    } catch (error) {
      toast({
        title: "Hata",
        description: "Fotoğraf kaldırılırken bir hata oluştu.",
        className: "bg-red-50 border-red-200 text-red-800"
      });
    }
  };

  const handleClose = () => {
    setSelectedFile(null);
    setPreviewUrl(null);
    setIsDragging(false);
    onClose();
  };

  return (
    <>
      {isOpen && (
        <div
          className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          onClick={handleClose}
        >
          <div
            className="bg-white rounded-2xl shadow-2xl max-w-md w-full p-6 transform transition-all duration-300 scale-100"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-semibold text-slate-900">Profil Fotoğrafı</h2>
              <button
                onClick={handleClose}
                className="p-2 hover:bg-slate-100 rounded-lg transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            {/* Current Avatar */}
            {currentAvatar && !previewUrl && (
              <div className="mb-6">
                <p className="text-sm text-slate-600 mb-3">Mevcut fotoğraf:</p>
                <div className="flex justify-center">
                  <img
                    src={currentAvatar}
                    alt="Current avatar"
                    className="w-24 h-24 rounded-full object-cover"
                  />
                </div>
              </div>
            )}

            {/* Preview */}
            {previewUrl && (
              <div className="mb-6">
                <p className="text-sm text-slate-600 mb-3">Önizleme:</p>
                <div className="flex justify-center">
                  <img
                    src={previewUrl}
                    alt="Preview"
                    className="w-24 h-24 rounded-full object-cover"
                  />
                </div>
              </div>
            )}

            {/* Upload Area */}
            <div
              onDrop={handleDrop}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              className={`border-2 border-dashed rounded-xl p-8 text-center transition-colors ${
                isDragging
                  ? 'border-blue-400 bg-blue-50'
                  : 'border-slate-300 hover:border-slate-400'
              }`}
            >
              <Camera className="w-12 h-12 text-slate-400 mx-auto mb-4" />
              <p className="text-slate-600 mb-2">
                Fotoğrafı buraya sürükleyin veya
              </p>
              <Button
                variant="outline"
                onClick={() => fileInputRef.current?.click()}
                className="mb-2"
              >
                <Upload className="w-4 h-4 mr-2" />
                Dosya Seç
              </Button>
              <p className="text-xs text-slate-500">
                PNG, JPG veya GIF (max. 5MB)
              </p>
            </div>

            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleFileInputChange}
              className="hidden"
            />

            {/* Actions */}
            <div className="flex gap-3 mt-6">
              {currentAvatar && (
                <Button
                  variant="outline"
                  onClick={handleRemove}
                  disabled={loading}
                  className="flex-1 text-red-600 border-red-200 hover:bg-red-50"
                >
                  <Trash2 className="w-4 h-4 mr-2" />
                  Kaldır
                </Button>
              )}

              <Button
                onClick={handleUpload}
                disabled={!selectedFile || loading}
                className="flex-1 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
              >
                {loading ? 'Yükleniyor...' : 'Kaydet'}
              </Button>
            </div>
          </div>
        </div>
      )}
    </>
  );
}
