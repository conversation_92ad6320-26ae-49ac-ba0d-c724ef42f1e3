"use client";

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Bell, Mail, Smartphone, MessageSquare, Save, Edit3 } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { useProfile } from '../context/ProfileContext';
import { toast } from '@/components/ui/use-toast';

export function NotificationPreferencesSection() {
  const { profileData, updateProfile, loading } = useProfile();
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    notifications: profileData?.notifications || {
      email: {
        campaigns: true,
        payments: true,
        security: true,
        marketing: false,
      },
      push: {
        campaigns: true,
        payments: true,
        security: true,
      },
      sms: {
        payments: true,
        security: true,
      },
    }
  });

  const handleSwitchChange = (category: string, type: string, value: boolean) => {
    setFormData(prev => ({
      ...prev,
      notifications: {
        ...prev.notifications,
        [category]: {
          ...prev.notifications[category as keyof typeof prev.notifications],
          [type]: value
        }
      }
    }));
  };

  const handleSave = async () => {
    try {
      await updateProfile(formData);
      setIsEditing(false);
      toast({
        title: "Başarılı",
        description: "Bildirim tercihleriniz güncellendi.",
        className: "bg-green-50 border-green-200 text-green-800"
      });
    } catch (error) {
      toast({
        title: "Hata",
        description: "Tercihler güncellenirken bir hata oluştu.",
        className: "bg-red-50 border-red-200 text-red-800"
      });
    }
  };

  const handleCancel = () => {
    setFormData({
      notifications: profileData?.notifications || {
        email: {
          campaigns: true,
          payments: true,
          security: true,
          marketing: false,
        },
        push: {
          campaigns: true,
          payments: true,
          security: true,
        },
        sms: {
          payments: true,
          security: true,
        },
      }
    });
    setIsEditing(false);
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  };

  const NotificationGroup = ({ 
    title, 
    icon: Icon, 
    category, 
    items 
  }: { 
    title: string; 
    icon: any; 
    category: string; 
    items: Array<{ key: string; label: string; description: string }> 
  }) => (
    <motion.div variants={itemVariants} className="space-y-4">
      <div className="flex items-center gap-2 pb-2 border-b border-slate-200">
        <Icon className="w-5 h-5 text-blue-600" />
        <h4 className="font-medium text-slate-900">{title}</h4>
      </div>
      
      <div className="space-y-4">
        {items.map((item) => (
          <div key={item.key} className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
            <div className="flex-1">
              <Label className="text-sm font-medium text-slate-900">{item.label}</Label>
              <p className="text-xs text-slate-500 mt-1">{item.description}</p>
            </div>
            <Switch
              checked={formData.notifications[category as keyof typeof formData.notifications][item.key as keyof any]}
              onCheckedChange={(checked) => handleSwitchChange(category, item.key, checked)}
              disabled={!isEditing}
            />
          </div>
        ))}
      </div>
    </motion.div>
  );

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="space-y-6"
    >
      <motion.div variants={itemVariants}>
        <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Bell className="w-5 h-5 text-blue-600" />
              Bildirim Tercihleri
            </CardTitle>
            
            {!isEditing ? (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsEditing(true)}
                className="hover:bg-blue-50 hover:border-blue-300"
              >
                <Edit3 className="w-4 h-4 mr-2" />
                Düzenle
              </Button>
            ) : (
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCancel}
                  disabled={loading}
                >
                  İptal
                </Button>
                <Button
                  size="sm"
                  onClick={handleSave}
                  disabled={loading}
                  className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
                >
                  <Save className="w-4 h-4 mr-2" />
                  {loading ? 'Kaydediliyor...' : 'Kaydet'}
                </Button>
              </div>
            )}
          </CardHeader>
          
          <CardContent className="space-y-8">
            {/* Email Notifications */}
            <NotificationGroup
              title="E-posta Bildirimleri"
              icon={Mail}
              category="email"
              items={[
                {
                  key: 'campaigns',
                  label: 'Kampanya Bildirimleri',
                  description: 'Yeni kampanyalar ve başvuru durumu güncellemeleri'
                },
                {
                  key: 'payments',
                  label: 'Ödeme Bildirimleri',
                  description: 'Ödeme onayları, transferler ve fatura bilgileri'
                },
                {
                  key: 'security',
                  label: 'Güvenlik Bildirimleri',
                  description: 'Hesap güvenliği ve şüpheli aktivite uyarıları'
                },
                {
                  key: 'marketing',
                  label: 'Pazarlama E-postaları',
                  description: 'Özel teklifler, haberler ve platform güncellemeleri'
                }
              ]}
            />

            {/* Push Notifications */}
            <NotificationGroup
              title="Anlık Bildirimler"
              icon={Smartphone}
              category="push"
              items={[
                {
                  key: 'campaigns',
                  label: 'Kampanya Bildirimleri',
                  description: 'Yeni kampanya fırsatları ve acil güncellemeler'
                },
                {
                  key: 'payments',
                  label: 'Ödeme Bildirimleri',
                  description: 'Ödeme onayları ve transfer bildirimleri'
                },
                {
                  key: 'security',
                  label: 'Güvenlik Uyarıları',
                  description: 'Hesap güvenliği ile ilgili acil bildirimler'
                }
              ]}
            />

            {/* SMS Notifications */}
            <NotificationGroup
              title="SMS Bildirimleri"
              icon={MessageSquare}
              category="sms"
              items={[
                {
                  key: 'payments',
                  label: 'Ödeme Bildirimleri',
                  description: 'Önemli ödeme işlemleri için SMS onayı'
                },
                {
                  key: 'security',
                  label: 'Güvenlik Kodları',
                  description: 'İki faktörlü doğrulama ve güvenlik kodları'
                }
              ]}
            />

            {/* Info Box */}
            <motion.div variants={itemVariants} className="p-4 bg-blue-50 rounded-lg border border-blue-200">
              <div className="flex items-start gap-3">
                <Bell className="w-5 h-5 text-blue-600 mt-0.5" />
                <div>
                  <h5 className="font-medium text-blue-900 mb-1">Bildirim Ayarları Hakkında</h5>
                  <p className="text-sm text-blue-700">
                    Güvenlik bildirimleri hesabınızın korunması için gereklidir ve kapatılamaz. 
                    Diğer bildirim türlerini istediğiniz zaman açıp kapatabilirsiniz.
                  </p>
                </div>
              </div>
            </motion.div>
          </CardContent>
        </Card>
      </motion.div>
    </motion.div>
  );
}
