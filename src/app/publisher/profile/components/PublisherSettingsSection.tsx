"use client";

import React, { useState } from 'react';
import { Briefcase, Building, Hash, Tag, DollarSign, Save, Edit3 } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Badge } from '@/components/ui/badge';
import { useProfile } from '../context/ProfileContext';
import { toast } from '@/components/ui/use-toast';

const businessTypes = [
  { value: 'individual', label: 'Bireysel' },
  { value: 'company', label: 'Şirket' },
  { value: 'agency', label: 'Ajans' },
];

const categories = [
  'technology', 'lifestyle', 'travel', 'food', 'fashion', 'beauty', 
  'fitness', 'gaming', 'education', 'business', 'entertainment', 'sports'
];

const contentTypes = [
  'blog', 'social', 'video', 'podcast', 'newsletter', 'review'
];

export function PublisherSettingsSection() {
  const { profileData, updateProfile, loading } = useProfile();
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    publisher: profileData?.publisher || {
      businessName: '',
      businessType: 'individual',
      taxId: '',
      categories: [],
      contentTypes: [],
      minCampaignBudget: 1000,
      autoApproval: false,
      contentGuidelines: '',
    }
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      publisher: {
        ...prev.publisher,
        [name]: name === 'minCampaignBudget' ? parseInt(value) || 0 : value
      }
    }));
  };

  const handleSelectChange = (key: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      publisher: {
        ...prev.publisher,
        [key]: value
      }
    }));
  };

  const handleSwitchChange = (key: string, value: boolean) => {
    setFormData(prev => ({
      ...prev,
      publisher: {
        ...prev.publisher,
        [key]: value
      }
    }));
  };

  const toggleCategory = (category: string) => {
    setFormData(prev => ({
      ...prev,
      publisher: {
        ...prev.publisher,
        categories: prev.publisher.categories.includes(category)
          ? prev.publisher.categories.filter(c => c !== category)
          : [...prev.publisher.categories, category]
      }
    }));
  };

  const toggleContentType = (type: string) => {
    setFormData(prev => ({
      ...prev,
      publisher: {
        ...prev.publisher,
        contentTypes: prev.publisher.contentTypes.includes(type)
          ? prev.publisher.contentTypes.filter(t => t !== type)
          : [...prev.publisher.contentTypes, type]
      }
    }));
  };

  const handleSave = async () => {
    try {
      await updateProfile(formData);
      setIsEditing(false);
      toast({
        title: "Başarılı",
        description: "Yayıncı ayarlarınız güncellendi.",
        className: "bg-green-50 border-green-200 text-green-800"
      });
    } catch (error) {
      toast({
        title: "Hata",
        description: "Ayarlar güncellenirken bir hata oluştu.",
        className: "bg-red-50 border-red-200 text-red-800"
      });
    }
  };

  const handleCancel = () => {
    setFormData({
      publisher: profileData?.publisher || {
        businessName: '',
        businessType: 'individual',
        taxId: '',
        categories: [],
        contentTypes: [],
        minCampaignBudget: 1000,
        autoApproval: false,
        contentGuidelines: '',
      }
    });
    setIsEditing(false);
  };

  return (
    <div className="space-y-6">
      <div>
        <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Briefcase className="w-5 h-5 text-blue-600" />
              Yayıncı Ayarları
            </CardTitle>
            
            {!isEditing ? (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsEditing(true)}
                className="hover:bg-blue-50 hover:border-blue-300"
              >
                <Edit3 className="w-4 h-4 mr-2" />
                Düzenle
              </Button>
            ) : (
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCancel}
                  disabled={loading}
                >
                  İptal
                </Button>
                <Button
                  size="sm"
                  onClick={handleSave}
                  disabled={loading}
                  className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
                >
                  <Save className="w-4 h-4 mr-2" />
                  {loading ? 'Kaydediliyor...' : 'Kaydet'}
                </Button>
              </div>
            )}
          </CardHeader>
          
          <CardContent className="space-y-6">
            {/* Business Information */}
            <div className="space-y-4">
              <h4 className="font-medium text-slate-900 border-b border-slate-200 pb-2">
                İş Bilgileri
              </h4>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="businessName">İş Adı</Label>
                  <div className="relative">
                    <Building className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
                    <Input
                      id="businessName"
                      name="businessName"
                      value={formData.publisher.businessName}
                      onChange={handleInputChange}
                      disabled={!isEditing}
                      className="pl-10 bg-slate-50 border-slate-200 focus:bg-white"
                      placeholder="İş/Şirket adınız"
                    />
                  </div>
                </div>
                
                <div className="space-y-2">
                  <Label>İş Türü</Label>
                  <Select
                    value={formData.publisher.businessType}
                    onValueChange={(value) => handleSelectChange('businessType', value)}
                    disabled={!isEditing}
                  >
                    <SelectTrigger className="bg-slate-50 border-slate-200 focus:bg-white">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {businessTypes.map((type) => (
                        <SelectItem key={type.value} value={type.value}>
                          {type.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="taxId">Vergi Numarası</Label>
                <div className="relative">
                  <Hash className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
                  <Input
                    id="taxId"
                    name="taxId"
                    value={formData.publisher.taxId}
                    onChange={handleInputChange}
                    disabled={!isEditing}
                    className="pl-10 bg-slate-50 border-slate-200 focus:bg-white"
                    placeholder="Vergi numaranız"
                  />
                </div>
              </div>
            </div>


            {/* Campaign Settings */}
            <div className="space-y-4">
              <h4 className="font-medium text-slate-900 border-b border-slate-200 pb-2">
                Kampanya Ayarları
              </h4>
              
              <div className="space-y-2">
                <Label htmlFor="minCampaignBudget">Minimum Kampanya Bütçesi (₺)</Label>
                <div className="relative">
                  <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-slate-400" />
                  <Input
                    id="minCampaignBudget"
                    name="minCampaignBudget"
                    type="number"
                    value={formData.publisher.minCampaignBudget}
                    onChange={handleInputChange}
                    disabled={!isEditing}
                    className="pl-10 bg-slate-50 border-slate-200 focus:bg-white"
                    placeholder="1000"
                  />
                </div>
              </div>

              <div className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                <div>
                  <Label className="text-sm font-medium text-slate-900">Otomatik Onay</Label>
                  <p className="text-xs text-slate-500 mt-1">
                    Uygun kampanyalar otomatik olarak onaylanır
                  </p>
                </div>
                <Switch
                  checked={formData.publisher.autoApproval}
                  onCheckedChange={(checked) => handleSwitchChange('autoApproval', checked)}
                  disabled={!isEditing}
                />
              </div>
            </div>

            {/* Content Guidelines */}
            <div className="space-y-2">
              <Label htmlFor="contentGuidelines">İçerik Kuralları</Label>
              <Textarea
                id="contentGuidelines"
                name="contentGuidelines"
                value={formData.publisher.contentGuidelines}
                onChange={handleInputChange}
                disabled={!isEditing}
                rows={4}
                className="bg-slate-50 border-slate-200 focus:bg-white resize-none"
                placeholder="İçerik üretimi konusundaki yaklaşımınızı ve kurallarınızı açıklayın..."
              />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
