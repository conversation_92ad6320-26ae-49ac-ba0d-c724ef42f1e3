"use client";

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Settings, Globe, Clock, Save, Edit3 } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useProfile } from '../context/ProfileContext';
import { toast } from '@/components/ui/use-toast';

const languages = [
  { value: 'tr', label: 'Türkçe' },
  { value: 'en', label: 'English' },
  { value: 'de', label: 'Deutsch' },
  { value: 'fr', label: 'Français' },
  { value: 'es', label: 'Español' },
];

const timezones = [
  { value: 'Europe/Istanbul', label: 'İstanbul (UTC+3)' },
  { value: 'Europe/London', label: 'Londra (UTC+0)' },
  { value: 'Europe/Berlin', label: 'Berlin (UTC+1)' },
  { value: 'America/New_York', label: 'New York (UTC-5)' },
  { value: 'America/Los_Angeles', label: 'Los Angeles (UTC-8)' },
  { value: 'Asia/Tokyo', label: 'Tokyo (UTC+9)' },
];

export function AccountSettingsSection() {
  const { profileData, updateProfile, loading } = useProfile();
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    language: profileData?.language || 'tr',
    timezone: profileData?.timezone || 'Europe/Istanbul',
  });

  const handleSave = async () => {
    try {
      await updateProfile(formData);
      setIsEditing(false);
      toast({
        title: "Başarılı",
        description: "Hesap ayarlarınız güncellendi.",
        className: "bg-green-50 border-green-200 text-green-800"
      });
    } catch (error) {
      toast({
        title: "Hata",
        description: "Ayarlar güncellenirken bir hata oluştu.",
        className: "bg-red-50 border-red-200 text-red-800"
      });
    }
  };

  const handleCancel = () => {
    setFormData({
      language: profileData?.language || 'tr',
      timezone: profileData?.timezone || 'Europe/Istanbul',
    });
    setIsEditing(false);
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="space-y-6"
    >
      <motion.div variants={itemVariants}>
        <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Settings className="w-5 h-5 text-blue-600" />
              Hesap Ayarları
            </CardTitle>
            
            {!isEditing ? (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsEditing(true)}
                className="hover:bg-blue-50 hover:border-blue-300"
              >
                <Edit3 className="w-4 h-4 mr-2" />
                Düzenle
              </Button>
            ) : (
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCancel}
                  disabled={loading}
                >
                  İptal
                </Button>
                <Button
                  size="sm"
                  onClick={handleSave}
                  disabled={loading}
                  className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
                >
                  <Save className="w-4 h-4 mr-2" />
                  {loading ? 'Kaydediliyor...' : 'Kaydet'}
                </Button>
              </div>
            )}
          </CardHeader>
          
          <CardContent className="space-y-6">
            {/* Language Setting */}
            <motion.div variants={itemVariants} className="space-y-2">
              <Label className="flex items-center gap-2">
                <Globe className="w-4 h-4 text-slate-500" />
                Dil
              </Label>
              <Select
                value={formData.language}
                onValueChange={(value) => setFormData(prev => ({ ...prev, language: value }))}
                disabled={!isEditing}
              >
                <SelectTrigger className="bg-slate-50 border-slate-200 focus:bg-white">
                  <SelectValue placeholder="Dil seçin" />
                </SelectTrigger>
                <SelectContent>
                  {languages.map((lang) => (
                    <SelectItem key={lang.value} value={lang.value}>
                      {lang.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-xs text-slate-500">
                Arayüz dili ve bildirimlerin gönderileceği dil
              </p>
            </motion.div>

            {/* Timezone Setting */}
            <motion.div variants={itemVariants} className="space-y-2">
              <Label className="flex items-center gap-2">
                <Clock className="w-4 h-4 text-slate-500" />
                Zaman Dilimi
              </Label>
              <Select
                value={formData.timezone}
                onValueChange={(value) => setFormData(prev => ({ ...prev, timezone: value }))}
                disabled={!isEditing}
              >
                <SelectTrigger className="bg-slate-50 border-slate-200 focus:bg-white">
                  <SelectValue placeholder="Zaman dilimi seçin" />
                </SelectTrigger>
                <SelectContent>
                  {timezones.map((tz) => (
                    <SelectItem key={tz.value} value={tz.value}>
                      {tz.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <p className="text-xs text-slate-500">
                Tarih ve saat bilgilerinin gösterileceği zaman dilimi
              </p>
            </motion.div>

            {/* Account Status */}
            <motion.div variants={itemVariants} className="pt-4 border-t border-slate-200">
              <h4 className="font-medium text-slate-900 mb-4">Hesap Durumu</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 bg-slate-50 rounded-lg">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-slate-600">E-posta Doğrulama</span>
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      profileData?.emailVerified 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {profileData?.emailVerified ? 'Doğrulandı' : 'Beklemede'}
                    </span>
                  </div>
                </div>
                
                <div className="p-4 bg-slate-50 rounded-lg">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-slate-600">Telefon Doğrulama</span>
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      profileData?.phoneVerified 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-yellow-100 text-yellow-800'
                    }`}>
                      {profileData?.phoneVerified ? 'Doğrulandı' : 'Beklemede'}
                    </span>
                  </div>
                </div>
              </div>
            </motion.div>

            {/* Account Info */}
            <motion.div variants={itemVariants} className="pt-4 border-t border-slate-200">
              <h4 className="font-medium text-slate-900 mb-4">Hesap Bilgileri</h4>
              <div className="space-y-3 text-sm">
                <div className="flex justify-between">
                  <span className="text-slate-600">Hesap ID:</span>
                  <span className="font-mono text-slate-900">{profileData?.id}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-600">Üyelik Tarihi:</span>
                  <span className="text-slate-900">
                    {profileData?.createdAt && new Date(profileData.createdAt).toLocaleDateString('tr-TR')}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-slate-600">Son Güncelleme:</span>
                  <span className="text-slate-900">
                    {profileData?.updatedAt && new Date(profileData.updatedAt).toLocaleDateString('tr-TR')}
                  </span>
                </div>
              </div>
            </motion.div>
          </CardContent>
        </Card>
      </motion.div>
    </motion.div>
  );
}
