// Publisher teması için stillendirme sabitleri

// Ana renkler
export const colors = {
  // Ana renkler
  primary: {
    light: '#EBF5FF',
    main: '#3B82F6',   // blue-500
    dark: '#1E40AF',   // blue-800
  },
  secondary: {
    light: '#F3E8FF',
    main: '#8B5CF6',   // violet-500
    dark: '#6D28D9',   // violet-700
  },
  accent: {
    light: '#F5F3FF',
    main: '#8B5CF6',   // violet-500
    dark: '#7C3AED',   // violet-600
  },
  
  // Arka plan renkleri
  background: {
    default: '#FFFFFF',
    paper: '#FFFFFF',
    light: '#F9FAFB',  // gray-50
    subtle: '#F3F4F6',  // gray-100
    muted: '#F1F5F9',  // slate-100
  },
  
  // Metin renkleri
  text: {
    primary: '#1E293B',  // slate-800
    secondary: '#475569', // slate-600
    muted: '#64748B',    // slate-500
    light: '#94A3B8',    // slate-400
  },
  
  // Kenarlık renkleri
  border: {
    light: '#E2E8F0',   // slate-200
    main: '#CBD5E1',    // slate-300
    accent: '#E0E7FF',  // indigo-100
  },
  
  // Durum renkleri
  status: {
    success: '#10B981',  // emerald-500
    warning: '#F59E0B',  // amber-500
    error: '#EF4444',    // red-500
    info: '#3B82F6',     // blue-500
  },
  
  // Gradient renkleri
  gradient: {
    primary: 'linear-gradient(to right, #3B82F6, #8B5CF6)',
    secondary: 'linear-gradient(to right, #8B5CF6, #D946EF)',
    accent: 'linear-gradient(to right, #3B82F6, #2DD4BF)',
  },
};

// Gölgeler
export const shadows = {
  sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
  md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
  lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
  xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
};

// Kenar yuvarlaklıkları
export const borderRadius = {
  sm: '0.125rem',  // 2px
  md: '0.375rem',  // 6px 
  lg: '0.5rem',    // 8px
  xl: '0.75rem',   // 12px
  '2xl': '1rem',   // 16px
  full: '9999px',
};

// Z-indeksleri
export const zIndex = {
  background: 0,
  base: 1,
  overlay: 10,
  modal: 20,
  toast: 30,
  tooltip: 40,
};

// Geçiş süreleri
export const transition = {
  fast: '150ms',
  normal: '300ms',
  slow: '500ms',
};

// CSS sınıfı üreticileri
export const classes = {
  // Kart sınıfları
  card: {
    base: 'border border-border-light bg-background-paper rounded-lg shadow-sm',
    hover: 'hover:shadow-md hover:border-border-accent transition-all',
    interactive: 'cursor-pointer transform hover:-translate-y-1 transition-transform',
  },
  
  // Arka plan sınıfları
  background: {
    light: 'bg-background-light',
    paper: 'bg-background-paper',
    subtle: 'bg-background-subtle',
    gradient: 'bg-gradient-to-br from-primary-light to-background-light',
  },
  
  // Buton sınıfları
  button: {
    primary: 'bg-primary-main hover:bg-primary-dark text-white',
    secondary: 'bg-secondary-main hover:bg-secondary-dark text-white',
    outline: 'border border-border-main bg-background-paper text-text-primary hover:bg-background-subtle',
    ghost: 'text-text-primary hover:bg-background-subtle',
  },
  
  // Metin sınıfları
  text: {
    title: 'text-text-primary font-medium',
    body: 'text-text-secondary',
    muted: 'text-text-muted text-sm',
    accent: 'text-accent-main',
  },
};

// Tailwind CSS yardımcı sınıfları için yardımcı fonksiyon
export const cn = (...inputs: (string | undefined)[]) => {
  return inputs.filter(Boolean).join(' ');
};

// Tema sabitleri
export const theme = {
  colors,
  shadows,
  borderRadius,
  zIndex,
  transition,
  classes,
  cn,
};

export default theme; 