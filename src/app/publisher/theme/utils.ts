import { theme } from './index';

/**
 * CSS sınıflarını birleştiren yardımcı fonksiyon
 */
export function clsx(...classes: (string | undefined | null | false)[]): string {
  return classes.filter(Boolean).join(' ');
}

/**
 * Tema değişkenlerini CSS sınıflarına dönüştüren yardımcı fonksiyonlar
 */

// Kart sınıfları oluşturur
export function createCardClasses(
  variant: 'default' | 'hover' | 'interactive' = 'default',
  additionalClasses: string = ''
): string {
  let baseClasses = theme.classes.card.base;
  
  if (variant === 'hover') {
    baseClasses = clsx(baseClasses, theme.classes.card.hover);
  } else if (variant === 'interactive') {
    baseClasses = clsx(baseClasses, theme.classes.card.hover, theme.classes.card.interactive);
  }
  
  return clsx(baseClasses, additionalClasses);
}

// Buton sınıfları oluşturur
export function createButtonClasses(
  variant: 'primary' | 'secondary' | 'outline' | 'ghost' = 'primary',
  additionalClasses: string = ''
): string {
  const baseClasses = theme.classes.button[variant];
  return clsx(baseClasses, additionalClasses);
}

// Arka plan sınıfları oluşturur
export function createBackgroundClasses(
  variant: 'light' | 'paper' | 'subtle' | 'gradient' = 'paper',
  additionalClasses: string = ''
): string {
  const baseClasses = theme.classes.background[variant];
  return clsx(baseClasses, additionalClasses);
}

// Metin sınıfları oluşturur
export function createTextClasses(
  variant: 'title' | 'body' | 'muted' | 'accent' = 'body',
  additionalClasses: string = ''
): string {
  const baseClasses = theme.classes.text[variant];
  return clsx(baseClasses, additionalClasses);
}

/**
 * Tema değerleri için getter fonksiyonları
 */

// Renk değerlerini alır
export function getColor(
  category: keyof typeof theme.colors, 
  variant: string
): string {
  return theme.colors[category][variant as keyof typeof theme.colors[typeof category]];
}

// Gölge değerlerini alır
export function getShadow(size: keyof typeof theme.shadows): string {
  return theme.shadows[size];
}

// Kenar yuvarlaklığı değerlerini alır
export function getBorderRadius(size: keyof typeof theme.borderRadius): string {
  return theme.borderRadius[size];
}

// Z-indeks değerlerini alır
export function getZIndex(level: keyof typeof theme.zIndex): number {
  return theme.zIndex[level];
}

// Geçiş süresi değerlerini alır
export function getTransition(speed: keyof typeof theme.transition): string {
  return theme.transition[speed];
}

// Tüm yardımcı fonksiyonları dışa aktar
export const themeUtils = {
  clsx,
  createCardClasses,
  createButtonClasses,
  createBackgroundClasses,
  createTextClasses,
  getColor,
  getShadow,
  getBorderRadius,
  getZIndex,
  getTransition
};

export default themeUtils; 