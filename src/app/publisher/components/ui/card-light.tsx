"use client";

import React from 'react';
import { Card, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { cn } from '@/lib/utils';
import theme from '@/app/publisher/theme';

interface CardLightProps {
  children: React.ReactNode;
  className?: string;
  variant?: 'default' | 'hover' | 'interactive';
  withHeader?: boolean;
  title?: string;
  footer?: React.ReactNode;
  headerContent?: React.ReactNode;
}

export function CardLight({
  children,
  className,
  variant = 'default',
  withHeader = false,
  title,
  footer,
  headerContent,
  ...props
}: CardLightProps & React.ComponentProps<typeof Card>) {
  // Kart varyantlarına göre stil sınıflarını oluştur
  let cardClasses = 'border border-slate-200 bg-white shadow-sm';
  
  // Varyanta göre ek stiller
  if (variant === 'hover') {
    cardClasses = cn(cardClasses, 'hover:shadow-md hover:border-indigo-200/60 transition-all duration-300');
  } else if (variant === 'interactive') {
    cardClasses = cn(
      cardClasses, 
      'hover:shadow-md hover:border-indigo-200/60 transition-all duration-300',
      'cursor-pointer transform hover:-translate-y-1'
    );
  }
  
  return (
    <Card className={cn(cardClasses, className)} {...props}>
      {withHeader && (
        <CardHeader className="pb-3">
          {title && <CardTitle className="text-slate-800">{title}</CardTitle>}
          {headerContent}
        </CardHeader>
      )}
      
      <CardContent className={withHeader ? undefined : 'pt-6'}>
        {children}
      </CardContent>
      
      {footer && (
        <CardFooter className="border-t border-slate-100 pt-4 pb-4">
          {footer}
        </CardFooter>
      )}
    </Card>
  );
}

// Gradient arka planlı kart bileşeni
export function CardGradient({
  children,
  className,
  ...props
}: React.ComponentProps<typeof CardLight>) {
  return (
    <CardLight 
      className={cn('bg-gradient-to-br from-white to-blue-50/80', className)} 
      {...props}
    >
      {children}
    </CardLight>
  );
} 