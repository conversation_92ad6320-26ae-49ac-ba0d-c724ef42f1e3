"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { LogOut, LayoutDashboard, LineChart, Settings, FileText } from "lucide-react";
import { useAuth } from "@/shared/hooks/useAuth";
import React from "react";

// Bu kısım daha sonra auth sisteminden gelecek
const user = {
  name: "Publisher User",
  email: "<EMAIL>"
};

const menuItems = [
  {
    title: "Dashboard",
    icon: LayoutDashboard,
    href: "/publisher"
  },
  {
    title: "İ<PERSON>tist<PERSON><PERSON>",
    icon: LineChart,
    href: "/publisher/statistics"
  },
  {
    title: "Raporlar",
    icon: FileText,
    href: "/publisher/reports"
  }
];

interface MenuItemProps {
  item: {
    title: string;
    icon: React.ElementType;
    href: string;
  };
}

function MenuItem({ item }: MenuItemProps) {
  const pathname = usePathname();
  const isActive = pathname === item.href;
  const Icon = item.icon;

  return (
    <Link 
      href={item.href}
      className={`sidebar-item ${isActive ? "active" : ""}`}
    >
      <Icon className="h-4 w-4 mr-3" />
      {item.title}
    </Link>
  );
}

export function Sidebar() {
  const { user, logout } = useAuth();

  const ProfileSection = () => (
    <div className="px-3 py-4">
      <div className="flex items-center gap-3 rounded-lg px-3 py-2">
        <Avatar>
          <AvatarFallback>{user?.name?.[0] || "U"}</AvatarFallback>
          <AvatarImage src="/avatar.png" />
        </Avatar>
        <div className="flex flex-col">
          <span className="text-sm font-medium">{user?.name}</span>
          <span className="text-xs text-muted-foreground">{user?.email}</span>
        </div>
      </div>
      <Button 
        variant="ghost" 
        className="w-full mt-2 text-red-500 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-950/50 justify-start gap-2"
        onClick={logout}
      >
        <LogOut className="h-4 w-4" />
        Çıkış Yap
      </Button>
    </div>
  );

  return (
    <div className="hidden md:flex flex-col fixed left-0 top-0 w-64 h-screen bg-background border-r z-30">
      <div className="flex h-16 items-center border-b px-6">
        <Link href="/publisher" className="flex items-center gap-2 font-semibold text-xl text-blue-600">
          <span>Publisher Panel</span>
        </Link>
      </div>
      <div className="flex-1 flex flex-col justify-between">
        <ScrollArea className="flex-1">
          <div className="p-2">
            <nav className="grid gap-1">
              {menuItems.map((item, index) => (
                <MenuItem key={index} item={item} />
              ))}
            </nav>
          </div>
        </ScrollArea>
        <div className="border-t bg-background">
          <ProfileSection />
        </div>
      </div>
    </div>
  );
} 