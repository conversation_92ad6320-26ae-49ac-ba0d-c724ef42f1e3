"use client";

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Home, Users, LayoutDashboard, Wallet } from 'lucide-react';

export function BottomNav() {
  const pathname = usePathname();
  
  const navItems = [
    { name: '<PERSON>', href: '/publisher', icon: Home },
    { name: '<PERSON><PERSON><PERSON>larım', href: '/publisher/communities', icon: Users },
    { name: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>', href: '/publisher/ads', icon: LayoutDashboard },
    { name: '<PERSON><PERSON><PERSON><PERSON>', href: '/publisher/payments', icon: Wallet },
  ];
  
  return (
    <div className="md:hidden fixed bottom-0 left-0 right-0 z-50 bg-white backdrop-blur-md border-t border-slate-200 shadow-[0_-4px_10px_rgba(0,0,0,0.05)]">
      <div className="flex justify-between h-16">
        {navItems.map((item) => {
          const isActive = pathname === item.href;
          const Icon = item.icon;
          
          return (
            <Link 
              key={item.href}
              href={item.href}
              className="flex-1 flex flex-col items-center justify-center"
            >
              <div 
                className={`flex flex-col items-center justify-center ${
                  isActive 
                    ? 'text-purple-700' 
                    : 'text-slate-500'
                }`}
              >
                <div className={`relative ${isActive ? 'after:content-[""] after:absolute after:h-1 after:w-1 after:bg-purple-500 after:rounded-full after:bottom-[-4px] after:left-1/2 after:-translate-x-1/2' : ''}`}>
                  <Icon className={`h-5 w-5 ${isActive ? 'text-purple-700' : 'text-slate-500'}`} />
                </div>
                <span className="text-[10px] mt-1">{item.name}</span>
              </div>
            </Link>
          );
        })}
      </div>
    </div>
  );
} 