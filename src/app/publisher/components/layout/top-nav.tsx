"use client";

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Bell, 
  Home, 
  LayoutDashboard, 
  Wallet, 
  Users, 
  Settings, 
  Menu as MenuIcon,
  X,
  LogOut,
  Languages
} from 'lucide-react';
import { useAuth } from '@/shared/hooks/useAuth';
import { useLanguage, SUPPORTED_LANGUAGES } from '@/shared/hooks/useLanguage';
import { toast } from '@/components/ui/use-toast';
import { getNotificationList, type Notification as APINotification } from '@/shared/services/publisher/publisher-notifications';

interface TopNavProps {
  scrolled?: boolean;
}

// NotificationList bileşeninin beklediği bildirim tipleri ile uyumlu hale getirildi
type FrontendNotificationType = 'system' | 'payment' | 'community' | 'ads' | string; // API'den gelen type string olabilir

// Bildirim tipi tanımı (API'den gelene göre ve frontend ihtiyacına göre ayarlandı)
interface Notification {
  id: string; // frontend'de kullanmak için _id'yi id olarak eşle
  title: string; // API response'undan
  message: string; // API response'undan
  date: string; // createdAt'i Date string'e çevireceğiz
  read: boolean; // API response'undan isRead alanı (frontend'de read olarak kullanılacak)
  type: FrontendNotificationType; // NotificationList bileşeninin beklediği tip
  // icon?: React.ReactNode; // NotificationList bileşeninde belirleniyor
  // API'den gelen diğer alanlar buraya eklenebilir
  _id: string; // API'den gelen _id'yi de tutabiliriz
  isRead: boolean; // API'den gelen isRead'i de tutabiliriz
  createdAt: number; // API'den gelen createdAt'i de tutabiliriz
}

export function TopNav({ scrolled = false }: TopNavProps) {
  // Hooks
  const pathname = usePathname();
  const { logout, user } = useAuth();
  const { changeLanguage, currentLanguage, getCurrentLanguageInfo, t } = useLanguage();

  // States
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);
  const [showProfileDropdown, setShowProfileDropdown] = useState(false);
  const [showLanguageDropdown, setShowLanguageDropdown] = useState(false);

  // API'den gelen bildirim verileri için state
  const [fetchedNotifications, setFetchedNotifications] = useState<Notification[]>([]);
  const [loadingNotifications, setLoadingNotifications] = useState(true);
  const [notificationError, setNotificationError] = useState<string | null>(null);

  // Bildirim verilerini çekme fonksiyonu
  const fetchNotifications = async () => {
    try {
      setLoadingNotifications(true);
      // Sadece okunmamış bildirimleri çekmek için API'de status filtresi varsa kullanılabilir.
      // Şu anki API tipine göre tüm listeyi çekip frontend'de filtreleyeceğiz.
      const result = await getNotificationList({
        limit: 20, // TopNav dropdown için makul bir limit belirle
        skip: 0,
        // status: 'unread', // Eğer API sadece okunmamışları filtrelemeyi destekliyorsa
      });
      
      // API'den gelen veriyi Notification tipine dönüştür
      const transformedNotifications: Notification[] = result.data.map(apiNotification => ({
        ...apiNotification, // API'den gelen tüm alanları kopyala
        id: apiNotification._id, // _id'yi id olarak eşle
        date: new Date(apiNotification.createdAt * 1000).toISOString(), // Unix timestamp'ten Date string'e
        read: apiNotification.isRead, // isRead'i read olarak eşle
        type: apiNotification.type as FrontendNotificationType, // API'den gelen tipi frontend tipine dönüştür
      }));

      setFetchedNotifications(transformedNotifications);
      setNotificationError(null);
    } catch (err: any) {
      console.error('Bildirimler yüklenirken hata oluştu:', err);
      setNotificationError(err.message || 'Bildirimler yüklenirken bir hata oluştu');
      setFetchedNotifications([]); // Hata durumunda listeyi temizle
    } finally {
      setLoadingNotifications(false);
    }
  };

  // Bileşen yüklendiğinde bildirimleri çek
  useEffect(() => {
    fetchNotifications();
  }, []); // Boş dependency array, sadece mount olduğunda çalışır

  // Menu Items
  const menuItems = [
    { name: t('dashboard.title'), href: '/publisher', icon: Home },
    { name: t('communities.title'), href: '/publisher/communities', icon: Users },
    { name: t('ads.title'), href: '/publisher/ads', icon: LayoutDashboard },
    { name: t('payments.title'), href: '/publisher/payments', icon: Wallet },
  ];

  // Okunmamış bildirim sayısını hesapla
  const unreadCount = fetchedNotifications.filter(n => !n.read).length;

  // Handlers
  const handleLanguageChange = async (langCode: 'tr' | 'en') => {
    try {
      await changeLanguage(langCode);
      setShowLanguageDropdown(false);
      toast({
        title: t('language.changeSuccess.title'),
        description: t('language.changeSuccess.description'),
      });
    } catch (error) {
      console.error('Dil değiştirme hatası:', error);
      toast({
        title: t('language.changeError.title'),
        description: t('language.changeError.description'),
        variant: 'destructive',
      });
    }
  };

  const handleLogout = () => {
    setShowProfileDropdown(false);
    logout();
  };

  // Components
  const NotificationItem = ({ notification }: { notification: Notification }) => (
    <div className="p-3 border-b border-slate-200 hover:bg-slate-50 transition-colors">
      <div className="flex items-start gap-3">
        <div className={`w-2 h-2 ${notification.read ? 'bg-transparent border border-slate-400' : 'bg-purple-500'} rounded-full mt-1.5`} />
        <div>
          <p className="text-sm text-slate-800">{notification.title}</p>
          <p className="text-xs text-slate-600 mt-1">{notification.message}</p>
          <p className="text-xs text-slate-500 mt-1">{notification.date}</p>
        </div>
      </div>
    </div>
  );

  const LanguageOption = ({ lang, onClick }: { lang: { code: 'tr' | 'en', name: string, flag: string }, onClick: () => void }) => (
    <button
      className={`w-full text-left px-4 py-2 text-sm hover:bg-slate-100 flex items-center gap-2 ${
        currentLanguage === lang.code ? 'text-purple-600 bg-purple-50' : 'text-slate-700'
      }`}
      onClick={onClick}
    >
      <span>{lang.flag}</span>
      <span>{lang.name}</span>
      {currentLanguage === lang.code && <span className="ml-auto text-purple-600">✓</span>}
    </button>
  );

  const MenuItem = ({ item, mobile = false }: { item: typeof menuItems[0], mobile?: boolean }) => {
    const isActive = pathname === item.href;
    const Icon = item.icon;
    
    return (
      <Link 
        href={item.href}
        onClick={() => mobile && setMobileMenuOpen(false)}
        className={`${
          mobile 
            ? `flex items-center gap-3 px-3 py-3 ${isActive ? 'text-purple-800 bg-purple-50 rounded-lg font-medium' : 'text-slate-600'}`
            : `px-3 py-2 rounded-lg flex items-center gap-2 transition-colors ${isActive ? 'bg-purple-100 text-purple-800 font-medium' : 'text-slate-600 hover:text-slate-900 hover:bg-slate-100'}`
        }`}
      >
        <Icon className={mobile ? "h-5 w-5" : "h-4 w-4"} />
        <span>{item.name}</span>
      </Link>
    );
  };

  return (
    <header 
      className={`fixed top-0 w-full z-50 ${
        scrolled 
          ? 'bg-white/95 backdrop-blur-md shadow-md border-b border-slate-200' 
          : 'bg-white backdrop-blur-md border-b border-slate-100'
      } transition-all duration-300`}
    >
      <div className="container mx-auto px-4 max-w-7xl">
        <div className="flex h-16 items-center justify-between">
          {/* Logo */}
          <Link href="/publisher" className="flex items-center gap-2 group">
            <div className="w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-lg flex items-center justify-center transform group-hover:rotate-6 transition-all duration-300 shadow-md">
              <span className="font-bold text-white">A</span>
            </div>
            <span className="font-medium text-slate-800 text-lg">Adnomio</span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-1">
            {menuItems.map((item) => (
              <MenuItem key={item.href} item={item} />
            ))}
          </nav>

          {/* Right Controls */}
          <div className="flex items-center space-x-3">
            {/* Notifications */}
            <div className="relative">
              <Button
                variant="ghost"
                size="icon"
                className="text-slate-600 hover:text-slate-800 hover:bg-slate-100 rounded-full relative"
                onClick={() => setShowNotifications(!showNotifications)}
              >
                <Bell className="h-5 w-5" />
                {unreadCount > 0 && (
                  <Badge className="absolute -top-1 -right-1 h-5 w-5 p-0 flex items-center justify-center bg-red-500 hover:bg-red-600 text-[10px]">
                    {unreadCount}
                  </Badge>
                )}
              </Button>

              {/* Notifications Dropdown */}
              {showNotifications && (
                <div className="absolute right-0 mt-2 w-80 bg-white border border-slate-200 rounded-lg shadow-xl z-50 overflow-hidden">
                  <div className="p-3 border-b border-slate-200 flex justify-between items-center">
                    <h3 className="font-medium text-slate-800">{t('notifications.title')}</h3>
                  </div>
                  <div className="max-h-72 overflow-y-auto">
                    {fetchedNotifications.length === 0 ? (
                      <div className="p-3 text-center text-slate-500 text-sm">
                        {notificationError ? notificationError : t('notifications.empty.title')}
                      </div>
                    ) : (
                      fetchedNotifications.map((notification) => (
                        <NotificationItem key={notification.id} notification={notification} />
                      ))
                    )}
                  </div>
                  <div className="p-2 text-center border-t border-slate-200">
                    <Link href="/publisher/notifications" onClick={() => setShowNotifications(false)}>
                      <Button variant="ghost" size="sm" className="text-xs text-purple-600 hover:text-purple-700 w-full">
                        {t('notifications.viewAll')}
                      </Button>
                    </Link>
                  </div>
                </div>
              )}
            </div>

            {/* Language Switcher */}
            <div className="relative">
              <Button
                variant="ghost"
                size="icon"
                className="text-slate-600 hover:text-slate-800 hover:bg-slate-100 rounded-full relative"
                onClick={() => setShowLanguageDropdown(!showLanguageDropdown)}
              >
                <Languages className="h-5 w-5" />
                <span className="absolute -bottom-1 -right-1 text-[10px]">
                  {getCurrentLanguageInfo().flag}
                </span>
              </Button>

              {showLanguageDropdown && (
                <div className="absolute right-0 mt-2 w-40 bg-white border border-slate-200 rounded-lg shadow-xl z-50 overflow-hidden">
                  <div className="py-1">
                    {Object.values(SUPPORTED_LANGUAGES).map((lang) => (
                      <LanguageOption
                        key={lang.code}
                        lang={lang}
                        onClick={() => handleLanguageChange(lang.code)}
                      />
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Avatar */}
            <div className="relative">
              <Button
                variant="ghost"
                size="icon"
                className="h-8 w-8 p-0 rounded-full"
                onClick={() => setShowProfileDropdown(!showProfileDropdown)}
              >
                <Avatar className="h-8 w-8 border border-slate-200">
                  <AvatarImage src="/placeholders/avatar.jpg" alt={user?.name} />
                  <AvatarFallback className="bg-gradient-to-br from-indigo-500 to-purple-500 text-white text-xs">
                    {user?.name?.split(' ').map(n => n[0]).join('')}
                  </AvatarFallback>
                </Avatar>
              </Button>

              {/* Profile Dropdown */}
              {showProfileDropdown && (
                <div className="absolute right-0 mt-2 w-48 bg-white border border-slate-200 rounded-lg shadow-xl z-50 overflow-hidden">
                  <div className="py-1">
                    {user && (
                      <div className="px-4 py-2 border-b border-slate-200">
                        <p className="text-sm font-medium text-slate-800">{user.name}</p>
                        <p className="text-xs text-slate-500">{user.email}</p>
                      </div>
                    )}
                    <Link 
                      href="/publisher/profile"
                      className="block px-4 py-2 text-sm text-slate-700 hover:bg-slate-100"
                      onClick={() => setShowProfileDropdown(false)}
                    >
                      <div className="flex items-center gap-2">
                        <Settings className="h-4 w-4" />
                        <span>{t('profile.settings')}</span>
                      </div>
                    </Link>
                    <button 
                      onClick={handleLogout}
                      className="w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-red-50"
                    >
                      <div className="flex items-center gap-2">
                        <LogOut className="h-4 w-4" />
                        <span>{t('profile.logout')}</span>
                      </div>
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* Mobile Menu Button */}
            <div className="md:hidden">
              <Button 
                variant="ghost"
                size="icon"
                className="text-slate-600 hover:text-slate-800 hover:bg-slate-100 rounded-full"
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              >
                {mobileMenuOpen ? <X className="h-6 w-6" /> : <MenuIcon className="h-6 w-6" />}
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      {mobileMenuOpen && (
        <div className="bg-white shadow-md border-t border-slate-200 md:hidden">
          <div className="py-2 px-4">
            {menuItems.map((item) => (
              <MenuItem key={item.href} item={item} mobile />
            ))}

            {/* Mobile Notifications Link */}
            <Link 
              href="/publisher/notifications"
              onClick={() => setMobileMenuOpen(false)}
              className={`flex items-center gap-3 px-3 py-3 ${
                pathname === '/publisher/notifications'
                  ? 'text-purple-800 bg-purple-50 rounded-lg font-medium' 
                  : 'text-slate-600'
              }`}
            >
              <Bell className="h-5 w-5" />
              <span>{t('notifications.title')}</span>
              {unreadCount > 0 && (
                <Badge className="ml-auto bg-red-500 hover:bg-red-600 text-[10px]">
                  {unreadCount}
                </Badge>
              )}
            </Link>

            {/* Mobile Language Options */}
            <div className="border-t border-slate-200 mt-2 pt-2">
              {Object.values(SUPPORTED_LANGUAGES).map((lang) => (
                <button
                  key={lang.code}
                  onClick={() => {
                    handleLanguageChange(lang.code);
                    setMobileMenuOpen(false);
                  }}
                  className={`flex items-center gap-3 px-3 py-3 w-full text-left ${
                    currentLanguage === lang.code
                      ? 'text-purple-600 bg-purple-50'
                      : 'text-slate-600 hover:bg-slate-50'
                  }`}
                >
                  <Languages className="h-5 w-5" />
                  <span>{lang.flag} {lang.name}</span>
                  {currentLanguage === lang.code && (
                    <span className="ml-auto text-purple-600">✓</span>
                  )}
                </button>
              ))}
            </div>

            {/* Mobile Logout Button */}
            <button 
              onClick={handleLogout}
              className="flex items-center gap-3 px-3 py-3 text-red-600 hover:bg-red-50 w-full text-left mt-2"
            >
              <LogOut className="h-5 w-5" />
              <span>{t('auth.logout')}</span>
            </button>
          </div>
        </div>
      )}
    </header>
  );
} 