"use client";

import React, { useState, useEffect } from 'react';
import { TopNav } from './top-nav';
import { BottomNav } from './bottom-nav';

interface PublisherLayoutProps {
  children: React.ReactNode;
}

export function DashboardLayout({ children }: PublisherLayoutProps) {
  const [scrolled, setScrolled] = useState(false);
  
  // Scroll olduğunda efekt eklemek için
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 10) {
        setScrolled(true);
      } else {
        setScrolled(false);
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);
  
  return (
    <div className="min-h-screen bg-white text-slate-800 overflow-hidden">
      {/* Arka plan deseni */}
      <div className="fixed inset-0 z-0 opacity-5">
        <div className="absolute inset-0" style={{ 
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%239C92AC' fill-opacity='0.4'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")` 
        }}></div>
      </div>
      
      {/* Dekoratif elementler */}
      <div className="fixed inset-0 z-0 overflow-hidden pointer-events-none">
        <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-to-br from-blue-400 to-purple-400 rounded-full mix-blend-multiply filter blur-3xl opacity-10 transform translate-x-1/3 -translate-y-1/3"></div>
        <div className="absolute bottom-0 left-0 w-96 h-96 bg-gradient-to-tr from-purple-400 to-pink-400 rounded-full mix-blend-multiply filter blur-3xl opacity-10 transform -translate-x-1/3 translate-y-1/3"></div>
      </div>
      
      {/* Renkli üst kenar şeridi */}
      <div className="fixed top-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 z-50"></div>
      
      {/* Üst navigasyon */}
      <TopNav scrolled={scrolled} />
      
      {/* Ana içerik */}
      <main className="container mx-auto px-4 pt-20 pb-24 max-w-7xl relative z-10">
        {children}
      </main>
      
      {/* Alt navigasyon (mobil) */}
      <BottomNav />
    </div>
  );
} 