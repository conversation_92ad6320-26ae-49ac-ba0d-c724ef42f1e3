"use client";

import React from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Link2, Users, Eye, ArrowRight, PlusCircle } from 'lucide-react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { useLanguage } from '@/shared/hooks/useLanguage';

interface Group {
  id: string;
  name: string;
  icon: string;
  platform: 'telegram' | 'whatsapp' | 'discord';
  members: number;
  views: number;
  url: string;
  isActive: boolean;
}

export function GroupsOverview() {
  const { t } = useLanguage();
  
  // Örnek veri
  const groups: Group[] = [
    {
      id: '1',
      name: '<PERSON><PERSON><PERSON>',
      icon: '🏷️',
      platform: 'telegram',
      members: 2497,
      views: 7,
      url: 'https://t.me/indirimfirsatlarim',
      isActive: true
    },
    {
      id: '2',
      name: '<PERSON>k<PERSON><PERSON>ji Dünyası',
      icon: '💻',
      platform: 'telegram',
      members: 1256,
      views: 12,
      url: 'https://t.me/teknolojidunyasi',
      isActive: true
    },
    {
      id: '3',
      name: 'Yemek Tarifleri',
      icon: '🍳',
      platform: 'telegram',
      members: 3845,
      views: 19,
      url: 'https://t.me/yemektarifleri',
      isActive: false
    },
  ];
  
  const platformBadge = (platform: 'telegram' | 'whatsapp' | 'discord') => {
    switch (platform) {
      case 'telegram':
        return (
          <Badge className="bg-blue-500 hover:bg-blue-600 text-[10px] h-5">
            {t('communities.platform.telegram')}
          </Badge>
        );
      case 'whatsapp':
        return (
          <Badge className="bg-green-500 hover:bg-green-600 text-[10px] h-5">
            {t('communities.platform.whatsapp')}
          </Badge>
        );
      case 'discord':
        return (
          <Badge className="bg-indigo-500 hover:bg-indigo-600 text-[10px] h-5">
            {t('communities.platform.discord')}
          </Badge>
        );
    }
  };
  
  const activeGroups = groups.filter(group => group.isActive);
  
  return (
    <Card className="border-blue-200 bg-white shadow-md hover:shadow-lg transition-all duration-300">
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-bold text-slate-800 flex items-center gap-2">
            <Users className="h-5 w-5 text-blue-500" />
            {t('dashboard.sections.communities.title')}
          </CardTitle>
          
          <Link href="/publisher/communities">
            <Button variant="ghost" size="sm" className="h-8 gap-1 text-blue-600 hover:text-blue-700 hover:bg-blue-50">
              <span className="text-xs">{t('dashboard.sections.ads.viewAll')}</span>
              <ArrowRight className="h-3.5 w-3.5" />
            </Button>
          </Link>
        </div>
      </CardHeader>
      
      <CardContent>
        {activeGroups.length === 0 ? (
          <div className="text-center py-8">
            <div className="w-12 h-12 rounded-full bg-blue-100 mx-auto flex items-center justify-center mb-3">
              <PlusCircle className="h-6 w-6 text-blue-500" />
            </div>
            <h3 className="text-base font-medium text-slate-700 mb-1">{t('communities.empty.title')}</h3>
            <p className="text-sm text-slate-500 mb-4 max-w-xs mx-auto">{t('communities.empty.description')}</p>
            <Link href="/publisher/communities/connect">
              <Button size="sm" className="bg-blue-500 hover:bg-blue-600">
                {t('communities.connect.addNew')}
              </Button>
            </Link>
          </div>
        ) : (
          <div className="space-y-3">
            {activeGroups.map(group => (
              <div key={group.id} className="p-3 rounded-lg border border-blue-100 bg-blue-50/50 hover:bg-blue-50 transition-colors">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-white rounded-full flex items-center justify-center text-xl border border-blue-200 shadow-sm">
                    <span>{group.icon}</span>
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-start justify-between">
                      <div>
                        <h3 className="text-sm font-medium text-slate-800 flex items-center gap-1.5">
                          {group.name}
                          {platformBadge(group.platform)}
                        </h3>
                        <div className="flex items-center gap-3 mt-1">
                          <span className="flex items-center text-xs text-slate-500">
                            <Users className="h-3 w-3 mr-1" />
                            {group.members.toLocaleString()} {t('communities.list.columns.members')}
                          </span>
                          <span className="flex items-center text-xs text-slate-500">
                            <Eye className="h-3 w-3 mr-1" />
                            {group.views.toLocaleString()} {t('communities.list.columns.views')}
                          </span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between mt-2">
                      <a 
                        href={group.url} 
                        target="_blank" 
                        rel="noopener noreferrer" 
                        className="text-xs text-blue-600 hover:text-blue-700 hover:underline flex items-center truncate max-w-[240px]"
                      >
                        <Link2 className="h-3 w-3 mr-1 flex-shrink-0" />
                        <span className="truncate">{group.url}</span>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            ))}
            
            <Link href="/publisher/communities/connect">
              <Button variant="outline" size="sm" className="w-full mt-1 border-blue-200 text-blue-600 hover:bg-blue-50 hover:text-blue-700">
                <PlusCircle className="h-4 w-4 mr-2" />
                {t('communities.connect.addNew')}
              </Button>
            </Link>
          </div>
        )}
      </CardContent>
    </Card>
  );
} 