"use client";

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Award, AlertTriangle, Loader2, SmilePlus, Users } from 'lucide-react';
import { useLanguage } from '@/shared/hooks/useLanguage';
import type { TopCommunity } from '@/shared/services/publisher/community-service';
import { Button } from '@/components/ui/button';
import ConnectCommunityStepper from '@/app/publisher/communities/connect/components/ConnectCommunityStepper';

interface TopPerformersProps {
  data: TopCommunity[] | null;
  loading: boolean;
  error: string | null;
}

export function TopPerformers({ data, loading, error }: TopPerformersProps) {
  const { t } = useLanguage();
  const [showConnectModal, setShowConnectModal] = useState(false);
  
  if (loading) {
    return (
      <Card className="border-amber-200 bg-white shadow-md h-full min-h-[420px] flex items-center justify-center">
        <Loader2 className="h-8 w-8 text-amber-500 animate-spin" />
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="border-red-200 bg-red-50 shadow-md h-full min-h-[420px] flex flex-col items-center justify-center text-center p-4">
        <AlertTriangle className="h-8 w-8 text-red-500 mb-2" />
        <p className="text-red-700 font-medium">{t('communities.connect.noCommunities')}</p>
        <p className="text-red-600 text-sm">{error}</p>
      </Card>
    );
  }

  if (data === null || data.length === 0) {
    return (
      <>
        <Card className="border-amber-200 bg-white shadow-md h-full min-h-[420px] flex flex-col text-center p-4">
          <CardHeader className="pb-2">
            <CardTitle className="text-lg font-bold text-slate-800 flex items-center justify-center gap-2">
              <Award className="h-5 w-5 text-amber-500" />
              {t('dashboard.charts.topPerformers')}
            </CardTitle>
          </CardHeader>
          <CardContent className="flex flex-col items-center justify-center pt-4">
            <SmilePlus className="h-10 w-10 text-amber-500 mb-3" />
            <h3 className="text-md font-semibold text-slate-700 mb-1">{t('dashboard.emptyStates.topPerformers.title')}</h3>
            <p className="text-sm text-slate-500 mb-4">{t('dashboard.emptyStates.topPerformers.subtitle')}</p>
            <Button 
              size="sm" 
              className="bg-amber-500 hover:bg-amber-600 text-white" 
              onClick={() => setShowConnectModal(true)}
            >
              {t('communities.connect.titleShort')}
            </Button>
          </CardContent>
        </Card>
        <ConnectCommunityStepper 
          isOpen={showConnectModal} 
          onClose={() => setShowConnectModal(false)}
          onSuccess={() => {
            setShowConnectModal(false);
          }}
        />
      </>
    );
  }
  
  return (
    <Card className="border-amber-200 bg-white shadow-md hover:shadow-lg transition-all duration-300 h-full min-h-[420px] flex flex-col">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-bold text-slate-800 flex items-center gap-2">
          <Award className="h-5 w-5 text-amber-500" />
          {t('dashboard.charts.topPerformers')}
        </CardTitle>
      </CardHeader>
      <CardContent className="flex-grow">
        <div className="space-y-2.5">
          {data.map((community, index) => (
            <div 
              key={community.community_username || index} 
              className="flex items-center justify-between p-3 rounded-lg bg-amber-50/30 border border-amber-200/80 hover:bg-amber-50/60 transition-colors shadow-sm"
            >
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-slate-100 rounded-full flex items-center justify-center border border-slate-200 text-slate-400">
                  <Users className="w-5 h-5" />
                </div>
                <div>
                  <h3 className="text-sm font-semibold text-slate-700 truncate max-w-[180px]">{community.community_name}</h3>
                  <p className="text-xs text-amber-700 font-medium">₺{community.earnings.toFixed(2)}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
} 