"use client";

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ExternalLink } from 'lucide-react';
import { useState } from 'react';
import { useLanguage } from '@/shared/hooks/useLanguage';
import ConnectCommunityStepper from '@/app/publisher/communities/connect/components/ConnectCommunityStepper';

export function ConnectChannelCTA() {
  const [showConnectModal, setShowConnectModal] = useState(false);
  const { t } = useLanguage();

  return (
    <>
      <Card className="border-indigo-200 overflow-hidden bg-gradient-to-r from-purple-50 to-indigo-50 shadow-md hover:shadow-lg transition-all duration-300">
        <CardContent className="p-6 relative">
          <div className="absolute top-0 right-0 w-40 h-40 bg-white/20 rounded-full -mt-20 -mr-20"></div>
          <div className="absolute bottom-0 left-0 w-24 h-24 bg-white/20 rounded-full -mb-12 -ml-12"></div>
          
          <div className="flex flex-col md:flex-row md:items-center gap-6">
            <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-xl flex items-center justify-center flex-shrink-0 shadow-md">
              <ExternalLink className="h-6 w-6 text-white" />
            </div>
            
            <div className="flex-1">
              <h3 className="text-lg font-bold text-slate-800">{t('dashboard.cta.connectChannel.title')}</h3>
              <p className="text-slate-600 mt-1">{t('dashboard.cta.connectChannel.description')}</p>
            </div>
            
            <div className="flex-shrink-0">
              <Button 
                className="bg-gradient-to-r from-purple-500 to-indigo-500 hover:from-purple-600 hover:to-indigo-600 text-white shadow-md hover:shadow-lg transition-all duration-300"
                onClick={() => setShowConnectModal(true)}
              >
                {t('dashboard.cta.connectChannel.button')}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
      <ConnectCommunityStepper 
        isOpen={showConnectModal} 
        onClose={() => setShowConnectModal(false)}
        onSuccess={() => {
          setShowConnectModal(false);
        }}
      />
    </>
  );
} 