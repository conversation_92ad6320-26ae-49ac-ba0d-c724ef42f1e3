"use client";

import React, { useState, useEffect } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Sparkles, TrendingUp, Users, Wallet, MousePointerClick, BarChart3 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import ConnectCommunityStepper from '@/app/publisher/communities/connect/components/ConnectCommunityStepper';
import { useLanguage } from '@/shared/hooks/useLanguage';
import type { DashboardStatisticsResponse } from '@/shared/services/publisher/dashboard-statistics';
import { Skeleton } from '@/components/ui/skeleton';

interface StatCardProps {
  title: string;
  value: string;
  subtitle: string;
  icon: React.ReactNode;
  trend?: {
    value: number;
    label: string;
    isPositive: boolean;
  };
  gradient: string;
  iconBg: string;
  iconColor: string;
}

function StatCard({ title, value, subtitle, icon, trend, gradient, iconBg, iconColor }: StatCardProps) {
  const { t } = useLanguage();

  
  return (
    <Card className={`border border-slate-200 shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden group ${gradient}`}>
      <div className="p-5 relative">
        <div className="absolute top-0 right-0 w-24 h-24 bg-slate-100/50 rounded-full -mt-8 -mr-8 transition-all group-hover:scale-110"></div>
        
        <div className="flex items-center justify-between mb-2">
          <p className="text-xs font-medium text-slate-700">{title}</p>
          <div className={`${iconBg} h-8 w-8 rounded-lg flex items-center justify-center`}>
            {icon}
          </div>
        </div>
        
        <h3 className="text-2xl md:text-3xl font-bold text-slate-800">{value}</h3>
        
        <div className="flex items-center justify-between mt-3">
          <p className="text-xs text-slate-600">{subtitle}</p>
          
          {trend && (
            <p className={`text-xs ${trend.isPositive ? 'text-green-600' : 'text-red-600'} flex items-center gap-1 bg-slate-100 rounded-full px-2 py-0.5`}>
              {trend.isPositive ? 
                t('dashboard.stats.trend.up', { value: trend.value }) : 
                t('dashboard.stats.trend.down', { value: trend.value })}
              <span className="text-[10px] text-slate-500">{trend.label}</span>
            </p>
          )}
        </div>
      </div>
    </Card>
  );
}

interface StatsCardsProps {
  earnings: number;
  loading: boolean;
  error: string | null;
  statistics: DashboardStatisticsResponse['result'] | null;
  statsLoading: boolean;
  statsError: string | null;
}

export function StatsCards({ earnings, loading, error, statistics, statsLoading, statsError }: StatsCardsProps) {
  const [showConnectModal, setShowConnectModal] = useState(false);
  const [userName, setUserName] = useState('');
  const { t } = useLanguage();

  useEffect(() => {
    const userStr = localStorage.getItem('user');
    if (userStr) {
      try {
        const user = JSON.parse(userStr);
        setUserName(`${user.name} ${user.surname}`);
      } catch (error) {
        console.error('Error parsing user data:', error);
        setUserName('');
      }
    }
  }, []);

  if (loading || statsLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {[...Array(4)].map((_, index) => (
          <Card key={index} className="border-0 bg-white shadow-sm rounded-xl">
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <Skeleton className="w-24 h-4" />
              <Skeleton className="w-4 h-4" />
            </CardHeader>
            <CardContent>
              <Skeleton className="w-32 h-6 mb-2" />
              <Skeleton className="w-20 h-4" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Üst başlık kısmı */}
      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl md:text-3xl font-bold flex items-center text-slate-800">
            <span>{t('dashboard.welcome')}, </span>
            <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-500 to-indigo-500 ml-1.5 capitalize">{userName}</span>
            <span className="ml-2">👋</span>
          </h1>
          <p className="text-slate-500 mt-1">{t('dashboard.subtitle')}</p>
        </div>
        
        <div className="flex gap-3">
          <Button 
            className="bg-gradient-to-r from-purple-500 to-indigo-500 hover:from-purple-600 hover:to-indigo-600 rounded-lg border-none shadow-md hover:shadow-lg transition-all duration-300"
            onClick={() => setShowConnectModal(true)}
          >
            <Users className="h-4 w-4 mr-2" />
            {t('communities.connect.title')}
          </Button>
          
          <Link href="/publisher/payments/withdraw">
            <Button variant="outline" className="border-indigo-300 hover:bg-indigo-50 text-slate-700 rounded-lg shadow-md hover:shadow-lg transition-all duration-300">
              <Wallet className="h-4 w-4 mr-2" />
              {t('payments.withdraw.title')}
            </Button>
          </Link>
        </div>
      </div>
      
      {/* Ana bakiye kartı */}
      <Card className="border border-slate-200 shadow-md hover:shadow-lg transition-all duration-300 overflow-hidden bg-gradient-to-r from-purple-500 to-indigo-500">
        <div className="p-6 relative">
          <div className="absolute top-0 right-0 w-64 h-64 bg-white/20 rounded-full -mt-16 -mr-16"></div>
          <div className="absolute bottom-0 left-0 w-32 h-32 bg-white/20 rounded-full -mb-10 -ml-10"></div>
          
          <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
            <div>
              <p className="text-indigo-100 text-sm">{t('payments.balance.total')}</p>
              <h2 className="text-4xl font-bold text-white mt-2">₺ {(earnings || 0).toFixed(2)}</h2>
              <p className="text-indigo-100 text-xs mt-2">{t('payments.withdraw.form.amount', { amount: '50.00', days: 2 })}</p>
            </div>
          </div>
        </div>
      </Card>
      
      {/* İstatistik kartları */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <StatCard
          title={t('dashboard.stats.totalViews')}
          value={(statistics?.totalViews || 0).toLocaleString()}
          subtitle={t('dashboard.stats.last30Days')}
          icon={<Users className="h-4 w-4 text-rose-500" />}
          trend={{ 
            value: Number((statistics?.viewsChangePercent || 0).toFixed(2)), 
            label: t('dashboard.stats.comparedToPrevMonth'), 
            isPositive: (statistics?.viewsChangePercent || 0) >= 0 
          }}
          gradient="bg-white"
          iconBg="bg-rose-100"
          iconColor="text-rose-500"
        />
        <StatCard
          title={t('dashboard.stats.totalClicks')}
          value={(statistics?.totalClicks || 0).toLocaleString()}
          subtitle={t('dashboard.stats.last30Days')}
          icon={<MousePointerClick className="h-4 w-4 text-blue-500" />}
          trend={{ 
            value: Number((statistics?.clicksChangePercent || 0).toFixed(2)), 
            label: t('dashboard.stats.comparedToPrevMonth'), 
            isPositive: (statistics?.clicksChangePercent || 0) >= 0 
          }}
          gradient="bg-white"
          iconBg="bg-blue-100"
          iconColor="text-blue-500"
        />
        <StatCard
          title={t('dashboard.stats.averageCTR')}
          value={`${(statistics?.avgCTR || 0).toFixed(1)}%`}
          subtitle={t('dashboard.stats.clickRate')}
          icon={<TrendingUp className="h-4 w-4 text-emerald-500" />}
          trend={{ 
            value: Number((statistics?.ctrChangePercent || 0).toFixed(2)), 
            label: t('dashboard.stats.comparedToPrevMonth'), 
            isPositive: (statistics?.ctrChangePercent || 0) >= 0 
          }}
          gradient="bg-white"
          iconBg="bg-emerald-100"
          iconColor="text-emerald-500"
        />
        <StatCard
          title={t('dashboard.stats.monthlyEarnings')}
          value={`₺${(statistics?.monthlyEarnings || 0).toFixed(2)}`}
          subtitle={t('dashboard.stats.thisMonth')}
          icon={<Sparkles className="h-4 w-4 text-amber-500" />}
          trend={{ 
            value: Number((statistics?.earningsChangePercent || 0).toFixed(2)), 
            label: t('dashboard.stats.comparedToPrevMonth'), 
            isPositive: (statistics?.earningsChangePercent || 0) >= 0 
          }}
          gradient="bg-white"
          iconBg="bg-amber-100"
          iconColor="text-amber-500"
        />
      </div>

      {/* Topluluk ekleme modalı */}
      <ConnectCommunityStepper 
        isOpen={showConnectModal} 
        onClose={() => setShowConnectModal(false)}
        onSuccess={() => {
          setShowConnectModal(false);
          // Burada gerekirse sayfayı yenileyebilir veya state'i güncelleyebiliriz
        }}
      />
    </div>
  );
} 