"use client";

import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Bar<PERSON>hart3, LineChart as LineChartIcon, TrendingUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import { useLanguage } from '@/shared/hooks/useLanguage';
import { 
  BarChart, 
  Bar, 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  Area,
  AreaChart
} from 'recharts';

import type { TooltipProps } from 'recharts';
import type { NameType, ValueType } from 'recharts/types/component/DefaultTooltipContent';
import { getEarningsGraph, type EarningsType, type EarningsData, type DailyEarnings, type WeeklyEarnings, type MonthlyEarnings } from '@/shared/services/publisher/earnings-service';
import { Skeleton } from "@/components/ui/skeleton";

interface EarningsChartProps {
  earnings: number;
  loading: boolean;
  error: string | null;
  earningsDetails: {
    total_earnings: number;
    monthly_earnings: number;
    available_for_withdrawal: number;
    pending_balance: number;
    minimum_withdrawal_amount: number;
    monthly_withdrawal_limit: number;
    // Diğer detaylar...
  } | null;
}

// Chart data interface for consistent data structure
interface ChartDataPoint {
  date: string;
  earnings: number;
}

export function EarningsChart({ earnings, loading, error, earningsDetails }: EarningsChartProps) {
  const [period, setPeriod] = useState<'day' | 'week' | 'month'>('month');
  const [chartType, setChartType] = useState<'line' | 'bar'>('line');
  const [chartData, setChartData] = useState<ChartDataPoint[]>([]);
  const [chartLoading, setChartLoading] = useState(false);
  const [chartError, setChartError] = useState<string | null>(null);
  const { t } = useLanguage();

  // Map tab values to API types
  const mapPeriodToApiType = (period: 'day' | 'week' | 'month'): EarningsType => {
    switch (period) {
      case 'day':
        return 'daily';
      case 'week':
        return 'weekly';
      case 'month':
        return 'monthly';
      default:
        return 'monthly';
    }
  };

  // Transform API data to chart format
  const transformApiDataToChart = (data: EarningsData, type: EarningsType): ChartDataPoint[] => {
    switch (type) {
      case 'daily':
        return (data as DailyEarnings[]).map(item => ({
          date: item.day,
          earnings: item.earnings
        }));
      case 'weekly':
        return (data as WeeklyEarnings[]).map(item => ({
          date: item.week,
          earnings: item.earnings
        }));
      case 'monthly':
        return (data as MonthlyEarnings[]).map(item => ({
          date: item.month,
          earnings: item.earnings
        }));
      default:
        return [];
    }
  };

  // Fetch chart data when period changes
  useEffect(() => {
    const fetchChartData = async () => {
      if (error) {
        // Set empty data for error state
        setChartData([]);
        return;
      }

      setChartLoading(true);
      setChartError(null);
      
      try {
        const apiType = mapPeriodToApiType(period);
        const data = await getEarningsGraph(apiType);
        const transformedData = transformApiDataToChart(data, apiType);
        setChartData(transformedData);
      } catch (err) {
        console.error('Error fetching chart data:', err);
        setChartError(err instanceof Error ? err.message : 'Grafik verileri alınamadı');
        // Set empty data on error
        setChartData([]);
      } finally {
        setChartLoading(false);
      }
    };

    fetchChartData();
  }, [period, error]);

  const totalEarnings = error ? 0 : earnings;
  const avgEarnings = error ? 0 : chartData.length > 0 ? chartData.reduce((sum, item) => sum + item.earnings, 0) / chartData.length : 0;
  const maxEarnings = error ? 0 : chartData.length > 0 ? Math.max(...chartData.map(item => item.earnings)) : 0;
  
  // Özel Tooltip bileşeni
  const CustomTooltip = ({ active, payload, label }: TooltipProps<ValueType, NameType>) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-white p-3 rounded-lg border border-slate-200 shadow-md">
          <p className="text-slate-600 text-sm font-medium mb-1">{label}</p>
          <p className="text-purple-600 text-sm font-bold">
            ₺{typeof payload[0].value === 'number' ? payload[0].value.toFixed(2) : '0'}
          </p>
        </div>
      );
    }
    return null;
  };

  if (loading) {
    return (
      <Card className="border-0 bg-white shadow-sm rounded-xl overflow-hidden">
        <CardContent className="p-4 sm:p-6">
          {/* Başlık ve Kontroller Skeletonları */}
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 mb-4">
            <div className="flex items-center gap-2">
              <Skeleton className="w-6 h-6 rounded-md" />
              <Skeleton className="w-32 h-6" />
            </div>
            <div className="flex flex-wrap gap-2">
              <Skeleton className="w-40 h-8 rounded-full" />
              <Skeleton className="w-16 h-8 rounded-lg" />
            </div>
          </div>

          {/* İstatistik Kartları Skeletonları */}
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 mb-6">
            <Skeleton className="h-20 rounded-xl" />
            <Skeleton className="h-20 rounded-xl" />
            <Skeleton className="h-20 rounded-xl" />
          </div>

          {/* Grafik Alanı Skeleton */}
          <div className="h-[300px]">
            <Skeleton className="w-full h-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border-0 bg-white shadow-sm rounded-xl overflow-hidden">
      <CardContent className="p-4 sm:p-6">
        {/* Başlık ve Kontroller */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3 mb-4">
          <div className="flex items-center gap-2">
            <div className="w-6 h-6 bg-purple-100 rounded-md flex items-center justify-center">
              <LineChartIcon className="h-4 w-4 text-purple-600" />
            </div>
            <h2 className="text-slate-800 font-medium">{t('dashboard.sections.earnings.chart.title')}</h2>
          </div>
          
          <div className="flex flex-wrap gap-2">
            <Tabs 
              defaultValue="month" 
              value={period} 
              onValueChange={(value) => setPeriod(value as 'day' | 'week' | 'month')}
              className="w-fit"
            >
              <TabsList className="p-0.5 h-8 sm:h-9 bg-slate-100/80 rounded-full">
                <TabsTrigger value="day" className="rounded-full text-xs px-3 sm:px-4">{t('dashboard.sections.earnings.chart.daily')}</TabsTrigger>
                <TabsTrigger value="week" className="rounded-full text-xs px-3 sm:px-4">{t('dashboard.sections.earnings.chart.weekly')}</TabsTrigger>
                <TabsTrigger value="month" className="rounded-full text-xs px-3 sm:px-4">{t('dashboard.sections.earnings.chart.monthly')}</TabsTrigger>
              </TabsList>
            </Tabs>
            
            <div className="flex gap-1 bg-slate-100/80 rounded-lg p-0.5">
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className={`h-full aspect-square ${chartType === 'line' ? 'bg-purple-100 text-purple-600' : 'bg-transparent text-slate-500'}`}
                onClick={() => setChartType('line')}
              >
                <LineChartIcon className="h-4 w-4" />
              </Button>
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className={`h-full aspect-square ${chartType === 'bar' ? 'bg-purple-100 text-purple-600' : 'bg-transparent text-slate-500'}`}
                onClick={() => setChartType('bar')}
              >
                <BarChart3 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
        
        {/* İstatistik Kartları */}
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 mb-6">
          <div className="bg-purple-50 rounded-xl p-3 border border-purple-100/50">
            <p className="text-xs font-medium text-slate-600 mb-1">{t('dashboard.stats.totalEarnings')}</p>
            <p className="text-xl sm:text-2xl font-bold text-purple-600 truncate">₺{totalEarnings.toFixed(2)}</p>
          </div>
          
          <div className="bg-blue-50 rounded-xl p-3 border border-blue-100/50">
            <p className="text-xs font-medium text-slate-600 mb-1">{t('dashboard.sections.earnings.average')}</p>  
            <p className="text-xl sm:text-2xl font-bold text-blue-600 truncate">₺{avgEarnings.toFixed(2)}</p>
            <p className="text-xs text-slate-500 mt-0.5">
              {period === 'day' ? t('dashboard.sections.earnings.chart.daily') : 
               period === 'week' ? t('dashboard.sections.earnings.chart.weekly') : 
               t('dashboard.sections.earnings.chart.monthly')}
            </p>
          </div>
          
          <div className="bg-green-50 rounded-xl p-3 border border-green-100/50">
            <p className="text-xs font-medium text-slate-600 mb-1">{t('dashboard.sections.earnings.highest')}</p>
            <p className="text-xl sm:text-2xl font-bold text-green-600 truncate">₺{maxEarnings.toFixed(2)}</p>
            <p className="text-xs text-slate-500 mt-0.5">
              {period === 'day' ? t('dashboard.sections.earnings.chart.daily') : 
               period === 'week' ? t('dashboard.sections.earnings.chart.weekly') : 
               t('dashboard.sections.earnings.chart.monthly')}
            </p>
          </div>
        </div>
        
        {/* Grafik Alanı */}
        <div className="h-[300px]">
          {chartLoading ? (
            <div className="flex items-center justify-center h-full">
              <p className="text-slate-500">Grafik yükleniyor...</p>
            </div>
          ) : chartError ? (
            <div className="flex items-center justify-center h-full">
              <p className="text-red-500">{chartError}</p>
            </div>
          ) : (
            <ResponsiveContainer width="100%" height="100%">
              {chartType === 'bar' ? (
                <BarChart data={chartData} margin={{ top: 10, right: 10, left: 10, bottom: 25 }}>
                  <CartesianGrid strokeDasharray="3 3" vertical={false} className="stroke-slate-100" />
                  <XAxis 
                    dataKey="date" 
                    axisLine={false}
                    tickLine={false}
                    tick={{ fontSize: 12, fill: '#64748b' }}
                    tickMargin={8}
                  />
                  <YAxis 
                    axisLine={false}
                    tickLine={false}
                    tick={{ fontSize: 12, fill: '#64748b' }}
                    tickMargin={8}
                    tickFormatter={(value) => `₺${value}`}
                  />
                  <Tooltip content={<CustomTooltip />} cursor={{ fill: '#f1f5f9' }} />
                  <Bar 
                    dataKey="earnings" 
                    fill="#9333EA" 
                    radius={[4, 4, 0, 0]}
                    minPointSize={3}
                  />
                </BarChart>
              ) : (
                <AreaChart data={chartData} margin={{ top: 10, right: 10, left: 10, bottom: 25 }}>
                  <defs>
                    <linearGradient id="colorEarnings" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#9333EA" stopOpacity={0.1} />
                      <stop offset="95%" stopColor="#9333EA" stopOpacity={0.01} />
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" vertical={false} className="stroke-slate-100" />
                  <XAxis 
                    dataKey="date" 
                    axisLine={false}
                    tickLine={false}
                    tick={{ fontSize: 12, fill: '#64748b' }}
                    tickMargin={8}
                  />
                  <YAxis 
                    axisLine={false}
                    tickLine={false}
                    tick={{ fontSize: 12, fill: '#64748b' }}
                    tickMargin={8}
                    tickFormatter={(value) => `₺${value}`}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Area
                    type="monotone"
                    dataKey="earnings"
                    stroke="#9333EA"
                    strokeWidth={2}
                    fillOpacity={1}
                    fill="url(#colorEarnings)"
                    dot={{
                      r: 4,
                      strokeWidth: 2,
                      stroke: "#9333EA",
                      fill: "white"
                    }}
                    activeDot={{
                      r: 6,
                      stroke: "#9333EA",
                      strokeWidth: 2,
                      fill: "white"
                    }}
                  />
                </AreaChart>
              )}
            </ResponsiveContainer>
          )}
        </div>
        
     
      </CardContent>
    </Card>
  );
} 