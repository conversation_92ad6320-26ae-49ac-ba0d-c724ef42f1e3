"use client";

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Eye, MousePointerClick, ArrowRight, AlertTriangle, Loader2, SmilePlus, Tag, <PERSON><PERSON>hart } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import Link from 'next/link';
import { useLanguage } from '@/shared/hooks/useLanguage';
import type { Campaign } from '@/shared/services/publisher/campaign-list';
import { Button } from '@/components/ui/button';

interface RecentAdsProps {
  campaigns: Campaign[];
  loading: boolean;
  error: string | null;
}

export function RecentAds({ campaigns, loading, error }: RecentAdsProps) {
  const { t } = useLanguage();

  const getStatusBadge = (status: Campaign['campaign_status']) => {
    const variants = {
      ACTIVE: { bg: 'bg-green-600/80', label: t('ads.status.active') },
      PLANNED: { bg: 'bg-amber-600/80', label: t('ads.status.pending') },
      STOP: { bg: 'bg-red-600/80', label: t('ads.status.completed') },
      FINISHED: { bg: 'bg-blue-600/80', label: t('ads.status.completed') }
    };
    
    const variant = variants[status] || { bg: 'bg-gray-500/80', label: status };
    
    return (
      <Badge className={`${variant.bg} text-[10px] h-5`}>
        {variant.label}
      </Badge>
    );
  };

  const formatDate = (timestamp: number) => {
    if (!timestamp) return '';
    return new Date(timestamp * 1000).toLocaleDateString('tr-TR', {
      day: 'numeric',
      month: 'long',
    });
  };

  if (loading) {
    return (
      <Card className="shadow-md h-full min-h-[420px] flex items-center justify-center">
        <Loader2 className="h-8 w-8 text-purple-500 animate-spin" />
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="shadow-md h-full min-h-[420px] flex flex-col items-center justify-center text-center p-4 bg-red-50 border-red-200">
        <AlertTriangle className="h-8 w-8 text-red-500 mb-2" />
        <p className="text-red-700 font-medium">{t('errors.genericTitle')}</p>
        <p className="text-red-600 text-sm">{error}</p>
      </Card>
    );
  }

  if (!campaigns || campaigns.length === 0) {
    return (
      <Card className="shadow-md h-full min-h-[420px] flex flex-col items-center justify-center text-center p-4 bg-blue-50 border-blue-200">
        <SmilePlus className="h-10 w-10 text-blue-500 mb-3" />
        <h3 className="text-md font-semibold text-slate-700 mb-1">{t('ads.empty.title')}</h3>
        <p className="text-sm text-slate-500 mb-4">{t('ads.empty.description')}</p>
      </Card>
    );
  }

  return (
    <Card className="shadow-md hover:shadow-lg transition-all duration-300 h-full min-h-[420px] flex flex-col">
      <CardHeader className="pb-2 flex flex-row items-center justify-between">
        <CardTitle className="text-lg font-bold text-slate-800 flex items-center gap-2">
          <Tag className="h-5 w-5 text-purple-500" />
          {t('dashboard.sections.ads.recentAds')}
        </CardTitle>
        <Link href="/publisher/ads">
          <Button variant="ghost" size="sm" className="gap-1 text-slate-600 hover:text-slate-800">
            {t('dashboard.sections.ads.viewAll')}
            <ArrowRight className="h-3 w-3" />
          </Button>
        </Link>
      </CardHeader>
      <CardContent className="flex-grow space-y-4">
        {campaigns.map(campaign => (
          <div key={campaign._id} className="flex justify-between items-start border-b pb-4 last:border-b-0 last:pb-0">
            <div className="flex items-start gap-3">
              <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center text-purple-600 flex-shrink-0">
                <Tag className="h-5 w-5" />
              </div>
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <span className="text-sm font-medium text-slate-800">{campaign.community_name || campaign.community_username || 'Bilinmeyen Topluluk'}</span>
                  {getStatusBadge(campaign.campaign_status)}
                </div>
                <p className="text-sm text-slate-600 mb-2 line-clamp-2">{campaign.campaign_title || campaign.campaign_desc || 'Açıklama yok'}</p>
                <div className="flex items-center gap-4 text-xs text-slate-500">
                  <span className="flex items-center gap-1"><Eye className="h-3 w-3" /> {campaign.views?.toLocaleString() || '0'} {t('ads.metrics.views')}</span>
                  <span className="flex items-center gap-1"><MousePointerClick className="h-3 w-3" /> {campaign.clicks?.toLocaleString() || '0'} {t('ads.metrics.clicks')}</span>
                  <span className="flex items-center gap-1"><BarChart className="h-3 w-3" /> {(campaign.ctr || 0).toFixed(1)}% {t('ads.metrics.ctr')}</span>
                </div>
              </div>
            </div>
            <div className="flex flex-col items-end text-sm">
              <span className="text-slate-500 text-xs mb-1">{formatDate(campaign.ends_at)}</span>
              <span className="font-bold text-green-600">₺{campaign.earnings?.toFixed(2) || '0.00'}</span>
            </div>
          </div>
        ))}
      </CardContent>
      <div className="p-4 pt-0 flex justify-end">
        <Link href="/publisher/ads">
          <Button variant="ghost" size="sm" className="gap-1 text-slate-600 hover:text-slate-800">
            {t('dashboard.sections.ads.viewAll')}
            <ArrowRight className="h-3 w-3" />
          </Button>
        </Link>
      </div>
    </Card>
  );
} 