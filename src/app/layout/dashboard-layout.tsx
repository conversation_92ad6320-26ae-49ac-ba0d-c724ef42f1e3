"use client"

import { Sidebar } from "./sidebar"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, <PERSON><PERSON><PERSON>onte<PERSON> } from "@/components/ui/sheet"
import { Menu } from "lucide-react"

interface DashboardLayoutProps {
  children: React.ReactNode
}

export function DashboardLayout({ children }: DashboardLayoutProps) {
  return (
    <div className="flex min-h-screen">
      {/* Mobil Navbar ve Slider */}
      <div className="md:hidden fixed top-0 left-0 w-full h-14 bg-white border-b z-40 flex items-center px-4 justify-between">
        <Sheet>
          <SheetTrigger asChild>
            <button className="p-2 rounded-md hover:bg-gray-100 focus:outline-none">
              <Menu className="h-6 w-6 text-gray-700" />
            </button>
          </SheetTrigger>
          <SheetContent side="left" className="p-0 w-64">
            <Sidebar mobile />
          </SheetContent>
        </Sheet>
        <span className="font-semibold text-lg text-blue-600">Adnomio</span>
      </div>
      {/* Sidebar sadece md ve üstü için */}
      <div className="hidden md:block">
        <Sidebar />
      </div>
      <div className="flex-1 p-6 md:p-8 overflow-auto pt-16 md:pt-0">{children}</div>
    </div>
  )
}
