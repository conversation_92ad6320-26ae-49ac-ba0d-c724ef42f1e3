"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { LucideIcon, Home, Users, CreditCard, BarChart2, Settings, DollarSign, LogOut, ChevronDown, ChevronRight } from "lucide-react"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { useState } from "react"
import { useAuth } from "@/shared/hooks/useAuth"

interface SidebarProps extends React.HTMLAttributes<HTMLDivElement> {
  placeItems?: "start" | "center" | "end"
  mobile?: boolean
}

interface MenuItem {
  href?: string
  icon: LucideIcon
  title: string
  subItems?: MenuItem[]
  roles?: string[]
}

const adminMenuItems: MenuItem[] = [
  { href: "/admin", icon: Home, title: "Dashboard", roles: ["admin"] },
  { href: "/admin/users", icon: Users, title: "<PERSON><PERSON><PERSON><PERSON><PERSON> Yönetimi", roles: ["admin"] },
  { 
    href: "/admin/developments", icon: BarChart2, title: "Gelişmeler", roles: ["admin"] 
  },
  { 
    icon: CreditCard, 
    title: "Ödemeler",
    roles: ["admin"],
    subItems: [
      { href: "/admin/payments", icon: CreditCard, title: "Reklamveren Ödemeleri" },
      { href: "/admin/publisher-payments", icon: DollarSign, title: "Yayıncı Ödemeleri" },
    ]
  },
  { href: "/admin/campaigns", icon: BarChart2, title: "Kampanya Yönetimi", roles: ["admin"] },
  { href: "/admin/blog", icon: BarChart2, title: "Blog yönetimi", roles: ["admin"] },
  { href: "/admin/ad-planning", icon: BarChart2, title: "Reklam Planlanması", roles: ["admin"] },
  { href: "/admin/settings", icon: Settings, title: "Ayarlar", roles: ["admin"] }
]

const advertiserMenuItems: MenuItem[] = [
  { href: "/advertiser", icon: Home, title: "Dashboard", roles: ["advertiser"] },
  { href: "/advertiser/campaigns", icon: BarChart2, title: "Kampanyalarım", roles: ["advertiser"] },
  { href: "/advertiser/payments", icon: CreditCard, title: "Ödemelerim", roles: ["advertiser"] },
  { href: "/advertiser/settings", icon: Settings, title: "Ayarlar", roles: ["advertiser"] }
]

const publisherMenuItems: MenuItem[] = [
  { href: "/publisher", icon: Home, title: "Dashboard", roles: ["publisher"] },
  { href: "/publisher/ads", icon: BarChart2, title: "Reklam Alanları", roles: ["publisher"] },
  { href: "/publisher/earnings", icon: DollarSign, title: "Kazançlarım", roles: ["publisher"] },
  { href: "/publisher/settings", icon: Settings, title: "Ayarlar", roles: ["publisher"] }
]

export function Sidebar({ className, placeItems = "start", mobile = false }: SidebarProps) {
  const pathname = usePathname()
  const [openMenus, setOpenMenus] = useState<string[]>([])
  const { user, logout, loading } = useAuth()

  const getMenuItems = () => {
    if (!user) return []
    
    switch (user.role) {
      case "admin":
        return adminMenuItems
      case "advertiser":
        return advertiserMenuItems
      case "publisher":
        return publisherMenuItems
      default:
        return []
    }
  }

  const toggleSubMenu = (title: string) => {
    setOpenMenus(prev => 
      prev.includes(title) 
        ? prev.filter(item => item !== title)
        : [...prev, title]
    )
  }

  const MenuItem = ({ item, level = 0 }: { item: MenuItem, level?: number }) => {
    const hasSubItems = item.subItems && item.subItems.length > 0
    const isOpen = openMenus.includes(item.title)
    const isActive = item.href ? pathname === item.href : false

    if (hasSubItems && item.subItems) {
      return (
        <div>
          <button
            onClick={() => toggleSubMenu(item.title)}
            className={cn(
              "flex items-center justify-between w-full rounded-lg px-3 py-2 text-sm transition-all hover:bg-accent",
              isOpen ? "bg-accent/50" : "transparent",
              "text-muted-foreground"
            )}
          >
            <div className="flex items-center gap-3">
              <item.icon className="h-4 w-4" />
              {item.title}
            </div>
            {isOpen ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />}
          </button>
          {isOpen && (
            <div className="ml-4 mt-1 space-y-1">
              {item.subItems.map((subItem, index) => (
                <MenuItem key={index} item={subItem} level={level + 1} />
              ))}
            </div>
          )}
        </div>
      )
    }

    return (
      <Link
        href={item.href || "#"}
        className={cn(
          "flex items-center gap-3 rounded-lg px-3 py-2 text-sm transition-all hover:bg-accent",
          isActive ? "bg-accent" : "transparent",
          isActive ? "text-accent-foreground" : "text-muted-foreground",
          level > 0 && "text-sm"
        )}
      >
        <item.icon className="h-4 w-4" />
        {item.title}
      </Link>
    )
  }

  const ProfileSection = () => (
    <div className="px-3 py-4">
      <div className="flex items-center gap-3 rounded-lg px-3 py-2">
        <Avatar>
          <AvatarFallback>{user?.name?.[0] || "U"}</AvatarFallback>
          <AvatarImage src="/avatar.png" />
        </Avatar>
        <div className="flex flex-col">
          <span className="text-sm font-medium">{user?.name}</span>
          <span className="text-xs text-muted-foreground">{user?.email}</span>
        </div>
      </div>
      {!mobile && (
        <Button 
          variant="ghost" 
          className="w-full mt-2 text-red-500 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-950/50 justify-start gap-2"
          onClick={logout}
        >
          <LogOut className="h-4 w-4" />
          Çıkış Yap
        </Button>
      )}
    </div>
  )

  return (
    <div className={(mobile ? "flex" : "hidden md:flex") + " flex-col fixed left-0 top-0 w-64 h-screen bg-background border-r z-30"}>
      <div className="flex h-16 items-center border-b px-6">
        <Link href="/" className="flex items-center gap-2 font-semibold text-xl text-blue-600">
          <span>Adnomio</span>
        </Link>
      </div>
      <div className="flex-1 flex flex-col h-[calc(100vh-4rem)]">
        <ScrollArea className="flex-1 overflow-y-auto">
          <div className="p-2">
            {loading ? (
              <div className="p-4 text-center text-muted-foreground">Yükleniyor...</div>
            ) : !user ? (
              <div className="p-4 text-center text-muted-foreground">Giriş yapmalısınız</div>
            ) : (
              <nav className="grid gap-1">
                {getMenuItems().map((item, index) => (
                  <MenuItem key={index} item={item} />
                ))}
                {mobile && (
                  <Button 
                    variant="ghost" 
                    className="w-full mt-4 text-red-500 hover:text-red-600 hover:bg-red-50 dark:hover:bg-red-950/50 justify-start gap-2"
                    onClick={logout}
                  >
                    <LogOut className="h-4 w-4" />
                    Çıkış Yap
                  </Button>
                )}
              </nav>
            )}
          </div>
        </ScrollArea>
        <div className="border-t bg-background mt-auto">
          <ProfileSection />
        </div>
      </div>
    </div>
  )
}
