'use client';

import type { <PERSON><PERSON><PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { Toaster } from "sonner";
import { Toaster as CustomToaster } from "@/components/ui/toaster";
import I18nProvider from "@/shared/config/i18n-provider";
import { FormValidationProvider } from '@/shared/hooks/FormValidationProvider';

const inter = Inter({ subsets: ["latin"] });


export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="tr" suppressHydrationWarning>
      <body className={inter.className}>
        <I18nProvider>
          <FormValidationProvider>
            {children}
            <Toaster position="top-right" expand richColors />
            <CustomToaster />
          </FormValidationProvider>
        </I18nProvider>
      </body>
    </html>
  );
}
