# Adnomio Yönetim Paneli - <PERSON>urs<PERSON>ı

## Proje <PERSON>
- `src/app`: Next.js 13+ App Router için sayfa ve route'lar
- `src/components`: UI bileşenleri ve sayfa bileşenleri
  - `/dashboard`: Gösterge paneli bileşenleri
  - `/layout`: Ana düzen bileşenleri
  - `/ui`: Temel UI bileşenleri (button, card vb.)
- `src/hooks`: Özel React Hook'ları
- `src/lib`: Yardımcı fonksiyonlar ve yardımcı programlar

## Kodlama Standartları

### Genel
- Tüm dosyalar TypeScript ile yazılmalıdır (`.tsx` veya `.ts` uzantılı)
- Dosya ve klasör isimleri küçük harfle ve kebab-case formatında olmalıdır
- Bileşen isimleri PascalCase formatında olmalıdır
- Her bileşen kendi dosyasında tanımlanmalıdır

### Component Kuralları
- UI bileşenleri için `src/components/ui` klasörünü kullanın
- <PERSON>fa düzenleri için `src/components/layout` klasörünü kullanın
- Sayfa özel bileşenleri için `src/components/dashboard` veya uygun bir alt klasör kullanın
- Radix UI bileşenleri için kendi UI bileşenlerimizde wrapper kullanın

### Stillendirme
- CSS için Tailwind CSS kullanın
- Custom stil gerektiğinde globals.css'e ekleme yapın
- Karmaşık tailwind sınıfları için clsx/cn yardımcı fonksiyonunu kullanın
```
import { cn } from "@/lib/utils"
```

### TypeScript
- Prop türlerini açıkça tanımlayın ve interface kullanın
- "any" türünden kaçının
- Uygun şekilde React.ReactNode, React.HTMLAttributes gibi yerleşik türleri kullanın

### İsimlendirme Kuralları
- Bileşenler: `Button.tsx`, `Card.tsx`, `DashboardLayout.tsx` 
- Hook'lar: `useUser.ts`, `useAuth.ts`
- Yardımcı fonksiyonlar: `utils.ts`, `api.ts`

### Dosya Organizasyonu
- Bileşenler içinde ilgili arayüzleri ve türleri tanımlayın
- Büyük sayfaları alt bileşenlere ayırın
- İlgili bileşenleri aynı klasöre koyun

### Import Sıralaması
1. React/Next.js kütüphaneleri
2. Üçüncü taraf kütüphaneler
3. Boş satır
4. Projeden import (proje kök adres olarak `@/` kullanır)

### Düzen Kuralları
- Kod içinde boşluklar için 2 boşluk kullanın
- Sınıf adları için cn/clsx yardımcı fonksiyonu ile düzenli tutun
- JSX içinde uzun prop listelerini birden çok satıra bölün

### Radix UI Kullanımı
- Radix UI bileşenlerini doğrudan kullanmak yerine kendi UI bileşenlerimizde sarmalayın
- Tüm tema ayarlarını components.json içinde tanımlayın

## Git Kullanımı
- Commit mesajları açıklayıcı olmalıdır
- Feature ve bug fix'ler için ayrı branch'ler oluşturun
- PR'lar küçük ve odaklanmış olmalıdır

## Performans Önlemleri
- Gereksiz yeniden oluşturmaları önlemek için React.memo, useMemo ve useCallback kullanın
- Büyük veri setleri için sayfalandırmayı uygulayın
- Dinamik içe aktarmalar veya Next.js lazy loading özelliklerini kullanın 