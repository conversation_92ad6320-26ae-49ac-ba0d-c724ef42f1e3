# AdnomioAdmin - Detaylı Proje Dokümantasyonu

## 1. <PERSON>je Genel Bakış

AdnomioAdmin, reklam yönetimi platformu için geliştirilmiş kapsamlı bir yönetim panelidir. Next.js framework'ü üzerine kurulmuş ve React bileşenleri kullanılarak geliştirilmiştir. Proje, çeşitli kullanıcı rollerine (admin, reklamveren, yayıncı) hizmet vermek üzere tasarlanmıştır.

### 1.1 Temel Özellikler

- **Çoklu Rol Desteği**: Admin, Reklamveren ve Yayıncı rolleri için özelleştirilmiş paneller
- **Kampanya Yönetimi**: Reklam kampanyalarının oluşturulması, takibi ve analizi
- **Kullanıcı Yönetimi**: Kullanıcı hesaplarının oluşturulması ve yönetimi
- **Ödeme İşlemleri**: Ödeme takibi ve yönetimi
- **Analitik Dashboard**: Performans metrikleri ve görselleştirmeleri
- **Kimlik Doğrulama**: Güvenli giriş ve yetkilendirme sistemi

## 2. Proje Yapısı

```
/src
├── app/                 # Next.js 13+ app router yapısı
│   ├── admin/           # Admin paneli sayfaları
│   ├── advertiser/      # Reklamveren sayfaları
│   ├── auth/            # Kimlik doğrulama sayfaları
│   ├── publisher/       # Yayıncı sayfaları
│   ├── layout/          # Yerleşim şablonları
│   ├── layout.tsx       # Root yerleşim
│   ├── ClientBody.tsx   # Client tarafı body bileşeni
│   └── globals.css      # Global stil tanımları
├── components/          # Paylaşılan UI bileşenleri
│   ├── ui/              # UI kütüphanesi bileşenleri
│   └── ClientDate.tsx   # Client tarafı tarih bileşeni
├── hooks/               # Özel React hookları
│   └── use-mobile.tsx   # Mobil görünüm tespiti için hook
├── lib/                 # Yardımcı fonksiyonlar ve kütüphaneler
│   └── utils.ts         # Genel yardımcı fonksiyonlar
└── shared/              # Paylaşılan modüller
    ├── services/        # API servisleri
    │   ├── admin/       # Admin için API servisleri
    │   ├── advertiser/  # Reklamveren için API servisleri
    │   ├── publisher/   # Yayıncı için API servisleri
    │   ├── auth-service.ts   # Kimlik doğrulama servisi
    │   └── api-config.ts     # API yapılandırması
    ├── types/           # TypeScript tip tanımları
    └── hooks/           # Paylaşılan özel hooklar
        └── useAuth.ts   # Kimlik doğrulama hook'u
```

## 3. Teknoloji Yığını

### 3.1 Frontend Framework ve Kütüphaneler

- **Next.js**: React tabanlı, sunucu tarafı rendering (SSR) destekli framework
- **React**: UI bileşenleri için JavaScript kütüphanesi
- **TypeScript**: Statik tip kontrolü için JavaScript süper kümesi
- **Recharts**: Veri görselleştirme için React tabanlı grafik kütüphanesi
- **Lucide Icons**: Modern, özelleştirilebilir ikonlar
- **TailwindCSS**: Utility-first CSS framework (Bileşen stillerinden anlaşılmaktadır)

### 3.2 Backend Entegrasyonu

- **REST API**: HTTP tabanlı API iletişimi
- **localStorage**: Kullanıcı oturumu ve kimlik bilgilerinin yerel depolanması

## 4. Modüller ve Bileşenler

### 4.1 Kimlik Doğrulama (Authentication)

Kimlik doğrulama sistemi, `src/shared/services/auth-service.ts` içinde yer alan `AuthService` sınıfı üzerine kuruludur. Bu servis:

- **login()**: Kullanıcı girişi
- **logout()**: Çıkış işlemi
- **getCurrentUser()**: Oturum açmış kullanıcı bilgilerini alma
- **getAuthToken()**: Yetkilendirme token'ını alma
- **isAuthenticated()**: Kullanıcının oturum durumunu kontrol etme
- **listUsers()**: Kullanıcı listesini alma

işlevlerini sağlar.

`useAuth` hook'u (`src/shared/hooks/useAuth.ts`), kimlik doğrulama durumunu yönetir ve rol tabanlı erişim kontrolü sağlar.

### 4.2 UI Bileşenleri

Proje, kapsamlı bir UI bileşen kütüphanesi içerir (`src/components/ui/`):

- **Formlar ve Girdiler**: Input, Button, Checkbox, Radio, Select, Textarea
- **Karmaşık Bileşenler**: Dialog, Dropdown, Sidebar, Sheet
- **Veri Görselleştirme**: Chart, Table, Calendar
- **Bildirimler**: Toast, Alert
- **Düzen**: Card, Tabs, Separator

Bu bileşenler, tutarlı bir kullanıcı deneyimi sağlar ve tüm uygulama boyunca tekrar kullanılır.

### 4.3 Admin Paneli

Admin panel'i (`src/app/admin/`), platformun yönetim merkezi olarak işlev görür:

- **Dashboard**: Genel bakış ve analitikler
- **Kullanıcı Yönetimi**: Kullanıcıları görüntüleme, düzenleme ve yetkilendirme
- **Kampanya Yönetimi**: Reklam kampanyalarını izleme ve yönetme
- **Ödeme İşlemleri**: Ödemelerin takibi ve yönetimi
- **Ayarlar**: Sistem yapılandırması

Dashboard, interaktif grafikler ve performans göstergeleri içerir:

- Görüntülenme, etkileşim ve dönüşüm istatistikleri
- Platform bazlı performans verileri
- Reklam türü analizleri
- Coğrafi dağılım haritası
- Son aktiviteler

### 4.4 Reklamveren ve Yayıncı Panelleri

- **Reklamveren Paneli** (`src/app/advertiser/`): Reklamverenlerin kampanyalarını yönetmesine olanak tanır
- **Yayıncı Paneli** (`src/app/publisher/`): Yayıncıların içeriklerini ve gelirlerini yönetmesini sağlar

### 4.5 API Entegrasyonu

API yapılandırması (`src/shared/services/api-config.ts`), tüm endpoint'leri ve temel URL'yi tanımlar:

```typescript
export const API_CONFIG = {
  ENDPOINTS: {
    AUTH: {
      LOGIN: "/user/login",
      LOGOUT: "/user/logout"
    },
    USERS: "/user/list",
    ADMIN: {
      USER_LIST: "/admin/user/list",
      USER_STATISTICS: "/admin/user/statistics"
    },
    CAMPAIGN: {
      LIST: "/advertiser/campaign/list",
      CREATE: "/advertiser/campaign/create",
      DELETE: "/advertiser/campaign/delete",
      PAUSE: "/advertiser/campaign/pause",
      RESUME: "/advertiser/campaign/resume",
      STATISTICS: "/advertiser/campaign/statistics"
    }
  }
}
```

Bu yapılandırma, servis modüllerinde kullanılır ve API endpoint'lerine tutarlı erişim sağlar.

## 5. Kimlik Doğrulama ve Yetkilendirme

### 5.1 Oturum Yönetimi

Oturum yönetimi, tarayıcı `localStorage`'ını kullanarak gerçekleştirilir:

- Kullanıcı başarıyla giriş yaptığında, kullanıcı bilgileri ve token `localStorage`'a kaydedilir
- `useAuth` hook'u, mevcut oturum durumunu kontrol eder ve rol tabanlı yönlendirme sağlar
- Çıkış yapıldığında, `localStorage`'dan kullanıcı bilgileri silinir

### 5.2 Rol Tabanlı Erişim Kontrolü

Sistem üç ana kullanıcı rolünü destekler (`src/shared/types/common.ts`):

```typescript
export type UserRole = 'admin' | 'advertiser' | 'publisher' 
```

Her rol için ayrı bir navigasyon akışı ve yetkiler bulunmaktadır:

- **Admin**: Tüm sistem işlevlerine erişim
- **Reklamveren**: Kampanya yönetimi ve analitiklere erişim
- **Yayıncı**: İçerik ve gelir yönetimine erişim

## 6. Kullanıcı Arayüzü

### 6.1 Responsive Tasarım

`use-mobile.tsx` hook'u, mobil cihazların tespiti için kullanılır ve responsive tasarımı destekler.

### 6.2 Tema ve Stiller

Proje, modern ve temiz bir tasarım dilini takip eder:
- Gradyan kartlar ve arkaplanlar
- Yuvarlak köşeli bileşenler
- İnteraktif hover efektleri
- Tutarlı renk paleti (mor, mavi, yeşil tonları)

## 7. Veri İşleme ve Yönetimi

### 7.1 API İletişimi

Tüm API istekleri, ilgili servis modüllerinde yönetilir:
- `fetch` API'si kullanılarak HTTP istekleri yapılır
- İstek başarısız olursa hatalar yakalanır ve uygun şekilde işlenir
- Başarılı yanıtlar, uygun tiplerle (TypeScript) dönüştürülür

### 7.2 Veri Görselleştirme

Veri görselleştirme için Recharts kütüphanesi kullanılır:
- Zaman serisi verileri için çizgi ve alan grafikleri
- Karşılaştırmalı veriler için çubuk grafikleri
- Kategori dağılımları için pasta grafikleri
- Coğrafi veriler için haritalar

## 8. Kurulum ve Başlangıç

### 8.1 Gereksinimler

- Node.js (>= 14.x)
- npm veya yarn

### 8.2 Kurulum Adımları

1. Projeyi klonlama:
   ```bash
   git clone [repository-url]
   cd AdnomioAdmin
   ```

2. Bağımlılıkları yükleme:
   ```bash
   npm install
   # veya
   yarn install
   ```

3. Geliştirme sunucusunu başlatma:
   ```bash
   npm run dev
   # veya
   yarn dev
   ```

4. Tarayıcıda açma:
   `http://localhost:3000`

## 9. Kod Standartları ve Kalite

### 9.1 TypeScript Kullanımı

Proje, tip güvenliği için TypeScript kullanır:
- Arayüzler ve tipler (`User`, `ApiResponse`, vb.) tanımlanmıştır
- Servisler ve bileşenler tipler ile belgelenmiştir
- "Strict mode" etkindir

### 9.2 Bileşen Yapısı

React bileşenleri genellikle şu yapıyı takip eder:
- İçe aktarmalar (imports)
- Tip tanımları
- Bileşen fonksiyonu
- Veri ve durum (state) tanımlamaları
- Yardımcı fonksiyonlar
- JSX render çıktısı

## 10. Güvenlik Önlemleri

### 10.1 Kimlik Doğrulama Güvenliği

- Token tabanlı yetkilendirme
- Oturum durumu kontrolü
- Rol tabanlı erişim kısıtlamaları

### 10.2 API Güvenliği

- Yetkilendirme header'ları
- Hata yakalama ve işleme
- Başarısız isteklerde güvenli geribildirim

## 11. Performans Optimizasyonları

- Client-side hydration için `suppressHydrationWarning`
- Minimum DOM manipülasyonları
- Hafıza sızıntılarını önlemek için useEffect temizleme
- `useMemo` ile hesaplamalı değerlerin önbelleğe alınması

## 12. İleri Geliştirme ve Genişletme

### 12.1 Potansiyel Geliştirmeler

- SSR (Server-side Rendering) optimizasyonları
- Daha fazla analitik entegrasyonu
- Ek ödeme sağlayıcıları
- İleri düzey kampanya hedefleme özellikleri
- AI tabanlı öneri sistemi

### 12.2 Ölçeklenebilirlik

Proje, modüler yapısı sayesinde kolay genişletilebilir:
- Yeni rol tipleri eklenebilir
- Yeni analitik modülleri entegre edilebilir
- Ek API servisleri kolayca eklenebilir

---

Bu dokümantasyon, AdnomioAdmin projesinin genel yapısını, bileşenlerini ve işlevselliğini kapsamlı bir şekilde ele almaktadır. Projede çalışacak geliştiriciler için bir başlangıç noktası ve referans olarak hazırlanmıştır. 