---
description: 
globs: 
alwaysApply: true
---
# Adnomio Yönetim Paneli - <PERSON><PERSON><PERSON>nemli not: dökümanlar için Context7 mcpsini kullan

## Proje <PERSON>
- `src/app`: Next.js 13+ App Router için sayfa ve route'lar
  - Her sayfa kendi klasöründe `page.tsx` olarak bulunur
  - Sayfa bileşenleri `components` alt klasöründe modüler olarak ayrılır
  - Sayfa özel tipler `types.ts` dosyasında tanımlanır
  - Say<PERSON> özel hooklar `hooks.ts` dosyasında tanımlanır
  - <PERSON><PERSON> özel yardımcı fonksiyonlar `utils.ts` dosyasında tanımlanır
- `src/components`: Genel UI bileşenleri
  - `/ui`: Temel UI bileşenleri (button, card vb.)
  - `/layout`: Ana düzen bileşenleri
  - `/dashboard`: Gösterge paneli bileşenleri
- `src/hooks`: <PERSON>l <PERSON>act Hook'ları
- `src/lib`: <PERSON>l yardımcı fonksiyonlar

## Kodlama Standartları

### Genel
- Tüm dosyalar TypeScript ile yazılmalıdır (`.tsx` veya `.ts` uzantılı)
- Dosya ve klasör isimleri küçük harfle ve kebab-case formatında olmalıdır
- Bileşen isimleri PascalCase formatında olmalıdır
- Her bileşen kendi dosyasında tanımlanmalıdır
- Tüm sayfaların yazı boyutları ve düzenleri standart olmasına özen göster
- Tekrarlayan kodlar yazmakçtan kaçın!

### Sayfa Yapısı
- Her sayfa kendi klasöründe olmalıdır
- Sayfa bileşenleri modüler olarak ayrılmalıdır
- Her bileşen tek bir sorumluluğa sahip olmalıdır
- Sayfa bileşenleri `components` alt klasöründe olmalıdır
- Sayfa özel tipler, hooklar ve yardımcı fonksiyonlar sayfanın kendi klasöründe olmalıdır

### Component Kuralları
- UI bileşenleri için `src/components/ui` klasörünü kullanın
- Sayfa düzenleri için `src/components/layout` klasörünü kullanın
- Sayfa özel bileşenleri için ilgili sayfanın `components` klasörünü kullanın
- Radix UI bileşenleri için kendi UI bileşenlerimizde wrapper kullanın

### Interface ve Type Tanımlamaları
- Sayfa özel tipler ilgili sayfanın `types.ts` dosyasında tanımlanmalı
- Genel tipler `src/types` klasöründe tanımlanmalı
- Interface isimleri PascalCase formatında olmalı
- Her interface açıklayıcı bir isme sahip olmalı

### Modal ve Dialog Kullanımı
- Modal bileşenleri ilgili sayfanın `components` klasöründe tanımlanmalı
- Modal state yönetimi sayfa seviyesinde yapılmalı
- Modal içeriği ve aksiyonları prop'lar ile yönetilmeli
- Her modal tek bir sorumluluk prensibine uymalı

### Stillendirme
- CSS için Tailwind CSS kullanın
- Custom stil gerektiğinde globals.css'e ekleme yapın
- Karmaşık tailwind sınıfları için clsx/cn yardımcı fonksiyonunu kullanın
```
import { cn } from "@/lib/utils"
```

### TypeScript
- Prop türlerini açıkça tanımlayın ve interface kullanın
- "any" türünden kaçının
- Uygun şekilde React.ReactNode, React.HTMLAttributes gibi yerleşik türleri kullanın

### İsimlendirme Kuralları
- Sayfalar: `page.tsx`
- Bileşenler: `ComponentName.tsx`
- Hook'lar: `useHookName.ts`
- Yardımcı fonksiyonlar: `utils.ts`

### Dosya Organizasyonu
- Her sayfa kendi klasöründe olmalı
- Sayfa bileşenleri modüler olarak ayrılmalı
- İlgili bileşenler aynı klasöre koyun
- Her bileşen tek bir sorumluluğa sahip olmalı

### Import Sıralaması
1. React/Next.js kütüphaneleri
2. Üçüncü taraf kütüphaneler
3. Boş satır
4. Projeden import (proje kök adres olarak `@/` kullanır)

### Düzen Kuralları
- Kod içinde boşluklar için 2 boşluk kullanın
- Sınıf adları için cn/clsx yardımcı fonksiyonu ile düzenli tutun
- JSX içinde uzun prop listelerini birden çok satıra bölün

### Radix UI Kullanımı
- Radix UI bileşenlerini doğrudan kullanmak yerine kendi UI bileşenlerimizde sarmalayın
- Tüm tema ayarlarını components.json içinde tanımlayın

## Git Kullanımı
- Commit mesajları açıklayıcı olmalıdır
- Feature ve bug fix'ler için ayrı branch'ler oluşturun
- PR'lar küçük ve odaklanmış olmalıdır

## Performans Önlemleri
- Gereksiz yeniden oluşturmaları önlemek için React.memo, useMemo ve useCallback kullanın
- Büyük veri setleri için sayfalandırmayı uygulayın
- Dinamik içe aktarmalar veya Next.js lazy loading özelliklerini kullanın

### State Yönetimi
- Lokal state için `useState` ve `useReducer` kullanın
- Global state için Zustand tercih edin
- Form state yönetimi için React Hook Form kullanın
- State güncellemelerini memoize edin
- Complex state logic için custom hook'lar oluşturun

### Error Handling
- Her API çağrısı için try-catch bloğu kullanın
- Error boundary'ler ile hataları yakalayın
- Toast bildirimleri için consistent bir yapı kullanın
- Validation hataları için form-level handling kullanın
- Network hataları için retry mekanizması ekleyin

### Data Fetching
- Server Components için direct fetch kullanın
- Client Components için SWR veya TanStack Query tercih edin
- API route'ları için consistent error handling kullanın
- Loading ve error state'leri için Suspense ve ErrorBoundary kullanın
- Cache stratejilerini doğru belirleyin

### Testing
- Unit testler için Jest ve React Testing Library kullanın
- E2E testler için Playwright tercih edin
- Her component için en az bir snapshot test yazın
- Critical path'ler için integration test yazın
- Test coverage minimum %80 olmalı

### Security
- Tüm form inputları için validation yapın
- API rotaları için rate limiting uygulayın
- Authentication state'i güvenli şekilde yönetin
- Sensitive dataları client'ta tutmayın
- CORS ve CSP politikalarını doğru yapılandırın

### Optimization
- Image optimizasyonu için next/image kullanın
- Bundle size için dynamic imports kullanın
- Heavy computation'lar için web workers kullanın
- Resource hints için next/head kullanın
- Lighthouse score minimum 90 olmalı

### Accessibility
- Tüm interaktif elementler için ARIA labelları ekleyin
- Keyboard navigation destekleyin
- Color contrast oranlarına dikkat edin
- Screen reader uyumluluğu sağlayın
- Focus yönetimini doğru yapın

### Documentation
- Her component için JSDoc ile dokümantasyon yazın
- Complex logic için inline comment ekleyin
- README dosyasını güncel tutun
- API endpoint'leri için Swagger/OpenAPI kullanın
- Değişiklikleri CHANGELOG.md'de belgelendirin

### Code Review
- PR'lar için template kullanın
- Review'da performans etkisini kontrol edin
- Bundle size değişimini inceleyin
- Accessibility sorunlarını kontrol edin
- Security vulnerability'leri tarayın

### Monitoring
- Client-side error tracking kurun
- Performance monitoring ekleyin
- User behavior analytics entegre edin
- API health checking implemente edin
- Resource usage monitoring ekleyin

## Environment ve Configuration
- Environment variable'ları merkezi yönetin
- Feature flag'ler için yapı kurun
- API URL'leri için config dosyası kullanın
- Environment-specific ayarları ayırın
- Sensitive dataları .env'de tutun