{"name": "admin-dashboard", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -H 0.0.0.0 --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-portal": "^1.1.8", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.13", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@react-jvectormap/core": "^1.0.4", "@tremor/react": "^3.18.7", "axios": "^1.6.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "i18next": "^25.1.2", "i18next-browser-languagedetector": "^8.1.0", "i18next-http-backend": "^3.0.2", "lucide-react": "^0.482.0", "next": "^15.2.0", "next-themes": "^0.4.6", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-i18next": "^15.5.1", "react-icons": "^5.5.0", "react-simple-maps": "^3.0.0", "recharts": "^2.15.1", "sonner": "^2.0.3", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "topojson-client": "^3.1.0", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@iconify-json/logos": "^1.2.4", "@iconify-json/skill-icons": "^1.2.0", "@iconify/react": "^6.0.0", "@types/node": "^20", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@types/react-simple-maps": "^3.0.6", "eslint": "^9", "eslint-config-next": "15.1.7", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}, "type": "module"}